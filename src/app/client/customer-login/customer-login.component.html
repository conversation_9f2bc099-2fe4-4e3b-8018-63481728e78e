<div class="client-signup">
  <div class="client-signup-inner">
    <div class="client-signup-leftside">
      <div class="vendor-signup-title">
        <h2 class="client-signup-title">{{ customerPortal?.lbl_partner_login || 'Partner Login' }}</h2>
        <span> {{ customerPortal?.lbl_login_to_your_account || 'Login to Your Account' }} </span>
      </div>
      <div class="userlogin-box">
          
        <div class="userlogin-box-inner">
          <div class="userlogin-body">
            <div class="custom-alert mt-3 mb-3 " #hasAlert></div>            
            <form class="loginform client-loginform" [formGroup]="form" (submit)="submit()">
              <!-- <p class="succesmsg-text" *ngIf="isSuccessMessage">{{successMessageText}}</p> -->
              <p class="errormsg-text" *ngIf="isErrorMessage">{{errorMessageText}}</p>
              
              <div class="form-group mb-4">
                <input type="text" name="email" class="input-field"               
                [attr.placeholder]="customerPortal?.lbl_email || 'Email' + (customerPortal?.lbl_email ? '*' : '')"
                formControlName="email">
                <small *ngIf="submitted && form.get('email').hasError('email') && !form.get('email').hasError('required')" class="form-text text-invalid">
                  Please enter a valid email address
                </small>
                <small *ngIf="submitted && form.get('email').hasError('required')" class="form-text text-invalid">
                    Email is <strong>required</strong>
                </small>
              </div>
              <div class="form-group mb-4 password-group">
                <input name="password" class="input-field"
                [type]="isShowPassword ? 'text' : 'password'"                 
                [attr.placeholder]="customerPortal?.lbl_password || 'Password' + (customerPortal?.lbl_password ? '*' : '')"
                formControlName="password">
                <div class="input-group-append">
                  <button class="btn" type="button"
                  (click)="isShowPassword = !isShowPassword"    
                  >
                    <i class="fa fa-eye"></i>
                  </button>
                </div>

                <small *ngIf="submitted && form.get('password').hasError('required')" class="form-text text-invalid">
                  Password is
                  <strong>required</strong>!
                </small>
                <small *ngIf="submitted && !form.get('password').hasError('required') && form.get('password').hasError('minlength')" class="form-text text-invalid">
                    Password must have at least
                    <strong>6</strong> character!
                </small>
              </div>
              <div class="form-group text-center">
                <button type="submit" class="login">{{ customerPortal?.lbl_login || 'Log in' }}</button>
              </div>
              <!-- <div class="form-group text-left mb-0 pt-3">
                <div class="custom-control custom-checkbox custom-control-inline mr-0 pl-0">
                    <a href="#"> Create an Account</a>
                </div>
                <div class="custom-control custom-checkbox custom-control-inline mr-0 pt-1 float-right">
                  <a href="#"> Forgot Your Password</a>
                </div>
              </div> -->
            </form>
          </div>
        </div>
      </div>
    </div>
    <div class="client-signup-rightside">
        <img src="./../../../assets/img/home/<USER>" />
    </div>
  </div>
</div>
