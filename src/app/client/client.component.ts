import { Component, Inject, OnInit, PLATFORM_ID } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { isPlatformBrowser } from '@angular/common';
import { filter } from 'rxjs/operators';
import { apiUrl } from '../globals/endPoint/config';
import { OnlineAuthService } from '../modules/online-store-auth/online-auth.service';

declare let gtag:any;

@Component({
  selector: 'app-client',
  templateUrl: './client.component.html',
  styleUrls: ['./client.component.css']
})
export class ClientComponent implements OnInit {

  contents:any;
  customCss;
  uId;
  is_customJs_exist;
  custom_js_code;
  constructor(
    private router: Router,
    @Inject(PLATFORM_ID) private platform: Object,
    private onlineAuth: OnlineAuthService
    ) { 
      if (isPlatformBrowser(this.platform)) {
        const store = JSON.parse(sessionStorage.getItem("online_store"));
        if (store && store.location && store.location.id && store.store.token) {
          this.uId = store.store.uid;
          this.customCss = store.store["custom-css"];
          this.custom_js_code = store.store["custom_js_code"];       
          this.setCustomCssRule();
          //this.setCustomJs();                  
        }
      }
    }

  ngOnInit() {
    if (isPlatformBrowser(this.platform)) {
      this.onlineAuth.loadGAScript();
    }
  }

  setCustomCssRule() {
    if (isPlatformBrowser(this.platform)) {   
    let hasLoadedForCustomCss = document.querySelector('style.home-custom-css');   
    if(hasLoadedForCustomCss){return}     
    const css = this.customCss;
    const head = document.getElementsByTagName("head")[0];
    const style = document.createElement("style");
    style.type = "text/css";
    style.className = "home-custom-css";
    style.appendChild(document.createTextNode(css));
    head.appendChild(style);
    }
  }

  setCustomJs() {
    let path = apiUrl + "js/upload/" + this.uId + ".js";
    if (this.is_customJs_exist) {
      if (isPlatformBrowser(this.platform)) {
        var script = document.createElement("script");
        script.type = "text/javascript";
        script.src = path;
        $("body").append(script);
        console.log('script??', script);
      }
    } 
    
    if(this.custom_js_code) {
      if (isPlatformBrowser(this.platform)) {
        const body = document.getElementsByTagName("body")[0];
        const script = document.createElement("script");
        script.type = "text/javascript";
        script.appendChild(document.createTextNode(this.custom_js_code));
        body.appendChild(script);
      }
    }
  }

}
