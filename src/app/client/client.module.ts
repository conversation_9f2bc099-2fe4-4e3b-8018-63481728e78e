import { Routes, RouterModule } from '@angular/router';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ClientComponent } from './client.component';
import { OnlineAuthGaurdService } from '../modules/online-store-auth/online-auth-guard.service';
import { ClientCustomerAuthGaurdService } from '../modules/online-store-auth/client-customer-auth-guard.service';
import { PreAuthService } from '../auth/pre-auth.service';
import { OnlineAuthService } from '../modules/online-store-auth/online-auth.service';

const routes: Routes = [
  {
    path: '',
    component: ClientComponent,
    children: [
      {
        path: 'activation',
        loadChildren: () => import('./customer-activation/customer-activation.module').then(m => m.CustomerActivationModule)
      },

      {
        path: 'login',
        loadChildren: () => import('./customer-login/customer-login.module').then(m => m.CustomerLoginModule),
        resolve: {
          onlineAuth: ClientCustomerAuthGaurdService
        },
      },
      {
        path: 'sign-up',
        loadChildren: () => import('./../auth/sign-up/sign-up.module').then(m => m.SignUpModule),
        data : { title:'Client Registration'}
      },
      {
        path: '**',
        redirectTo: 'login'
      }
    ]
  }
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
  ],
  exports: [RouterModule],
  declarations: [ClientComponent],
  providers: [OnlineAuthGaurdService, ClientCustomerAuthGaurdService, PreAuthService, OnlineAuthService]
})
export class ClientModule { }
