import { NgModule, Pipe, PipeTransform } from '@angular/core';
import { CommonModule } from '@angular/common';

@Pipe({
  name: 'customFieldPrice'
})

export class CustomFieldPricePipe implements PipeTransform {

  constructor() {}

  /**
   * @param value =>get value from HTML text
   */
  transform(value): unknown {
    if (value !== undefined) {
      const amount = value;
      const config = JSON.parse(localStorage.getItem('currency'));
      const locale = config?.locale ? config.locale : "";
      const priceAmount = (locale.length) ?
        new Intl.NumberFormat(locale, { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(Number(Number(amount))) :
        Number(Number(amount)).toFixed(2);
      if (config.pre == true && config.post == false) {
        return config.symbol + priceAmount;
      } else if (config.pre == false && config.post == true) {
        return priceAmount + ' ' + config.code;
      } else {
        return config.symbol + priceAmount + ' ' + config.code;
      }
    }
  }
}

@NgModule({
  declarations: [CustomFieldPricePipe],
  imports: [
    CommonModule,
  ],
  exports: [CustomFieldPricePipe]
})
export class CustomFieldPriceModule { }
