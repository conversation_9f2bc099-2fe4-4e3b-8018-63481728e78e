import { environment } from "../../../environments/environment";

// local server

export const storeHoshName = environment.storeHoshName;

export const EndPoint = environment.api_url + "api/";
export const user_image = environment.asset_url + "users/";
export const customer_image = environment.asset_url + "customers/";
export const product_image = environment.asset_url + "products/";
export const apiUrl = environment.api_url;
export const imgURL = environment.asset_url;

export const domainName = (store, check: string) => {
  store = environment.storeHoshName.includes('localhost:4200') || check === "company" ? '' : store;
  const host = environment.storeHoshName;
  return `${environment.protocal}${store}${host}`;
};

export const logOutStore = environment.storeHoshName.includes('localhost:4200') ? 'http://localhost:4200' : (environment.protocal + 'client' + environment.storeHoshName);
