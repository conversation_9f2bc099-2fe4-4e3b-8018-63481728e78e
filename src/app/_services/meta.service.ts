import { DOCUMENT } from '@angular/common';
import { Inject, Injectable, Renderer2, RendererFactory2  } from "@angular/core";
import { Meta, Title } from "@angular/platform-browser";
import { environment } from 'src/environments/environment';
import { GET_CUSTOM_DOMAIN_NAME, getdomain } from '../globals/_classes/functions';

@Injectable()
export class MetaService {
  defDescription = 'RentMy – The complete rental management solution';
  defKeywords = 'RentMy, rental, equipment rental, booking, party rental';
  defTitle = 'RentMy';

  public renderer: Renderer2; // Renderer2 instance
  public canonicalLinkElement: HTMLLinkElement | null = null; // Store the reference to the canonical link element
  constructor(
    private meta: Meta, private title: Title,
    @Inject(DOCUMENT) private document: any,
    public rendererFactory: RendererFactory2 
    ) {
      this.renderer = this.rendererFactory.createRenderer(null, null); 
    }

  updateTags(data) {
    // console.log(data);
    for (let d of data) {
      this.meta.updateTag(d);
    }
  }

  setTitle(name) {
    this.title.setTitle(name);
  }
  addTags(data) {
    const metaItems = new Object();
    for (let d in data) {
      const name = d.split("home_page_")[1];
      if (name === "title") {
        metaItems[name] = data[d];
        this.title.setTitle(data[d]);
        this.meta.updateTag({ name: 'title', content: data[d] });
      } else {
        metaItems[name] = data[d];
        this.meta.updateTag({ name: String(name), content: data[d] });
      }
    }
    // let metaEl: HTMLMetaElement = this.meta.getTag('name = "description"');
    // console.log(metaEl);
   // console.log(metaItems);
    this.addSeoDescription(metaItems);
  }

  addTagsForAdmin(data) {
    const metaItems = new Object();
    for (let d in data) {
      const name = d.split("home_page_")[1];
      
        metaItems[name] = data[d];
        this.meta.updateTag({ name: String(name), content: data[d] });
      
    }
   
    this.addSeoDescription(metaItems);
  }

  addSeoDescription(metaItems) {
    let url = this.document.location.href;
    url = !url.includes('https') ? url.replace('http','https') : url;
    let data = [
      { name: "description", content: metaItems.description || "" },
      { name: "keyword", content: metaItems.keywords || "" },
      { name: "twitter:description", content: metaItems.description || "" },
      { name: "twitter:title", content: metaItems.title || ""},
      { name: "viewport", content: "width=device-width, initial-scale=1, shrink-to-fit=no" },
      { name: "author", content: metaItems.title || "" },
      { name: "keywords", content: metaItems.keywords || "" }, //not set in index.html so, can't be updated
      { property: "og:title", content: metaItems.title || ""},
      { property: "og:type", content: "website" },
      { property: "og:site_name", content: metaItems.site_name || ""},
      { property: "og:description", content: metaItems.description || ""},
      { property: "og:url", content: url }
    ];
    if(!environment?.production) {
      data.push({ name: "robots", content: "noindex" })
    } else {
      const current_domain = getdomain();
      if(environment['robotBlocklistHost'] && environment['robotBlocklistHost']?.length) {
        environment['robotBlocklistHost'].forEach((host)=>{
          if(current_domain == host) {
            data.push({ name: "robots", content: "noindex" })
          }
        });
      }
    }
    this.updateTags(data);
  }

  // Set the Canonical URL using Renderer2
  setCanonicalURL(url: string, hasCustomCanonicalUrl?): void {
    const canonicalURL = this.canonicalURLFormat(url, hasCustomCanonicalUrl);
    // Method: 01
    // if (!this.canonicalLinkElement) {
    //   this.canonicalLinkElement = this.renderer.createElement('link') as HTMLLinkElement;
    //   this.renderer.setAttribute(this.canonicalLinkElement, 'rel', 'canonical');
    //   this.renderer.appendChild(this.document.head, this.canonicalLinkElement);
    // }
    // this.renderer.setAttribute(this.canonicalLinkElement, 'href', canonicalURL);
    // Method: 01
    const head = this.document.getElementsByTagName('head')[0];
    var element: HTMLLinkElement= this.document.querySelector(`link[rel='canonical']`) || null
    if (element==null) {
      element= this.document.createElement('link') as HTMLLinkElement;
      head.appendChild(element);
    }
    element.setAttribute('rel','canonical');
    element.setAttribute('href', canonicalURL);
  }

  // Format the URL appropriately
  canonicalURLFormat(url: string, hasCustomCanonicalUrl): string {  
    //let testUrl = 'https://www.greenmovebox.com/';
    let urlObj = new URL(url);
    let tmpCustomDomainName = GET_CUSTOM_DOMAIN_NAME();
    if(tmpCustomDomainName && !hasCustomCanonicalUrl){
      urlObj.hostname = tmpCustomDomainName;
    }
    // No need this condition 
    // if (urlObj.hostname.includes('.com') && !urlObj.hostname.includes('www.')) {
    //   urlObj.hostname = 'www.' + urlObj.hostname;
    // }

    let formattedUrl = urlObj.toString();
    if (urlObj.pathname === '/') {
      formattedUrl = formattedUrl.replace(/\/$/, '');  // Remove trailing slash only for the root path
    }
    return formattedUrl;
  }

  // Remove the Canonical URL element using Renderer2
  removeCanonicalURL(): void {
    if (this.canonicalLinkElement) {
      // Remove the canonical link element from the DOM
      this.renderer.removeChild(this.document.head, this.canonicalLinkElement);
      this.canonicalLinkElement = null;  // Reset the stored reference
    }
  }

}
