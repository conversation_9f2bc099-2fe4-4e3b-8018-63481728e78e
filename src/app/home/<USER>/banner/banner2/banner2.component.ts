import { Component, OnInit,Inject,PLATFORM_ID, Input } from "@angular/core";
import { Banner } from "../../type.model";
import { ContentResoveService } from "../../../home-service/contetn-resolve.service";
import { HttpService } from "../../../../modules/http-with-injector/http.service";
import { map, catchError } from "rxjs/operators";
import { of } from "rxjs";
import { Router } from "@angular/router";
import { DOCUMENT } from '@angular/common';
import { isPlatformBrowser,isPlatformServer } from '@angular/common';
declare let $:any;

@Component({
  selector: "app-banner2",
  templateUrl: "./banner2.component.html",
  styleUrls: ["./banner2.component.css"]
})
export class Banner2Component implements OnInit {
  @Input() data: Banner[];

  bannerImage: any;
  categoryList = [];
  otherContents;
  constructor(
    @Inject(PLATFORM_ID) private platformId: Object,@Inject(DOCUMENT) private document: any,
    private router: Router,
    private service: ContentResoveService,
    private http: HttpService
  ) {
    if(isPlatformBrowser(this.platformId)) {
      this.otherContents = JSON.parse(localStorage.getItem('contents'))?.site_specific?.others || {};
    }
  }

  ngOnInit() {
    const bg =
      this.service.contents && this.service.contents.bannerBackground
        ? Array.isArray(this.service.contents.bannerBackground) &&
          this.service.contents.bannerBackground.length > 0
          ? this.service.contents.bannerBackground[0]
          : this.service.contents.bannerBackground
        : "";
    this.getBannerImage(bg);
    this.getAllcaterogry().subscribe(res => {
      // console.log(res);
      this.categoryList = res;
    });
    if(isPlatformBrowser(this.platformId)){
      $('.carousel').carousel({
        interval: 2000
      })
    }
    
    
  }

  get bground() {
    return [
      {
        label: "",
        link: null,
        text: "",
        url:
          "https://www.logistec.com/wp-content/uploads/2017/12/placeholder.png"
      }
    ];
  }

  getBannerImage(bg) {
    if (bg) {
      this.bannerImage = {
        "background-image": `url(${bg.url})`,
        "background-repeat": "no-repeat",
        "background-position": "right center",
        "background-size": "100%"
      };
    }
  }

  getAllcaterogry() {
    return this.http.get("categories").pipe(
      map(res => {
        const data = res.result.data;
        return data;
      }),
      catchError(err => of([]))
    );
  }

  trackByCat(index, item) {
    return item.id; // unique id corresponding to the item
  }

  changeCategory(item) {
    this.router.navigate(["category/" + item.uuid + "/" + item.url]);
  }
}
