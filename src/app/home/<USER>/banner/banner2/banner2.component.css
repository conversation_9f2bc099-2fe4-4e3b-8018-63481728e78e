

/*-- mega menu --*/
.mega-menu-area {
    padding-right: 0;
  }
  .homevertical-menu {
    margin-top: -55px;
  }
  .vertical-menu {
    height: 505px;
  }
  .vertical-menu-title {
      padding: 12.6px 0;
  }
  .vertical-menu-title span{
    font-size: 20px;
    color: #eee;
    padding-left: 20px;
    text-transform: uppercase;
    font-weight: 300;
    letter-spacing: .8px;
  }
  .vertical-menu-title span i{
      font-size: 22px;
  }
    
  .mcd-menu {
    list-style: none;
    padding: 0;
    margin: 0;
    width: 100%;
    padding-top: 15px;
  }
  .mcd-menu li {
    position: relative;
  }
  .mcd-menu li a {
    display: block;
    text-decoration: none;
    padding: 10px 20px;
    text-align: left;
    position: relative;
    text-transform: uppercase;
    color: #fff;
    font-weight: 300;
    border-bottom: 1px solid #0e1b3a;
    letter-spacing: 1px;
    margin-top: -1px;
    font-size: 14px;
  
  }
  .mcd-menu li a i {
    float: left;
    margin: 3px 5px 0 0;
    
  }
  .mcd-menu li a span {
    float: right; 
  }
  .mcd-menu li a p {
    float: left;
    margin: 0 ;
  }
  .mcd-menu li a strong {
    display: block;
    text-transform: uppercase;
  }
  .mcd-menu li a small {
    display: block;
    font-size: 10px;
  }
  .mcd-menu li a i, .mcd-menu li a strong, .mcd-menu li a small {
    position: relative;
    
    transition: all 300ms linear;
    -o-transition: all 300ms linear;
    -ms-transition: all 300ms linear;
    -moz-transition: all 300ms linear;
    -webkit-transition: all 300ms linear;
  }
  .mcd-menu li:hover > a i {
      opacity: 1;
  }
  .mcd-menu li:hover a strong {
      opacity: 1;
      -webkit-animation: moveFromLeft 300ms ease-in-out;
      -moz-animation: moveFromLeft 300ms ease-in-out;
      -ms-animation: moveFromLeft 300ms ease-in-out;
      -o-animation: moveFromLeft 300ms ease-in-out;
      animation: moveFromLeft 300ms ease-in-out;
  }
  .mcd-menu li:hover a small {
      opacity: 1;
      -webkit-animation: moveFromRight 300ms ease-in-out;
      -moz-animation: moveFromRight 300ms ease-in-out;
      -ms-animation: moveFromRight 300ms ease-in-out;
      -o-animation: moveFromRight 300ms ease-in-out;
      animation: moveFromRight 300ms ease-in-out;
  }
  
  .mcd-menu li a.active {
    position: relative;
    color: #e67e22;
    border:0;
    box-shadow: 0 0 5px #DDD;
    -moz-layout3-box-shadow: 0 0 5px #DDD;
    -webkit-layout3-box-shadow: 0 0 5px #DDD;
    border-left: 4px solid #e67e22;
    border-right: 4px solid #e67e22;
    margin: 0 -4px;
  }
  .mcd-menu li a.active:before {
    content: "";
    position: absolute;
    top: 42%;
    left: 0;
    border-left: 5px solid #e67e22;
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
  }
  
  .mcd-menu li a.active:after {
    content: "";
    position: absolute;
    top: 42%;
    right: 0;
    border-right: 5px solid #e67e22;
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
  }
  
  @-webkit-keyframes moveFromTop {
      from {
          opacity: 0;
          -webkit-transform: translateY(200%);
          -moz-transform: translateY(200%);
          -ms-transform: translateY(200%);
          -o-transform: translateY(200%);
          transform: translateY(200%);
      }
      to {
          opacity: 1;
          -webkit-transform: translateY(0%);
          -moz-transform: translateY(0%);
          -ms-transform: translateY(0%);
          -o-transform: translateY(0%);
          transform: translateY(0%);
      }
  }
  @-webkit-keyframes moveFromLeft {
      from {
          opacity: 0;
          -webkit-transform: translateX(200%);
          -moz-transform: translateX(200%);
          -ms-transform: translateX(200%);
          -o-transform: translateX(200%);
          transform: translateX(200%);
      }
      to {
          opacity: 1;
          -webkit-transform: translateX(0%);
          -moz-transform: translateX(0%);
          -ms-transform: translateX(0%);
          -o-transform: translateX(0%);
          transform: translateX(0%);
      }
  }
  @-webkit-keyframes moveFromRight {
      from {
          opacity: 0;
          -webkit-transform: translateX(-200%);
          -moz-transform: translateX(-200%);
          -ms-transform: translateX(-200%);
          -o-transform: translateX(-200%);
          transform: translateX(-200%);
      }
      to {
          opacity: 1;
          -webkit-transform: translateX(0%);
          -moz-transform: translateX(0%);
          -ms-transform: translateX(0%);
          -o-transform: translateX(0%);
          transform: translateX(0%);
      }
  }
  .mcd-menu li ul {
    display: none;
    position: absolute;
    height: auto;
    min-width: 200px;
    min-height: 350px;
    padding: 0;
    margin: 0;
    opacity: 0;
    visibility: hidden;
    transition: all 300ms linear;
    -o-transition: all 300ms linear;
    -ms-transition: all 300ms linear;
    -moz-transition: all 300ms linear;
    -webkit-transition: all 300ms linear;
    left:100%;
    top: 0px;
    background-color: #fff;
    box-shadow: 0 6px 12px rgba(0,0,0,.175);
  }
  .mcd-menu li:hover > ul{
    display: block;
    opacity: 1;
    visibility: visible;
  }
  .vertical-submenu {
      width: 450px;
      z-index: 99999;
  }
  .submenu-inner {
    height: auto;
    min-width: 200px;
    min-height: 350px;
    padding: 15px 15px 0 15px !important;
  }
  .vertical-submenu-list {
      padding: 0 0px;
  }
  .vertical-submenu-list h5 {
    text-transform: uppercase;
    font-weight: 500;
    font-size: 16px;
  }
  .vertical-submenu-list ol {
    padding-left: 15px;
  }
  .vertical-submenu-list ol li {
    float: left;
    width: 100%;
  }
  
  .vertical-submenu-list ol li a {
      color: #aaa;
      border-bottom: none;
      padding: 4px 0 !important;
      border: none;
      font-size: 14px;
      text-transform: capitalize;
  }
  .Costume {
      background-repeat: no-repeat;
    background-size: 80% 80%;
    background-position: right bottom;
  }
  .Hats {
      background-repeat: no-repeat;
    background-size: 100% 100%;
  
  }
  .Makeup {
      background-repeat: no-repeat;
    background-size: 100% 100%;
  }
  .Wigs {
      background-repeat: no-repeat;
    background-size: 100% 100%;
  }
  .Accessories {
      background-repeat: no-repeat;
    background-size: 100% 100%;
  }
  .col-lg-4 {
    position: unset;
  }  
  .vt-menu {
    width: 900px;
    background-position: right bottom;
  }
  .vtm-list {
    width: 26% !important;
  }
  .vertical-submenu-list a {
    cursor: pointer;
  }
  @media (max-width: 991px) {
    .mega-menu-area {
      display: none;
    }
  }
  
  /*-- slider css --*/
  .main-slider {
    padding-left: 0;
  }
  .layou3-slider-area .sliderlink {
    position: absolute;
    left: 11%;
    bottom: 60px;
    width: 90px;
    height: 80px;
    z-index: 9;
  }
  .layou3-slider-area .sliderlink a {
    display: block;
    text-align: center;
    width: 90px;
    height: 80px;
    cursor: pointer;
  }
  .layou3-slider-area .slider-area .carousel-inner img {
    height: 450px !important;
    width: 100%;
  }
  .layou3-slider-area .carousel-control-next-icon,
  .layou3-slider-area .carousel-control-prev-icon {
    background-image: none;
  }
  .slider-area .carousel-control-prev {
    right: unset;
    left: 50px !important;
    background-color: transparent !important;
    }
  .slider-area .carousel-control-next {
    right: 50px;
    background-color: transparent !important;
    }
  @media (max-width: 1550px) {
    .layou3-slider-area .sliderlink {
      left: 10%;
      bottom: 38px;
    }
  }
  @media (min-width: 1200px) and (max-width: 1399px) {
    .vertical-menu {
      height: 455px;
    }
    .layou3-slider-area .slider-area .carousel-inner img {
      height: 400px !important;
    }
  }
  @media (min-width: 992px) and (max-width: 1199px) {
    .vertical-menu {
      height: 435px;
    }
    .layou3-slider-area .slider-area .carousel-inner img {
      height: 380px !important;
    }
  }
  @media (max-width: 991px) {
    .layou3-slider-area {
      padding: 0 15px !important;
    }
    .layou3-slider-area .slider-area .carousel-inner img {
      height: auto !important;
    }
  }
  @media (max-width: 767px) {
      .main-slider {
          padding-right: 0;
      }
      .layou3-slider-area .slider-area .carousel-inner img {
          height: auto !important;
      }
    .layou3-slider-area .sliderlink {
      bottom: 20px;
    }
    
  }
  @media (max-width: 575px) {
      .layou3-slider-area .slider-area .carousel-inner img {
          height: auto !important;
      }
    .layou3-slider-area .sliderlink {
      width: 50px;
      height: 50px;
      left: 10%;
      bottom: 10px;
    }
    .layou3-slider-area {
      padding: 0;
    }
    
  }

  