<section class="slider-section mt-0">
  <div class="container-fluid">
    <div class="row">
      <div class="col-lg-3 col-12 mega-menu-area">
        <div class="vertical-menu homevertical-menu">
          <div class="vertical-menu-title">
            <span> <i class="fa fa-bars"></i> 
              {{ otherContents?.home_page_category_label ? otherContents.home_page_category_label : "Categories" }}
            </span>
          </div>
          <nav *ngIf="categoryList.length > 0">
            <ul class="mcd-menu">
              <li *ngFor="let category of categoryList; trackBy: trackByCat">
                <a href="#"
                  >{{ category?.name }}
                  <span class="vm-icon">
                    <i class="fa fa-angle-right"></i>
                  </span>
                </a>
                <ul class="vertical-submenu">
                  <div class="submenu-inner">
                    <div class="row">
                      <div class="col-md-4 vertical-submenu-list">
                        <h5 class="pl-3 pb-2">{{ category?.name }}</h5>
                        <ol class="w-100">
                          <li
                            *ngFor="
                              let child of category.children;
                              trackBy: trackByCat
                            "
                          >
                            <a (click)="changeCategory(child)">{{
                              child?.name
                            }}</a>
                          </li>
                        </ol>
                      </div>
                    </div>
                  </div>
                </ul>
              </li>
            </ul>
          </nav>
          <div class="nav-pattern">
          </div>
        </div>
      </div>

      <!-- layout 2 slider -->
      <div class="col-lg-9 col-12 main-slider layou3-slider-area">
        <div class="slider-area">
          <div id="carouselExampleIndicators" class="carousel slide" data-ride="carousel">
            <ol *ngIf="data.length > 1" class="carousel-indicators">
                <li *ngFor="let banner of data;let i=index" data-target="#carouselExampleIndicators" data-slide-to="0" [ngClass]="{'active':i===0}"></li>
            </ol>
            <div class="carousel-inner">
              <div class="carousel-item active" [ngClass]="{ active: i === 0 }" *ngFor="let banner of data; let i = index" data-interval="10000">
                <img [src]="banner.url" class="" alt="slide" />
              </div>
            </div>
            <a *ngIf="data.length > 1" class="carousel-control-prev" href="#carouselExampleIndicators" role="button"data-slide="prev">
              <span class="carousel-control-prev-icon" aria-hidden="true">
                <i class="fa fa-long-arrow-left"></i>
              </span>
              <span class="sr-only">Previous</span>
            </a>
            <a *ngIf="data.length > 1" class="carousel-control-next" href="#carouselExampleIndicators" role="button" data-slide="next">
              <span class="carousel-control-next-icon" aria-hidden="true">
                <i class="fa fa-long-arrow-right"></i>
              </span>
              <span class="sr-only">Next</span>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- <section class="slider-section slider-2">
  <div id="myCarousel2" class="carousel slide default-slider carousel-fade" data-ride="carousel" [ngStyle]="bannerImage">
    <ol class="carousel-indicators">
      <li *ngFor="let banner of data;let i=index" data-target="#myCarousel2" data-slide-to="0" [ngClass]="{'active':i===0}"></li>
    </ol>
    <div class="carousel-inner">
      <div class="carousel-item  carousel-item-1" [ngClass]="{'active':i===0}" *ngFor="let banner of data;let i=index">
        <div class="mask flex-center">
          <div class="container-fluid">
            <div class="row align-items-center">
              <div class="col-md-5 col-sm-5 col-12 order-md-1 order-2">
                <img [src]="banner.url" class="" alt="slide">
              </div>
              <div class="col-md-7 col-sm-7 col-12 order-md-2 order-1">
                <h3>{{banner.title}} </h3>
                <h4>{{banner.label}}</h4>
                <p [innerHTML]="banner.text"></p>
                <ng-container *ngIf="banner.link && banner.type">
                  <a [attr.href]="banner.link" *ngIf="banner.type == 'External'; else otherType" class="button theme-btn slider-button m-btn--icon">{{banner.btn_text}}
                    <i class="fa fa-angle-right ml-1"></i>
                  </a>
                  <ng-template #otherType>
                    <a [routerLink]="banner.link" class="button theme-btn slider-button m-btn--icon">{{banner.btn_text}}
                      <i class="fa fa-angle-right ml-1"></i>
                    </a>
                  </ng-template>
                </ng-container>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>

    <a class="carousel-control-prev" href="#myCarousel2" role="button" data-slide="prev">
      <span class="carousel-control-prev-icon" aria-hidden="true">
        <i class="fas fa-long-arrow-alt-left"></i>
      </span>
    </a>
    <a class="carousel-control-next" href="#myCarousel2" role="button" data-slide="next">
      <span class="carousel-control-next-icon" aria-hidden="true">
        <i class="fas fa-long-arrow-alt-right"></i>
      </span>
    </a>
  </div>
</section> -->
