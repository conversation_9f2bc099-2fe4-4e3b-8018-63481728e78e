import { Component, OnInit,Inject,PLATFORM_ID, Input, OnChanges } from "@angular/core";
import { Banner } from "../../type.model";
import { DOCUMENT } from '@angular/common';
import { isPlatformBrowser,isPlatformServer } from '@angular/common';
@Component({
  selector: "app-banner3",
  templateUrl: "./banner3.component.html",
  styleUrls: ["./banner3.component.css"]
})
export class Banner3Component implements OnInit, OnChanges {
  @Input() data: Banner[];
  layoutSection: any;
  store: string;

  constructor(@Inject(PLATFORM_ID) private platformId: Object,@Inject(DOCUMENT) private document: any) {
    
    this.layoutSection = this.document.online_store
      ? JSON.parse(this.document.online_store).store.layout.section
      : null;
    this.store = this.document.online_store
      ? JSON.parse(this.document.online_store).store.name
      : null;
  }

  ngOnChanges() {}

  ngOnInit() {}
}
