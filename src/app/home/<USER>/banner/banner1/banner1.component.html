<section class="slider-section">
  <div id="myCarousel2" class="carousel slide default-slider carousel-fade" data-ride="carousel" [ngStyle]="bannerImage">
    <ol *ngIf="data.length > 1" class="carousel-indicators">
      <li *ngFor="let banner of data;let i=index" data-target="#myCarousel2" data-slide-to="1" [ngClass]="{'active':i===0}"></li>

    </ol>
    <div class="carousel-inner" [ngStyle]="styles">
      <div class="carousel-item" [ngClass]="{'active':i===0}" *ngFor="let banner of data;let i=index" data-interval="10000">
        <div class="mask flex-center">
          <div class="container-fluid">
            <div class="row align-items-center">
              <div class="col-md-7 col-sm-7 col-12 order-md-1 order-2">
                <!-- <h3>{{banner.title}} </h3> -->
                <h4 [innerHTML]="banner.label | safeHtml" ></h4>
                <p [innerHTML]="banner.text | safeHtml"></p>
                <ng-container *ngIf="banner.link && banner.type">
                  <a [attr.href]="banner.link" *ngIf="banner.type == 'External'; else otherType" class="button theme-btn slider-button m-btn--icon"
                  [innerHTML]="banner.btn_text  | safeHtml"
                  >
                    <i class="fa fa-angle-right ml-1"></i>
                  </a>
                  <ng-template #otherType>
                    <a [routerLink]="banner.link" class="button theme-btn slider-button m-btn--icon"
                    [innerHTML]="banner.btn_text | safeHtml"
                    >
                      <i class="fa fa-angle-right ml-1"></i>
                    </a>
                  </ng-template>
                </ng-container>
              </div>
              <div class="col-md-5 col-sm-5 col-12 order-md-2 order-1">
                <img [src]="banner.url" class="mx-auto" alt="slide">
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <a *ngIf="data.length > 1" class="carousel-control-prev" href="#myCarousel2" role="button" data-slide="prev">
      <span class="carousel-control-prev-icon" aria-hidden="true">
        <i class="fas fa-long-arrow-alt-left"></i>
      </span>
      <span class="sr-only">Previous</span>
    </a>
    <a *ngIf="data.length > 1" class="carousel-control-next" href="#myCarousel2" role="button" data-slide="next">
      <span class="carousel-control-next-icon" aria-hidden="true">
        <i class="fas fa-long-arrow-alt-right"></i>
      </span>
      <span class="sr-only">Next</span>
    </a>
  </div>
</section>
