
.m-login__container {
	max-width: 767px !important;
	min-width: 320px !important;
}

.m-login.m-login--2.m-login-2--skin-1 .m-login__container .m-login__form .m-login__form-action .m-login__btn.m-login__btn--primary {
	color: #555 !important;
}

.custom-alert{
    margin-top:20px;   
}

.form-control.m-input {
    background: #f2f3f8 !important;
    color: #555!important;
}
.m-login__signin {
	background-color: #fff!important;
	box-shadow: 0px 1px 15px 1px rgba(69, 65, 78, 0.08);
	overflow: hidden;
	/*padding: 15px;*/
}
.login-side {
	padding: 15px 30px;
	margin-bottom: 15px;
}
.login-side h2 {
	margin-top: 30px;
	margin-bottom: 10px;
	padding-left: 5px;
}
.m-login__title {
	color: #555 !important;
	font-weight: 300;
    font-size: 17px !important;
    padding-left: 5px;
}

.login-shape {
	background-color: #f2f3f8!important;
	height: 450px;
    width: 450px;
    border-radius: 60% 50% 100% 40%;
    margin-left: -86px;
    margin-top: -80px;
}
.login-shape img {
	width: 50%;
    margin-top: 150px;
    margin-left: 130px;
}
.login-side img {
	width: 40%;
    display: block;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 20px;
    margin-top: 20px;
}
form {
	padding-top: 0 !important;
	margin-top: 10px !important;
}
.btn-outline-focus.m-btn--air, .btn-focus.m-btn--air, .m-btn--gradient-from-focus.m-btn--air {
	box-shadow: none !important;
	background-color: #f2f3f8 !important;
	border: 1px solid #f2f3f8 !important;
	display: block;
    width: 100%;
    font-weight: 500;
}

#m_login_signin_submit {
	color: #555;
}

.login-side .m-login__form {
	margin: 2rem auto!important;
}

.login-side .m-link {
    color: #041531!important;
    cursor: pointer;
}

.login-side .m-link:hover:after {
    border-bottom: 1px solid #041531;
    opacity: 0.3;
    filter: alpha(opacity=30);
}

.error-span {
	padding-left: 10px;
}

.success-message{
    color: green;
    text-align: center;
    margin: 40% 0px;
    vertical-align: middle;
}

.error-message{
    color: #f66e84;
    text-align: center;
    margin: 40% 0px;
    vertical-align: middle;
}

.normal-message{
    color: #041531;
    text-align: center;
    margin: 40% 0px;
    vertical-align: middle;
}

.input-addon {
	color: #041531;
    position: absolute;
    right: 18px;
    top: 19px;
    cursor: pointer;
}

.unique-check{
    position: absolute;
    color: green;
    top: 15px;
    right: 40px;
}
.fa-close {
    color: red!important;
}
.checkLoader{
    position: absolute;
    top: 15px;
    right: 40px;
}

@media (max-width: 767px) {
	.hidden-div {
		display: none;
	}
}

/* New Added */


.sign-up-content {

}

.sign-up-content .login-shape {
    background-color: #f2f3f800!important;
    height: 450px;
    width: 450px;
    border-radius: 60% 50% 100% 40%;
    margin-left: -86px;
    margin-top: -80px;
}

.sign-up-content .left-part {
    background-color: #f2f3f8;
}


.sign-up-content .form-control {
    display: block;
    width: 90%;
    padding: 0.65rem 1rem;
    font-size: 1rem;
    line-height: 1.25;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.sign-up-content .m-login__form .m-form__group .form-control {
    border-radius: 0px;
    border: none;
    padding: 1.1rem 1.1rem;
    margin-top: 1.5rem;
}

.sign-up-content h2, .sign-up-content .h2 {
    font-size: 1.8rem;
}

.sign-up-content .login-shape img {
    width: 70%;
    margin-top: 130px;
    margin-left: 130px;
}


.login-shape h4, .login-shape .h4 {
    font-size: 1.1rem;
    margin-left: 142px;
}

.sign-up-logo {
    max-width: 100%;
    margin-top:20px;
    margin-bottom: 30px;
}

.sms-section h5{
    color: #343a40!important;
    padding-left: 0px;
}

.authanticateUser .btn {
    border-radius: 40px!important;
    padding: 0.65rem 1.25rem!important;
    background: #ebedf2!important;
}

.authanticateUser .list-group-item {
    padding: 0.65rem 1.25rem!important;
    border-radius: 40px!important;
    color: #212529!important;
    font-size: 1rem!important;
    line-height: 1.25!important;
}

.authanticateUser .list-group-item.active {
    color: #212529!important;
    background: white!important;
    border-color: #ebedf2!important;
}

.authanticateUser .list-group-item:hover {
    color: #212529!important;
    background: #ebedf2!important;
    border-color: #ebedf2!important;
}

.authanticateUser p {
    margin-bottom: 2px!important;
}

.authanticateUser .cartePart {
    position: absolute;
    top: 18px;
    right: 20px;
}

.authanticateUser #collapseExample {
    margin-top: 5px;
    position: absolute;
    width: 100%;
    height: 160px;
    z-index: 9999;
    overflow-y: auto;
}

.value-prop {
    display: flex;
}
.value-prop b {
    font-weight: 500 !important;
    margin-left: 5px;
}
.mlogin-signup-btn {
    color: #fff !important;
    background-color: #508e50;
}
.mlogin-signup-btn i {
    color: #fff;
}
.mlogin-signup-btn:active,
.mlogin-signup-btn:focus,
.mlogin-signup-btn:hover {
    background-color: #508e50 !important;
    color: #fff !important;
}
.auth-wrapper{
    /* margin-top: 60px; */
}