import { Component, OnInit } from '@angular/core';
import { Helpers } from '../helpers';
import { PLATFORM_ID,Inject } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { apiUrl } from '../globals/endPoint/config';

@Component({
  selector: 'app-auth',
  templateUrl: './auth.component.html',
  styleUrls: ['./auth.component.css']
})
export class AuthComponent implements OnInit {

  //globalBodyClass = 'm-page--loading-non-block m-page--fluid m--skin- m-content--skin-light2 m-header--fixed m-header--fixed-mobile m-aside-left--enabled m-aside-left--skin-dark m-aside-left--offcanvas m-footer--push m-aside--offcanvas-default';
  globalBodyClass = 'm-page--fluid m--skin- m-content--skin-light2 m-header--fixed m-header--fixed-mobile m-aside-left--enabled m-aside-left--skin-dark m-aside-left--offcanvas m-footer--push m-aside--offcanvas-default';

  customCss;
  uId;
  is_customJs_exist;
  custom_js_code;

  constructor(@Inject(PLATFORM_ID) private platform: Object) { 
    if (isPlatformBrowser(this.platform)) {
      const store = JSON.parse(sessionStorage.getItem("online_store"));
      if (store && store.location && store.location.id && store.store.token) {
        this.uId = store.store.uid;
        this.customCss = store.store["custom-css"];
        this.custom_js_code = store.store["custom_js_code"];       
        this.setCustomCssRule();
        //this.setCustomJs();                  
      }
    }
  }

  ngOnInit() {
    Helpers.bodyClass(this.globalBodyClass);   
  }

  setCustomCssRule() {
    if (isPlatformBrowser(this.platform)) {
    let hasLoadedForCustomCss = document.querySelector('style.home-custom-css');  
    if(hasLoadedForCustomCss){return}     
    const css = this.customCss;
    const head = document.getElementsByTagName("head")[0];
    const style = document.createElement("style");
    style.type = "text/css";
    style.className = "home-custom-css";
    style.appendChild(document.createTextNode(css));
    head.appendChild(style);
    }
  }

  setCustomJs() {
    let path = apiUrl + "js/upload/" + this.uId + ".js";
    if (this.is_customJs_exist) {
      if (isPlatformBrowser(this.platform)) {
        var script = document.createElement("script");
        script.type = "text/javascript";
        script.src = path;
        $("body").append(script);
      }
    } 
    
    if(this.custom_js_code) {
      if (isPlatformBrowser(this.platform)) {
        const body = document.getElementsByTagName("body")[0];
        const script = document.createElement("script");
        script.type = "text/javascript";
        script.appendChild(document.createTextNode(this.custom_js_code));
        body.appendChild(script);
      }
    }
  }

}
