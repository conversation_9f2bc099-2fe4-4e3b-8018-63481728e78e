import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AuthComponent } from './auth.component';
import {AuthRoutingModule} from './auth-routing.module';
import {HttpClientModule} from '@angular/common/http';
import { PreAuthService } from './pre-auth.service';
import { CheckRouteService } from './check-route.service';
import { QuickbookConfigComponent } from './quickbook-config/quickbook-config.component';
import { RedirectViewComponent } from './redirect-view/redirect-view.component';
import { RedirectUrlResolveService } from './redirect-view/resolve/redirect-url-resolve.service';




@NgModule({
  imports: [
    CommonModule,
    AuthRoutingModule,
    HttpClientModule
  ],
  
  declarations: [AuthComponent, QuickbookConfigComponent, RedirectViewComponent],
  providers: [PreAuthService, CheckRouteService,RedirectUrlResolveService]
})
export class AuthModule { }
