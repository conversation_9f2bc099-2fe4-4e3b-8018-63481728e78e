<div class="m-login__signin animated fadeIn">
  <div class="row">
    <div class="col-md-6 hidden-div">
      <div class="login-shape">
        <img src="assets/img/home/<USER>">
      </div>
    </div>
    <div class="col-md-6 login-side animated fadeIn" style="position: relative;">
      <div *ngIf="loader || failed; else activationDone">
        <div class="error-msg" *ngIf="failed; else loadingOn">
          <h4 class="error-message animated zoomIn">
            {{message}}
          </h4>
        </div>
        <ng-template #loadingOn>
          <div class="table-load m-loader m-loader--brand"></div>
        </ng-template>
      </div>
      <ng-template #activationDone>
        <div class="m-login__head">
					<h2 class="text-left">{{contents?.site_specific?.others?.txt_enter_new_pass || 'Enter New Password'}}</h2>
        </div>
        
        <div class="custom-alert" style="margin-top: 20px;" #hasAlert></div>

        <form #form="ngForm" (ngSubmit)="( form.form.valid || checkPassword(newPass,password)) && submitPass(form)" class="m-login__form m-form">
          <div class="form-group m-form__group" style="position: relative;">
            <input [type]="showPass ? 'text' : 'password'" class="form-control m-input" autocomplete="off" placeholder="Enter Password" name="new" #pass="ngModel" minlength="6" [(ngModel)]="newPass" required style="padding-right: 45px;">

            <div class="input-addon" (click)="showPassword()">
              <i class="fa fa-eye"></i>
            </div>

            <span *ngIf="pass.valid">
              <password-strength-bar [passwordToCheck]="newPass" [showText]="true"></password-strength-bar>
            </span>
            <span class="error-span" *ngIf="pass.errors && pass.touched">
              <small *ngIf="pass.errors.required" class="error">{{contents?.site_specific?.others?.txt_password_required || 'Password Required'}}.</small>
              <small *ngIf="pass.errors.minlength" class="info">{{contents?.site_specific?.others?.lbl_password_six_character || 'Password must be 6 characters'}}.</small>
            </span>
          </div>
          <div class="form-group m-form__group">
            <input type="password" class="form-control m-input" placeholder="Confirm Password" autocomplete="off"
              name="confirm" #confirm="ngModel" [(ngModel)]="password" required>
            <span class="error-span" *ngIf="confirm.valid">
              <small *ngIf="checkPassword(newPass,password)" class="error">{{contents?.site_specific?.others?.txt_pass_mismatched || 'Mismatched Password'}}</small>
            </span>
            <span class="error-span" *ngIf="confirm.errors && confirm.touched">
              <small *ngIf="confirm.errors.required" class="error">{{contents?.site_specific?.others?.txt_confirm_password_required || 'Confirm Password Required'}}.</small>
            </span>
          </div>
          <div class="d-flex" style="align-items: center; padding: 20px 10px 0px;">
            <div class="text-left" style="flex:1;">
              <button [disabled]="send" type="submit" class="btn m-btn--pill btn-secondary btn-lg m-btn--icon" [ngClass]="{'m-loader m-loader--skin-dark m-loader--right': send}" style="margin-top: 0px;">Submit</button>
            </div>
            <div class="text-right" style="flex:1;" *ngIf="failed; else forgetPass">
              <a routerLink="/auth/forgot-password" class="m-link">
                {{contents?.site_specific?.others?.txt_forgot_pass || 'Forget Password'}}
              </a>
            </div>
            <ng-template #forgetPass>
              <div class="text-right" style="flex:1;">
                <a routerLink="/auth/login" class="m-link">
                  {{contents?.site_specific?.customer_portal?.lbl_login || 'Login'}}
                </a>
              </div>
            </ng-template>
          </div>
        </form>
      </ng-template>

    </div>
  </div>
</div>