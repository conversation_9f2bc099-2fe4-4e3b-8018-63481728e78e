import { Component, OnInit, ViewChild, ElementRef } from "@angular/core";
import { PreAuthService } from "../pre-auth.service";
import { Helpers } from "../../helpers";
import { domainName } from "../../globals/endPoint/config";
import { AlertService } from "../../modules/alert/alert.service";
import { encrypt, eLogin } from "../../globals/_classes/functions";
import { ContentService } from "../../admin/pages/settings/setting-service/contents.service";
import { Router, ActivatedRoute } from "@angular/router";

@Component({
  selector: "app-init-login",
  templateUrl: "./init-login.component.html",
  styleUrls: ["../auth.component.css"]
})
export class InitLoginComponent implements OnInit {
  signInModel: any = {
    email: "",
    password: ""
  };
  stores = [];
  loading = false;
  authanticateUser: boolean;
  currentStroe = {
    name: "",
    slug: "",
    id: 0
  };
  carte: boolean;

  redirect_url='';

  @ViewChild("hasAlert") alertContainer: ElementRef;

  constructor(
    private contentService: ContentService,
    private alertS: AlertService,
    private auth: PreAuthService,
    private route: ActivatedRoute
  ) {}

  ngOnInit() {
    
    this.route.queryParamMap.subscribe(params => {
      if(params.get("redirect_url") !=undefined && params.get("plan_name") !=undefined && params.get("type") !=undefined)
      {
        this.redirect_url ='&redirect_url='+params.get("redirect_url")+'&plan_name='+params.get("plan_name")+'&type='+params.get("type");
      }
      else if(params.get("redirect_url") !=undefined && params.get("plan_name") !=undefined){
        this.redirect_url ='&redirect_url='+params.get("redirect_url")+'&plan_name='+params.get("plan_name")
      }
      else if(params.get("redirect_url")){
        this.redirect_url ='&redirect_url='+params.get("redirect_url")
      }
      else{
        this.redirect_url =''
      }
     
    });


    Helpers.setLoading(false);
  }

  getDomain(name) {
    return domainName(name, "store");
  }

  get length() {
    return this.stores.length > 1;
  }

  authanticate() {
    this.loading = true;
    this.auth.authenticate(this.signInModel).subscribe(
      data => {
        if (data.status == "OK") {
          this.authanticateUser = true;
          this.stores = this.swapName(data.result.data);
          if (this.stores[0]) {
            this.currentStroe = this.stores[0];
          }
          this.loading = false;
        } else {
          this.alertS.error(this.alertContainer, data.result.error, true, 5000);
          this.loading = false;
          this.authanticateUser = false;
        }
      },
      error => {
        console.log(error.result.error);
        this.alertS.error(
          this.alertContainer,
          this.auth.getErrorMessage(error),
          true,
          5000
        );
        this.loading = false;
        this.authanticateUser = false;
      }
    );
  }

  private swapName(data) {
    if (data) {
      return data.map(m => {
        const n = m["slug"];
        m["slug"] = m["name"];
        m["name"] = n;
        return m;
      });
    }
    return [];
  }

  signIn(data) {
    this.currentStroe = data;
    if (!this.currentStroe.slug) return;
    this.carte = !this.carte;

    this.loading = true;
    const sendData = {
      ...this.signInModel,
      store_name: this.currentStroe.slug
    };

    this.auth.login(sendData, true).subscribe(
      data => {
        if (data.status == "OK") {
          localStorage.setItem('storeName',JSON.stringify(this.currentStroe.slug));
          this.addFacebookPixels();
          if(data.result.user_type_id===4){
            this.gotoPosLogin(data.result, sendData);
          } else{
            this.gotoStoreLogin(data.result, sendData);
          }
        } else {
          this.alertS.error(this.alertContainer, data.result.error, true, 5000);
          this.loading = false;
        }
      },
      error => {
        console.log(error.result.error);
        this.alertS.error(
          this.alertContainer,
          this.auth.getErrorMessage(error),
          true,
          5000
        );
        this.loading = false;
      }
    );
  }
  
  private gotoStoreLogin(user, data) {
    const returnUrl ="/admin/dashboard" // user.setup ? "/admin/dashboard" : "/admin/newusertour";
    const a = eLogin(
      encrypt(encrypt(encrypt(JSON.stringify(user), "upper"), "lower"))
    );



    const href =
      domainName(data.store_name, "store") + `${returnUrl}?user=${a}${this.redirect_url}`;
      //window.open(href, "_self");
  }


  private gotoPosLogin(user, data) {
      const returnUrl = "/cash-register";
      const a = eLogin(
        encrypt(encrypt(encrypt(JSON.stringify(user), "upper"), "lower"))
      );
      const href =
        domainName(data.store_name, "store") + `${returnUrl}?user=${a}`;
        //window.open(href, "_self");
    }

  addFacebookPixels() {
    const fb_tracking_id = 590809078159894;
    const js = `
      !function(f,b,e,v,n,t,s)
      {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
      n.callMethod.apply(n,arguments):n.queue.push(arguments)};
      if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
      n.queue=[];t=b.createElement(e);t.async=!0;
      t.src=v;s=b.getElementsByTagName(e)[0];
      s.parentNode.insertBefore(t,s)}(window, document,'script',
      'https://connect.facebook.net/en_US/fbevents.js');
      fbq('init', '${fb_tracking_id}');
      fbq('track', 'PageView');`;
    if (fb_tracking_id) {
      //FB pixel Script
      const head = document.getElementsByTagName("head")[0];
      const script = document.createElement("script");
      script.type = "text/javascript";
      script.appendChild(document.createTextNode(js));
      head.appendChild(script);
      //Noscript
      var myImg = document.createElement("img");
      myImg.src = `https://www.facebook.com/tr?id=${fb_tracking_id}&ev=PageView&noscript=1`;
      myImg.style.display = "none";
      const noScript = document.createElement("noscript");
      noScript.appendChild(myImg);
      $("body").append(noScript);
    }
  }
}
