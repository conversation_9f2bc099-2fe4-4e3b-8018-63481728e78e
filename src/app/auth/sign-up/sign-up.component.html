<div class="client-signup">
  <div class="client-signup-inner">
    <div class="client-signup-leftside">
        <ng-container *ngIf="!isSuccessMessage; else confirmationContent">
            <div class="vendor-signup-title">
                <h2 class="client-signup-title">{{ customerPortal?.lbl_partner_signup || 'Partner Sign Up' }}</h2>
                <span> {{ customerPortal?.lbl_create_your_account || 'Create Your Account' }} </span>
            </div>
            <div class="userlogin-box">
            <div class="userlogin-body">        
                <!-- <p class="succesmsg-text" *ngIf="isSuccessMessage">{{successMessageText}}</p> -->
                <p class="errormsg-text" *ngIf="isErrorMessage">{{errorMessageText}}</p>
    
                <form [formGroup]="form" (submit)="submit()" class="loginform row">
                    <div class="col-md-6 col-sm-6 col-6 form-group">
                        <input type="text" name="first_name" class="input-field"
                        [attr.placeholder]="customerPortal?.lbl_first_name || 'First Name' + (customerPortal?.lbl_first_name ? '*' : '')"
                            formControlName="first_name">
                        <small *ngIf="submitted && form.get('first_name').hasError('required')" class="form-text text-invalid">
                            First Name is <strong>required</strong>!
                        </small>
                    </div>
                    <div class="col-md-6 col-sm-6 col-6 form-group">
                        <input type="text" name="last_name" class="input-field"
                        [attr.placeholder]="customerPortal?.lbl_last_name || 'Last Name' + (customerPortal?.lbl_last_name ? '*' : '')"
                            formControlName="last_name">
                        <small *ngIf="submitted && form.get('last_name').hasError('required')" class="form-text text-invalid">
                            Last Name is
                            <strong>required</strong>
                        </small>
                    </div>
                    <div class="col-md-12 col-sm-12 col-12 form-group">
                        <input type="text" name="email" class="input-field"
                        [attr.placeholder]="customerPortal?.lbl_email || 'Email' + (customerPortal?.lbl_email ? '*' : '')" formControlName="email">
                        <small *ngIf="submitted && form.get('email').hasError('email') && !form.get('email').hasError('required')" class="form-text text-invalid">
                            Please enter a valid email address
                        </small>
                        <small *ngIf="submitted && form.get('email').hasError('required')" class="form-text text-invalid">
                            Email is
                            <strong>required</strong>
                        </small>
                    </div>           
                    <div class="col-md-6 col-sm-6 col-6 form-group">
                        <input type="text" name="company" class="input-field" [attr.placeholder]="customerPortal?.lbl_company_name || 'Company Name'"
                            formControlName="company">
                    </div>
                    <div class="col-md-6 col-sm-6 col-6 form-group">
                        <input type="text" name="mobile" class="input-field" [attr.placeholder]="customerPortal?.lbl_contact_no || 'Phone Number'"
                            formControlName="mobile">
                    </div>
                    <div class="col-md-6 col-sm-6 col-6 form-group password-group">
                        <input name="password" class="input-field"
                        [type]="isShowPassword ? 'text' : 'password'"
                        [attr.placeholder]="customerPortal?.lbl_password || 'Password' + (customerPortal?.lbl_password ? '*' : '')"
                            formControlName="password">
                        <div class="input-group-append">
                            <button class="btn" type="button"
                            (click)="isShowPassword = !isShowPassword"    
                            >
                                <i class="fa fa-eye"></i>
                            </button>
                        </div>

                        <small *ngIf="submitted && form.get('password').hasError('required')" class="form-text text-invalid">
                            Password is
                            <strong>required</strong>
                        </small>
                        <small *ngIf="submitted && !form.get('password').hasError('required') && form.get('password').hasError('minlength')" class="form-text text-invalid">
                            Password must have at least
                            <strong>6</strong> character
                        </small>
                    </div>
                    <div class="col-md-6 col-sm-6 col-6 form-group password-group">
                        <input type="password" name="password" class="input-field"
                        [type]="isShowConfirmPassword ? 'text' : 'password'"
                        [attr.placeholder]="customerPortal?.lbl_confirm_password || 'Confirm Password' + (customerPortal?.lbl_confirm_password ? '*' : '')"
                            formControlName="email" formControlName="confirm_password">
                        <div class="input-group-append">
                            <button class="btn" type="button"
                            (click)="isShowConfirmPassword = !isShowConfirmPassword"    
                            >
                                <i class="fa fa-eye"></i>
                            </button>
                        </div>
                        
                        <small *ngIf="submitted && form.get('confirm_password').hasError('required')" class="form-text text-invalid">
                            Confirm Password is 
                            <strong>required</strong>
                        </small>
                        <small
                            *ngIf="!form.get('confirm_password').hasError('required')&&form.get('confirm_password').hasError('password_match')" class="form-text text-invalid">
                            Passwords does't match
                        </small>
                    </div>
                    <div class="col-md-6 col-sm-6 col-6 form-group">
                        <!-- <input type="text" name="address_line1" class="input-field"
                        [attr.placeholder]="customerPortal?.lbl_address_line1 + (customerPortal?.lbl_address_line1 ? '*' : '')"
                            formControlName="address_line1"> -->
                            <app-address-google-search *ngIf="google_autocomplete_rerender"
                            [allowedCountry]="selectedCountry"
                            [location]="location"
                            [placeholder]="placeholder || 'Address Line1'"
                            (address)="address($event)"
                            (isAddressFieldEmpty)="checkLocation($event)"
                            >
                            </app-address-google-search>
                        <small *ngIf="submitted && !location" class="form-text text-invalid">
                            Address 1 is
                            <strong>required</strong>!
                        </small>
                    </div>
                    <div class="col-md-6 col-sm-6 col-6 form-group">
                        <input type="text" name="address_line2" class="input-field" [attr.placeholder]="customerPortal?.lbl_address_line2 || 'Address Line2'"
                            formControlName="address_line2">
                    </div>
    
                    <div class="col-md-6 col-sm-6 col-6 form-group">
                        <select class="form-control m-input dropdown-cls" id="country_online"
                        (change)="onChange($event.target.value)" formControlName="country">
                            <option
                            [value]="item.code"
                            *ngFor="let item of countries">
                            {{ item.name }}
                            </option>
                        </select>
                    </div>
    
                    <div class="col-md-6 col-sm-6 col-6 form-group">
                        <input
                            type="text"
                            name="city"
                            class="input-field"
                            [attr.placeholder]="customerPortal?.lbl_city || 'City'"
                            formControlName="city"
                            id="city_online"
                            autocomplete="address-level2"
                            (change)="onChangeCity()">
                        <small *ngIf="submitted && form.get('city').hasError('required')" class="form-text text-invalid">
                            City is
                            <strong>required</strong>!
                        </small>
                    </div>
                    <div class="col-md-6 col-sm-6 col-6 form-group">
                        <input type="text" name="state" id="state_online" class="input-field" [attr.placeholder]="customerPortal?.lbl_state || 'State'"
                            formControlName="state">
                    </div>
                    <div class="col-md-6 col-sm-6 col-6 form-group">
                        <input type="text" id="zipcode_online" name="zipcode" class="input-field" [attr.placeholder]="customerPortal?.lbl_zipcode || 'Zip Code'"
                            formControlName="zipcode">
                    </div>
                    <div class="col-md-12 form-group">
                       <p> {{ customerPortal?.txt_already_registered || 'Already Registered?' }}  
                           <a 
                            class="login-link" 
                            href="javascript:void(0)"
                            (click)="goToBack()">{{ customerPortal?.txt_login_here || 'Login Here' }} 
                            </a>
                        </p>
                    </div>
                    <!-- <div class="col-md-6 col-sm-6 col-6 form-group text-right">
                        
                    </div> -->
    
                    <div class="col-md-12 col-sm-12 col-12 form-group text-center mt-0 mb-0">
                        <button type="submit" class="login">{{customerPortal?.lbl_signup || 'Sign Up'}}</button>
                    </div>
                </form>          
            </div>
            </div>            
        </ng-container>
        <ng-template #confirmationContent>
            <div class="vendor-thankyou-msg">
                <div class="vendor-thankyou-msg-inner">
                    <h2>{{ customerPortal?.lbl_thank_you || 'Thank You' }}</h2>                    
                    <p>{{ messageFormet(customerPortal?.partner_registration_thank_you_message) }} </p>
                    <!-- <p>for registering as a Partner for {{ onlineStoreName }}! Your registration is confirmed.
                        We look forward to receiving your full participation details and showcase information soon.
                    </p> -->
                    <button type="button" class="btn btn-dark" 
                    (click)="goToBack()">
                        <i class="la la-arrow-left mr-1"></i>{{ customerPortal?.btn_back || 'Back' }} 
                    </button>
                </div>
            </div>
        </ng-template>
    </div>
    <div class="client-signup-rightside">
        <img src="./../../../assets/img/home/<USER>" />
    </div>
  </div>
</div>