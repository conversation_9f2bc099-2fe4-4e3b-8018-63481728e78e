.client-signup {
  position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    height: 100vh;
    padding-top: 40px;
    padding-bottom: 40px;
}
.client-signup::before {
    content: "";
    position: fixed;
    left: -42px;
    bottom: -140px;
    background-image: url("./../../../assets/img/home/<USER>");
    background-repeat: no-repeat;
    background-position: center left;
    width: 700px;
    height: 550px;
    background-size: contain;
    z-index: 1;
}
.client-signup::after {
    content: "";
    position: fixed;
    right: -400px;
    top: -140px;
    background-image: url("./../../../assets/img/home/<USER>");
    background-repeat: no-repeat;
    background-position: center left;
    width: 700px;
    height: 550px;
    background-size: contain;
    z-index: 1;
}
.client-signup-inner {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #fff;
    width: 900px;
    min-height: 500px;
    box-shadow: 0px 0px 10px #ddd;
    padding: 25px;
    border-radius: 10px;
    z-index: 9;
}
.client-signup-leftside {
    width: 45%;
}
.client-signup-rightside {
    width: 55%;
    padding-left: 30px;
}
.vendor-signup-title {
  text-align: center;
  margin-bottom: 12px;
}
.client-signup-title {
    font-size: 30px;
    font-weight: 700;
    margin-bottom: 0;
    text-align: center;
}
.vendor-signup-title span {
  display: block;
  color: #666;
}
.userlogin-box {
	transition: all .3s;
}
.login-register-content .nav-tabs .nav-item {
    margin-bottom: -1px;
	width: 50%;
    text-align: center;
    display: inline-block;
    margin-right: 0;
    margin: 0;
}
.login-register-content .nav-tabs .nav-item .nav-link, 
.login-register-content .nav-tabs .nav-link {
	background-color: #edf1f5;
	border: none;
	color: #212529;
	font-weight: 500;
	letter-spacing: 1px;
	padding: 12px 15px;
	border-radius: 0;
	text-transform: uppercase;
}
.login-register-content .nav-tabs .nav-item.show .nav-link, 
.login-register-content .nav-tabs .nav-link.active {
	background-color: #212529 !important;
	color: #fff !important;
}

.login-title {
	  width: 100%;
	 height: auto;
	 text-align: center;
}
.login-title h1 {
	font-size: 20px;
	  font-weight: 500;
	  text-transform: uppercase;
	  color: #222;
	  letter-spacing: 1px;
	  padding-top: 5px;
}
.userlogin-body {
	width: 100%;
	height: auto;
	padding: 20px 0 0;
}
.loginform a {
	font-size: 11px;
	color: #7f8c8d;
}
.loginform a:hover{
	text-decoration: underline;
}
.loginform.row {
  margin-left: -7.5px;
  margin-right: -7.5px;
}
.loginform.row .col-md-6,
.loginform.row .col-md-12 {
  padding-left: 7.5px;
  padding-right: 7.5px;
}
.userlogin-body .form-control,
.userlogin-body .input-field {
	width: 100%;
	 height: 40px;
	 padding: 8px 12px 8px 12px;
	 font-size: 12px;
	 color: #7f8c8d;
	 /* background: #edf1f5; */
	 border: 1px solid #ddd;
	 outline: 0;
	 border-radius: 3px;
   font-weight: 400;
}
.userlogin-body .form-control::placeholder,
.userlogin-body .input-field::placeholder {
	 font-size: 12px;
	 color: #7f8c8d;
   font-weight: 400;
}
.userlogin-body input:hover,
.userlogin-body input:focus{
	box-shadow: none;
	border: 1px solid #ddd;
}
.login{
	width: 180px;
    height: 42px;
    padding: 10px;
    text-align: center;
    font-size: 14px;
    background-color: #212529;
    color: #fff;
    border: 0px;
    transition: .3s ease-in-out;
    -webkit-transition: .3s ease-in-out;
    outline: 0;
    cursor: pointer;
    border-radius: 3px;
    font-weight: 400;
    text-transform: uppercase;
}
.login:focus,
.login:hover{
	background-color: #444;
	outline: 0;
	border: none;
}
.userlogin-body .custom-control label {
	color: #555 !important;
	font-size: 14px;
	font-weight: 400 !important;
}
.userlogin-body .custom-control a {
	color: #555 !important;
	font-size: 14px !important;
	font-weight: 400 !important;
	text-decoration: underline;
	padding: 0;
	position: unset;
	letter-spacing: 0;
	display: inline-block !important;
	text-transform: unset;
}
  
.custom-checkbox .custom-control-input:checked~.custom-control-label::before {
    background-color: #212529;
}
.succesmsg-text {
    color: #69BA6C;
    font-weight: 400;
}
.errormsg-text {
    color: #DE322E;
    font-weight: 400;
}
.form-group {
    margin-bottom: 20px;
}



.vendor-thankyou-msg {
  text-align: center;
}
.vendor-thankyou-msg h2 {
    font-weight: 700;
    font-size: 40px;
}
.vendor-thankyou-msg p {
    margin-bottom: 18px;
}
.vendor-thankyou-msg button {
    padding: 0.65rem 1.3rem;
}
.login-link{
  font-size: 14px !important;
  color: #3E97FF !important;
  font-weight: 500 !important;
  text-decoration: none  !important;
}

@media (max-width: 1400px) {
  .client-signup {
    padding-top: 40px;
    padding-bottom: 40px;
    overflow: auto;
  }
  .client-signup-inner {
    margin-top: 170px;
  }
}
@media (max-width: 992px) {
  .client-signup-inner {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #fff;
    width: 450px;
    min-height: 500px;
    box-shadow: 0px 0px 10px #ddd;
    padding: 25px;
    border-radius: 10px;
    z-index: 9;
  }
  .client-signup-leftside {
    width: 100%;
  }
  .client-signup-rightside {
      display: none;
  }
}
@media (max-width: 575px) {
  .client-signup {
    overflow: unset;
    padding-left: 15px;
    padding-right: 15px;
    height: unset;
  }
  .client-signup-inner {
    width: 100%;
    min-height: 400px;
    padding: 20px;
    margin-top: 0px;
  }
  .client-signup-leftside {
    width: 100%;
  }
  .client-signup-rightside {
      display: none;
  }
  .form-group {
    margin-bottom: 15px;
  }
}