import { Injectable } from "@angular/core";
import {
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  Router,
  CanActivate,
  Resolve
} from "@angular/router";
import { HttpService } from "../../../modules/http-with-injector/http.service";
import { environment } from "../../../../environments/environment";
import { Helpers } from "../../../../app/helpers";



@Injectable()
export class RedirectUrlResolveService implements Resolve<any> {
  constructor(
    private http: HttpService
  ) {

  }

  async  resolve(router: ActivatedRouteSnapshot, state: RouterStateSnapshot) {

      Helpers.setLoading(true);
      const res = await this.http.get(`connect/session/store_name`).toPromise();
      if(res.status=="OK"){
       Helpers.setLoading(false);
       //window.location.href =environment.protocal+res.result.data + (environment.storeHoshName !='localhost:4200' ?environment.storeHoshName : '' )+'/admin/settings/payment-gateways?code='+router.queryParamMap.get('code');
      }
    }
      
      
  }

