<div class="m-login__signin animated fadeIn">
  <div class="row">
    <div class="col-md-6 hidden-div">
      <div class="login-shape">
        <img src="assets/img/home/<USER>">
      </div>
    </div>
    <div class="col-md-6 login-side animated fadeIn">
      <div class="custom-alert" #hasAlert></div>
      <div *ngIf="message; else forGotPass">
        <div class="normal-message animated zoomIn" *ngIf="!activated">
          {{message}} <br> <button *ngIf="loading" (click)="resendEmail()" class="btn btn-dark m-btn--icon" style="margin: 10px;"
            [ngClass]="{'m-loader m-loader--light m-loader--right': sendingEmail}">Send Activation Email</button> <br> Or <br>
            <button type="button" (click)="goToSmsRoute()" class="btn btn-dark" style="margin: 10px;">Activate Account By SMS</button>
        </div>
        <div class="success-message animated zoomIn" *ngIf="activated">
          {{message}} <br> <a routerLink="/auth/login" class="m-link" style="font-weight: 500;">Go To Login</a>
        </div>
      </div>
      <ng-template #forGotPass>
        <div class="m-login__head">
          <h2 class="text-left">Forgotten Password?</h2>
          <h3 class="m-login__title text-left">Enter your email to reset your password</h3>
        </div>
        <form (ngSubmit)="f.form.valid && forgotPass(f)" #f="ngForm" class="m-login__form m-form" action="">
          <div class="form-group m-form__group">
            <input class="form-control m-input" type="text" pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,6}$"
              placeholder="Email*" name="email" ngModel #email="ngModel" id="m_email" autocomplete="off" required>
            <span class="error-span" *ngIf="email.errors && email.touched">
              <small *ngIf="email.errors.required" class="error">Email required</small>
              <small *ngIf="email.errors.pattern" class="error">Please enter correct email</small>
            </span>
          </div>
          <div class="m-login__form-action">
            <button type="submit" [disabled]="loading" class="btn m-btn--pill btn-outline-dark" [ngClass]="{'m-loader m-loader--skin-dark m-loader--right': loading}"
              style="margin-top: 0px;">Submit</button>
            &nbsp;&nbsp;
            <button type="reset" [disabled]="loading" class="btn m-btn--pill btn-outline-danger" (click)="cancel()">
              Cancel
            </button>
          </div>
        </form>
      </ng-template>
    </div>
  </div>
</div>
