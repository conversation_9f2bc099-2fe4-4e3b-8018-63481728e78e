import { HttpClient } from '@angular/common/http';
import { Subscription } from 'rxjs';
import { ActivatedRoute } from '@angular/router';
import { Component, OnInit, OnDestroy, Inject } from '@angular/core';
import { DOCUMENT } from '@angular/common';

@Component({
  selector: 'app-quickbook-config',
  templateUrl: './quickbook-config.component.html',
  styleUrls: ['./quickbook-config.component.css']
})
export class QuickbookConfigComponent implements OnInit, OnDestroy {
  sub: Subscription;

  constructor(
    private route: ActivatedRoute,
    @Inject(DOCUMENT) private document: any,
    private http: HttpClient,
  ) {
  }

  ngOnInit() {
    let storeName = '';
    let code = '';
    let state = '';
    let realmId = '';
    this.sub = this.route
      .queryParams
      .subscribe(params => {
        console.log(params);
        code = params['code'] ? params['code'] : null;
        state = params['state'] ? params['state'] : null;
        realmId = params['realmId'] ? params['realmId'] : null;
      });
    this.http.get('https://clientapi.rentmy.co/api/generic/data/quickbook_store').toPromise().then(
     (res: any) => {
        if (res.status === 'OK') {
          storeName = res.result.data;
          this.document.location.href = `https://${storeName}.rentmy.co/admin/settings/quick-book?code=${code}&state=${state}&realmId=${realmId}`;
        }
      }
    );
  }

  ngOnDestroy() {
    this.sub.unsubscribe();
  }

}
