import {NgModule} from '@angular/core';
import {Routes, RouterModule} from '@angular/router';
import {AuthComponent} from './auth.component';
import { CheckRouteService } from './check-route.service';
import { QuickbookConfigComponent } from './quickbook-config/quickbook-config.component';
import { RedirectViewComponent } from './redirect-view/redirect-view.component';
import { RedirectUrlResolveService } from './redirect-view/resolve/redirect-url-resolve.service';

const routes: Routes = [
    {
        path: '',
        component: AuthComponent,
        children: [
            // {
            //     path: 'login',
            //     loadChildren: () => import('./init-login/init-login.module').then(m => m.InitLoginModule),
            //     data : { title:'Client Login'}
            //     // data: {preloadDashboard: 'store'}
            // },
            // {
            //     path: 'partner-login',
            //     loadChildren: () => import('./login/login.module').then(m => m.LoginModule)
            //     // data: {preloadDashboard: 'company'}
            // },
            {
                path: 'sign-up',
                loadChildren: () => import('./sign-up/sign-up.module').then(m => m.SignUpModule),
                data : { title:'Client Registration'}
            },
            // {
            //     path: 'activation',
            //     loadChildren: () => import('./activation/activation.module').then(m => m.ActivationModule),
            //     canActivate: [CheckRouteService]
            // },
            // {
            //     path: 'sms-activation',
            //     loadChildren: () => import('./sms-activation/sms-activation.module').then(m => m.SmsActivationModule),
            //     canActivate: [CheckRouteService],
            //     data : { title:'Client SMS Activation'}
            // },
            // {
            //     path: 'forgot-password',
            //     loadChildren: () => import('./forget-password/forget-password.module').then(m => m.ForgetPasswordModule),
            //     data : { title:'Client Forgot Password'}
            // },
            // {
            //     path: 'reset-password/:key',
            //     loadChildren: () => import('./reset-password/reset-password.module').then(m => m.ResetPasswordModule),
            //     canActivate: [CheckRouteService],
            //     data : { title:'Client Reset Password'}
            // },
            // {
            //     path: 'configure/quickbook',
            //     component: QuickbookConfigComponent
            // },
            // {
            //     path: 'stripe',
            //     component: RedirectViewComponent,
            //     resolve: { list:RedirectUrlResolveService}
            // },
            {
                path: '**',
                redirectTo: 'sign-up'
            }
        ]
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class AuthRoutingModule {
}
