import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { CustomerAccessControlComponent } from './customer-access-control.component';

describe('CustomerAccessControlComponent', () => {
  let component: CustomerAccessControlComponent;
  let fixture: ComponentFixture<CustomerAccessControlComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [CustomerAccessControlComponent]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(CustomerAccessControlComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
