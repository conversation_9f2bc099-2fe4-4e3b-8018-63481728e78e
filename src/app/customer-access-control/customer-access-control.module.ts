import { Routes, RouterModule } from '@angular/router';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CustomerAccessControlComponent } from './customer-access-control.component';
import { OnlineAuthGaurdService } from '../modules/online-store-auth/online-auth-guard.service';
import { ClientCustomerAuthGaurdService } from '../modules/online-store-auth/client-customer-auth-guard.service';
import { CustomerAccessControlService } from './customer-access-control.service';
import { PreAuthService } from '../auth/pre-auth.service';
import { OnlineAuthService } from '../modules/online-store-auth/online-auth.service';

const routes: Routes = [
  {
    path: '',
    component: CustomerAccessControlComponent,
    children: [
      {
        path: 'login',
        loadChildren: () => import('./customer-login/customer-login.module').then(m => m.CustomerLoginModule),
        resolve: {
          onlineAuth: ClientCustomerAuthGaurdService
        },
      },
      {
        path: 'register',
        loadChildren: () => import('./sign-up/sign-up.module').then(m => m.SignUpModule),
        resolve: {
          onlineAuth: ClientCustomerAuthGaurdService
        },
      },
      {
        path: '**',
        redirectTo: 'login'
      }
    ]
  }
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes)
  ],
  exports: [RouterModule],
  declarations: [CustomerAccessControlComponent],
  providers: [OnlineAuthGaurdService, ClientCustomerAuthGaurdService, CustomerAccessControlService, PreAuthService, OnlineAuthService]
})
export class CustomerAccessControlModule { }
