import { PasswordStrengthBarModule } from './../../modules/password-strength-bar/password-strength-bar.module';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SignUpComponent } from './sign-up.component';
import { RouterModule, Routes } from '@angular/router';
import { AddressGoogleSearchModule } from 'src/app/modules/address-google-search/address-google-search.module';


const route: Routes = [
  { path: '', component: SignUpComponent }
];


@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    PasswordStrengthBarModule,
    RouterModule.forChild(route),
    AddressGoogleSearchModule
  ],

  declarations: [SignUpComponent],
})
export class SignUpModule { }
