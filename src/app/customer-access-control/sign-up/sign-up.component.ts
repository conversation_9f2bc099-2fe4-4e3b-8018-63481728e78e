import {
  Component,
  OnInit,
  Inject,
  PLATFORM_ID,
  ViewChild,
  ElementRef,
  AfterViewInit,
  On<PERSON><PERSON>roy
} from "@angular/core";
import {
  FormGroup,
  FormBuilder,
  Validators,
  AbstractControl
} from "@angular/forms";
import { isPlatformBrowser, DOCUMENT } from "@angular/common";
import { Router, ActivatedRoute } from "@angular/router";
import { AlertService } from "../../modules/alert/alert.service";
import { Helpers } from "../../helpers";
import { allCountry } from "src/app/globals/_classes/country";
import { OnlineAuthService } from "src/app/modules/online-store-auth/online-auth.service";
import { PreAuthService } from "src/app/auth/pre-auth.service";

class SignUp {
  store_name: string;
  slug: string;
  first_name: string;
  last_name: string;
  email: string;
  password: string;
  user_type_id: number;
  mobile: string;
}

@Component({
  selector: "app-sign-up",
  templateUrl: "./sign-up.component.html",
  styleUrls: ["./sign-up.component.css"]
})
export class SignUpComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild("hasAlert") alertContainer: ElementRef;
  form: FormGroup;
  submitted = false;
  countries = allCountry;
  countryCode = 230;
  selectedCountry: string;
  placesAutocomplete: any;
  hasError = false;
  errorMessage = [];
  location: string;
  placeholder: string;
  errorMessageText = '';
  isSuccessMessage: boolean = false;
  isErrorMessage: boolean = false;
  contents;
  customerPortal: any = {};
  redirectUrl: string = '';
  onlineStoreName = '';
  response: any;
  google_autocomplete_rerender: boolean = true;
  isShowPassword: boolean = false;
  isShowConfirmPassword: boolean = false;

  constructor(
    @Inject(PLATFORM_ID) private platformId: Object,
    @Inject(DOCUMENT) private document: any,
    private _router: Router,
    private activatedRoute: ActivatedRoute,
    private alert: AlertService,
    private auth: PreAuthService,
    public formBuilder: FormBuilder,
    private service: OnlineAuthService,
  ) {
  }

  ngOnInit() {
    Helpers.setLoading(false);
    this.redirectUrl = '';
    if (isPlatformBrowser(this.platformId)) {
      const store = JSON.parse(sessionStorage.getItem("online_store"));
      this.contents = JSON.parse(localStorage.getItem('contents'));
      this.customerPortal = this.contents?.site_specific?.customer_portal;
      this.placeholder = this.customerPortal?.lbl_address_line1 + (this.customerPortal?.lbl_address_line1 ? '*' : '');
      const storeCountry = store.location.country.toLowerCase() || "us";
      this.selectedCountry = storeCountry;
      this.initForm();
    } else {
      const store = JSON.parse(this.document.online_store);
      const storeCountry = store.location.country.toLowerCase() || "us";
      this.selectedCountry = storeCountry;
      this.initForm();
    }
    this.redirectUrl = this.activatedRoute.snapshot.queryParams['ref'] || '';
    this.getStoreName();
  }

  ngAfterViewInit() {
    Helpers.setLoading(false);
  }

  ngOnDestroy() { }

  initForm(): void {
    this.form = this.formBuilder.group({
      first_name: ["", Validators.required],
      last_name: ["", Validators.required],
      email: ["", [Validators.required, Validators.email]],
      company: [''],
      password: ["", [Validators.required, Validators.minLength(6)]],
      confirm_password: [
        "",
        [Validators.required, this.matchPassword.bind(this)]
      ],
      mobile: [""],
      address_line1: ['', Validators.required],
      address_line2: [''],
      city: [''],
      state: [''],
      zipcode: [''],
      country: [this.selectedCountry, Validators.required]
    });
  }

  matchPassword(control: AbstractControl) {
    return this.form && control.value !== this.form.controls["password"].value
      ? { password_match: true }
      : null;
  }

  submit() {
    this.errorMessageText = '';
    this.submitted = true;
    if (this.form.invalid) {
      return;
    }
    if (isPlatformBrowser(this.platformId)) {
      Helpers.setLoading(true);
    }
    this.service
      .onlineStoreCustomerRegister(this.form.getRawValue())
      .then(res => {
        if (isPlatformBrowser(this.platformId)) {
          Helpers.setLoading(false);
        }
        if (res.status === "OK") {
          this.submitted = false;
          this.isSuccessMessage = true;
          this.service.setUser(res.result.data, true);
          this.form.reset();
          this.location = '';
          this.getStoreName();
        } else if (res.status === 'NOK') {
          if (isPlatformBrowser(this.platformId)) {
            this.errorMessageText = res.result.message;
            this.isErrorMessage = true
            setTimeout(() => {
              this.isErrorMessage = false;
            }, 3000);
          }
        } else {
          if (isPlatformBrowser(this.platformId)) {
            this.errorMessageText = "Something went wrong!";
            this.isErrorMessage = true
            setTimeout(() => {
              this.isErrorMessage = false;
            }, 3000);
          }
        }
      })
      .catch(err => {
        if (isPlatformBrowser(this.platformId)) {
          Helpers.setLoading(false);
        }
        this.submitted = false;
        if (isPlatformBrowser(this.platformId)) {
          this.errorMessageText = err.error.result ? err.error.result.message : err.message;
          this.isErrorMessage = true
          setTimeout(() => {
            this.isErrorMessage = false;
          }, 3000);
        }
      });
  }

  onChangeCity() {
    if (isPlatformBrowser(this.platformId)) {
      this.form.get("city").setValue(
        $("#city_online")
          .val()
          .toString()
      );
      this.form.get("state").setValue(
        $("#state_online")
          .val()
          .toString()
      );

      this.form.get("zipcode").setValue(
        $("#zipcode_online")
          .val()
          .toString()
      );
    }
  }

  onChange(countryCode) {
    this.selectedCountry = countryCode;
    this.google_autocomplete_rerender = false;
    setTimeout(() => { this.google_autocomplete_rerender = true; }, 100);
    this.form.get("country").setValue(countryCode);
    if (isPlatformBrowser(this.platformId)) {
      this.placesAutocomplete.configure({ countries: [this.selectedCountry] });
      $("#city_online").val('')
      $("#state_online").val('')
      $("#zipcode_online").val('')
    }
    this.form.get("city").setValue('');
    this.form.get("state").setValue('');
    this.form.get("zipcode").setValue('');
  }

  public checkLocation(locationCheck: string) {
    this.form.get("address_line1").setValue(locationCheck);
    this.location = locationCheck
  }
  public address(address) {
    const address_country = this.countries.find(n => address.country_short_name === n.code.toUpperCase());
    if (address_country) {
      this.form.get("country").setValue(address_country.code ? address_country.code : 230);
    }
    this.form.get("address_line1").setValue(address.location ? address.location : '');
    this.form.get("zipcode").setValue(address.zipcode ? address.zipcode : '');
    this.form.get("city").setValue(address.city ? address.city : '');
    this.form.get("state").setValue(address.state ? address.state : '');
  }

  getStoreName() {
    let online_store = JSON.parse(sessionStorage.getItem('online_store'));
    this.onlineStoreName = online_store?.store?.name || '';
  }

  goToBack() {
    this._router.navigate(['/customer/login']);
  }

  messageFormet(msg) {
    return msg.replace("{{store_name}}", this.onlineStoreName)
  }

}
