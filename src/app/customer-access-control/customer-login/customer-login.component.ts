import { PreAuthService } from './../../auth/pre-auth.service';
import { AlertService } from './../../modules/alert/alert.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Helpers } from './../../helpers';
import { MetaService } from "./../../_services/meta.service";
import { Component, OnInit, Inject, PLATFORM_ID, ElementRef, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { OnlineAuthService } from '../../modules/online-store-auth/online-auth.service';
import { isPlatformBrowser, DOCUMENT } from '@angular/common';
@Component({
  selector: 'app-customer-login',
  templateUrl: './customer-login.component.html',
  styleUrls: ['./customer-login.component.css']
})
export class CustomerLoginComponent implements OnInit {
  form: FormGroup;
  @ViewChild("hasAlert") alertContainer: ElementRef;
  submitted = false;
  errorMessageText = '';
  successMessageText: string = '';
  isSuccessMessage: boolean = false;
  isErrorMessage: boolean = false;
  contents;
  customerPortal: any = {};
  isShowPassword: boolean = false;
  is_token: boolean = false;

  constructor(
    @Inject(PLATFORM_ID) private platform: {},
    @Inject(DOCUMENT) private document: any,
    private metaS: MetaService,
    private fb: FormBuilder,
    private router: Router,
    private alertService: AlertService,
    private service: PreAuthService,
    private onlineAuthS: OnlineAuthService
  ) {
    this.form = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]]
    });
  }

  ngOnInit() {
    const onlineAuthUser = JSON.parse(localStorage.getItem('onlineAuthUser'));
    if (onlineAuthUser?.token) {
      this.router.navigate(['/user/profile']);
      this.is_token = true;
      return;
    } else {
      this.is_token = false;
    }
    if (isPlatformBrowser(this.platform)) {
      Helpers.setLoading(false);
      this.contents = JSON.parse(localStorage.getItem('contents'));
      this.customerPortal = this.contents?.site_specific?.customer_portal;
    }
    const title = JSON.parse(this.document.online_store).store.slug + ":: Client Login";
    this.metaS.setTitle(title);
  }

  submit() {
    this.submitted = true;
    if (this.form.invalid) {
      return;
    }
    if (isPlatformBrowser(this.platform)) {
      Helpers.setLoading(true);
    }
    this.submitted = true;
    this.service
      .clientLogin(this.form.getRawValue())
      .then(res => {
        if (isPlatformBrowser(this.platform)) {
          Helpers.setLoading(false);
        }
        this.submitted = false;
        if (res.status === "OK") {
          this.onlineAuthS.setUser(res.result.data, true);
          if (res.result.data.customer_type === 2) {
            this.router.navigate(['/user/profile']);
          } else {
            this.router.navigate(['/']);
          }

          this.successMessageText = res.result.message;
          this.isSuccessMessage = true
          setTimeout(() => {
            this.isSuccessMessage = false;
          }, 2000);

        } else if (res.status === 'NOK') {
          this.errorMessageText = res.result.message;
          this.isErrorMessage = true
          setTimeout(() => {
            this.isErrorMessage = false;
          }, 2000);

        } else {
          this.errorMessageText = 'Something went wrong!';
          this.isErrorMessage = true
          setTimeout(() => {
            this.isErrorMessage = false;
          }, 2000);
        }
      })
      .catch(err => {
        if (isPlatformBrowser(this.platform)) {
          Helpers.setLoading(false);
        }
        this.submitted = false;
        this.errorMessageText = err.error.result.message;
        this.isErrorMessage = true
        setTimeout(() => {
          this.isErrorMessage = false;
        }, 2000);

      });
  }

  goToBack() {
    this.router.navigate(['/customer/register']);
  }

  forgot() {
    this.router.navigate(['/forgot-password']);
  }
}
