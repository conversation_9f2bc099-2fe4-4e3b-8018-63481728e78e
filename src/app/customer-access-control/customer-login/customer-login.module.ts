import { PreAuthService } from './../../auth/pre-auth.service';
import { HomeResoveService } from './../../home/<USER>/home-resolve.service';
import { RouterModule, Routes } from '@angular/router';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CustomerLoginComponent } from './customer-login.component';

const routes: Routes = [
  {
    path: '',
    component: CustomerLoginComponent,
    resolve: {
      content: HomeResoveService
    }
  }
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    FormsModule,
    ReactiveFormsModule,
  ],
  declarations: [CustomerLoginComponent],
  providers: [PreAuthService, HomeResoveService]
})
export class CustomerLoginModule { }
