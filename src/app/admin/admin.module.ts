import { DashboarServiceModule } from './pages/dashboard/dashboard.service/dashboard-service.module';
import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { AdminComponent } from "./admin.component";
import { AdminService } from "./admin.service";
import { AdminRoutingModule } from "./admin-routing.module";
import { LayoutModule } from "./layouts/layout.module";
import { SidebarServiceModule } from "./pages/sidebar-service/sidebar-service.module";
import { CartServiceModule } from "./cart-service/cart-service.module";
import { ScriptLoaderService } from "../_services/script-loader.service";
import { AdminRoleGaurdService } from "./admin-role-gaurd.service";
import { AdminUtilityService } from "./admin-utility.service";
import { SettingService } from "./pages/settings/setting-service/setting.service";
import { DashboarService } from "./pages/dashboard/dashboard.service/dashboard.service";
import { PLATFORM_ID,Inject } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';

@NgModule({
  imports: [
    CommonModule,
    AdminRoutingModule,
    LayoutModule,
    SidebarServiceModule.forRoot(),
    CartServiceModule.forRoot()
  ],
  exports: [AdminRoutingModule],
  declarations: [AdminComponent],
  providers: [AdminService, AdminRoleGaurdService, AdminUtilityService, SettingService, DashboarService]
})
export class AdminModule {
  credit: string[] = [
    "assets/vendors/base/vendors.bundle.min.js",
    "assets/js/admin/scripts.bundle.min.js",
    // "assets/js/admin/tawk.js"
  ];

  constructor(@Inject(PLATFORM_ID) private platform: Object,private _script: ScriptLoaderService) {
    for (const s of this.credit) {
      this.isScriptAlreadyIncluded(s);
    }
  }

  private isScriptAlreadyIncluded(src) {
    if(isPlatformBrowser(this.platform)){
      const scripts = document.getElementsByTagName("script");
      const source = [];
      if (scripts) {
        for (let i = 0; i < scripts.length; i++) {
          source.push(scripts[i].getAttribute("src"));
        }
      }
      if (!source.includes(src)) {
        this._script.loadScript("body", src);
      }
   }
  }
}
