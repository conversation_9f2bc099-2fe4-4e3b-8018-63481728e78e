import { Component, OnInit } from "@angular/core";
import { FormGroup, FormBuilder, Validators } from "@angular/forms";
import { ActivatedRoute } from "@angular/router";
import { SettingService } from "../setting-service/setting.service";
import { AlertService } from "../../../../modules/alert/alert.service";
import { getSubdomain } from "../../../../globals/_classes/functions";
import { Helpers } from "../../../../helpers";

@Component({
  selector: "app-atrium",
  templateUrl: "./atrium.component.html",
  styleUrls: ["./atrium.component.css"]
})
export class AtriumComponent implements OnInit {
  atriumForm: FormGroup;
  authorization: {};
  redirectUrl;
  is_disable_btn;
  instituteList;
  instituteId: number;
  is_dropdownShow;
  show_institute_error;

  constructor(
    fb: FormBuilder,
    private route: ActivatedRoute,
    private settingS: SettingService,
    private alertS: AlertService
  ) {
    this.show_institute_error = false;
    this.instituteList = [];
    this.atriumForm = fb.group({
      apiKey: ["", Validators.required],
      clientID: ["", Validators.required],
      institute_code: [""]
    });
  }

  ngOnInit() {
    this.is_dropdownShow = false;
    this.settingS
      .getAtrium()
      .then(res => {
        if (res.status === "OK") {
          Helpers.setLoading(false);
          const data = res.result.data;
          if (data.hasOwnProperty("institute_code")) {
            this.settingS
              .getinstituteList()
              .then(res => {
                if (res.status === "OK") {
                  Helpers.setLoading(false);
                  this.instituteList = res.result.data.institutions;
                  if (this.instituteList.length > 0) {
                    this.is_dropdownShow = true;
                  }
                }
              })
              .catch(err => console.log(err));
          }
          this.atriumForm.patchValue(data);
        }
      })
      .catch(err => console.log(err));
  }

  instituteChange(selectedValue) {
    console.log(selectedValue);
  }

  onSubmit() {
    Helpers.setLoading(true);
    const atriumFormData = this.atriumForm.value;

    if (atriumFormData.institute_code !== "") {
      this.settingS
        .setAtrium(atriumFormData)
        .then(res => {
          if (res.status === "OK") {
            Helpers.setLoading(false);
            this.show_institute_error=false;
          }
        })
        .catch(err => console.log(err));
    } else {
      if (
        this.instituteList.length > 0 &&
        atriumFormData.institute_code == ""
      ) {
        this.show_institute_error = true;
        Helpers.setLoading(false);
      } else {
        this.settingS
          .setAtrium(atriumFormData)
          .then(res => {
            if (res.status === "OK") {
              this.settingS
                .getinstituteList()
                .then(res => {
                  if (res.status === "OK") {
                    Helpers.setLoading(false);
                    this.instituteList = res.result.data.institutions;
                    if (this.instituteList.length > 0) {
                      this.is_dropdownShow = true;
                    }
                  }
                })
                .catch(err => console.log(err));
            }
          })
          .catch(err => console.log(err));
      }
    }
  }
}
