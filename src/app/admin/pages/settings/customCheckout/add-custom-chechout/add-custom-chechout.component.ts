import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { SidebarService } from '../../../sidebar-service/sidebar.service';
import { NgForm, FormGroup, FormBuilder, FormControl, Validators } from '@angular/forms';
import { stringify } from 'querystring';
import { CustomCheckout } from '../../models/settings.models';
import { CustomCheckoutService } from '../customCheckout.setvice';
import { SettingService } from '../../setting-service/setting.service';

@Component({
  selector: 'add-custom-chechout',
  templateUrl: './add-custom-chechout.component.html',
  styleUrls: ['./add-custom-chechout.component.css']
})
export class AddCustomCheckoutComponent implements OnInit {

  edit: boolean;
  customCheckout: CustomCheckout;
  is_ValuesFieldShow=false;
  loader: boolean;
  @Output() submitForm = new EventEmitter();
  customCheckoutForm: FormGroup;

  constructor(
    private fb: FormBuilder,
    private cus_checkoutService:CustomCheckoutService,
    private settingS: SettingService,
    private sidebarS: SidebarService) {
    this.customCheckoutForm = this.fb.group({
      field_name: new FormControl('', [Validators.required]),
      field_label: new FormControl('', [Validators.required]),
      field_type : new FormControl('', [Validators.required]),
      field_values: new FormControl('', [Validators.nullValidator]),
      field_is_required: new FormControl(true, [Validators.nullValidator])
    });
    this.cus_checkoutService.customCheckoutReload.subscribe(val => {
      if (val.reload) {
        this.customCheckoutForm.reset();
        this.edit = val.editMode;
      }
      if (val.editMode) {
        this.edit = val.editMode;
        this.customCheckoutForm.patchValue(val.customCheckout);
        this.customCheckout = val.customCheckout;
      }
    });

  }

  ngOnInit() {
  }

  formatData(data) {
    return {
      field_name: data['field_name'],
      field_label: data['field_label'],
      field_type: data['field_type'],
      field_is_required: data['field_is_required'] ? true : false,
      field_values: data['field_values']
    }
  }

  onChangeTypes(type){
    if(type ==="0" || type ==="2" || type==="-1"){
     this.is_ValuesFieldShow=false;
    }
    else{
     this.is_ValuesFieldShow=true;
    }
   // alert(type);
  }


  onChangeLabel(value){

  }


  submit() {
    this.loader = true;
    this.settingS.addCustomCheckout(this.formatData(this.customCheckoutForm.getRawValue()))
    .then(
      res => {
         if(res.status !=="NOK"){
          this.sendEmitedData(res.result.data, false, 'Custom checkout has been added');
         }
         else{
          this.sendEmitedData(res.result.data,true, res.result.error);
         }
      
      }
    ).catch(
      err => {
        console.log(err);
        this.sendEmitedData([], true, 'Something wrong! Custom checkout has been not added');
      }
    );
  }

  update() {
    this.loader = true;
    this.settingS.updateCustomCheckout(this.customCheckout.id, this.formatData(this.customCheckoutForm.getRawValue()))
    .then(
      res => {
        if(res.status !=="NOK"){
          this.sendEmitedData(res.result.data, false, 'Custom checkout has been updated')
         }
         else{
          this.sendEmitedData(res.result.data,true, res.result.error);
         }
      }
    ).catch(
      err => {
        console.log(err);
        this.sendEmitedData([], true, 'Something wrong! Custom checkout has been not updated')
      }
    );
  }

  sendEmitedData(result, error, message) {
    this.loader = false;
    // console.log(result);
    const emit_data = {data: result, alert: {error: error, message: message}}
    this.submitForm.emit(emit_data);
  }
}

