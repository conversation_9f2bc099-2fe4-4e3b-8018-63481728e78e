<div style="padding-top: 35px;">
    <h5 class="colorPurpel" *ngIf="edit; else add">Update Custom Checkout</h5>
    <ng-template #add class="colorPurpel">
        <h5 class="colorPurpel">Add Custom Checkout </h5>
    </ng-template>
    <form [formGroup]="customCheckoutForm" class="m-form m-form--fit m-form--label-align-right">
        <div class="form-group">
            <label for="title">
                Field Name<sup>*</sup>
            </label>
            <input type="text" formControlName="field_name" class="form-control m-input" placeholder="Enter field name"
                   id="title"
                   name="field_name" autocomplete="off" trim>
            <span class="m-form__help">Field name must be unique & without any space.</span>
        </div>

        <div class="form-group m-form__group">
            <label for="title">
         Label<sup>*</sup>
        </label>
        <input type="text" formControlName="field_label"
        (change)="onChangeLabel($event.target.value)"
          class="form-control m-input" placeholder="Enter label"
          id="label" 
        name="field_label" autocomplete="off">
      </div>

        <div class="form-group">
            <label for="Types">
                Type<sup>*</sup>
            </label>
            <select class="form-control m-input" id="types" name="field_type" (change)="onChangeTypes($event.target.value)" formControlName="field_type">
                <option value="-1">-Select Types-</option>
                <option value="0">Text</option>
                <option value="1">Selectbox</option>
                <option value="2">File</option>
                <!-- <option value="2">Checkbox</option>
                <option value="3">Radio</option> -->
<!--                <option value="4">Datepicker</option>-->
<!--                <option value="5">Address</option>-->
            </select>
        </div>

        <div class="form-group" *ngIf="is_ValuesFieldShow">
            <label for="values">
                Values
            </label>
            <input type="text" formControlName="field_values" class="form-control m-input" placeholder="Enter Values" name="field_values" autocomplete="off">
            <span class="m-form__help">Please add multiple values separated by semicolon(;) .</span>
        </div>

        <div class="form-group">
            <label class="m-checkbox">
                <input type="checkbox" formControlName="field_is_required" name="field_is_required">
                Is Required
                <span></span>
            </label>
        </div>
        <div class="m-portlet__foot m-portlet__foot--fit text-right">
            <div class="m-form__actions m-form__actions" style="padding: 30px 0px;">
                <div *ngIf="loader; else button" class="m-loader m-loader--brand" style="width: 30px; padding-left: 30px; display: inline-block;"></div>
                <ng-template #button>
                    <button type="button" [disabled]="!customCheckoutForm.valid" class="btn btn-brand" *ngIf="edit; else addbtn" (click)="update()">
                        <i class="fa fa-save"></i> <span style="padding-left:10px;">Update</span>
                    </button>
                    <ng-template #addbtn>
                        <button type="button" [disabled]="!customCheckoutForm.valid" class="btn btn-brand" (click)="submit()">
                            <i class="fa fa-save"></i> <span style="padding-left:10px;">Submit</span>
                        </button>
                    </ng-template>
                </ng-template>
            </div>
        </div>
    </form>
</div>