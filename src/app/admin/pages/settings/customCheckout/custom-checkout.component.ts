import { Compo<PERSON>, OnInit, HostListener, AfterViewInit, ViewChild, ElementRef, OnDestroy } from '@angular/core';
import { SidebarService } from '../../sidebar-service/sidebar.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { DialogBoxComponent } from '../../../../modules/dialog-box/dialog-box.component';
import { CustomCheckout } from '../models/settings.models';
import { AlertService } from '../../../../modules/alert/alert.service';
import { SettingService } from '../setting-service/setting.service';
import { ActivatedRoute } from '@angular/router';
import { Helpers } from '../../../../helpers';
import { CustomCheckoutService } from './customCheckout.setvice';
@Component({
  selector: 'app-custom-checkout',
  templateUrl: './custom-checkout.component.html',
  styleUrls: ['./custom-checkout.component.css']
})
export class CustomCheckoutComponent implements OnInit, AfterViewInit {

  customCheckoutList: CustomCheckout[] = [];
  sideBaropen: boolean;
  customCheckout: CustomCheckout;
  EditMode: boolean;

  @HostListener('window:resize', ['$event'])
  onResize(event) {
    if (this.sideBaropen) {
      $('.native-routing').css('display', 'block');
      this.sidebarS.openSidebar();
    }
  }

  @ViewChild('hasCusAlert') alertContainer: ElementRef;

  constructor(
    private customCheckoutService: CustomCheckoutService,
    private sidebarS: SidebarService,
    private settingS: SettingService,
    private alertS: AlertService,
    private activeRoute: ActivatedRoute,
    private modalService: NgbModal
  ) {
    const list = this.activeRoute.snapshot.data['list'];
    if (list) {
      this.customCheckoutList = list.data;
    }
  }

  ngOnInit() {}

  ngAfterViewInit() {
    //window.scrollTo(0, 0);
    this.closeSidebar();
    this.executeAction();
  }

  trackCustomCheckout(index, data) {
    return data ? data.id : null;
  }

  editCustomCheckout(data) {
    this.openSidebar();
    this.EditMode = true;
    this.customCheckout = data;
    this.customCheckoutService.customCheckoutReload.next({ reload: false, editMode: true, customCheckout: data });
  }

  AddCustomCheckout() {
    this.openSidebar();
    this.customCheckout = null;
    this.EditMode = false;
    this.customCheckoutService.customCheckoutReload.next({ reload: true, editMode: false});
  }

  openSidebar() {
    $('.native-routing').css('display', 'block');
    this.sidebarS.openSidebar();
    this.sideBaropen = true;
  }

  deleteCustomCheckout(id, i) {
    const modalRef = this.modalService.open(DialogBoxComponent, {
      centered: true,
      size: 'sm',
      windowClass: 'animated fadeIn'
    });
    modalRef.componentInstance.massage = 'Are you sure you want to delete?';
    modalRef.result
      .then((result) => {
        if (result) {
          Helpers.setLoading(true);
          this.archiveCustomCheckout(id, i);
        }
      }, (res) => {
        console.log(res);
      });

  }

  archiveCustomCheckout(id, i) {
    this.settingS.deleteCustomCheckout(id).then(
      res => {
        this.customCheckoutList.splice(i, 1);
        this.alert({error: false, message: 'Custom checkout has been deleted'});
      }
    ).catch (
      err => {
        console.log(err);
        this.alert({error: true, message: 'Something wrong! Custom checkout has been not deleted'});
      }
    )
  }

  SubmitCustomCheckout(event) {
    if (!event.alert.error) {
      if (this.EditMode) {
        this.customCheckout.field_name = event.data.field_name;
        this.customCheckout.field_label = event.data.field_label;
        this.customCheckout.field_type = event.data.field_type;
        this.customCheckout.field_values = event.data.field_values;
        this.customCheckout.field_is_required = event.data.field_is_required;
      } else {
        this.customCheckoutList.push(event.data);
      }
      this.executeAction();
    }
    // console.log(event);
    this.alert(event.alert);
  }

  closeSidebar() {
    $('.close-sidebar').click((e) => {
      e.preventDefault();
      this.executeAction();
    });
    $('.close-sidebar-upper').click((e) => {
      e.preventDefault();
      this.executeAction();
    });
  }

  executeAction() {
    this.sideBaropen = null;
    this.sidebarS.removeSidebar();
    $('.native-routing').css('display', 'none');
  }

  alert(data) {
    Helpers.setLoading(false);
    if (data.error) {
      this.alertS.error(this.alertContainer, data.message, true, 5000);
    } else {
      this.alertS.success(this.alertContainer, data.message, true, 5000);
    }
  }

  


}
