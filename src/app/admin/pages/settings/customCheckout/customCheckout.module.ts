import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Routes, RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DialogBoxComponent } from '../../../../modules/dialog-box/dialog-box.component';
import { DialogBoxModule } from '../../../../modules/dialog-box/dialog-box.module';
import { PaginationModule } from '../../../../modules/pagination/pagination.module';
import { CustomCheckoutComponent } from './custom-checkout.component';
import { AddCustomCheckoutComponent } from './add-custom-chechout/add-custom-chechout.component';
import { CustomCheckoutService } from './customCheckout.setvice';
import { InputTrimModule } from 'ng2-trim-directive';


const route: Routes = [
  {
    path: '',
    component: CustomCheckoutComponent
  }
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(route),
    FormsModule,
    DialogBoxModule,
    PaginationModule,
    ReactiveFormsModule,
    InputTrimModule
  ],
  entryComponents: [DialogBoxComponent],
  exports: [RouterModule],
  declarations: [CustomCheckoutComponent,AddCustomCheckoutComponent],
  providers: [CustomCheckoutService]
})
export class CustomCheckoutModule { }
