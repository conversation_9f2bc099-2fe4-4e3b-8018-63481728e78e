import { Component, OnInit, Output, EventEmitter } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { SettingService } from "../../setting-service/setting.service";
import { Stores } from "../../models/settings.models";
import { GET_USER } from "../../../../../globals/_classes/functions";
import { allCountry } from "../../../../../globals/_classes/country";

@Component({
  selector: "app-distance-settings",
  templateUrl: "./distance-settings.component.html",
  styleUrls: ["../delivery.component.css"]
})
export class DistanceSettingsComponent implements OnInit {

  loading: boolean;
  loader: boolean;
  distanceForm: FormGroup;
  locations: Stores[] = [];
  locationId: string = GET_USER().location_id;
  countries = allCountry;
  allState;
  countryCode = 230;
  stateCode;

  @Output("alert") alert: EventEmitter<any> = new EventEmitter();

  constructor(private fb: FormBuilder, private service: SettingService) {
    this.getLocation();
  }

  ngOnInit() {
    this.distanceForm = this.fb.group(this.initForm());
  }

  private initForm() {
    return {
      address: ["", Validators.required],
      city: ["", Validators.required],
      state: ["", Validators.required],
      zipcode: ["", Validators.required],
      country: ["", Validators.required],
      in_mile: [false, Validators.required],
      distance: ["", Validators.required],
      min_delivery_fee: ["", Validators.required],
      distance_price: ["", Validators.required]
    };
  }

  getLocation() {
    this.service.getterminals().subscribe(res => {
      this.locations = res.data.filter(m => m.status);
      this.getLocationData();
    });
  }

  get unit() {
    return this.distanceForm.get('in_mile').value;
  }

  getLocationData() {
    this.loader = true;
    this.service.getDeliveryDistance(this.locationId).subscribe(
      res => {
        if(res) {
          this.distanceForm.patchValue(res);
          const country = this.countries.find(
            f => f.code.toUpperCase() == res.country
          );
          this.countryCode = country ? country.id : 230;
          if(!country) {
            this.distanceForm.get('country').setValue('US');
          }
        } else {
          this.distanceForm.reset();
        }
        this.loader = false;
        this.getState();
      }
    );
  }

  selectLocation() {
    this.getLocationData();
  }

  submitForm() {
    this.loading = true;
    this.service.saveDeliveryDistance(this.distanceForm.value, this.locationId)
    .then(
      res => {
        this.loading = false;
        if (res.status === "OK" && res.result.message) {
          this.alert.emit({ type: "success", message: res.result.message });
        } else {
          this.alert.emit({ type: "error", message: res.result.error });
        }
      }
    )
    .catch(
      err => {
        this.loading = false;
        this.alert.emit({
          type: "error",
          message: "Someting went wrong!!! Please try again."
        });
      }
    )
  }

  private getState() {
    this.service.getState(this.countryCode).subscribe(res => {
      this.allState = res;
      const state = this.distanceForm.get("state");
      if (this.allState && state.value) {
        const data = this.allState.find(
          f => f.code.toUpperCase() == state.value
        );
        this.stateCode = data ? data.id : null;
        if (!data) {
          state.setValue("");
        }
      }
    });
  }

  changeCountry(e) {
    this.countryCode = e.id ? e.id : 230;
    this.getState();
    const country = this.countries.find(f => f.id === this.countryCode);
    this.distanceForm.get("country").setValue(country.code.toUpperCase());
  }

  changeState(e) {
    if (e.id) {
      this.stateCode = e.id;
      const state = this.allState.find(f => f.id === e.id);
      this.distanceForm.get("state").setValue(state.code.toUpperCase());
    } else {
      this.distanceForm.get("state").setValue("");
    }
  }


}
