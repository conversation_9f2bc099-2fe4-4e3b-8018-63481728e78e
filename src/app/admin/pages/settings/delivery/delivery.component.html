<div class="custom-alert" #hasCusAlert></div>

<div class="m-subheader">
  <div class="d-flex align-items-center">
    <div class="mr-auto">
      <h3 class="m-subheader__title m-subheader__title--separator">
        Delivery
      </h3>
      <ul class="m-subheader__breadcrumbs m-nav m-nav--inline">
        <li class="m-nav__item m-nav__item--home">
          <a routerLink="/admin/dashboard" class="m-nav__link m-nav__link--icon">
            <i class="m-nav__link-icon la la-home"></i>
          </a>
        </li>
        <li class="m-nav__separator">
          <i class="fa fa-angle-right"></i>
        </li>
        <li class="m-nav__item">
          <a class="m-nav__link">
            <span class="m-nav__link-text">
              Settings
            </span>
          </a>
        </li>
        <li class="m-nav__separator" style="padding-left: 10px">
          <i class="fa fa-angle-right"></i>
        </li>
        <li class="m-nav__item">
          <a class="m-nav__link">
            <span class="m-nav__link-text">
              Delivery
            </span>
          </a>
        </li>
      </ul>
    </div>
  </div>
</div>
<!-- END: Subheader -->
<div class="m-content animated fadeIn">
  <div class="row m-portlet">
    <div class="col-md-6">
        <div class="m-portlet__head title-head">
            <div class="m-portlet__head-caption">
              <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text colorPurpel">
                    Delivery Settings
                </h3>
              </div>
            </div>
          </div>
      <div class="m-portlet__body">
      <section class="d-settings">
        <form class="mt-3 m-form m-form--fit m-form--label-align-right" [formGroup]="settingForm" (ngSubmit)="submitForm()">
          <div class="row">
            <div class="col-8">
              <h6>Display delivery options?</h6>
            </div>
            <div class="col-4">
              <div class="m-form__group form-group">
                <app-switch-button [domId]="'is_requiered'" [name]="'is_requiered'" [value]="getValue('is_requiered')"
                                   (changeValue)="changeValue($event, 'is_requiered')"></app-switch-button>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-8">
              <h6>Disable in-store pickup?</h6>
            </div>
            <div class="col-4">
              <div class="m-form__group form-group">
                <app-switch-button [domId]="'disable_instore_pickup'" [name]="'disable_instore_pickup'" [value]="getValue('disable_instore_pickup')"
                                   (changeValue)="changeValue($event, 'disable_instore_pickup')"></app-switch-button>
              </div>
            </div>
            <p *ngIf="errorMessage" style="margin-left: 15px;color: red">Sorry!! You can not disable your in-store pick up.</p>
          </div>
          <div class="row">
            <div class="col-8">
              <h6>Apply sales tax on delivery charges?</h6>
            </div>
            <div class="col-4">
              <div class="m-form__group form-group">
                <app-switch-button [domId]="'sales_tax'" [name]="'sales_tax'" [value]="getValue('sales_tax')" (changeValue)="changeValue($event, 'sales_tax')"></app-switch-button>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-8">
              <h6>Do you charge for delivery by zone? </h6>
              <small>(choose "<b>no</b>" if you charge per mile/per km )</small>
            </div>
            <div class="col-4">
              <div class="m-form__group form-group">
                <app-switch-button [domId]="'charge_by_zone'" [name]="'charge_by_zone'" [value]="getValue('charge_by_zone')"
                  (changeValue)="changeValue($event, 'charge_by_zone')"></app-switch-button>
              </div>
            </div>
          </div>
          <div class="m-portlet__foot m-portlet__foot--fit">
            <div class="m-form__actions m-form__actions text-right" style="padding: 20px 0px;">
              <button type="submit" class="btn btn-brand" [ngClass]="{'m-loader m-loader--light m-loader--right': loading}">
                Submit
              </button>
            </div>
          </div>
        </form>
      </section>
    </div>
    </div>

    <div class="col-md-6">
      <ng-template #deliveryForm></ng-template>
    </div>
  </div>
</div>
