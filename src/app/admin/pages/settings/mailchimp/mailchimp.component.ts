import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { SettingService } from '../setting-service/setting.service';
import { AlertService } from '../../../../modules/alert/alert.service';
import { getSubdomain, getdomain } from '../../../../globals/_classes/functions';

@Component({
  selector: 'app-mailchimp',
  templateUrl: './mailchimp.component.html',
  styleUrls: ['./mailchimp.component.css']
})
export class MailchimpComponent implements OnInit {

  @ViewChild("hasCusAlert") alertContainer: ElementRef;
  
  mailChimpForm: FormGroup;
  authorization: {};
  redirectUrl;
  is_disable_btn;
  is_show_disconnect_btn=false;

  constructor(
    fb: FormBuilder,
    private route: ActivatedRoute,
    private settingS: SettingService,
    private alertS: AlertService
  ) {
    this.route.queryParams.subscribe(params => {
      //console.log(params);
      this.is_disable_btn =params.code ? true:false;
      this.authorization = params;
      if (params.code) {
        this.settingS
          .setMailchimpToken(this.authorization)
          .then(res => {
            if (res.status === "OK") {
              this.is_show_disconnect_btn=true;
             this.alertS.success(this.alertContainer,res.result.message,true,5000);
            }
            else{
              this.alertS.error(this.alertContainer,res.result.message,true,5000);
            }
          })
          .catch(err => {
            
            console.log(err)
          });
      }
    });


    const domain = getdomain();
    const subDomain = getSubdomain();
    const subDom = subDomain.includes("192")
      ? "http://" + domain
      : "https://" + subDomain + ".rentmy.co";
    this.redirectUrl = subDom + "/admin/settings/mailchimp";


    this.mailChimpForm = fb.group({
      client_id: ["",Validators.required],
      client_secret: ["",Validators.required],
      redirect_url: [this.redirectUrl]
    });
  }

  ngOnInit() {
   
    this.settingS.getLeadLagTime().subscribe(
      res => {
        if (res.data !== null) {
          if (res.data.hasOwnProperty("mailchimp")) {
            this.mailChimpForm.patchValue(res.data.mailchimp);
          }
        }
      },
      err => console.error(err)
    );
  }

  onSubmit() {
    const mailChimpForm = this.mailChimpForm.value;
    this.settingS
      .setMailchimpConfig(mailChimpForm)
      .then(res => {
        if (res.status === "OK") {
          console.log(res.result.data);
          window.location.href = res.result.data;
          
        }
        else{
          this.alertS.error(this.alertContainer,res.result.message,true,5000);
        }
      })
      .catch(err => console.log(err));
  }


  onClickDisconnect(){
    this.settingS
      .disconnectMailchimp()
      .then(res => {
        if (res.status === "OK") {

          this.mailChimpForm.patchValue({client_id:'',client_secret:''});

          this.alertS.success(this.alertContainer,res.result.message,true,5000);  
        }
        else{
          this.alertS.error(this.alertContainer,res.result.message,true,5000);
        }
      })
      .catch(err => console.log(err));
  }
}
