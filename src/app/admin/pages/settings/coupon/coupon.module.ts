import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CouponComponent } from './coupon.component';
import { AddCouponComponent } from './add-coupon/add-coupon.component';
import { RouterModule, Routes } from '@angular/router';
import { DialogBoxComponent } from '../../../../modules/dialog-box/dialog-box.component';
import { PaginationModule } from '../../../../modules/pagination/pagination.module';
import { DialogBoxModule } from '../../../../modules/dialog-box/dialog-box.module';
import { FormsModule } from '@angular/forms';
import { NumberOnlyDirectiveModule } from '../../../../modules/directive/directive.module';
import { ProductSearchComponent } from './add-coupon/product-search/product-search.component';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { InventoryService } from '../../inventory/inventory-serveice/inventory.service';

const route: Routes = [
  {
    path: '',
    component: CouponComponent
  }
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(route),
    FormsModule,
    DialogBoxModule,
    PaginationModule,
    NumberOnlyDirectiveModule,
    NgbModule
  ],
  entryComponents: [DialogBoxComponent],
  exports: [RouterModule],
  declarations: [CouponComponent, AddCouponComponent, ProductSearchComponent],
  providers:[InventoryService]
})
export class CouponModule { }
