<div class="custom-alert" #hasCusAlert></div>

<div class="m-subheader">
    <div class="d-flex align-items-center">
        <div class="mr-auto">
            <h3 class="m-subheader__title m-subheader__title--separator">
                Coupons
            </h3>
            <ul class="m-subheader__breadcrumbs m-nav m-nav--inline">
              <li class="m-nav__item m-nav__item--home">
                <a routerLink="/admin" class="m-nav__link m-nav__link--icon">
                  <i class="m-nav__link-icon la la-home"></i>
                </a>
              </li>
              <li class="m-nav__separator">
                <i class="fa fa-angle-right"></i>
              </li>
              <li class="m-nav__item">
                <a class="m-nav__link">
                  <span class="m-nav__link-text">
                    Settings
                  </span>
                </a>
              </li>
              <li class="m-nav__separator" style="padding-left: 10px">
                <i class="fa fa-angle-right"></i>
              </li>
              <li class="m-nav__item">
                <a class="m-nav__link">
                  <span class="m-nav__link-text">
                    Coupons
                  </span>
                </a>
              </li>
            </ul>
        </div>
    </div>
  </div>
  <!-- END: Subheader -->
  <div class="m-content animated fadeIn">
    <div class="m-portlet">
        <div class="m-portlet__head title-head">
            <div class="m-portlet__head-caption">
              <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text colorPurpel">
                    Coupons
                </h3>
                <div class="add-list-btn text-right">
                    <button class="btn btn-brand" (click)="initAddCoupon()">
                        Add Coupon
                    </button>
                </div>
              </div>
            </div>
      </div>
      <div class="m-portlet__body">
        <div class="">
          <div class="m-section__content">
            <div *ngIf="loader" class="table-load m-loader m-loader--brand"></div>
            <div class="price-table table-responsive">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th>
                      Id
                    </th>
                    <th>
                      Product name
                    </th>
                    <th>
                      Code
                    </th>
                    <th>
                      Start Date
                    </th>
                    <th>
                      End Date
                    </th>
                    <th>
                       Quantity Range
                      </th>
                    <th>Amount</th>
                    <th>Status</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody *ngIf="couponList.length > 0; else noDate">
                  <tr *ngFor="let cop of couponList; let i = 'index'; trackBy: trackCoupon; let o='odd'; let e='even'" [ngClass]="{'odd-tr':o, 'even-tr':e}">
                    <td>
                      {{cop.id}}
                    </td>
                    <td>
                        {{cop.type == 3 ? cop?.product_name : '-'}}
                      </td>
                    <td>
                      {{cop.type == 1 ? cop.code : '-'}}
                    </td>
                    <td>
                      {{cop?.start_time !=null ? (getDate(cop?.start_time) | date: 'mediumDate') : '-'}}
                    </td>
                    <td>
                      {{cop?.end_time !=null ? (getDate(cop?.end_time) | date: 'mediumDate') : '-'}}
                    </td>
                    <td>
                        {{cop.type != 1 ? cop.min_quantity : '' }}-{{cop.type != 1 ? cop.max_quantity : ''}}
                      </td>
                    <td>
                      {{(cop.unit_type == 2) ? '$' : '' }}{{cop.amount}}{{(cop.unit_type == 1) ? '%' : '' }}
                    </td>
                    <td>
                      <span [ngClass]="{'green': cop?.status, 'red': !cop?.status}">
                        {{cop?.status ? 'Active' : 'Inactive'}}
                      </span>
                    </td>
                    <td>       
                      <a id="m_quick_sidebar_toggle" (click)="editCoupon(cop)"
                        class="m-portlet__nav-link btn m-btn m-btn--hover-info m-btn--icon m-btn--icon-only m-btn--pill">
                        <i class="fa fa-edit"></i>
                      </a>
                      <a id="m_quick_sidebar_toggle" (click)="deleteCoupon(cop.id)"
                        class="m-portlet__nav-link btn m-btn m-btn--hover-danger m-btn--icon m-btn--icon-only m-btn--pill">
                        <i class="fa fa-trash"></i>
                      </a>
                    </td>
                  </tr>
                </tbody>
                <ng-template #noDate>
                  <tbody><tr *ngIf="!loader"><td colspan="7"><h5 class="text-center">No Coupon Found</h5></td></tr></tbody>
                </ng-template>
              </table>
              </div>
            <!-- pagination Start-->
            <boot-pagination [totalSize]="pagi.total" [listSize]="pagi.limit" (pageChange)="reloadTable($event)"></boot-pagination>
            <!-- pagination End-->
          </div>

        </div>
			<!--end::Section-->
      </div>
    </div>
  </div>


  <!-- sidebar -->

<div class="native-routing animated">
	<button class="close-sidebar btn btn-sm btn-brand">
		<i class="fa fa-chevron-right"></i>
  </button>
  <span class="close-sidebar-upper">
    <i class="la la-close"></i>
  </span>
    <div class="native-routing-container">
        <app-add-coupon [coupon]="coupon" (submit)="submitCoupon($event)"></app-add-coupon>
    </div>
</div>
<!-- <div class="backdrop animated"></div> -->



