import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { CustomCssComponent } from './custom-css.component';
import { FormsModule } from '@angular/forms';

const route: Routes =[
  {
    path: '',
    component: CustomCssComponent
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    RouterModule.forChild(route)
  ],
  declarations: [CustomCssComponent]
})
export class CustomCssModule { }
