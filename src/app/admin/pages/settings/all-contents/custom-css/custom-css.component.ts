import { Component, OnInit, ViewChild, ElementRef } from "@angular/core";
import { AlertService } from "../../../../../modules/alert/alert.service";
import { ContentService } from "../../setting-service/contents.service";
import { empty } from "rxjs";

@Component({
  selector: "app-custom-css",
  templateUrl: "./custom-css.component.html",
  styleUrls: ["./custom-css.component.css"]
})
export class CustomCssComponent implements OnInit {
  @ViewChild("hasCusAlert") alertContainer: ElementRef;

  customCss: string;

  constructor(private alert: AlertService, private service: ContentService) {}

  ngOnInit() {
    this.service.getCustomCss().subscribe(res => {
      this.customCss = res.data.contents;
    });
  }

  onClickSubmit() {
    if (this.customCss !== undefined) {
      console.log(this.customCss);
      const data = {
        contents: this.customCss
      };

      this.service
        .addCustomCss(data)
        .then(res => {
          console.log(res);
          if (res.status == "OK") {
            this.alert.success(
              this.alertContainer,
              res.result.message,
              true,
              3000
            );
          } else {
            this.alert.error(
              this.alertContainer,
              "somthing wrong!!",
              true,
              3000
            );
          }
        })
        .catch(err => {
          this.alert.error(this.alertContainer, "somthing wrong!!", true, 3000);
        });
    }

  }
}
