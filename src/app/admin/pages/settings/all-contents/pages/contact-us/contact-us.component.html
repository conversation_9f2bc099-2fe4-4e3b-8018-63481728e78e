<h5>Page Contents</h5>
<div class="row animated">
  <div class="col-md-6">
    <div class="form-group">
      <label for="name">Page Header</label>
      <input id="name" class="form-control m-input" name="header" placeholder="Page Header" [(ngModel)]="contents.heading">
    </div>


    <label>Contents</label>

    <div class="summernote-content"></div>
  </div>
  <div class="col-md-6">
    <h6>Location Map</h6>
    <form [formGroup]="textForm" class="locForm">
      <div formArrayName="location">
        <div class="row" *ngFor="let item of textArray.controls; let i=index" [formGroupName]="i" style="margin-top:20px">
          <div class="col-10">
            <div class="form-group">
              <label for="name">Latitude</label>
              <input type="text" class="form-control m-input" placeholder="Enter Latitude" name="latitude" formControlName="lat" autocomplete="off">
            </div>
            <div class="form-group">
              <label for="name">Longitude</label>
              <input type="text" class="form-control m-input" placeholder="Enter Longitude" name="longitude" formControlName="lng" autocomplete="off">
            </div>
          </div>
          <div class="col-2 form-group addNewBtn">
            <button type="button" class="m-portlet__nav-link btn m-btn m-btn--hover-brand m-btn--icon m-btn--icon-only m-btn--pill" (click)="addNewText()">
              <i class="fa fa-plus"></i>
            </button>
            <button *ngIf="i!=0" type="button" class="m-portlet__nav-link btn m-btn m-btn--hover-danger m-btn--icon m-btn--icon-only m-btn--pill" (click)="deleteText(i)">
              <i class="fa fa-trash"></i>
            </button>
          </div>
        </div>
      </div> 
    </form>
  </div>
  <div class="col-12">
    <div class="m-portlet__foot m-portlet__foot--fit">
      <div class="m-form__actions m-form__actions" style="padding: 30px 0px;">
        <div class="m-loader m-loader--brand"  *ngIf="loader; else btnSubmit" style="width: 30px; padding-left: 30px; display: inline-block;"></div>
        <ng-template #btnSubmit>
          <button type="submit" class="btn btn-brand" (click)="save()">
            <i class="fa fa-save"></i>
            <span style="padding-left:10px;">Submit</span>
          </button>
        </ng-template>
      </div>
    </div>
  </div>
</div>