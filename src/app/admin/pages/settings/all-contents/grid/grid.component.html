<div class="custom-alert" #hasCusAlert></div>
<section>
  <div class="text-right">
    <button *ngIf="edit" class="btn btn-sm btn-dark" (click)="addNew()">Add More</button>
    <button *ngIf="addNewClicked" class="btn btn-sm btn-dark" (click)="backToList()">Back</button>
  </div>
  <div class="row" *ngIf="!edit; else labelSadd" style="position: relative;">
    <div class="col-md-6" style="position: relative;">
      <small>Image Size: {{imageSize}}</small>
      <drag-drop [multi]='image.multi' [url]="image.url" [width]="120" [height]="120" [info]="config" [fileName]="'image'"
        [accept]="'image/*'" (upload)="uploadImages($event)"></drag-drop>
    </div>
    <div *ngIf="loader" class="table-load m-loader m-loader--brand"></div>
  </div>

  <ng-template #labelSadd>
    <form [formGroup]="textForm" (ngSubmit)="save()">
      <div formArrayName="contents">
        <div class="row" *ngFor="let item of textArray.controls; let i=index" [formGroupName]="i" style="margin-top:20px">
          <div class="col-sm-3 col-md-2 form-group text-center">
            <img class="img-fluid img-thumbnail" [src]="item.value.url" alt="No Image">
          </div>
          <div class="col-sm-8 col-md-7">
            <div class="form-group">
              <input type="text" class="form-control m-input" placeholder="Enter Header Text" name="label"
                formControlName="label" autocomplete="off">
            </div>
            <div class="form-group">
              <input type="text" class="form-control m-input" placeholder="Enter Sub-header Text" name="text"
                formControlName="text" autocomplete="off">
            </div>
            <div class="form-group">
              <input type="text" class="form-control m-input" placeholder="Enter Button Text" name="link"
                formControlName="btn_text" autocomplete="off">
            </div>
            <div class="form-group">
              <div class="input-group m-input-group">
                <div class="input-group-prepend">
                  <select name="type" class="form-control m-input" formControlName="type" (change)="changeType(i)">
                    <option value="">-Select Link Type-</option>
                    <option value="Product">Product</option>
                    <option value="Tag">Tag</option>
                    <option value="Category">Category</option>
                    <option value="Page">Page</option>
                    <option value="External">External</option>
                  </select>
                </div>
                <div class="input-group-prepend" *ngIf="getType(i) && !(getType(i)=='External')">
                  <ng-container *ngIf="getType(i) == 'Product'; else normal">
                    <select2-ajax [cache]="true" [placeholder]="'Select Product'" [domId]="'type' + i" [url]="urlProduct" [prop]="'name'"
                      (changeValue)="productSelect($event, i)"></select2-ajax>
                  </ng-container>
                  <ng-template #normal>
                    <app-select2-normal [data]="types[getType(i)]" [domId]="'type' + i" [value]="getType(i, 'content_id')"
                      [placeholder]="getType(i)" (changeValue)="makeLink($event, i)"></app-select2-normal>
                  </ng-template>
                </div>
                <span *ngIf="getType(i) && !(getType(i)=='External')" class="form-control m-input">{{getType(i,
                  'link')}}</span>
                <input *ngIf="getType(i) && (getType(i)=='External')" type="text" class="form-control m-input"
                  placeholder="Enter Link" formControlName="link" name="link">
              </div>
            </div>
            <div class="form-group">
              <small>({{item.value.url}})</small>
            </div>
          </div>
          <div class="col-sm-1 col-md-2 form-group">
            <button type="button" class="m-portlet__nav-link btn m-btn m-btn--hover-danger m-btn--icon m-btn--icon-only m-btn--pill"
              (click)="deleteText(i)">
              <i class="fa fa-trash"></i>
            </button>
          </div>
        </div>
      </div>
      <div class="m-portlet__foot m-portlet__foot--fit">
        <div class="m-form__actions m-form__actions" style="padding: 30px 0px;">
          <div class="m-loader m-loader--brand" *ngIf="loader; else btnSubmit" style="width: 30px; padding-left: 30px; display: inline-block;"></div>
          <ng-template #btnSubmit>
            <button type="submit" class="btn btn-brand">
              <i class="fa fa-save"></i>
              <span style="padding-left:10px;">Submit</span>
            </button>
          </ng-template>
        </div>
      </div>
    </form>
  </ng-template>
</section>
