<div class="custom-alert" #hasCusAlert></div>

<!-- END: Subheader -->
<div class="animated fadeIn">

      <div style="padding-bottom: 20px;">
        <button class="btn btn-brand" (click)="addContent()">
          Add Content
        </button>
      </div>
      <div class="m-section">
        <div class="m-section__content price-table" style="position:relative">
          <div *ngIf="loader" class="table-load m-loader m-loader--brand" ></div>
          <table class="table table-hover" style="padding-bottom: 10px;">
            <thead>
              <tr>
                <th>Tag</th>
                <th>Status</th>
                <th>Action</th>
              </tr>
            </thead>
            <tbody *ngIf="contents.length > 0; else noFound">
              <tr *ngFor="let content of contents; let i = 'index'; trackBy: trackContent; let o='odd'; let e='even'" [ngClass]="{'odd-tr':o, 'even-tr':e}">
                <td>{{content.config.label}}</td>
                <td [ngClass]="{'success-mgs': content.config.status, 'error': !content.config.status}">{{content?.config?.status ? 'Active' : 'Inactive'}}</td>
                <td>
                  <div class="m-loader m-loader--brand"  *ngIf="deleteId===content.id; else btnSubmit" style="width: 30px; padding-left: 30px; display: inline-block;"></div>
                  <ng-template #btnSubmit>
                    <button (click)="editContent(content)" class="m-portlet__nav-link btn m-btn m-btn--hover-info m-btn--icon m-btn--icon-only m-btn--pill">
                      <i class="fa fa-edit"></i>
                    </button>
                    <button (click)="deleteContent(content.id)" class="m-portlet__nav-link btn m-btn m-btn--hover-danger m-btn--icon m-btn--icon-only m-btn--pill">
                      <i class="fa fa-trash"></i>
                    </button>
                  </ng-template>
                </td>
              </tr>
            </tbody>
            <ng-template #noFound>
              <tbody>
                <tr *ngIf="!loader">
                  <td colspan="3"><h4 class="text-center">No Data Found</h4></td>
                </tr>
              </tbody>
            </ng-template>

          </table>
        </div>
      </div>

</div>


<!-- sidebar -->

<div class="native-routing animated">
  <button class="close-sidebar btn btn-sm btn-brand">
    <i class="fa fa-chevron-right"></i>
  </button>
  <span class="close-sidebar-upper">
    <i class="la la-close"></i>
  </span>
  <div class="native-routing-container">
    <app-contents-form (allertShow)="allertShow($event)"></app-contents-form>
  </div>
</div>
<!-- <div class="backdrop animated"></div> -->