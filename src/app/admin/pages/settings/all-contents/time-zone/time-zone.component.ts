import {
  <PERSON>mpo<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ementRef,
  ViewChild
} from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { ContentService } from "../../setting-service/contents.service";
import { Subscription } from "rxjs";
import { ActivatedRoute } from "@angular/router";
import { AlertService } from "../../../../../modules/alert/alert.service";
import { isJson, getdomain, getSubdomain, Time_format_list } from "../../../../../globals/_classes/functions";
import { HttpService } from "../../../../../modules/http-with-injector/http.service";

@Component({
  selector: "app-time-zone",
  templateUrl: "./time-zone.component.html",
  styleUrls: ["./time-zone.component.css"]
})
export class TimeZoneComponent implements OnInit, OnDestroy {
  timeForm: FormGroup;
  loading: boolean;
  zoneList = [];
  currencyList = [];
  sub: Subscription;
  data;
  format1 = false;
  format2 = false;
  format3 = false;
  url: string;
  currency_code = "USD";
  currency_symbol = "$";
  is_show_customer_integration= false;
  sales_tax = {
    rent_tax: 0.0,
    buy_tax: 0.0,
    delivery_tax: 0.0
    // tax_on_rent: false,
    // tax_on_buy: false,
    // tax_on_both: true DD MMM YYYY
  };
  @ViewChild("hasCusAlert") alertContainer: ElementRef;
  is_client_active: any;

  time_format_list = Time_format_list;

  constructor(
    private service: ContentService,
    private activeRoute: ActivatedRoute,
    private alert: AlertService,
    public http: HttpService,
    private fb: FormBuilder
  ) {
    this.sub = activeRoute.data.subscribe(val => {
      this.zoneList = val["list"];
    });
    this.service.getCurrencyList().subscribe(res => {
      this.currencyList = res.data;
    });

    this.service.getTimeZone().subscribe(res=>{
      const content=res.timezone;
      if(content.hasOwnProperty('customer'))
      {
        this.is_show_customer_integration=content.customer.active;
      }

    })
  }

  ngOnInit() {
    const domain = getdomain();
    const subDomain = getSubdomain();
    this.url = subDomain.includes("localhost")
      ? "http://" + domain
      : "https://" + subDomain + ".rentmy.co";
    console.log(this.url);
    this.timeForm = this.fb.group({
      timezone: "",
      code: "",
      symbol: "",
      format: "",
      rent_tax: ["0.00"],
      buy_tax: ["0.00"],
      delivery_tax: ["0.00"],
      tax_apply_on: ['1'],
      fb_tracking_id: "",
      fb_page_id: [''],
      rental_day: ["24"],
      show_end_date: [true],
      show_end_time: [true],
      show_start_date: [true],
      show_start_time: [true],
      show_checkout_additional_field: [true],
      show_checkout_address_field: [true],
      show_checkout_availability_text: [true],
      rental_price_option: [true],
      admin_signature: [],
      pos_signature: [],
      online_signature: [],
      checkout_online_capture: [false],
      online_order: [true],
      quote_order: [false],
      show_related_product_as_addon: [false],
      customer_registration : [true],
      customer_guest_checkout : [true],
      enable_store: [true],
      maintenance : [false],
      maintenance_content: [false],
      show_category_parent: [false],
      breadcrumb: [true],
      show_category_sidebar: [true],
      show_product_id: [false],
      client_online_public_access: [false],
      date_format: ['DD MMM YYYY'],
      pos_skip_fulfillment: [false],
      pos_skip_payment: [false],
      checkout_single_page: [true],
      checkout_sms: [true]
    });

    this.getFormData();
  }

  getFormData() {
    this.service.getTimeZone().subscribe(
      res => {
        console.log(res);
        this.setTaxValue(res.timezone);
        this.setSignatureValue(res.timezone);
        if (res.timezone.currency_format) {
          const currency = res.timezone.currency_format;
          if (currency) {
            localStorage.setItem("currency", JSON.stringify(currency));
          }
          this.timeForm.get("code").setValue(res.timezone.currency_format.code);
          this.currency_code = res.timezone.currency_format.code;
          this.currency_symbol = res.timezone.currency_format.symbol;
          if (
            res.timezone.currency_format.pre == true &&
            res.timezone.currency_format.post == true
          ) {
            this.format1 = true;
          } else if (
            res.timezone.currency_format.pre == false &&
            res.timezone.currency_format.post == true
          ) {
            this.format2 = true;
          } else {
            this.format3 = true;
          }
        }
        this.is_client_active = res.timezone.hasOwnProperty('client') && res.timezone.client.hasOwnProperty('active') ? res.timezone.client.active : false;
        this.data = {
          timezone: res.timezone.timezone,
          show_start_date: res.timezone.show_start_date,
          show_end_date: res.timezone.show_end_date,
          show_start_time: res.timezone.show_start_time,
          show_end_time: res.timezone.show_end_time,
          rental_price_option: res.timezone.rental_price_option,
          show_related_product_as_addon: res.timezone.show_related_product_as_addon,
          show_checkout_additional_field: res.timezone.hasOwnProperty(
            "show_checkout_additional_field"
          )
            ? res.timezone.show_checkout_additional_field
            : false,
          show_checkout_address_field: res.timezone.hasOwnProperty(
            "show_checkout_address_field"
          )
            ? res.timezone.show_checkout_address_field
            : true,
          show_checkout_availability_text: res.timezone.hasOwnProperty(
            "show_checkout_availability_text"
          )
            ? res.timezone.show_checkout_availability_text
            : true,
          fb_tracking_id: res.timezone.hasOwnProperty("fb_tracking_id")
            ? res.timezone.fb_tracking_id
            : "",
          rental_day: res.timezone.hasOwnProperty('rental_day') ? res.timezone.rental_day : '24',
          checkout_online_capture: res.timezone.hasOwnProperty('checkout_online_capture') ? res.timezone.checkout_online_capture : false,
          online_order: res.timezone.hasOwnProperty('checkout') && res.timezone.checkout.hasOwnProperty('online_order') ? res.timezone.checkout.online_order : true,
          quote_order: res.timezone.hasOwnProperty('checkout') && res.timezone.checkout.hasOwnProperty('quote_order') ? res.timezone.checkout.quote_order : false,
          customer_registration: res.timezone.hasOwnProperty('customer') && res.timezone.customer.hasOwnProperty('customer_registration') ? res.timezone.customer.customer_registration : true,
          customer_guest_checkout: res.timezone.hasOwnProperty('customer') && res.timezone.customer.hasOwnProperty('customer_guest_checkout') ? res.timezone.customer.customer_guest_checkout : true,
          enable_store: res.timezone.hasOwnProperty('online_store') && res.timezone.online_store.hasOwnProperty('active') ? res.timezone.online_store.active : true,
          maintenance: res.timezone.hasOwnProperty('online_store') && res.timezone.online_store.hasOwnProperty('maintenance') ? res.timezone.online_store.maintenance : false,
          breadcrumb: res.timezone.hasOwnProperty('page_title') && res.timezone.page_title.hasOwnProperty('breadcrumb') ? res.timezone.page_title.breadcrumb : true,
          show_product_id: res.timezone.hasOwnProperty('online_store') && res.timezone.online_store.hasOwnProperty('show_product_id') ? res.timezone.online_store.show_product_id : false,
          client_online_public_access: res.timezone.hasOwnProperty('client') && res.timezone.client.hasOwnProperty('client_online_public_access') ? res.timezone.client.client_online_public_access : false,
          date_format: res.timezone.hasOwnProperty('date_format') ? res.timezone.date_format : 'DD MMM YYYY',
          skip_fulfillment: res.timezone.hasOwnProperty('pos') && res.timezone.pos.hasOwnProperty('skip_fulfillment') ? res.timezone.pos.skip_fulfillment : false,
          skip_payment: res.timezone.hasOwnProperty('pos') && res.timezone.pos.hasOwnProperty('skip_payment') ? res.timezone.pos.skip_payment : false,
          sms: res.timezone.hasOwnProperty('checkout') && res.timezone.checkout.hasOwnProperty('sms') ? res.timezone.checkout.sms : true,
          single_page: res.timezone.hasOwnProperty('checkout') && res.timezone.checkout.hasOwnProperty('single_page') ? res.timezone.checkout.single_page : true,
        };
        if (res.timezone.category) {
          this.timeForm
            .get("show_category_parent")
            .setValue(
              res.timezone.category.hasOwnProperty('parent')
                ? res.timezone.category.parent
                : false
            );
          const sidebar = res.timezone.category.hasOwnProperty('show_sidebar')
            ? res.timezone.category.show_sidebar
            : true;
          this.timeForm
            .get("show_category_sidebar")
            .setValue(sidebar);
        }
        this.timeForm.get("timezone").setValue(this.data.timezone);
        this.timeForm
          .get("show_start_date")
          .setValue(this.data.show_start_date);
        this.timeForm.get("show_end_date").setValue(this.data.show_end_date);
        this.timeForm.get("show_end_time").setValue(this.data.show_end_time);
        this.timeForm
          .get("show_start_time")
          .setValue(this.data.show_start_time);
        this.timeForm
          .get("rental_price_option")
          .setValue(this.data.rental_price_option);
        this.timeForm
          .get("show_checkout_address_field")
          .setValue(this.data.show_checkout_address_field);
        this.timeForm
          .get("show_checkout_additional_field")
          .setValue(this.data.show_checkout_additional_field);
        this.timeForm
          .get("show_checkout_availability_text")
          .setValue(this.data.show_checkout_availability_text);
        this.timeForm
          .get("fb_tracking_id")
          .setValue(this.data.fb_tracking_id);
        this.timeForm
          .get("rental_day")
          .setValue(this.data.rental_day);

        this.timeForm
          .get("checkout_online_capture")
          .setValue(this.data.checkout_online_capture);

        this.timeForm
          .get("online_order")
          .setValue(this.data.online_order);

        this.timeForm
          .get("quote_order")
          .setValue(this.data.quote_order);

        this.timeForm
          .get("show_related_product_as_addon")
          .setValue(this.data.show_related_product_as_addon);

        this.timeForm
          .get("customer_registration")
          .setValue(this.data.customer_registration);

        this.timeForm
          .get("customer_guest_checkout")
          .setValue(this.data.customer_guest_checkout);


        this.timeForm
          .get("enable_store")
          .setValue(this.data.enable_store);


        this.timeForm
          .get("maintenance")
          .setValue(this.data.maintenance);

        this.timeForm
          .get("breadcrumb")
          .setValue(this.data.breadcrumb);

        this.timeForm
          .get("show_product_id")
          .setValue(this.data.show_product_id);

        this.timeForm
          .get("client_online_public_access")
          .setValue(this.data.client_online_public_access);

        this.timeForm
          .get("date_format")
          .setValue(this.data.date_format);

        this.timeForm
          .get("pos_skip_fulfillment")
          .setValue(this.data.skip_fulfillment);

        this.timeForm
          .get("pos_skip_payment")
          .setValue(this.data.skip_payment);

        this.timeForm
          .get("checkout_single_page")
          .setValue(this.data.single_page);
        
        this.timeForm
          .get("checkout_sms")
          .setValue(this.data.sms);

      },
      err => console.error(err)
    );
  }

  ngOnDestroy() {
    this.sub.unsubscribe();
  }

  setTaxValue(res) {
    if (res.hasOwnProperty("tax")) {
      this.timeForm.get("buy_tax").setValue(res.tax.buy_tax);
      this.timeForm.get("rent_tax").setValue(res.tax.rent_tax);
      this.timeForm.get("delivery_tax").setValue(res.tax.delivery_tax);
      this.timeForm.get("tax_apply_on").setValue(res.tax.tax_apply_on);
      console.log(this.timeForm.get('tax_apply_on').value)
    }
  }

  setSignatureValue(res) {
    if (res.hasOwnProperty("signature")) {
      this.timeForm.get("admin_signature").setValue((res.signature.admin) || (res.signature.admin == 1) ? true : false);
      this.timeForm.get("pos_signature").setValue((res.signature.pos) || (res.signature.pos == 1) ? true : false);
      this.timeForm.get("online_signature").setValue((res.signature.online) || (res.signature.online == 1) ? true : false);
    }
  }


  save() {
    this.getTaxValue();
    this.loading = true;
    Object.assign(this.timeForm.value, this.sales_tax);
     console.log(this.timeForm.value);
    this.service
      .saveTimeZone(this.timeForm.value)
      .then(res => {
        this.loading = false;
        if (res.result) {
          this.alert.error(this.alertContainer, res.result.error, true, 3000);
        } else {
          this.getFormData();
          this.setContent();
          this.alert.success(
            this.alertContainer,
            "Settings Updated",
            true,
            3000
          );
        }
      })
      .catch(err => {
        this.loading = false;
        this.alert.error(
          this.alertContainer,
          "Something went wrong!!! Please try again.",
          true,
          3000
        );
      });
  }



  formatTag(text: String) {
    return text.replace(/(\-[a-z])/g, function ($1) { return $1.toUpperCase().replace('-', ''); });
  }

  setContent(){
    this.http.get('contents').toPromise()
        .then(res => {
            if (res.status === 'OK' && res.result.data.length > 0) {
                const content = {};
                const data = res.result.data.filter(f => {
                    return f['config'].status === 1;
                });

                for (const c of data) {
                    const tag = this.formatTag(c.config.tag);
                    content[tag] = isJson(c.contents) ? JSON.parse(c.contents) : c.contents;
                }
                localStorage.setItem('contents', JSON.stringify(content));
                return content;
            } else {
                return {};
            }
        }).catch(err => console.log(err));
  }

  getTaxValue() {
    this.sales_tax.buy_tax = this.timeForm.get("buy_tax").value;
    this.sales_tax.rent_tax = this.timeForm.get("rent_tax").value;
    this.sales_tax.delivery_tax = this.timeForm.get("delivery_tax").value;
  }
  seletTaxOption(value) {
    Object.assign(this.sales_tax, value);
    // console.log(this.sales_tax);
  }
  selectedCurrency(i) {
    this.service.getCurrencyDetails(i).subscribe(
      res => {
        this.currency_code = res.result.data.code;
        this.currency_symbol = res.result.data.symbol;
      },
      err => console.error(err)
    );
  }
}
