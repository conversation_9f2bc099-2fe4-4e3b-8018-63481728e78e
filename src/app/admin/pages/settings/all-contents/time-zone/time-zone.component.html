<div class="custom-alert" #hasCusAlert></div>
<form [formGroup]="timeForm" (ngSubmit)="save()">
    <div class="row">
        <div class="col-md-3">
            <h5>Select Time Zone</h5>
        </div>
        <div class="col-md-9">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label>Select Timezone</label>
                        <select class="form-control m-select" name="timezone" formControlName="timezone">
                            <option *ngFor="let z of zoneList" [value]="z.name">{{
                                z.label
                                }}</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <hr />
    <!-- <div class="row">
        <div class="col-md-3">
            <h5>Sales Tax</h5>
            <small>Manage how your store calculates and shows tax on your store.
            </small>
        </div>
        <div class="col-md-9">
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <div class="m-radio-inline">
                            <label>Select Sale Tax Options</label>&nbsp;&nbsp;
                            <label class="m-radio">
                                <input [checked]="timeForm.get('tax_apply_on').value == '3'" type="radio"
                                    formControlName="tax_apply_on" name="tax_apply_on" value="3" />
                                Rent Only <span></span>
                            </label>
                            <label class="m-radio">
                                <input [checked]="timeForm.get('tax_apply_on').value == '2'" type="radio"
                                    formControlName="tax_apply_on" name="tax_apply_on" value="2" />
                                Buy Only <span></span>
                            </label>
                            <label class="m-radio">
                                <input [checked]="timeForm.get('tax_apply_on').value == '1'" type="radio"
                                    formControlName="tax_apply_on" name="tax_apply_on" value="1" />
                                Both <span></span>
                            </label>
                            <label class="m-radio">
                                <input [checked]="timeForm.get('tax_apply_on').value == '0'" type="radio"
                                    formControlName="tax_apply_on" name="tax_apply_on" value="0" />
                                None <span></span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group m-form__group">
                        <label for="">Sales Tax for Buy Items</label>
                        <div class="input-group">
                            <input numberOnly formControlName="buy_tax" type="text" class="form-control m-input"
                                name="sales_tax" aria-describedby="basic-addon2" />
                            <div class="input-group-append">
                                <span class="input-group-text"><i class="fa fa-percent"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group m-form__group">
                        <label for="">Sales Tax for Rental Items</label>
                        <div class="input-group">
                            <input numberOnly formControlName="rent_tax" type="text" class="form-control m-input"
                                name="sales_tax_rent" aria-describedby="basic-addon2" />
                            <div class="input-group-append">
                                <span class="input-group-text"><i class="fa fa-percent"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group m-form__group">
                        <label for="">Sales Tax on Delivery charge</label>
                        <div class="input-group">
                            <input numberOnly type="text" formControlName="delivery_tax" class="form-control m-input"
                                name="sales_tax_delivery" aria-describedby="basic-addon2" />
                            <div class="input-group-append">
                                <span class="input-group-text" id="basic-addon2"><i class="fa fa-percent"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div> 
    <hr />
    -->
    <div class="row">
        <div class="col-md-3">
            <h5>Select Currency</h5>
        </div>
        <div class="col-md-9">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label>Select Currency</label>
                        <select class="form-control m-select" name="code" formControlName="code"
                            (change)="selectedCurrency($event.target.value)">
                            <option *ngFor="let z of currencyList" [value]="z.code">{{
                                z.label
                                }}</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3"></div>
        <div class="col-md-9">
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <div class="m-radio-inline">
                            <label class="m-radio">
                                <input type="radio" name="format" [checked]="format1" formControlName="format"
                                    [value]="[true, true]" />
                                {{ currency_symbol }} Amount {{ currency_code }} <span></span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <div class="m-radio-inline">
                            <label class="m-radio">
                                <input type="radio" name="format" [checked]="format2" formControlName="format"
                                    [value]="[false, true]" />
                                Amount {{ currency_code }} <span></span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <div class="m-radio-inline">
                            <label class="m-radio">
                                <input type="radio" name="format" [checked]="format3" formControlName="format"
                                    [value]="[true, false]" />
                                {{ currency_symbol }} Amount <span></span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <hr />

    <div class="row">
        <div class="col-md-3">
            <h5>Date format</h5>
        </div>
        <div class="col-md-9">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label>Pick your date format</label>
                        <select class="form-control m-select" name="date_format" formControlName="date_format">
                            <option *ngFor="let z of time_format_list" [value]="z.value">{{z.label}}</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <hr />

    <div class="row">
        <div class="col-md-3">
            <h5>Category Listing</h5>
        </div>
        <div class="col-md-9">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <div class="m-radio-inline">
                            <label>Hide parent categories when expanding children ? &nbsp;</label>
                            <label class="m-radio">
                                <input type="radio" name="show_category_parent" formControlName="show_category_parent"
                                    [value]="true" />
                                Yes <span></span>
                            </label>
                            <label class="m-radio">
                                <input type="radio" name="show_category_parent" formControlName="show_category_parent"
                                    [value]="false" />
                                No <span></span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <div class="m-radio-inline">
                            <label>Show category and filter sidebar ? &nbsp;</label>
                            <label class="m-radio">
                                <input type="radio" name="show_category_sidebar" formControlName="show_category_sidebar"
                                    [value]="true" />
                                Yes <span></span>
                            </label>
                            <label class="m-radio">
                                <input type="radio" name="show_category_sidebar" formControlName="show_category_sidebar"
                                    [value]="false" />
                                No <span></span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <hr />

    <div class="row">
        <div class="col-md-3">
            <h5>Page Title & Breadcrumb</h5>
        </div>
        <div class="col-md-9">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <div class="m-radio-inline">
                            <label>Show Page Title & Breadcrumb? &nbsp;</label>
                            <label class="m-radio">
                                <input type="radio" name="breadcrumb" formControlName="breadcrumb" [value]="true" />
                                Yes <span></span>
                            </label>
                            <label class="m-radio">
                                <input type="radio" name="breadcrumb" formControlName="breadcrumb" [value]="false" />
                                No <span></span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <hr />

    <div class="row">
        <div class="col-md-3">
            <h5>Pricing Options</h5>
        </div>
        <div class="col-md-9">
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <div class="m-radio-inline">
                            <label>What is a Rental Day ?</label>&nbsp;&nbsp;
                            <label class="m-radio">
                                <input type="radio" name="rental_day" formControlName="rental_day" value="calendar" />
                                Calendar Day <span></span>
                            </label>
                            <label class="m-radio">
                                <input type="radio" name="rental_day" formControlName="rental_day" value="24" />
                                24 hours <span></span>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="form-group">
                        <div class="m-radio-inline">
                            <label>Do you allow user to select start date?</label>&nbsp;&nbsp;
                            <label class="m-radio">
                                <input type="radio" name="show_start_date" formControlName="show_start_date"
                                    [value]="true" />
                                Yes <span></span>
                            </label>
                            <label class="m-radio">
                                <input type="radio" name="show_start_date" formControlName="show_start_date"
                                    [value]="false" />
                                No <span></span>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="form-group">
                        <div class="m-radio-inline">
                            <label>Do you allow user to select start time?</label>&nbsp;&nbsp;
                            <label class="m-radio">
                                <input type="radio" name="show_start_time" formControlName="show_start_time"
                                    [value]="true" />
                                Yes <span></span>
                            </label>
                            <label class="m-radio">
                                <input type="radio" name="show_start_time" formControlName="show_start_time"
                                    [value]="false" />
                                No <span></span>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="form-group">
                        <div class="m-radio-inline">
                            <label>Do you allow user to select end date?</label>&nbsp;&nbsp;
                            <label class="m-radio">
                                <input type="radio" name="show_end_date" formControlName="show_end_date"
                                    [value]="true" />
                                Yes <span></span>
                            </label>
                            <label class="m-radio">
                                <input type="radio" name="show_end_date" formControlName="show_end_date"
                                    [value]="false" />
                                No <span></span>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="form-group">
                        <div class="m-radio-inline">
                            <label>Do you allow user to select end time?</label>&nbsp;&nbsp;
                            <label class="m-radio">
                                <input type="radio" name="show_end_time" formControlName="show_end_time"
                                    [value]="true" />
                                Yes <span></span>
                            </label>
                            <label class="m-radio">
                                <input type="radio" name="show_end_time" formControlName="show_end_time"
                                    [value]="false" />
                                No <span></span>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="form-group">
                        <div class="m-radio-inline">
                            <label>Do you want to show rental pricing options?</label>&nbsp;&nbsp;
                            <label class="m-radio">
                                <input type="radio" name="rental_price_option" formControlName="rental_price_option"
                                    [value]="true" />
                                Yes <span></span>
                            </label>
                            <label class="m-radio">
                                <input type="radio" name="rental_price_option" formControlName="rental_price_option"
                                    [value]="false" />
                                No <span></span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <hr />
    <div class="row mt-5">
        <div class="col-md-3">
            <h5>Checkout Options</h5>
        </div>
        <div class="col-md-9">
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <div class="m-radio-inline">
                            <label>Collect contact address ?</label>&nbsp;&nbsp;
                            <label class="m-radio">
                                <input type="radio" name="show_checkout_address_field"
                                    formControlName="show_checkout_address_field" [value]="true" />
                                Yes <span></span>
                            </label>
                            <label class="m-radio">
                                <input type="radio" name="show_checkout_address_field"
                                    formControlName="show_checkout_address_field" [value]="false" />
                                No <span></span>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="form-group">
                        <div class="m-radio-inline">
                            <label>Do you want to show additional fields in checkout ?</label>&nbsp;&nbsp;
                            <label class="m-radio">
                                <input type="radio" name="show_checkout_additional_field"
                                    formControlName="show_checkout_additional_field" [value]="true" />
                                Yes <span></span>
                            </label>
                            <label class="m-radio">
                                <input type="radio" name="show_checkout_additional_field"
                                    formControlName="show_checkout_additional_field" [value]="false" />
                                No <span></span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="col-md-12">
                    <div class="form-group">
                        <div class="m-radio-inline">
                            <label>Do you want to show availability text on product details
                                ?</label>&nbsp;&nbsp;
                            <label class="m-radio">
                                <input type="radio" name="show_checkout_availability_text"
                                    formControlName="show_checkout_availability_text" [value]="true" />
                                Yes <span></span>
                            </label>
                            <label class="m-radio">
                                <input type="radio" name="show_checkout_availability_text"
                                    formControlName="show_checkout_availability_text" [value]="false" />
                                No <span></span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="col-md-12">
                    <div class="form-group">
                        <div class="m-radio-inline">
                            <label>Capture customer signature</label>&nbsp;&nbsp;
                            <div class="m-checkbox-inline">
                                <label class="m-checkbox">
                                    <input type="checkbox" name="admin_signature" formControlName="admin_signature" />
                                    Admin store <span>&nbsp;&nbsp;</span>
                                </label>
                                <label class="m-checkbox">
                                    <input type="checkbox" name="pos_signature" formControlName="pos_signature" />
                                    POS <span>&nbsp;&nbsp;</span>
                                </label>
                                <label class="m-checkbox">
                                    <input type="checkbox" name="online_signature" formControlName="online_signature" />
                                    Online store <span></span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-12">
                    <div class="form-group">
                        <div class="m-radio-inline">
                            <label>When will funds be captured</label>
                            &nbsp;&nbsp;
                            <label class="m-radio">
                                <input type="radio" name="checkout_online_capture"
                                    formControlName="checkout_online_capture" [value]="true" />
                                Immediately<span></span>
                            </label>
                            <label class="m-radio">
                                <input type="radio" name="checkout_online_capture"
                                    formControlName="checkout_online_capture" [value]="false" />
                                Upon return<span></span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="col-md-12">
                    <div class="form-group">
                        <div class="m-radio-inline">
                            <label>Enable Online Ordering</label>
                            &nbsp;&nbsp;
                            <label class="m-radio">
                                <input type="radio" name="online_order" formControlName="online_order" [value]="true" />
                                Yes<span></span>
                            </label>
                            <label class="m-radio">
                                <input type="radio" name="online_order" formControlName="online_order"
                                    [value]="false" />
                                No<span></span>
                            </label>
                        </div>
                    </div>
                </div>


                <div class="col-md-12">
                    <div class="form-group">
                        <div class="m-checkbox-inline">
                            <label class="m-checkbox">
                                <input type="checkbox" name="show_related_product_as_addon"
                                    formControlName="show_related_product_as_addon" />
                                Show related products as optional add-ons in cart view <span>&nbsp;&nbsp;</span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="col-md-12">
                    <div class="form-group">
                        <div class="m-radio-inline">
                            <label>Allow Quote checkout</label>
                            &nbsp;&nbsp;
                            <label class="m-radio">
                                <input type="radio" name="quote_order" formControlName="quote_order" [value]="true" />
                                Yes<span></span>
                            </label>
                            <label class="m-radio">
                                <input type="radio" name="quote_order" formControlName="quote_order"
                                    [value]="false" />
                                No<span></span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="col-md-12">
                    <div class="form-group">
                        <div class="m-radio-inline">
                            <label>Send SMS texts to customers (USA only)</label>
                            &nbsp;&nbsp;
                            <label class="m-radio">
                                <input type="radio" name="checkout_sms" formControlName="checkout_sms" [value]="true" />
                                Yes<span></span>
                            </label>
                            <label class="m-radio">
                                <input type="radio" name="checkout_sms" formControlName="checkout_sms"
                                    [value]="false" />
                                No<span></span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="col-md-12">
                    <div class="form-group">
                        <div class="m-radio-inline">
                            <label>Show product id in products details page</label>
                            &nbsp;&nbsp;
                            <label class="m-radio">
                                <input type="radio" name="show_product_id" formControlName="show_product_id" [value]="true" />
                                Yes<span></span>
                            </label>
                            <label class="m-radio">
                                <input type="radio" name="show_product_id" formControlName="show_product_id"
                                    [value]="false" />
                                No<span></span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="col-md-12">
                    <div class="form-group">
                        <div class="m-radio-inline">
                            <label>Skip fulfillment in POS</label>
                            &nbsp;&nbsp;
                            <label class="m-radio">
                                <input type="radio" name="pos_skip_fulfillment" formControlName="pos_skip_fulfillment" [value]="true" />
                                Yes<span></span>
                            </label>
                            <label class="m-radio">
                                <input type="radio" name="pos_skip_fulfillment" formControlName="pos_skip_fulfillment"
                                    [value]="false" />
                                No<span></span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="col-md-12">
                    <div class="form-group">
                        <div class="m-radio-inline">
                            <label>Skip payment in POS</label>
                            &nbsp;&nbsp;
                            <label class="m-radio">
                                <input type="radio" name="pos_skip_payment" formControlName="pos_skip_payment" [value]="true" />
                                Yes<span></span>
                            </label>
                            <label class="m-radio">
                                <input type="radio" name="pos_skip_payment" formControlName="pos_skip_payment"
                                    [value]="false" />
                                No<span></span>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="form-group">
                        <div class="m-radio-inline">
                            <label>Single Page Checkout</label>
                            &nbsp;&nbsp;
                            <label class="m-radio">
                                <input type="radio" name="checkout_single_page" formControlName="checkout_single_page" [value]="true" />
                                Yes<span></span>
                            </label>
                            <label class="m-radio">
                                <input type="radio" name="checkout_single_page" formControlName="checkout_single_page"
                                    [value]="false" />
                                No<span></span>
                            </label>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <hr />

    <div *ngIf="is_show_customer_integration" class="row mt-5">
        <div class="col-md-3">
            <h5>Customer Integration</h5>
        </div>
        <div class="col-md-9">
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <div class="m-radio-inline">
                            <label>Allow customer registration</label>&nbsp;&nbsp;
                            <label class="m-radio">
                                <input type="radio" name="customer_registration" formControlName="customer_registration"
                                    [value]="true" />
                                Yes <span></span>
                            </label>
                            <label class="m-radio">
                                <input type="radio" name="customer_registration" formControlName="customer_registration"
                                    [value]="false" />
                                No <span></span>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="form-group">
                        <div class="m-radio-inline">
                            <label>Allow guest checkout</label>&nbsp;&nbsp;
                            <label class="m-radio">
                                <input type="radio" name="customer_guest_checkout"
                                    formControlName="customer_guest_checkout" [value]="true" />
                                Yes <span></span>
                            </label>
                            <label class="m-radio">
                                <input type="radio" name="customer_guest_checkout"
                                    formControlName="customer_guest_checkout" [value]="false" />
                                No <span></span>
                            </label>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <hr *ngIf="is_show_customer_integration" />

    <div class="row mt-5">
        <div class="col-md-3">
            <h5>Store Config</h5>
        </div>
        <div class="col-md-9">
            <div class="row">

                <div class="col-md-12">
                    <div class="form-group">
                        <div class="m-radio-inline">
                            <label>Enable store</label>&nbsp;&nbsp;
                            <label class="m-radio">
                                <input type="radio" name="enable_store" formControlName="enable_store" [value]="true" />
                                Yes <span></span>
                            </label>
                            <label class="m-radio">
                                <input type="radio" name="enable_store" formControlName="enable_store"
                                    [value]="false" />
                                No <span></span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="col-md-12">
                    <div class="form-group">
                        <div class="m-radio-inline">
                            <label>Coming Soon Page</label>&nbsp;&nbsp;
                            <label class="m-radio">
                                <input type="radio" name="maintenance" formControlName="maintenance" [value]="true" />
                                Yes <span></span>
                            </label>
                            <label class="m-radio">
                                <input type="radio" name="maintenance" formControlName="maintenance" [value]="false" />
                                No <span></span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="col-md-12" *ngIf="timeForm.get('maintenance').value === true">
                    <div class="form-group">
                        <div class="m-radio-inline">
                            <label>Enable Home page and Contact page while your coming soon page is
                                active</label>&nbsp;&nbsp;
                            <label class="m-radio">
                                <input type="radio" name="maintenance_content" formControlName="maintenance_content"
                                    [value]="true" />
                                Yes <span></span>
                            </label>
                            <label class="m-radio">
                                <input type="radio" name="maintenance_content" formControlName="maintenance_content"
                                    [value]="false" />
                                No <span></span>
                            </label>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <hr *ngIf="is_client_active"/>

    <div class="row mt-5" *ngIf="is_client_active">
        <div class="col-md-3">
            <h5>Subrent Portal</h5>
        </div>
        <div class="col-md-9">
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <div class="m-radio-inline">
                            <label>Allow public access to online store?</label>&nbsp;&nbsp;
                            <label class="m-radio">
                                <input type="radio" name="client_online_public_access" formControlName="client_online_public_access" [value]="true" />
                                Yes <span></span>
                            </label>
                            <label class="m-radio">
                                <input type="radio" name="client_online_public_access" formControlName="client_online_public_access" [value]="false" />
                                No <span></span>
                            </label>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <!-- <div class="row mt-5">
        <div class="col-md-3">
            <h5>
                Facebook Pixels <br />
                <small style="font-size:12px;">Facebook Pixel helps you create ad campaigns to find new customers
                    that look most like your buyers.
                </small>
            </h5>
        </div>
        <div class="col-md-9">
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <div class="m-radio-inline">
                            <label>Facebook Pixel ID </label>&nbsp;&nbsp;
                            <input class="form-control" class="form-control col-md-6" formControlName="fb_tracking_id"
                                type="text" placeholder="Paste your Facebook Pixel ID here" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row mt-5">
        <div class="col-md-3">
            <h5>
                Facebook Messenger <br />
                <small style="font-size:12px;">Facebook Messenger chat helps you coomunicate with your customers through
                    your store.
                </small>
            </h5>
        </div>
        <div class="col-md-9">
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <div class="m-radio-inline">
                            <label>Facebook Page ID </label>&nbsp;&nbsp;
                            <input class="form-control" class="form-control col-md-9" formControlName="fb_page_id"
                                type="text"
                                placeholder="Paste your 15 digit Facebook page ID here, eg: 123456789012345" />
                            <small>Note: To configure your Facebook page for chatting, go to Facebook page settings >
                                Messaging and then
                                "Add Messenger to your website" and add this domain "{{url}}" to "ADD WEBSITE DOMAIN
                                NAME" and click Finish button.
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div> -->
    <div class="m-portlet__foot m-portlet__foot--fit">
        <div class="m-form__actions m--margin-top-30">
            <button type="submit" [disabled]="loading" class="btn btn-dark m-btn--icon"
                [ngClass]="{ 'm-loader m-loader--light m-loader--right': loading }">
                Submit
            </button>
        </div>
    </div>
</form>