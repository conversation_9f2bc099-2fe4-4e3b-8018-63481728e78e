import { AlertService } from './../../../../../../modules/alert/alert.service';
import { Component, OnInit, ComponentRef, ViewContainerRef, ViewChild, Input, EventEmitter, Output, ComponentFactoryResolver, AfterViewInit, ElementRef } from '@angular/core';
import { PageContent } from '../../../models/contents.model';
import { FormBuilder, Validators, FormGroup} from '@angular/forms';
import { ContentService } from '../../../setting-service/contents.service';
import { isJson } from './../../../../../../globals/_classes/functions';

declare let $: any;

@Component({
  selector: 'app-add-form',
  templateUrl: './add-form.component.html',
  styleUrls: ['./add-form.component.css']
})
export class AddFormComponent implements OnInit, AfterViewInit {

  componentRef: ComponentRef<any>;
  contentId: number;
  content: string;
  slug: string;
  addContentForm: FormGroup;
  availablePages = [
    { name: 'Home', value: 1, slug: '/home' }
  ];

  @ViewChild('pageComponent', { read: ViewContainerRef }) pageComponent: ViewContainerRef;
  @ViewChild('hasCusAlert') alertContainer: ElementRef;
  @Input('config') config: PageContent;
  @Input('pages') pages: any[];
  @Input('edit') edit: boolean;
  @Input('addNew') addNew: boolean;
  @Output('submit') submit: EventEmitter<any> = new EventEmitter();
  contents: any;


  constructor(
    private formBuilder: FormBuilder,
    private service: ContentService,
    private alertService: AlertService
  ) {
    this.initForm();
  }

  ngOnInit() {
    if (this.edit) {
      this.addContentForm.patchValue(this.config);
    }
    console.log(this.config)
    if (this.edit && this.config.content) {
      const data = isJson(this.config.content) ? JSON.parse(this.config.content) : '';
      console.log(data)
      if (data) {
        this.contents = data;
        console.log(this.contents);
      }
    }
  }

  initForm() {
    this.addContentForm = this.formBuilder.group({
      name : ['', Validators.required],
      page_id : [this.availablePages[0].value, Validators.required],
      page_slug : [this.availablePages[0].slug],
      content: ['']
    });
  }

  onSubmit() {
    console.log(this.addContentForm.getRawValue());
    // return;
    if (this.addContentForm.invalid) {
      return;
    }
    if (this.edit) {
      this.service.updateContentsForLayout(this.addContentForm.getRawValue(), this.config.id).then(
        res => {
          if (res.status === 'OK') {
            this.submit.emit(res);
          } else {
            this.alertService.error(this.alertContainer, "Something went wrong!!!", true, 5000);
          }
        }
      ).catch(
        error => {
          this.alertService.error(this.alertContainer, "Server error!!! Try again later", true, 5000);
        }
      );
    } else {
      this.service.addContentsForLayout(this.addContentForm.getRawValue()).then(
        res => {
          if (res.status === 'OK') {
            this.submit.emit(res);
            // this.alertService.success(this.alertContainer, res.result.message, true, 5000);
          } else {
            this.alertService.error(this.alertContainer, "Something went wrong!!!", true, 5000);
          }
        }
      ).catch(
        error => {
          this.alertService.error(this.alertContainer, "Server error!!! Try again later", true, 5000);
        }
      );
    }
  }

  ngAfterViewInit() {
    this._description();
  }

  private _description = () => {
    $('.summernote-content').summernote(this.service.summarNote());

      $('.summernote-content').on('summernote.blur', () => {
        this.addContentForm.get('content').setValue($('.summernote-content').summernote('code'))
      });
      if (this.edit) {
        console.log(this.config.contents);
        console.log(this.contents)
        $('.summernote-content').summernote('code', this.config.content);
      }
  }

}
