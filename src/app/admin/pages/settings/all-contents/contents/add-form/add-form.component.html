<div class="custom-alert" #hasCusAlert></div>
<section class="animated" style="padding-top: 20px;">
  <h4 *ngIf="addNew">Create Page</h4>
  <h4 *ngIf="!addNew">Pages Contents</h4>
  <form [formGroup]="addContentForm" (ngSubmit)="onSubmit()">
    <div class="row">
      <div class="col-md-6">
        <div class="form-group m-form__group">
            <label for="">Name</label>
            <div class="input-group">
                <input
                  formControlName="name"
                  type="text"
                  class="form-control m-input"
                  name="name"
                  aria-describedby="basic-addon2"
                />
            </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-group m-form__group">
            <label for="">Select your page (The page where the content will be placed)</label>
            <select name="page_id" formControlName="page_id" class="form-control m-input">
              <option [value]="page.value" *ngFor="let page of availablePages">{{page.name}}</option>
            </select>
        </div>
      </div>
      <div class="col-md-12">
        <label>Contents</label>
        <div class="summernote-content"></div>
      </div>

      <div class="col-md-12">
          <button type="submit" class="btn btn-brand" style="margin-top: 20px;">
            <i class="fa fa-save"></i>
            <span *ngIf="edit" style="padding-left:10px;">Update</span>
            <span *ngIf="!edit" style="padding-left:10px;">Submit</span>
          </button>
      </div>
    </div>
  </form>
  <div>
    <ng-template #pageComponent></ng-template>
  </div>
</section>
