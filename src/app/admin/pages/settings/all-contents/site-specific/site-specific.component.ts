import { Component, OnInit, <PERSON>Child, ElementRef } from "@angular/core";
import { Content } from "../../models/contents.model";
import { FormBuilder, FormGroup, FormArray } from "@angular/forms";
import { ContentService } from "../../setting-service/contents.service";
import { AlertService } from "../../../../../modules/alert/alert.service";
import { isJson } from "../../../../../globals/_classes/functions";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { UploadSiteImgComponent } from "./upload-site-img/upload-site-img.component";
import { AdminUtilityService } from "../../../../admin-utility.service";
import { UploadSiteFavComponent } from "./upload-site-favicon/upload-site-fav.component";

@Component({
  selector: "app-site-specific",
  templateUrl: "./site-specific.component.html",
  styleUrls: ["./site-specific.component.css"]
})
export class SiteSpecificComponent implements OnInit {
  config: Content;
  textForm: FormGroup;
  loader: boolean;
  edit: boolean;
  contentId: number;
  links = [];
  formArray = [];
  home_promotion_img = "";
  general_favicon = "";
  @ViewChild("hasCusAlert") alertContainer: ElementRef;

  constructor(
    private modalService: NgbModal,
    private service: ContentService,
    private alert: AlertService,
    private fb: FormBuilder,
    private utilService: AdminUtilityService
  ) {
    this.getContentList();
    this.config = new Content();
    this.utilService.siteSpecificSettings.subscribe(res => {
      if (res.reload) {
        this.setHomePromoImg(res.image_url);
      }
    });

    this.utilService.favIconSetting.subscribe(res => {
      if (res.reload) {
        this.setFavIcon(res.image_url);
      }
    });
  }

  setHomePromoImg(url) {
    this.home_promotion_img = url;
    this.textForm
      .get("home_page_promotion")
      .get("image")
      .setValue(url);
   // console.log(this.textForm.get("home_page_promotion").value);
  }

  setFavIcon(url) {
   
    this.general_favicon = url;
    this.textForm
      .get("general")
      .get("favicon")
      .setValue(url);
   // console.log(this.textForm.get("home_page_promotion").value);
  }


  ngOnInit() {
    this.config.tag = "site_specific";
    this.config.type = "site-links";
    this.config.status = 1;
  }

  private getContentList() {
    this.service.getContents("site-links").subscribe(res => {
      this.links = res;
      this.findLinks();
    });
  }

  private findLinks() {
    if (this.links.length > 0) {
      this.contentId = this.links[0].id;
      const data = this.links[0].contents;
      this.formArray = this.service.fornmatSiteSecific(data);
      this.textForm = this.fb.group(this.createForm(this.formArray));
      this.edit = true;
    } else {
      this.edit = false;
    }
  }

  private createForm(data) {
    const obj = {};
    for (const f of data) {
      const iner = {};
      for (const i of f.contorls) {
        iner[i.name] = i.value;
      }
      obj[f.group] = this.fb.group(iner);
    }
    return obj;
  }

  save() {
    this.loader = true;
    const data = {
      config: this.config,
      contents: JSON.stringify(this.textForm.value)
    };
    this.service.addUpdate(data, this.contentId, this.edit).then(res => {
      this.loader = res.loader;
      this.allertShow(res.alert);
    });
  }

  allertShow(e) {
    this.getContentList();
    if (e.message) {
      if (e.status) {
        this.alert.success(this.alertContainer, e.message, true, 5000);
      } else {
        this.alert.error(this.alertContainer, e.message, true, 5000);
      }
    }
  }

  uploadImg() {
    const modalRef = this.modalService.open(UploadSiteImgComponent, {
      size: "lg"
    });

    modalRef.componentInstance.name = "World";
  }

  uploadFavIcon(){
    const modalRef = this.modalService.open(UploadSiteFavComponent, {
      size: "lg"
    });

    modalRef.componentInstance.name = "World";
  }
}
