<div class="custom-alert" #hasCusAlert></div>

<form
  [formGroup]="textForm"
  (ngSubmit)="save()"
  *ngIf="formArray.length > 0"
  class=""
>
  <ng-container *ngFor="let f of formArray">
    <div class="row" *ngIf="f.label !== 'CONFG'">
      <div class="col-md-3">
        <h5>{{ f.label }}</h5>
      </div>
      <div class="col-md-9">
        <div class="row" [formArrayName]="f.group">
          <div class="col-md-6" *ngFor="let c of f.contorls">
            <div
              class="form-group"
              *ngIf="c.name == 'image' || c.name == 'favicon'; else data"
            >
              <input
                type="hidden"
                class="form-control m-input"
                [attr.name]="c.name"
                [formControlName]="c.name"
                autocomplete="off"
              />
              <div *ngIf="c.name == 'image'" class="form-group mt-2">
                <!-- {{c | json}} -->

                <button
                  type="button"
                  (click)="uploadImg()"
                  class="btn mt-4  btn-success"
                >
                  {{ "Upload Image " }}
                </button>
                <img
                  style="width: 75px;
                margin-left: 15px;
                height: 41px;
                position: absolute;
                top: 27px;"
                  *ngIf="c.value || home_promotion_img"
                  [src]="home_promotion_img ? home_promotion_img : c.value"
                  alt=""
                />
              </div>
              <div *ngIf="c.name == 'favicon'" class="form-group mt-2">
                <!-- {{c | json}} -->

                <button
                  type="button"
                  (click)="uploadFavIcon()"
                  class="btn mt-4  btn-success"
                >
                  {{ "Upload Favicon " }}
                </button>
                <img
                  style="width: 75px;
                  margin-left: 15px;
                  height: 41px;
                  position: absolute;
                  top: 27px;"
                  *ngIf="c.value || general_favicon"
                  [src]="general_favicon ? general_favicon : c.value"
                  alt=""
                />
              </div>
            </div>
            <ng-template #data>
              <div *ngIf="c.type === 'text'; else checkbox">
                <label>{{ c.label }}</label>
                <input
                  class="form-control m-input"
                  [attr.name]="c.name"
                  [formControlName]="c.name"
                  autocomplete="off"
                />
              </div>
              <ng-template #checkbox>
                <label class="m-checkbox mt-3">
                  <input
                    [checked]="c.value"
                    type="checkbox"
                    [attr.name]="c.name"
                    [formControlName]="c.name"
                  />
                  {{  c.label.includes('Gdpr') ? (c.label | uppercase) : (c.label) }}
                  <span></span>
                </label>
              </ng-template>
            </ng-template>
          </div>
        </div>
      </div>
    </div>
    <div>&nbsp;</div>
    <hr />
  </ng-container>
  <div class="m-portlet__foot m-portlet__foot--fit">
    <div class="m-form__actions m-form__actions" style="padding: 30px 0px;">
      <div
        class="m-loader m-loader--brand"
        *ngIf="loader; else btnSubmit"
        style="width: 30px; padding-left: 30px; display: inline-block;"
      ></div>
      <ng-template #btnSubmit>
        <button type="submit" class="btn btn-brand">
          <i class="fa fa-save"></i>
          <span style="padding-left:10px;">Submit</span>
        </button>
      </ng-template>
    </div>
  </div>
</form>
