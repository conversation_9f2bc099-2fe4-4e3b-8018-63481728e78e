import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { SiteSpecificComponent } from "./site-specific.component";
import { Routes, RouterModule } from "@angular/router";
import { ReactiveFormsModule } from "@angular/forms";
import { DragDropModule } from "../../../../../modules/drag-drop/drag-drop.module";
import { UploadSiteImgComponent } from "./upload-site-img/upload-site-img.component";
import { NgbModalModule } from "@ng-bootstrap/ng-bootstrap";
import { UploadSiteFavComponent } from "./upload-site-favicon/upload-site-fav.component";

const route: Routes = [
  {
    path: "",
    component: SiteSpecificComponent
  }
];

@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    DragDropModule,
    RouterModule.forChild(route),
    NgbModalModule
  ],
  entryComponents: [UploadSiteImgComponent,UploadSiteFavComponent],
  declarations: [SiteSpecificComponent, UploadSiteImgComponent,UploadSiteFavComponent]
})
export class SiteSpecificModule {}
