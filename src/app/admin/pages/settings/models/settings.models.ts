import { GET_STORE_ID } from "../../../../globals/_classes/functions";

export class SettingsServiceConfig {
  settings: any;
}

export class VariantSet {
  id?: number;
  store_id: number = GET_STORE_ID();
  name: string;
  variants: Variant[] = [];
  slug: string;
}

export class Variant {
  id?: number;
  variant_set_id?: number;
  name: string;
}

export class Tag {
  id: number;
  name: string;
  is_shown_in_nav: boolean;
  status?: number;
}

export class CustomCheckout {
  id: number;
  field_name: string;
  field_label: string;
  field_type: string;
  field_is_required: boolean;
  field_values?: string;
  error? : string;
}

export class Coupon {
  id: number;
  type:number = 1;
  code: string;
  start_time: string;
  end_time: string;
  unit_type: number = 1;
  amount: number;
  min_quantity:number;
  max_quantity:number;
  product_id:string;
  product_name:string;
  status: boolean=true;
}


export class Addons {
  id: number;
  uid: number;
  type:number = 1;
  min_quantity:number;
  is_required:boolean=true;
  item_id=[];
  product_id=[];
  label:string;
  status:boolean=true;
}


export class CustomFields {
  id: number;
  name:string;
  label:string;
  type:string = 'textBox';
  products=[];
  status:boolean=true;
  is_private:boolean=false;
}

export class Stores {
  id?: number;
  name: string;
  status: string = null;
  stores_terminals: Terminal[];
  is_online: boolean = false;
  is_virtual?: boolean = false;
}

export class Terminal {
  id?: number;
  name: string;
  hsn: string;
  status: string = null;
  location_id: number;
}

export class Supplier {
  id?: number;
  name: string;
  email: string;
  phone: string;
}


export class VideoWall {
  id?: number;
  name: string;
  display_type: number;
  show_price: string;
  show_availability: string;
  interval: number;
}

export class Gateway {
  id?: number;
  name: string;
  config: Object = {};
  status: number;
  is_online: boolean;
  is_admin: boolean;
  type?: string;
}

export class Shipping {
  id?: number;
  name: string;
  config: any;
  status: number = 1;
}
