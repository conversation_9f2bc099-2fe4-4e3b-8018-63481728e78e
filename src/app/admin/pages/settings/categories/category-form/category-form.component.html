<form class="" #form="ngForm">
  <div class="m-portlet__body" style="padding: 20px 0px 0px 0px;">
    <div class="form-group m-form__group" *ngIf="hiddenCate">
      <label for="parent">
        Parent Category
      </label>
      <input type="text" class="form-control m-input" id="parent" placeholder="Enter Parent Category Name" name="parent"
        [(ngModel)]="category.parent_name" disabled autocomplete="off">
    </div>
    <div class="form-group m-form__group">
      <label for="cateName">
        Category Name 
        <span *ngIf="category.id">(Category ID : {{ category.id }}) </span>
      </label>
      <input type="text" class="form-control m-input" id="cateName" placeholder="Enter Category Name" name="child"
        [(ngModel)]="category.name" (ngModelChange)="makeCategoryURL()" required autocomplete="off">
    </div>
    <div class="form-group m-form__group" style="display:none">
      <label for="url">
        URL
      </label>
      <input type="text" class="form-control m-input" id="url" name="child-url" [(ngModel)]="category.url" placeholder="Enter URL"
        readonly autocomplete="off">
    </div>
    <div class="form-group m-form__group" *ngIf="category.id">
      <div class="input-group m-input-group">
        <input type="text" class="form-control m-input" id="gid" name="id" [value]="formatedUrl" readonly #copyText>
        <div class="input-group-append cursor-pointer" (click)="copyUrl(copyText)">
          <span class="input-group-text" id="table-cost-addon">
            Copy
          </span>
        </div>
       
      </div>
      <span class="m-form__help">You can copy this url and set it on banners & other places </span>
    </div>

    <div class="form-group m-form__group">
        <label for="status">Select Status</label>
        <select id="status" class="form-control m-select" name="status" [(ngModel)]="category.status">
          <option value="1">Active</option>
          <option value="0">Inactive</option>
        </select>
      </div>

    <div *ngIf="loader; else button" class="m-loader m-loader--brand" style="width: 30px; padding-left: 30px; display: inline-block;"></div>

    <ng-template #button>
      <div *ngIf="!edit; else updateDelete" class="action-btn">
        <button type="button" class="btn btn-brand" [disabled]="!form.form.valid" (click)="addCategory(form)" style="margin-right:10px">
          Save
        </button>
        <button type="reset" class="btn btn-danger" (click)="resetCategory()">
          Cancel
        </button>
      </div>
      <ng-template #updateDelete>
        <div class="action-btn">
          <button type="button" class="btn btn-brand" (click)="updateCategory()" style="margin-right:10px">
            Update
          </button>
          <button type="button" class="btn btn-danger" (click)="deleteCategory()">
            Delete
          </button>
        </div>
      </ng-template>
    </ng-template>

  </div>

</form>