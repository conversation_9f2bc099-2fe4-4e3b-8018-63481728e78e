<div class="custom-alert" #hasCusAlert xmlns="http://www.w3.org/1999/html"></div>

<div class="m-subheader">
    <div class="d-flex align-items-center">
        <div class="mr-auto">
            <h3 class="m-subheader__title m-subheader__title--separator">
                Settings
            </h3>
            <ul class="m-subheader__breadcrumbs m-nav m-nav--inline">
                <li class="m-nav__item m-nav__item--home">
                    <a routerLink="/admin" class="m-nav__link m-nav__link--icon">
                        <i class="m-nav__link-icon la la-home"></i>
                    </a>
                </li>
                <li class="m-nav__separator">
                    <i class="fa fa-angle-right"></i>
                </li>
                <li class="m-nav__item">
                    <a class="m-nav__link">
                        <span class="m-nav__link-text">
                  Business Settings
                </span>
                    </a>
                </li>
                <li class="m-nav__separator" style="padding-left: 10px">
                    <i class="fa fa-angle-right"></i>
                </li>
                <li class="m-nav__item">
                    <a class="m-nav__link">
                        <span class="m-nav__link-text">
                    Settings
                </span>
                    </a>
                </li>
            </ul>
        </div>
    </div>
</div>
<!-- END: Subheader -->

<div class="m-content animated fadeIn">
    <div class="m-portlet">
        <div class="m-portlet__head title-head">
            <div class="m-portlet__head-caption">
                <div class="m-portlet__head-title">
                    <h3 class="m-portlet__head-text colorPurpel">
                         Settings
                    </h3>
                </div>
            </div>
        </div>
        <div class="m-portlet__body">
            <form [formGroup]="leadLagForm" (ngSubmit)="onSubmit()">
                <div class="row">
                    <div class="col-md-3">
                        <h5>Lead/Lag Time</h5>
                    </div>
                    <div class="col-md-9">
                        <div class="row">
                            <div class="col-md-12">
                                <div>
                                    <span style="margin-right: 20px;">Add lead time?</span>
                                    <label class="m-radio" style="margin-right: 10px;">
                                        <input type="radio" value="true" name="lead" (change)="changeLead($event)"
                                               formControlName="lead">
                                               &nbsp;Yes
                                        <span></span>
                                    </label>
                                    <label class="m-radio">
                                        <input type="radio" value="false" name="lead" (change)="changeLead($event)"
                                               formControlName="lead">
                                               &nbsp;No
                                        <span></span>
                                    </label>
                                </div>
                                <div *ngIf="leadTime" class="row">
                                    <div class="col-md-4">
                                        <label for="">Lead time: </label>
                                        <input type="text" formControlName="lead_time_value" class="form-control"></div>
                                    <div class="col-md-4">
                                        <label for="">&nbsp; </label>
                                        <select formControlName="lead_time_type" class="form-control">
                                            <option [value]="item.value"
                                                    *ngFor="let item of time; let i = index">{{item.name}}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12 mt-1">
                                <div>
                                    <span style="margin-right: 20px;">Add lag time?</span>
                                    <label class="m-radio" style="margin-right: 10px;">
                                        <input type="radio" value="true" name="lag" (change)="changeLag($event)"
                                               formControlName="lag">
                                               &nbsp;Yes
                                        <span></span>
                                    </label>
                                    <label class="m-radio">
                                        <input type="radio" value="false" name="lag" (change)="changeLag($event)"
                                               formControlName="lag">
                                               &nbsp;No
                                        <span></span>
                                    </label>
                                </div>
                                <div *ngIf="lagTime" class="row">
                                    <div class="col-md-4">
                                        <label for="">Lag time: </label>
                                        <input type="text" formControlName="lag_time_value" class="form-control">
                                    </div>
                                    <div class="col-md-4">
                                        <label for="">&nbsp; </label>
                                        <select formControlName="lag_time_type" class="form-control">
                                            <option [value]="item.value"
                                                    *ngFor="let item of time; let i = index">{{item.name}}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button [disabled]="leadLagForm.invalid || inValid" type="submit" class="btn btn-sm btn-dark">Submit
                        </button>
                    </div>
                </div>
            </form>
            <hr/>
            <form [formGroup]="posPaymentForm" (ngSubmit)="onSubmitPosPayment()">
                <div class="row mt-5">
                    <div class="col-md-3">
                        <h5>Required Payments<br/>
                            <small style="font-size: 12px;">Set the requirement payment amount at order booking, pick-up, and return.</small>
                        </h5>
                    </div>
                    <div class="col-md-9">

                        <div class="row">
                            <div class="col-md-12">
                                <div>
                                    <label class="m-radio" style="margin-right: 10px;">
                                        <input type="radio" value="percent" name="type" (change)="changeUnit($event)"
                                               formControlName="type">
                                               &nbsp;Percentage
                                        <span></span>
                                    </label>
                                    <label class="m-radio">
                                        <input type="radio" value="fixed" name="type" (change)="changeUnit($event)"
                                               formControlName="type">
                                               &nbsp;Fixed amount
                                        <span></span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group m-form__group">
                                    <label for="">Booking</label>
                                    <div class="input-group">
                                        <input numberOnly formControlName="booking" type="text" class="form-control m-input" name="booking" aria-describedby="basic-addon2" />
                                        <div class="input-group-append">
                                            <span class="input-group-text">
                                                <i 
                                                    [ngClass]="unit == 'percent' ? 'fa-percent': 'fa-dollar'" 
                                                    class="fa">
                                                </i>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group m-form__group">
                                    <label for="">Pickup </label>
                                    <div class="input-group">
                                        <input numberOnly formControlName="pickup" type="text" class="form-control m-input" name="pickup" aria-describedby="basic-addon2" />
                                        <div class="input-group-append">
                                            <span class="input-group-text">
                                                <i 
                                                    [ngClass]="unit == 'percent' ? 'fa-percent': 'fa-dollar'" 
                                                    class="fa">
                                                </i>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group m-form__group">
                                    <label for="">Return</label>
                                    <div class="input-group">
                                        <input numberOnly type="text" formControlName="return" class="form-control m-input" name="return" aria-describedby="basic-addon2" />
                                        <div class="input-group-append">
                                            <span class="input-group-text">
                                                <i 
                                                    [ngClass]="unit == 'percent' ? 'fa-percent': 'fa-dollar'" 
                                                    class="fa">
                                                </i>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-sm btn-dark">
                          Submit</button>
                    </div>

                </div>
            </form>
            <hr/>
            <div class="row mt-5">
                <div class="col-md-3">
                    <h5>New order status handling:<br/>
                    </h5>
                </div>
                <div class="col-md-9">

                    <div class="row">
                        <div class="col-md-6">
                            <div>
                                <select name="" id="" [(ngModel)]="order_config" class="form-control">
                                    <option value="auto">Advance order to order prep(with minimum payment)</option>
                                    <!-- <option value="auto_admin_pos">Auto confirm Admin/POS only</option> -->
                                    <option value="manual">Manually update order status</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <button type="submit" (click)="order_config_submit()" style="margin-top: 15px;" class="btn btn-sm btn-dark">
                      Submit
                    </button>
                </div>

            </div>

            <hr/>
            <div *ngIf="arb" class="row mt-5">
                <div class="col-md-3">
                    <h5>ARB Integration:<br/>
                    </h5>
                </div>
                <div class="col-md-9">

                    <div class="row">
                        <div class="col-md-6">
                            <div>
                                <label class="m-radio" style="margin-right: 10px;">
                                    <input type="radio" value="standard" [(ngModel)]="store_arb_activation" name="store_arb_activation">
                                           &nbsp;Standard billing
                                    <span></span>
                                </label>
                                <label class="m-radio" style="margin-right: 10px;">
                                    <input type="radio" value="before_rental" [(ngModel)]="store_arb_activation" name="store_arb_activation">
                                           &nbsp;Recurring billing before rental
                                    <span></span>
                                </label>
                                <label class="m-radio">
                                    <input type="radio" value="after_rental" [(ngModel)]="store_arb_activation" name="store_arb_activation">
                                           &nbsp;Recurring billing after rental
                                    <span></span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <button type="submit" (click)="arb_submit()" style="margin-top: 15px;" class="btn btn-sm btn-dark">
                      Submit
                    </button>
                </div>

            </div>
        </div>
    </div>
</div>