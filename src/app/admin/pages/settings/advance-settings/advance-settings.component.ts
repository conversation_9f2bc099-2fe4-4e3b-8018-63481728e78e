import { Payment } from './../../../../home/<USER>/type.model';
import { Component, OnInit, ElementRef,ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { SettingService } from './../setting-service/setting.service'
import { AlertService } from "./../../../../modules/alert/alert.service";

@Component({
  selector: 'app-advance-settings',
  templateUrl: './advance-settings.component.html',
  styleUrls: ['./advance-settings.component.css']
})

export class AdvanceSettingsComponent implements OnInit {

  leadLagForm: FormGroup;
  posPaymentForm: FormGroup;
  leadTime: boolean;
  lagTime: boolean;
  inValid: boolean;
  time = [
    {name: 'Hour(s)', value: 'H'},
    {name: 'Day(s)', value: 'D'}
  ];
  unit: string = 'percent';
  order_config = 'auto';
  arb = false;
  store_arb_activation: string = 'standard';
  @ViewChild("hasCusAlert") alertContainer: ElementRef;
  constructor(
    fb: FormBuilder,
    private activeRoute: ActivatedRoute,
    private settingS: SettingService,
    private alertS: AlertService
  ) {
    this.leadLagForm = fb.group({
      lead : ['false'],
      lead_time_type : [this.time[0].value],
      lead_time_value : [''],
      lag : ['false'],
      lag_time_value : [''],
      lag_time_type : [this.time[0].value]
    });
    this.posPaymentForm = fb.group({
      type: ['percent'],
      booking: ['100'],
      pickup: ['100'],
      return: ['100']
    });
    this.getLagLeadTimeFromRoute();
    this.getPaymentOptions();
   }

  ngOnInit() {
    if (!this.leadTime && !this.lagTime) {
      this.inValid = true;
    } else {
      this.inValid = false;
    }
    this.leadLagForm.get('lead').valueChanges.subscribe(
      (lead) => {
        if (lead === 'true') {
          this.inValid = false;
          this.leadLagForm.get('lead_time_value').setValidators([Validators.required]);
        } else {
          this.leadLagForm.get('lead_time_value').clearValidators();
        }
        this.leadLagForm.get('lead_time_value').updateValueAndValidity();
      }
    );
    this.leadLagForm.get('lag').valueChanges.subscribe(
      (lag) => {
        if (lag === 'true') {
          this.inValid = false;
          this.leadLagForm.get('lag_time_value').setValidators([Validators.required]);
        } else {
          this.leadLagForm.get('lag_time_value').clearValidators();
        }
        this.leadLagForm.get('lag_time_value').updateValueAndValidity();
      }
    );
  }

  changeLead(e) {
    const leadTime = JSON.parse(e.target.value);
    leadTime ? this.leadTime = true : this.leadTime = false;
  }

  changeLag(e) {
    const lagTime = JSON.parse(e.target.value);
    lagTime ? this.lagTime = true : this.lagTime = false;
  }

  order_config_submit() {
    const data = {
      order_status: this.order_config
    }
    this.settingS.setLeadLagTime(data)
      .then(res => {
        if (res.status === 'OK') {
          this.alertS.success(
            this.alertContainer,
            "Updated Successfully.",
            true,
            5000
          );
        } else {
          this.alertS.error(
            this.alertContainer,
            "Something went wrong",
            true,
            5000
          );
        }
      })
      .catch(err => {
        this.alertS.error(
          this.alertContainer,
          "Something went wrong",
          true,
          5000
        );
      });
  }

  onSubmit() {
    // console.log(this.leadLagForm.value);
    const leadLagTime = this.leadLagForm.value;
    if (leadLagTime.lead === 'false') {
      leadLagTime.lead_time_type = "";
      leadLagTime.lead_time_value = "";
    }
    if (leadLagTime.lag === 'false') {
      leadLagTime.lag_time_type = "";
      leadLagTime.lag_time_value = "";
    }
    this.settingS.setLeadLagTime(leadLagTime).then(
      res => {
        // console.log(res);
        if (res.status === 'OK') {
          this.alertS.success(
            this.alertContainer,
            "Updated Successfully.",
            true,
            5000
          );
        }
      }
    ).catch(
      err => console.log(err)
    );
  }

  getLagLeadTimeFromRoute() {
    const leadLagTime = this.activeRoute.snapshot.data['leadLag'];
    if (leadLagTime.data) {
      if (leadLagTime.data.hasOwnProperty('arb')) {
        this.arb = leadLagTime.data.arb.active ? leadLagTime.data.arb.active : false;
        this.store_arb_activation = leadLagTime.data.arb.store_active ? leadLagTime.data.arb.store_active : 'standard';
      }
      if (leadLagTime.data.hasOwnProperty('order')) {
        this.leadLagForm.patchValue(leadLagTime.data.order);
        if (leadLagTime.data.order.order_status) {
          this.order_config = leadLagTime.data.order.order_status;
        }
        if (leadLagTime.data.order.hasOwnProperty('lead')) {
          if (leadLagTime.data.order.lead === 'true') {
            this.leadTime = true;
          } else {
            this.leadTime = false;
            this.leadLagForm.get('lead_time_type').setValue(this.time[0].value);
          }
        }
        if (leadLagTime.data.order.hasOwnProperty('lag')) {
          if (leadLagTime.data.order.lag === 'true') {
            this.lagTime = true;
          } else {
            this.lagTime = false;
            this.leadLagForm.get('lag_time_type').setValue(this.time[0].value);
          }
        }
      }
    }
  }

  getPaymentOptions() {
    const config = this.activeRoute.snapshot.data['leadLag'];
    if (config.data) {
      if (config.data.hasOwnProperty('payments')) {
        this.posPaymentForm.patchValue(config.data.payments);
        this.unit = config.data.payments.type ? config.data.payments.type : 'percent';
      }
    }
  }

  arb_submit() {
    const data = {
      store_arb_activation: this.store_arb_activation
    }
    this.settingS.saveTimeZone(data)
      .then(res => {
        if (res.status === 'OK') {
          const contents = localStorage.getItem('contents') 
            ? JSON.parse(localStorage.getItem('contents')) 
            : null;
          if (contents) {
            contents.site_specific.confg.arb.store_active = data.store_arb_activation;
            localStorage.setItem('contents', JSON.stringify(contents));
          }
          this.alertS.success(
            this.alertContainer,
            "Updated Successfully.",
            true,
            5000
          );
        } else {
          this.alertS.error(
            this.alertContainer,
            "Something went wrong",
            true,
            5000
          );
        }
      })
      .catch(err => {
        this.alertS.error(
          this.alertContainer,
          "Something went wrong",
          true,
          5000
        );
      });
  }

  onSubmitPosPayment() {
    const data = this.posPaymentForm.getRawValue();
    // if (data.show_checkout_additional_field === false) {
    //   return;
    // }
    this.settingS.setPosPayments(data).then(
      res => {
        // console.log(res);
        if (res.status === 'OK') {
          this.alertS.success(
            this.alertContainer,
            "Updated Successfully.",
            true,
            5000
          );
        }
      }
    ).catch(
      err => {
        // console.log(err);
        this.alertS.error(
          this.alertContainer,
          "Something went wrong",
          true,
          5000
        );
      }
    );
  }

  changeUnit(e) {
    this.unit = e.target.value;
  }

}
