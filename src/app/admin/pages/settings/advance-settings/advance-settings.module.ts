import { DateTimeRangeModule } from './../../../../modules/date-time-range/date-time-range.module';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LayoutModule } from "../../../layouts/layout.module";
import { RouterModule, Routes } from '@angular/router';
import { ReactiveFormsModule, FormsModule } from "@angular/forms";
import { AdvanceSettingsComponent } from './advance-settings.component';

const route: Routes = [
  {
    path: '',
    component: AdvanceSettingsComponent
  }
]

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(route),
    LayoutModule,
    ReactiveFormsModule,
    FormsModule,
    DateTimeRangeModule
  ],
  declarations: [AdvanceSettingsComponent]
})
export class AdvanceSettingsModule { }
