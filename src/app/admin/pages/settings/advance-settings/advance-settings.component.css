.custom-alert {
    position: fixed;
    top: 90px;
    right: 30px;
    z-index: 5000;
}

.green {
    color: green;
}

.red {
    color: red;
}

app-exact-start-time-date {
    width: 100%;
}

.exact-start-time-area {
    border-top: 1px solid #f2f3f8;
}

.m-radio>span {
    border: 1px solid #bdc3d4;
}

.m-radio>span {
    border-radius: 50%!important;
}

.m-radio>span,
.m-checkbox>span {
    border-radius: 3px;
    background: none;
    position: absolute;
    top: 1px;
    left: 0;
    height: 18px;
    width: 18px;
}

.m-radio>span:after {
    border: solid #7281a4;
    background: #7281a4;
}

.m-radio>span:after {
    top: 50%;
    left: 50%;
    margin-left: -3px;
    margin-top: -3px;
    height: 6px;
    width: 6px;
    border-radius: 100% !important;
}

.m-radio>span:after,
.m-checkbox>span:after {
    content: '';
    position: absolute;
    display: none;
}

.m-radio>input:checked~span:after,
.m-checkbox>input:checked~span:after {
    display: block;
}