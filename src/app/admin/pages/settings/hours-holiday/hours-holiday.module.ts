import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HoursHolidayComponent } from './hours-holiday.component';
import { HoursComponent } from './hours/hours.component';
import { HolidaysComponent } from './holidays/holidays.component';
import { AddFormComponent } from './holidays/add-form/add-form.component';
import { Routes, RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { DialogBoxModule } from '../../../../modules/dialog-box/dialog-box.module';
import { DialogBoxComponent } from '../../../../modules/dialog-box/dialog-box.component';
import { SeasonalPriceFormComponent } from './seasonal-price/seasonal-price-form/seasonal-price-form.component';
import { SeasonalHoursComponent } from './seasonal-price/seasonal-hours/seasonal-hours.component';
import { SeasonalPriceComponent } from './seasonal-price/seasonal-price.component';

const route: Routes = [
  { path: '', component: HoursHolidayComponent }
]

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(route),
    FormsModule,
    DialogBoxModule
  ],
  entryComponents: [
    DialogBoxComponent,
    AddFormComponent,
    SeasonalPriceFormComponent,
    SeasonalHoursComponent],
  declarations: [
    HoursHolidayComponent,
    HoursComponent,
    HolidaysComponent,
    AddFormComponent,
    SeasonalPriceComponent,
    SeasonalPriceFormComponent,
    SeasonalHoursComponent
  ]
})
export class HoursHolidayModule { }
