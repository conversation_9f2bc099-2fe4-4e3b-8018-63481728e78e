import { Component, OnInit, AfterViewInit, OnDestroy, ViewChild, ElementRef, OnChanges } from '@angular/core';
import { HoursHolidaysService, hoursTime, weekDays, WeekDays } from '../../setting-service/hours-holidays.service';
import { ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { AlertService } from '../../../../../modules/alert/alert.service';
import { Helpers } from '../../../../../helpers';

declare let $: any;

@Component({
  selector: 'app-hours',
  templateUrl: './hours.component.html',
  styleUrls: ['./hours.component.css']
})
export class HoursComponent implements OnInit, AfterViewInit, OnDestroy {

  hoursGroup: any[] = [];
  weekDays: WeekDays[] = [];
  sub: Subscription[]=[];
  loading: boolean;
  season;
  selectedSwitchType;

  @ViewChild('hasCusAlert') alertContainer: ElementRef;

  constructor(
    private service: HoursHolidaysService,
    private alertS: AlertService,
    private activeRoute: ActivatedRoute
  ) { }

  ngOnInit() {
    this.sub.push(
      this.activeRoute.data.subscribe(
        val => {
          this.season=val.weeks.season;
          if(this.season=='not exists'){
            this.selectedSwitchType='day'
          }
          else{
            this.selectedSwitchType='season'
          }

          this.weekDays = this.service.formatWeekDays(val.weeks.data);
        }
      )
    )

    this.sub.push(
      this.service.seasonChange.subscribe(res=>{
        if(res !=null){
          if(res.reload){
            this.onSwitch(this.selectedSwitchType)
          }
        }
      })
    )


    this.hoursGroup = hoursTime;

  }

  

  ngAfterViewInit() {
     this.showBootstrapToggle();
  }

  ngOnDestroy() {
    this.sub.forEach(s=>{
      s.unsubscribe();
    });
  }

  private showBootstrapToggle() {
    for( let w of this.weekDays) {
      $('#open-' + w.day).bootstrapSwitch();
      $('#open-' + w.day).on('switchChange.bootstrapSwitch', () => {
        this.changeSwitch(w.day, true, '.bootstrap-switch-id-always-' + w.day);
      });
      $('#always-' + w.day).bootstrapSwitch();
      $('#always-' + w.day).on('switchChange.bootstrapSwitch', () => {
        this.changeSwitch(w.day, false);
      });
      if(!w.is_open) {
        $('.bootstrap-switch-id-always-' + w.day).hide();
      }
    }
  }

  private changeSwitch(day: number, open: boolean, id?:string) {
    const index = this.weekDays.findIndex( f => f.day === day);
    if(open) {
      this.weekDays[index].is_open = !this.weekDays[index].is_open;
      if(!this.weekDays[index].is_open) {
        $(id).hide();
        this.weekDays[index].always_open = true;
      } else {
        $(id).show();
      }
    } else {
      this.weekDays[index].always_open = !this.weekDays[index].always_open;
    }
  }

  submit() {
    this.loading = true;
    let sendData;
    if(this.season){
      sendData = this.service.formatSeasonalHoursSubmitData(this.weekDays,this.season.id);
    }
    else{
      sendData = this.service.formatSubmitData(this.weekDays);
    }
    
    this.service.saveWeekDays(sendData)
    .then(
      res => {
        this.loading = false;
        console.log(res)
        if(res.status === 'OK' && res.result.message) {
          this.alertS.success(this.alertContainer, res.result.message, true, 3000);
        } else {
          this.alertS.error(this.alertContainer, res.result.error, true, 3000);
        }
      }
    )
    .catch(
      err => {
        this.loading = false;
        console.error(err);
        this.alertS.error(this.alertContainer, 'Something went wrong!!! Please try again', true, 3000);
      }
    );
  }

  onSwitch(type){
    if(type){
      this.selectedSwitchType=type;
      Helpers.setLoading(true)
      this.service.getWeekDays(type).subscribe(res=>{
        if(res){
          this.season=res.season;
          this.weekDays = this.service.formatWeekDays(res.data);

          setTimeout(t=>{
            Helpers.setLoading(false)
            this.showBootstrapToggle();
          },1000)
        }
       
      });
    }
    

  }

}
