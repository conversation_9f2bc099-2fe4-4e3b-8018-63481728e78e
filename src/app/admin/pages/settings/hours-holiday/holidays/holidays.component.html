<div class="custom-alert" #hasCusAlert></div>


<div class="m-portlet">
    <div class="m-portlet__head title-head">
        <div class="m-portlet__head-caption">
          <div class="m-portlet__head-title">
            <h3 class="m-portlet__head-text colorPurpel">
              Holidays list
            </h3>
            <div class="float-right text-center holiday-add">
              <button  type="button" (click)="AddEdit()" class="btn btn-secondary m-btn m-btn--icon m-btn--icon-only m-btn--pill m-btn--air float-right"><i class="fa fa-plus"></i></button>
            </div>
          </div>
        </div>
      </div>
  <div class="m-portlet__body">
    <div class="m-section" style="margin: 0px;">
      <div class="m-section__content">
        <div class="table-responsive">
          <table class="table">
            <thead>
              <tr>
                <th>Start date</th>
                <th>End date</th>
                <th>Name</th>
                <th>Action</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let h of holidays; let i=index; trackBy: trackByHoildays">
                <td>{{getDate(h.start_date)}}</td>
                <td>{{getDate(h.end_date)}}</td>
                <td>{{h.description}}</td>
                <td>
                  <a (click)="AddEdit(h)" class="m-portlet__nav-link btn m-btn m-btn--hover-brand m-btn--icon m-btn--icon-only m-btn--pill">
                    <i class="fa fa-edit"></i>
                  </a>
                  <div *ngIf="deleteId == h.id; else deleteS" class="m-loader m-loader--brand" style="width: 30px; display: inline-block;"></div>
                  <ng-template #deleteS>
                    <a (click)="deleteHoliday(h, i)" class="m-portlet__nav-link btn m-btn m-btn--hover-danger m-btn--icon m-btn--icon-only m-btn--pill">
                      <i class="fa fa-trash"></i>
                    </a>
                  </ng-template>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<app-seasonal-price></app-seasonal-price>

