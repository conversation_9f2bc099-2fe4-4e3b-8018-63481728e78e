import { Component, OnInit, AfterViewInit, OnDestroy, ViewChild, ElementRef, Input } from '@angular/core';
import { Subscription } from 'rxjs';
import { WeekDays, HoursHolidaysService, hoursTime } from '../../../setting-service/hours-holidays.service';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';


declare let $: any;

@Component({
  selector: 'app--seasonal-hours',
  templateUrl: './seasonal-hours.component.html',
  styleUrls: ['./seasonal-hours.component.css']
})
export class SeasonalHoursComponent implements OnInit, AfterViewInit, OnDestroy {

  hoursGroup: any[] = [];
  sub: Subscription;
  loading: boolean;

  @Input('season_id') season_id;
  @Input('weekDays') weekDays: WeekDays[] = [];

  @ViewChild('hasCusAlert') alertContainer: ElementRef;

  constructor(
    private service: HoursHolidaysService,
    private activeModal:NgbActiveModal
  ) { }

  ngOnInit() {
    this.hoursGroup = hoursTime;
  }

  

  ngAfterViewInit() {
     this.showBootstrapToggle();
  }

  ngOnDestroy() {
    
  }



  private showBootstrapToggle() {
    for( let w of this.weekDays) {
      $('#open-s-' + w.day).bootstrapSwitch();
      $('#open-s-' + w.day).on('switchChange.bootstrapSwitch', () => {
        this.changeSwitch(w.day, true, '.bootstrap-switch-id-always-s' + w.day);
      });
      $('#always-s-' + w.day).bootstrapSwitch();
      $('#always-s-' + w.day).on('switchChange.bootstrapSwitch', () => {
        this.changeSwitch(w.day, false);
      });
      if(!w.is_open) {
        $('.bootstrap-switch-id-always-s' + w.day).hide();
      }
    }
  }

  private changeSwitch(day: number, open: boolean, id?:string) {
    const index = this.weekDays.findIndex( f => f.day === day);
    if(open) {
      this.weekDays[index].is_open = !this.weekDays[index].is_open;
      if(!this.weekDays[index].is_open) {
        $(id).hide();
        this.weekDays[index].always_open = true;
      } else {
        $(id).show();
      }
    } else {
      this.weekDays[index].always_open = !this.weekDays[index].always_open;
    }
  }

  submit() {
    this.loading = true;
    const sendData = this.service.formatSeasonalHoursSubmitData(this.weekDays,this.season_id);
    this.service.saveWeekDays(sendData)
    .then(
      res => {
        this.loading = false;
        console.log(res)
        if(res.status === 'OK' && res.result.message) {
          this.activeModal.close({type:'success',message:res.result.message});
        } else {
          this.activeModal.close({type:'success',message:res.result.error});
        }
      }
    )
    .catch(
      err => {
        this.loading = false;
        console.error(err);
        this.activeModal.close({type:'success',message:'Something went wrong!!! Please try again'});
      }
    );
  }

  onClickCloseModal(){
    this.activeModal.close();
  }

}
