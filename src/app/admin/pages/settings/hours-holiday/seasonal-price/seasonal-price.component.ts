import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { HoursHolidaysService, SeasonalPrice } from '../../setting-service/hours-holidays.service';
import { AlertService } from '../../../../../modules/alert/alert.service';
import { calandarDateFormat } from '../../../../../globals/_classes/functions';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { DialogBoxComponent } from '../../../../../modules/dialog-box/dialog-box.component';
import { SeasonalPriceFormComponent } from './seasonal-price-form/seasonal-price-form.component';
import { SeasonalHoursComponent } from './seasonal-hours/seasonal-hours.component';
import { Helpers } from '../../../../../helpers';


@Component({
  selector: 'app-seasonal-price',
  templateUrl: './seasonal-price.component.html',
  styleUrls: ['./seasonal-price.component.css']
})
export class SeasonalPriceComponent implements OnInit {

  seasonalPrices: SeasonalPrice[] = [];
  seasonalPrice: SeasonalPrice;
  edit: boolean;
  deleteId: number = null;

  @ViewChild('hasCusAlert') alertContainer: ElementRef;

  constructor(
    private service: HoursHolidaysService,
    private alertS: AlertService,
    private modalService: NgbModal
  ) { }

  ngOnInit() {
    this.getSeasonalPriceList();
    this.seasonalPrice = new SeasonalPrice();

  }

  trackByHoildays(index, item) {
    return item.id ? item.id : null;
  }

  private getSeasonalPriceList() {
    this.service.getHolidaysList('season')
    .subscribe(
      res => {
        this.seasonalPrices = res;
      },
      err => {
        console.error(err)
      }
    );
  }

  getDate(date) {
    return calandarDateFormat(date)
  }


  alerts(e) {
    if(e.type === 'info') {
      this.alertS.info(this.alertContainer, e.message, true, 3000);
    } else if(e.type === 'success') {
      this.getSeasonalPriceList();
      this.alertS.success(this.alertContainer, e.message, true, 3000);
    } else if(e.type === 'error') {
      this.alertS.error(this.alertContainer, e.message, true, 3000);
    } else {
      if('edit' in e) {
        this.edit = e.edit;
        this.seasonalPrice = e.data;
      }
    }
  }

  deleteSeasonalPrice(h, i) {
    const modalRef = this.modalService.open(DialogBoxComponent, {
      centered: true,
      size: 'sm',
      windowClass: 'animated fadeIn'
    });
    modalRef.componentInstance.massage = 'Are you sure you want to delete?';
    modalRef.result
      .then((result) => {
        if (result) {
          this.deleteId = h.id;
         // holidayComp.resetAll();
          this.archive(h.id, i);
        }
      }, (res) => {
        console.log(res);
      });

  }

  archive(id, i) {
    this.service.deleteHoliday(id).then(
      res => {
        this.deleteId = null;
        if (res.status === 'OK' && res.result.message) {
          this.service.onChangeSeason({reload:true})
          this.seasonalPrices.splice(i, 1);
          this.alertS.success(this.alertContainer, res.result.message, true, 3000);
        } else {
          this.alerts({ type: 'error', message: res.result.error });
        }
      }
    ).catch (
      err => {
        this.deleteId = null;
        console.log(err);
        this.alerts({type: 'error', message: 'Something went wrong! Please try again'});
      }
    )
  }


  AddEdit(data?){

    if(data){
      this.seasonalPrice=data;
      this.edit = true;
    }
    else{
      this.edit = false;
    }

    const modalRef = this.modalService.open(SeasonalPriceFormComponent, {
      centered: true,
      backdrop: "static",
      keyboard: false,
      size: "lg",
      windowClass: 'animated fadeIn'
    });
    modalRef.componentInstance.seasonalPrice = this.seasonalPrice;
    modalRef.componentInstance.edit = this.edit;

    modalRef.result
      .then((e) => {
        if (e) {
         if(e.type === 'info') {
            this.alertS.info(this.alertContainer, e.message, true, 3000);
          } else if(e.type === 'success') {
            this.service.onChangeSeason({reload:true})
            this.getSeasonalPriceList();
            this.alertS.success(this.alertContainer, e.message, true, 3000);
          } else if(e.type === 'error') {
            this.alertS.error(this.alertContainer, e.message, true, 3000);
          } else {
            if('edit' in e) {
              this.edit = e.edit;
              this.seasonalPrice = e.data;
            }
          }
        }
      }, (res) => {
        console.log(res);
      });

  }


  onClickSetHours(season_id){
    Helpers.setLoading(true)
    let weekDays=[];

    this.service.getSeasonWeeekHours(season_id).subscribe(res=>{
      Helpers.setLoading(false)

      weekDays = this.service.formatWeekDays(res);
      const modalRef = this.modalService.open(SeasonalHoursComponent, {
        centered: true,
        // backdrop: "static",
        size: "lg",
        keyboard: false,
        windowClass: 'animated fadeIn'
      });
  
      modalRef.componentInstance.season_id=season_id;
      modalRef.componentInstance.weekDays=weekDays;
  
      modalRef.result
        .then((e) => {
          if (e) {
            if(e.type === 'success') {
              this.alertS.success(this.alertContainer, e.message, true, 3000);
            } else {
              this.alertS.error(this.alertContainer, e.message, true, 3000);
            } 
          }
        }, (res) => {
          console.log(res);
        });

    })


  }

}
