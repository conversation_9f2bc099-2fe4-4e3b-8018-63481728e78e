import { Component, OnInit, Input, Output, EventEmitter, AfterViewInit, OnChanges } from '@angular/core';
import { HoursHolidaysService, SeasonalPrice } from '../../../setting-service/hours-holidays.service';
import { convertTime12to24, GETTIME } from '../../../../../../globals/_classes/functions';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

declare let $: any;

@Component({
  selector: 'seasonal-price-form',
  templateUrl: './seasonal-price-form.component.html',
  styleUrls: ['./seasonal-price-form.component.css']
})
export class SeasonalPriceFormComponent implements OnChanges, OnInit, AfterViewInit {

  @Input('seasonalPrice') seasonalPrice: SeasonalPrice;
  @Input('edit') edit: boolean;
  @Output('output') message: EventEmitter<any> = new EventEmitter();

  seasonalPrices;
  loading: boolean;

  constructor(
    private service: HoursHolidaysService,
    private activeModal:NgbActiveModal
  ) { }

  ngOnChanges() {
  
  }

  ngOnInit() {
    if (this.edit) {
       this.updateDate(this.seasonalPrice.start_date, this.seasonalPrice.end_date);
       this.seasonalPrices = {
        start_date: this.seasonalPrice.start_date,
        end_date:  this.seasonalPrice.end_date,
        start_time: '',
        end_time: ''
      };

     
    }
    else{
      this.resetHolidayDate();
    }
   
  }

  ngAfterViewInit() {
    this.dateTimePicker();
  }

  private resetHolidayDate() {
    this.seasonalPrices = {
      start_date: '',
      end_date: '',
      start_time: '',
      end_time: ''
    };
    this.seasonalPrice=new SeasonalPrice();
  }

  private dateTimePicker() {
    this.service.datePicker(true);
    this.service.timePicker();
    this.rentalDateChange();
    this.rentalTimeChange();
  }

  private rentalDateChange() {
    $('#holiday-date-start').datepicker().on('changeDate', (e) => {
      let date = e.date;
      this.seasonalPrices.start_date = this.service.getCurrentDateTime(date);
    });
    $('#holiday-date-end').datepicker().on('changeDate', (e) => {
      let date = e.date;
      this.seasonalPrices.end_date = this.service.getCurrentDateTime(date);
    });
  }

  private rentalTimeChange() {
    $('#holiday-time-start').on('change', () => {
      let time = $('#holiday-time-start').data('timepicker').getTime();
      this.seasonalPrices.start_time = convertTime12to24(time);
    });
    $('#holiday-time-end').on('change', () => {
      let time = $('#holiday-time-end').data('timepicker').getTime();
      this.seasonalPrices.end_time = convertTime12to24(time);
    });
  }

  submit() {
    this.loading = true;
    if (this.checkDates()) {
      this.seasonalPrice.start_date = (this.seasonalPrices.start_date.split(' ').length>0 ? this.seasonalPrices.start_date.split(' ')[0] :this.seasonalPrices.start_date)  + ' ' +  "00:00";
      this.seasonalPrice.end_date = (this.seasonalPrices.end_date.split(' ').length>0 ? this.seasonalPrices.end_date.split(' ')[0] :this.seasonalPrices.end_date)  + ' ' + "00:00";
      this.service.saveHolidays(this.seasonalPrice)
        .then(
          res => {
            this.loading = false;
            if (res.status === 'OK' && res.result.message) {
              this.activeModal.close({ type: 'success', message: res.result.message })
              this.resetAll();
            } else {
              this.activeModal.close({ type: 'error', message: res.result.error })
            }
          }
        )
        .catch(
          err => {
            console.error(err);
            this.loading = false;
            this.sendMessage({ type: 'error', message: 'Something went wrong!!! Please try again' });
          }
        )
    } else {
      this.sendMessage({ type: 'info', message: 'Plesae select dates' });
    }

  }

  resetAll() {
    this.seasonalPrice=new SeasonalPrice();
    this.resetHolidayDate();
    this.updateDate();
  }

  private updateDate(start?, end?) {
    $('#holiday-date-start').datepicker('update', start ? new Date(start) : '');
    $('#holiday-date-end').datepicker('update', end ? new Date(end) : '');
    const s = start ? GETTIME(start) : '12:00 AM';
    const e = end ? GETTIME(end) : '11:59 PM';
    $('#holiday-time-start').timepicker('setTime', s);
    $('#holiday-time-end').timepicker('setTime', e);
    
  }

  private sendMessage(data) {
    this.message.emit(data);
  }

  private checkDates() {
    if (this.edit) {
      this.seasonalPrice.start_date = this.seasonalPrice.start_date ? this.seasonalPrice.start_date : this.seasonalPrice.start_date.split(' ')[0];
      this.seasonalPrice.end_date = this.seasonalPrice.end_date ? this.seasonalPrice.end_date : this.seasonalPrice.end_date.split(' ')[0];
      this.seasonalPrices.start_time = this.seasonalPrices.start_time ? this.seasonalPrices.start_time : this.seasonalPrice.start_date.split(' ')[1];
      this.seasonalPrices.end_time = this.seasonalPrices.end_time ? this.seasonalPrices.end_time : this.seasonalPrice.end_date.split(' ')[1];
    }
    return this.seasonalPrices.start_date && this.seasonalPrices.end_date;
  }

  onClickCloseModal(){
    this.activeModal.close();
  }

}
