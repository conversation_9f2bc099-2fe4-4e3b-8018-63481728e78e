<div class="m-subheader">
  <div class="d-flex align-items-center">
    <div class="mr-auto">
      <h3 class="m-subheader__title m-subheader__title--separator">
        Advanced settings
      </h3>
      <ul class="m-subheader__breadcrumbs m-nav m-nav--inline">
        <li class="m-nav__item m-nav__item--home">
          <a routerLink="/admin" class="m-nav__link m-nav__link--icon">
            <i class="m-nav__link-icon la la-home"></i>
          </a>
        </li>
        <li class="m-nav__separator">
          <i class="fa fa-angle-right"></i>
        </li>
        <li class="m-nav__item">
          <a class="m-nav__link">
            <span class="m-nav__link-text">
              Settings
            </span>
          </a>
        </li>
        <li class="m-nav__separator" style="padding-left: 10px">
          <i class="fa fa-angle-right"></i>
        </li>
        <li class="m-nav__item">
          <a class="m-nav__link">
            <span class="m-nav__link-text">
              QuickBooks Integration
            </span>
          </a>
        </li>
      </ul>
    </div>
  </div>
</div>
<!-- END: Subheader -->

<div class="m-content animated fadeIn">
  <div class="m-portlet">
    <div class="m-portlet__head title-head">
      <div class="m-portlet__head-caption">
        <div class="m-portlet__head-title">
          <h3 class="m-portlet__head-text colorPurpel">
            Quickbook Integration
          </h3>
          <p *ngIf="is_disable_btn" class="mt-4" style="color: red;">Quickbooks athorization completed.</p>
        </div>
      </div>
    </div>
    <div class="m-portlet__body">
      <form [formGroup]="quickbookForm" (ngSubmit)="onSubmit()">
        <div class="row">
          <div class="col-md-3">
            <h5>
              Configuration Details <br />
              <small style="font-size:12px;"
                ><a
                  href="https://developer.intuit.com/app/developer/qbo/docs/get-started"
                  target="_blank"
                  >Click here
                </a>
                to Get QuickBooks Integration details.
              </small>
            </h5>
          </div>
          <div class="col-md-9">
            <div class="row">
              <div class="col-md-12">
                <div class="form-group m-form__group">
                  <label for="">Client ID</label>
                  <input
                    numberOnly
                    formControlName="clientID"
                    type="text"
                    class="form-control m-input"
                    name="clientID"
                    aria-describedby="basic-addon2"
                  />
                </div>
                <div class="form-group m-form__group">
                  <label for="">Client Secret</label>
                  <input
                    numberOnly
                    formControlName="clientSecret"
                    type="text"
                    class="form-control m-input"
                    name="clientSecret"
                    aria-describedby="basic-addon2"
                  />
                </div>
                <div class="form-group m-form__group">
                  <label for="">Redirect Url</label>
                  <input
                    type="text"
                    class="form-control m-input"
                    disabled
                    aria-describedby="basic-addon2"
                    value="{{ redirectUrl }}"
                  />
                  <span
                    class="m-form__help"
                    style="font-size:12px; font-style: italic;"
                    >Redirect Url needs to be added on QuickBooks apps setup
                    page.</span
                  >
                </div>
                <button
                  [disabled]="is_disable_btn"
                  type="submit"
                  class="btn btn-sm btn-dark"
                >
                  Submit
                </button>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
