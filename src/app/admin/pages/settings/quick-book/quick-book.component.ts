import { OnInit, Component } from "@angular/core";
import { Form<PERSON><PERSON>er, FormGroup } from "@angular/forms";
import { ActivatedRoute } from "@angular/router";
import { SettingService } from "../setting-service/setting.service";
import { AlertService } from "../../../../modules/alert/alert.service";
import { getSubdomain } from "./../../../../globals/_classes/functions";

@Component({
  selector: "app-quick-book",
  templateUrl: "./quick-book.component.html",
  styleUrls: ["./quick-book.component.css"]
})
export class QuickBookComponent implements OnInit {
  quickbookForm: FormGroup;
  authorization: {};
  redirectUrl;
  is_disable_btn;
  constructor(
    fb: FormBuilder,
    private route: ActivatedRoute,
    private settingS: SettingService,
    private alertS: AlertService
  ) {
    this.route.queryParams.subscribe(params => {
      //console.log(params);
      this.is_disable_btn =params.code ? true:false;
      this.authorization = params;
      if (params.code && params.realmId) {
        this.settingS
          .setQuickbookToken(this.authorization)
          .then(res => {
            if (res.status === "OK") {
              //console.log(res.result.data);
              // this.alertS.success(
              //     this.alertContainer,
              //     "Updated Successfully.",
              //     true,
              //     5000
              // );
            }
          })
          .catch(err => console.log(err));
      }
    });
    this.quickbookForm = fb.group({
      clientID: [""],
      clientSecret: [""]
    });
  }

  ngOnInit() {
    const subDomain = getSubdomain();
    const subDom = subDomain.includes("localhost")
      ? "http://localhost:4200"
      : "https://" + subDomain + ".rentmy.co";
    this.redirectUrl = subDom + "/admin/settings/quick-book";

    //localStorage.setItem('quickbookSubdomain', subDom);
    this.settingS.getLeadLagTime().subscribe(
      res => {
        if (res.data !== null) {
          if (res.data.hasOwnProperty("quickbook")) {
            this.quickbookForm.patchValue(res.data.quickbook);
          }
        }
      },
      err => console.error(err)
    );
  }

  onSubmit() {
    const quickbookForm = this.quickbookForm.value;
    this.settingS
      .setQuickbookConfig(quickbookForm)
      .then(res => {
        if (res.status === "OK") {
          console.log(res.result.data);
          //window.location.href = res.result.data;
          // this.alertS.success(
          //     this.alertContainer,
          //     "Updated Successfully.",
          //     true,
          //     5000
          // );
        }
      })
      .catch(err => console.log(err));
  }
}
