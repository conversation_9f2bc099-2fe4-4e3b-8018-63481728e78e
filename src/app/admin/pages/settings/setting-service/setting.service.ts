import { Injectable, Optional } from "@angular/core";
import { HttpService } from "../../../../modules/http-with-injector/http.service";
import { SettingsServiceConfig } from "../models/settings.models";
import { map, catchError } from "rxjs/operators";
import { BehaviorSubject, of } from "rxjs";

declare let $: any;

@Injectable()
export class SettingService {
  config: SettingsServiceConfig;

  private COUPON = new BehaviorSubject<any>({ open: null, edit: null });
  addEditOpen = this.COUPON.asObservable();
  addEditChange(data) {
    this.COUPON.next(data);
  }

  editGateForm: BehaviorSubject<any> = new BehaviorSubject({
    edit: false,
    data: {}
  });

  editShippingForm: BehaviorSubject<any> = new BehaviorSubject({
    edit: false,
    data: {}
  });

  private productSearchSubject = new BehaviorSubject<any>(null);
  productSearch = this.productSearchSubject.asObservable();

  private orderLimitSubject = new BehaviorSubject<any>(null);
  orderLimit = this.orderLimitSubject.asObservable();

  private storeContentSubject = new BehaviorSubject<any>(null);
  storeContentChange = this.storeContentSubject.asObservable();

  private subscriptionChangeSubject = new BehaviorSubject<any>(null);
  subscriptionChange = this.subscriptionChangeSubject.asObservable();

  constructor(
    @Optional() config: SettingsServiceConfig,
    private http: HttpService
  ) {
    this.config = config;
  }

  onStoreContentChange(data){
    this.storeContentSubject.next(data);
   }

  onOrderLimitChange(data){
    this.orderLimitSubject.next(data);
   }

  onProductSearchParamChange(data){
   this.productSearchSubject.next(data);
  }

  onSubscriptionChange(data){
    this.subscriptionChangeSubject.next(data);
   }

  datePickerObj() {
    return {
      todayHighlight: true,
      orientation: "bottom left",
      format: "yyyy-mm-dd",
      templates: {
        leftArrow: '<i class="la la-angle-left"></i>',
        rightArrow: '<i class="la la-angle-right"></i>'
      }
    };
  }

  formatGateWay(data) {
    const arr = { list: [], all: [] };
    for (let d in data) {
      const obj = {};
      obj["name"] = d;
      obj["status"] = 1;
      obj["is_online"] = true;
      obj["config"] = {};
      for (let i in data[d]) {
        const key = data[d][i];
        obj["config"][key] = "";
      }
      arr.all.push(obj);
      arr.list.push(d);
    }
    return arr;
  }

  formatShipping(data) {
    const arr = { list: [], all: [] };
    for (let d in data) {
      const obj = {};
      obj["name"] = d;
      obj["status"] = 1;
      obj["config"] = data[d];
      arr.all.push(obj);
      arr.list.push(d);
    }
    return arr;
  }

  getDate(date) {
    return (
      date.getFullYear() + "-" + (date.getMonth() + 1) + "-" + date.getDate()
    );
  }

  getCoupons(page, limit) {
    return this.http
      .get(`coupons?page_no=${page}&limit=${limit}`)
      .pipe(map(res => res.result));
  }

  getCouponCode() {
    return this.http.get(`coupons/get-coupns`).pipe(map(res => res.result));
  }

  addCoupon(data) {
    return this.http.post(`coupons`, data).toPromise();
  }

  updateCoupon(data) {
    return this.http.post(`coupons/${data.id}`, data).toPromise();
  }

  deleteCoupon(id) {
    return this.http.delete(`coupons/${id}`).toPromise();
  }



  getAddons(page, limit,searchTerm?) {
    let url='';
    if(searchTerm){
      url=`addons?page_no=${page}&limit=${limit}&search=${searchTerm}`
    }
    else{
      url=`addons?page_no=${page}&limit=${limit}`
    }

    return this.http
      .get(url)
      .pipe(map(res => res.result));
  }

 
  addAddons(data) {
    return this.http.post(`addons`, data).toPromise();
  }

  updateAddons(data) {
    return this.http.post(`addons/edit`, data).toPromise();
  }

  deleteAddons(id) {
    return this.http.delete(`addons/${id}`).toPromise();
  }
  

  getAttributeSets(page?, limit?, sort?) {
    return this.http
      .get(`variants/value?page_no=${page}&limit=${limit}${sort ? sort : ""}`)
      .pipe(map(res => res.result));
  }

  deleteAttributeSet(id) {
    return this.http.delete(`variants/${id}`).toPromise();
  }

  addAttributeSet(data) {
    return this.http.post(`variants`, data).toPromise();
  }

  updateAttributeSet(id, data) {
    return this.http.post(`variants/${id}`, data).toPromise();
  }

  addAttribute(data, id) {
    return this.http.post(`variants/${id}/value`, data).toPromise();
  }

  updateAttribute(v_id, id, data) {
    return this.http.post(`variants/${v_id}/value/${id}`, data).toPromise();
  }

  deleteAttribute(id) {
    return this.http.delete(`variants/value/${id}`).toPromise();
  }

  getTags() {
    return this.http.get(`tags`).pipe(map(res => res.result));
  }

 

  addTag(data) {
    return this.http.post(`tags`, data).toPromise();
  }

  updateTag(id, data) {
    return this.http.post(`tags/${id}`, data).toPromise();
  }

  deleteTag(id) {
    return this.http.delete(`tags/${id}`).toPromise();
  }


  getCustomCheckouts(section?) {
    let params = '';
    if (section) params = `?section=${section}`;
    return this.http.get(`custom-fields${params}`).pipe(map(res => res.result));
  }

  addCustomCheckout(data) {
    return this.http.post(`custom-fields`, data).toPromise();
  }

  updateCustomCheckout(id, data) {
    return this.http.post(`custom-fields/${id}`, data).toPromise();
  }

  deleteCustomCheckout(id) {
    return this.http.delete(`custom-fields/${id}`).toPromise();
  }

  getterminals() {
    return this.http.get(`locations`).pipe(
      map(res => res.result),
      catchError(e => of({ data: [] }))
    );
  }

  addTerminal(data) {
    return this.http.post(`locations`, data).toPromise();
  }

  updateTerminal(data) {
    return this.http.post(`locations/${data.id}`, data).toPromise();
  }

  deleteTerminal(id) {
    return this.http.delete(`locations/${id}`).toPromise();
  }

  addStoreTerminal(data) {
    return this.http.post(`terminals`, data).toPromise();
  }

  updateStoreTerminal(data) {
    return this.http.post(`terminals/${data.id}`, data).toPromise();
  }

  deleteStroeTerminal(id) {
    return this.http.delete(`terminals/${id}`).toPromise();
  }

  getSupplier(page, limit) {
    return this.http
      .get(`suppliers?page_no=${page}&limit=${limit}`)
      .pipe(map(res => res.result));
  }

  addSupplier(data) {
    return this.http.post(`suppliers`, data).toPromise();
  }


  updateSupplier(data) {
    return this.http.post(`suppliers/${data.id}`, data).toPromise();
  }

  deleteSupplier(id) {
    return this.http.delete(`suppliers/${id}`).toPromise();
  }


  getVideoWallList() {
    return this.http
      .get(`video-wall/list`)
      .pipe(map(res => res.result));
  }

  addVideoWall(data) {
    return this.http.post(`video-wall`, data).toPromise();
  }

  updateVideoWall(data,id) {
    return this.http.post(`video-wall/${id}`, data).toPromise();
  }

  deleteVideoWall(id) {
    return this.http.delete(`video-wall/${id}`).toPromise();
  }

  getGatewaySettings() {
    return this.http.get(`payments/gateway/settings`);
  }

  getGateways() {
    return this.http.get(`payments/gateway`);
  }

  getDeliveryCondition() {
    return this.http.get(`delivery-settings`).pipe(
      map(m => m.result.data),
      catchError(e => of(null))
    );
  }

  getLeadLagTime() {
    return this.http.get('store-config').pipe(map(res => res.result), catchError(e => of(null)));
  }

  setLeadLagTime(data) {
    return this.http.post('store-config/update/order', data).toPromise();
  }

  saveTimeZone(data) {
    return this.http.post("timezones", data).toPromise();
  }

  setPosPayments(data) {
    return this.http.post(`store-config/update/payments`, data).toPromise();
  }
  setStoreConfig(type, data) {
    return this.http.post(`store-config/update/${type}`, data).toPromise();
  }
  setQuickbookConfig(data) {
    return this.http.post(`quickbook/setup`, data).toPromise();
  }
  setQuickbookToken(data) {
    return this.http.post(`quickbook/token`, data).toPromise();
  }

  setMailchimpConfig(data) {
    return this.http.post(`mailchimp/setup`, data).toPromise();
  }
  setMailchimpToken(data) {
    return this.http.post(`mailchimp/token`, data).toPromise();
  }

  
  setStripeToken(data) {
    return this.http.post(`stripe/token`, data).toPromise();
  }


  setStoreNameForStripe(data) {
    return this.http.post(`connect/session`, data).toPromise();
  }

  getStoreNameForStripe() {
    return this.http.get(`connect/session/store_name`).toPromise();
  }

  disconnectStripe() {
    return this.http.get(`stripe/disconnect`).toPromise();
  }



  disconnectMailchimp() {
    return this.http.get(`mailchimp/disconnect`).toPromise();
  }

  setAtrium(data) {
    return this.http.post(`store-config/update/atrium`, data).toPromise();
  }

  getAtrium() {
    return this.http.get(`store-config/atrium`).toPromise();
  }

  getinstituteList(){
    return this.http.get(`atrium/institutions`).toPromise();
  }

  saveDeliveryCondition(data) {
    return this.http.post(`delivery-settings`, data).toPromise();
  }
  checkInStorePick() {
    return this.http.get('delivery/check-settings');
  }
  getDeliveryDistance(id) {
    return this.http.get(`locations/${id}`).pipe(
      map(m => m.result.data),
      catchError(e => of(null))
    );
  }

  saveDeliveryDistance(data, id) {
    return this.http.post(`locations/${id}`, data).toPromise();
  }


  getDeliveryZone() {
    return this.http.get(`delivery-zone`).pipe(
      map(m => m.result.data),
      catchError(e => of([]))
    );
  }

  saveDeliveryZone(data, id) {
    if (id) {
      return this.http.post(`delivery-zone/${id}`, data).toPromise();
    } else {
      return this.http.post(`delivery-zone`, data).toPromise();
    }
  }

  deleteDeliveryZone(id) {
    return this.http.delete(`delivery-zone/${id}`).toPromise();
  }

  getLocationWithAddress() {
    return this.http.get(`locations/all`).pipe(
      map(m => m.result.data),
      catchError(e => of([]))
    );
  }

  getShippingSettings() {
    return this.http.get(`shipping/gateway/settings`);
  }

  getFedexService() {
    return this.http.get(`fedex-service-list`).pipe(
      map(m => m.result.data),
      catchError(e => of({}))
    );
  }

  getShippings() {
    return this.http.get(`carrier-list`);
  }

  changeShippingStatus(id, data) {
    return this.http.post(`shipping/change-status/${id}`, data).toPromise();
  }

  deleteShipping(id) {
    return this.http.delete(`delete-carrier/${id}`).toPromise();
  }

  
  syncShippingEngine(data) {
    return this.http.post(`shipping/shipengine/sync`, data).toPromise();
  }
  

  addUpdateShipping(data, id, edit, submit) {
    data["type"] = "online";
    let promise: Promise<any>;
    if (edit && id) {
      promise = this.http.post(`update-carrier/${id}`, data).toPromise();
    } else {
      promise = this.http.post(`connect-carrier`, data).toPromise();
    }
    return this.afterPromise(promise, submit);
  }
  updateStoreConfig(type,data) {
    return this.http.post(`store-config/update/${type}`, data).toPromise();
  }
  getStoreConfig(type) {
    return this.http.get(`store-config/${type}`).toPromise();
  }
  private afterPromise(promise, submit) {
    return promise
      .then(res => {
        // console.log(res);
        if (res.status === "OK" && "message" in res.result) {
          this.sendEmitedData(true, res.result.message, submit);
        } else {
          this.sendEmitedData(false, res.result.error, submit);
        }
        return this.falseLoader();
      })
      .catch(err => {
        console.log(err);
        this.sendEmitedData(
          false,
          "Something wrong!!! Please try again.",
          submit
        );
        return this.falseLoader();
      });
  }

  addUpdateGateway(data, id, edit, submit) {
    let promise: Promise<any>;
    if (edit && id) {
      promise = this.http.post(`payments/gateway/${id}`, data).toPromise();
    } else {
      promise = this.http.post(`payments/gateway`, data).toPromise();
    }
    return this.afterPromise(promise, submit);
  }

  private falseLoader() {
    const loader: Promise<any> = new Promise((resolve, reject) => {
      resolve(false);
    });
    return loader;
  }

  sendEmitedData(error, message, submit) {
    let emit_data;
    emit_data = { status: error, message: message };

    submit.emit(emit_data);
  }

  getState(id) {
    return this.http.get("state-by-country/" + id).pipe(
      map(m => m.result.data),
      catchError(e => of(null))
    );
  }

  getUSPSFormData() {
    return this.http.get("shipping/gateway/settings").pipe(
      map(m => m.result.data),
      catchError(e => of(null))
    );
  }

  additionalGateway(edit, id, data, submit) {
    data["type"] = "offline";
    let promise: Promise<any>;

    if (edit && id) {
      promise = this.http.post(`payments/gateway/${id}`, data).toPromise();
    } else {
      promise = this.http.post(`payments/gateway`, data).toPromise();
    }
    return this.afterPromise(promise, submit);
  }

  getAvailableShippingServices(carrier_id) {
    return this.http.get('carrier-services/' + carrier_id).toPromise();
  }

  saveShippingServices(data: any, carrier_id: number) {
    return this.http.post('carrier-services/' + carrier_id, data).toPromise();
  }
}
