import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SalesTaxComponent } from './sales-tax.component';
import { RouterModule, Routes } from '@angular/router';
import { ReactiveFormsModule } from '@angular/forms';

const routes: Routes = [
  {
    path: '',
    component: SalesTaxComponent
  }
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    ReactiveFormsModule
  ],
  declarations: [SalesTaxComponent]
})
export class SalesTaxModule { }
