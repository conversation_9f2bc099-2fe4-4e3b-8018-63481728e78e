import { Component, OnInit, ElementRef, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { ContentService } from '../setting-service/contents.service';
import { ActivatedRoute } from '@angular/router';
import { AlertService } from './../../../../modules/alert/alert.service';
import { HttpService } from './../../../../modules/http-with-injector/http.service';
import { isJson } from './../../../../globals/_classes/functions';

@Component({
  selector: 'app-sales-tax',
  templateUrl: './sales-tax.component.html',
  styleUrls: ['./sales-tax.component.css']
})
export class SalesTaxComponent implements OnInit {
  timeForm: FormGroup;
  sales_tax = {
    rent_tax: 0.0,
    buy_tax: 0.0,
    delivery_tax: 0.0
  };
  loading: boolean;
  tax_lookup_super: boolean;

  @ViewChild("hasCusAlert") alertContainer: ElementRef;

  constructor(
    private service: ContentService,
    private activeRoute: ActivatedRoute,
    private alert: AlertService,
    public http: HttpService,
    private fb: FormBuilder
  ) { }

  ngOnInit() {
    this.timeForm = this.fb.group({
      rent_tax: ["0.00"],
      buy_tax: ["0.00"],
      delivery_tax: ["0.00"],
      tax_apply_on: ['1'],
      store_lookup: [false]
    });
    this.getFormData();
  }

  getFormData() {
    this.service.getTimeZone().subscribe(
      res => {
        this.tax_lookup_super = res.timezone.tax.hasOwnProperty('lookup')
          ? res.timezone.tax.lookup
          : false;
        this.setTaxValue(res.timezone);
      });
  }

  setTaxValue(res) {
    if (res.hasOwnProperty("tax")) {
      this.timeForm.get("buy_tax").setValue(res.tax.buy_tax);
      this.timeForm.get("rent_tax").setValue(res.tax.rent_tax);
      this.timeForm.get("delivery_tax").setValue(res.tax.delivery_tax);
      this.timeForm.get("tax_apply_on").setValue(res.tax.tax_apply_on);
      if (this.tax_lookup_super) {
        this.timeForm.get("store_lookup").setValue(res.tax.store_lookup ? res.tax.store_lookup : false);
      }
    }
  }

    setContent(){
    this.http.get('contents').toPromise()
        .then(res => {
            if (res.status === 'OK' && res.result.data.length > 0) {
                const content = {};
                const data = res.result.data.filter(f => {
                    return f['config'].status === 1;
                });

                for (const c of data) {
                    const tag = this.formatTag(c.config.tag);
                    content[tag] = isJson(c.contents) ? JSON.parse(c.contents) : c.contents;
                }
                localStorage.setItem('contents', JSON.stringify(content));
                return content;
            } else {
                return {};
            }
        }).catch(err => console.log(err));
  }

  formatTag(text: String) {
    return text.replace(/(\-[a-z])/g, function ($1) { return $1.toUpperCase().replace('-', ''); });
  }

  save() {
    this.getTaxValue();
    this.loading = true;
    Object.assign(this.timeForm.value, this.sales_tax);
    console.log(this.timeForm.value);
    this.service
      .saveTimeZone(this.timeForm.value)
      .then(res => {
        this.loading = false;
        if (res.result) {
          this.alert.error(this.alertContainer, res.result.error, true, 3000);
        } else {
          this.getFormData();
          this.setContent();
          this.alert.success(
            this.alertContainer,
            "Settings Updated",
            true,
            3000
          );
        }
      })
      .catch(err => {
        this.loading = false;
        this.alert.error(
          this.alertContainer,
          "Something went wrong!!! Please try again.",
          true,
          3000
        );
      });
  }

  getTaxValue() {
    this.sales_tax.buy_tax = this.timeForm.get("buy_tax").value;
    this.sales_tax.rent_tax = this.timeForm.get("rent_tax").value;
    this.sales_tax.delivery_tax = this.timeForm.get("delivery_tax").value;
  }
}
