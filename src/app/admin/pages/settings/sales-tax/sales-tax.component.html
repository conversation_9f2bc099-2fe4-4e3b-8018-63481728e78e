<div class="m-subheader">
  <div class="d-flex align-items-center">
      <div class="mr-auto">
          <h3 class="m-subheader__title m-subheader__title--separator">
              Sales Tax
          </h3>
          <ul class="m-subheader__breadcrumbs m-nav m-nav--inline">
              <li class="m-nav__item m-nav__item--home">
                  <a routerLink="/admin" class="m-nav__link m-nav__link--icon">
                      <i class="m-nav__link-icon la la-home"></i>
                  </a>
              </li>
              <li class="m-nav__separator">
                  <i class="fa fa-angle-right"></i>
              </li>
              <li class="m-nav__item">
                  <a class="m-nav__link">
                      <span class="m-nav__link-text">
              Business Settings
            </span>
                  </a>
              </li>
              <li class="m-nav__separator" style="padding-left: 10px">
                  <i class="fa fa-angle-right"></i>
              </li>
              <li class="m-nav__item">
                  <a class="m-nav__link">
                      <span class="m-nav__link-text">
                        Sales Tax
            </span>
                  </a>
              </li>
          </ul>
      </div>
  </div>
</div>
<!-- END: Subheader -->
<div class="m-content fadeIn">
  <div class="m-portlet m-portlet--tabs">
      <div class="m-portlet__head title-head">
          <div class="m-portlet__head-caption">
              <div class="m-portlet__head-title">
                  <h3 class="m-portlet__head-text">Sales Tax</h3>
              </div>
          </div>
      </div>
      <div class="m-portlet__body">
          <div class="tab-content">
              <div class="custom-alert" #hasCusAlert></div>
              <form [formGroup]="timeForm" (ngSubmit)="save()">
                <div class="row">
                  <div class="col-md-3">
                      <h5>Sales Tax</h5>
                      <small>Manage how your store calculates and shows tax on your store.
                      </small>
                  </div>
                  <div class="col-md-9">
                      <div class="row">
                          <div class="col-md-12">
                              <div class="form-group">
                                  <div class="m-radio-inline">
                                      <label>Select Sale Tax Options</label>&nbsp;&nbsp;
                                      <label class="m-radio">
                                          <input [checked]="timeForm.get('tax_apply_on').value == '3'" type="radio"
                                              formControlName="tax_apply_on" name="tax_apply_on" value="3" />
                                          Rent Only <span></span>
                                      </label>
                                      <label class="m-radio">
                                          <input [checked]="timeForm.get('tax_apply_on').value == '2'" type="radio"
                                              formControlName="tax_apply_on" name="tax_apply_on" value="2" />
                                          Buy Only <span></span>
                                      </label>
                                      <label class="m-radio">
                                          <input [checked]="timeForm.get('tax_apply_on').value == '1'" type="radio"
                                              formControlName="tax_apply_on" name="tax_apply_on" value="1" />
                                          Both <span></span>
                                      </label>
                                      <label class="m-radio">
                                          <input [checked]="timeForm.get('tax_apply_on').value == 0" type="radio"
                                              formControlName="tax_apply_on" name="tax_apply_on" [value]="0" />
                                          None <span></span>
                                      </label>
                                  </div>
                              </div>
                          </div>
                      </div>
                      <div class="row">
                          <div class="col-md-4">
                              <div class="form-group m-form__group">
                                  <label for="">Sales Tax for Buy Items</label>
                                  <div class="input-group">
                                      <input numberOnly formControlName="buy_tax" type="text" class="form-control m-input"
                                          name="sales_tax" aria-describedby="basic-addon2" />
                                      <div class="input-group-append">
                                          <span class="input-group-text"><i class="fa fa-percent"></i>
                                          </span>
                                      </div>
                                  </div>
                              </div>
                          </div>
                          <div class="col-md-4">
                              <div class="form-group m-form__group">
                                  <label for="">Sales Tax for Rental Items</label>
                                  <div class="input-group">
                                      <input numberOnly formControlName="rent_tax" type="text" class="form-control m-input"
                                          name="sales_tax_rent" aria-describedby="basic-addon2" />
                                      <div class="input-group-append">
                                          <span class="input-group-text"><i class="fa fa-percent"></i>
                                          </span>
                                      </div>
                                  </div>
                              </div>
                          </div>
                          <div class="col-md-4">
                              <div class="form-group m-form__group">
                                  <label for="">Sales Tax on Delivery charge</label>
                                  <div class="input-group">
                                      <input numberOnly type="text" formControlName="delivery_tax" class="form-control m-input"
                                          name="sales_tax_delivery" aria-describedby="basic-addon2" />
                                      <div class="input-group-append">
                                          <span class="input-group-text" id="basic-addon2"><i class="fa fa-percent"></i>
                                          </span>
                                      </div>
                                  </div>
                              </div>
                          </div>
                      </div>
                      <div class="row" *ngIf="tax_lookup_super">
                        <div class="col-md-12">
                            <div class="form-group">
                                <div class="m-radio-inline">
                                    <label>Enable Tax Lookup API usage:</label>&nbsp;&nbsp;
                                    <label class="m-radio">
                                        <input type="radio" [checked]="timeForm.get('store_lookup').value == true"
                                            formControlName="store_lookup" name="store_lookup" [value]="true" />
                                        Yes <span></span>
                                    </label>
                                    <label class="m-radio">
                                        <input type="radio" [checked]="timeForm.get('store_lookup').value == false"
                                            formControlName="store_lookup" name="store_lookup" [value]="false" />
                                        No <span></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                      </div>
                  </div>
              </div>
                  <div class="m-portlet__foot m-portlet__foot--fit">
                      <div class="m-form__actions m--margin-top-30">
                          <button type="submit" [disabled]="loading" class="btn btn-dark m-btn--icon" [ngClass]="{ 'm-loader m-loader--light m-loader--right': loading }">
                              Submit
                          </button>
                      </div>
                  </div>
              </form>
          </div>
      </div>
  </div>
</div>