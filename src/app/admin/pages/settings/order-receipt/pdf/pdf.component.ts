import { FormBuilder, Validators, FormGroup } from '@angular/forms';
import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { GET_USER } from '../../../../../globals/_classes/functions';
import { Router } from '@angular/router';
import { OrderReceiptService, staticData, store_address, general, pdfvariables, PDF } from '../../setting-service/orderReceipt.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { PreviewDialogBoxComponent } from '../dialog-box/dialog-box.component';
import { AlertService } from '../../../../../modules/alert/alert.service';

@Component({
  selector: 'app-pdf',
  templateUrl: './pdf.component.html',
  styleUrls: ['./pdf.component.css']
})
export class PdfComponent implements OnInit {

  staticData = staticData;
  pdf: PDF = new PDF();
  loading: boolean;
  pdfvariables = pdfvariables;
  report_name;

  @ViewChild('hasCusAlert') alertContainer: ElementRef;
  form: FormGroup;
  controlArray: any = [];

  constructor(
    private alertS: AlertService,
    private router: Router,
    private modalService: NgbModal,
    private service: OrderReceiptService,
    private fb: FormBuilder
  ) { 
    this.pdf.type = 1;
    this.getPdf();
  }

  ngOnInit() {
    this.form = this.initForm();
    this.initPdf();
  }

  private initPdf() {
    this.pdf.general = general;
    this.pdf.store_address = store_address;
    this.showLogo();
  }

  getPdf() {
    this.service.getTemplateData(this.pdf.type).subscribe(
      res => {
        if(res) {
          this.pdf = res;
          this.form.patchValue(res.options);
          this.report_name=res.options ? res.options.report_title : 'SALES RECEIPT' ;
        } else {
          this.pdf = new PDF();
          this.pdf.type = 1;
          this.initPdf();
        }
      }
    );
  }

  private initForm(): FormGroup {
    const group = this.fb.group({
      report_title: [''],
      sub_total_text: [''],
      sub_total: [''],
      delivery_charge_text: [''],
      delivery_charge: [''],
      delivery_tax_text: [''],
      delivery_tax: [''],
      discount_text: [''],
      discount: [''],
      sales_tax_text: [''],
      sales_tax: [''],
      deposite_amount_text: [''],
      deposite_amount: [''],
      additional_charge_text: [''],
      additional_charge: [''],
      total_text: [''],
      total: [''],
    });
    // const group = this.fb.group({});
    // for(let control in this.pdf.options) {
    //   this.controlArray.push(
    //     {
    //       label: control,
    //       value: this.pdf.options[control],
    //       type: control.includes('_text') ? 'input' : 'checkbox'
    //     }
    //   );
    //   group.addControl(
    //     control, 
    //     this.fb.control(this.pdf.options[control], Validators.required)
    //   )
    // }
    // console.log(this.controlArray)
    return group;
  }

  changeLogo() {
    this.router.navigateByUrl('/admin/settings/contents/store-logo');
  }

  doNotShow() {
    this.pdf.logo = null;
  }

  showLogo() {
    this.pdf.logo = GET_USER().logo;
  }

  submit() {
    this.loading = true;
    this.pdf.options = this.form.getRawValue();
    this.pdf.options['report_title'] = this.report_name;
    this.service.saveTemplateData(this.pdf)
    .then(
      res => {
        this.loading = false;
        if(res.status === 'OK' && res.result.message) {
          this.alertS.success(this.alertContainer, res.result.message, true, 3000);
        } else {
          this.alertS.error(this.alertContainer, res.result.error, true, 3000);
        }
      }
    )
    .catch(
      err => {
        console.error(err);
        this.loading = false;
        this.alertS.error(this.alertContainer, 'Something went wrong!!! Please try again.', true, 3000);
      }
    )
  }

  preview() {
    const modalStatus = this.modalService.open(PreviewDialogBoxComponent, {
      centered: true,
      size: 'lg'
    });
    modalStatus.componentInstance.pdf = this.pdf;
    modalStatus.componentInstance.table = this.staticData;
    modalStatus.result;
  }

}
