<div class="custom-alert" #hasCusAlert></div>

<div class="m-subheader">
  <div class="d-flex align-items-center">
    <div class="mr-auto">
      <h3 class="m-subheader__title m-subheader__title--separator">
        Order Receipt
      </h3>
      <ul class="m-subheader__breadcrumbs m-nav m-nav--inline">
        <li class="m-nav__item m-nav__item--home">
          <a routerLink="/admin/dashboard" class="m-nav__link m-nav__link--icon">
            <i class="m-nav__link-icon la la-home"></i>
          </a>
        </li>
        <li class="m-nav__separator">
          <i class="fa fa-angle-right"></i>
        </li>
        <li class="m-nav__item">
          <a class="m-nav__link">
            <span class="m-nav__link-text">
              Settings
            </span>
          </a>
        </li>
        <li class="m-nav__separator" style="padding-left: 10px">
          <i class="fa fa-angle-right"></i>
        </li>
        <li class="m-nav__item">
          <a class="m-nav__link">
            <span class="m-nav__link-text">
              Order Receipt
            </span>
          </a>
        </li>
      </ul>
    </div>
  </div>
</div>
<!-- END: Subheader -->
<div class="m-content animated fadeIn">
  <div class="m-portlet">
    <div class="m-portlet__head title-head">
      <div class="m-portlet__head-caption">
        <div class="m-portlet__head-title">
          <h3 class="m-portlet__head-text colorPurpel">
            PDF Settings
          </h3>
          <div class="add-list-btn text-right">
            <button class="btn btn-dark" (click)="preview()">Preview</button>
          </div>
        </div>
      </div>
    </div>

    <div class="m-portlet__body">
      <div class="row">
        <div class="col-md-8">
          <div class="row">
            <div class="col-md-6">
              <h6 class="text-left">Store Logo</h6>
              <div class="company-logo text-left" style="max-width: 100%;" *ngIf="pdf.logo">
                <img class="img-fluid img-avatar img-thumbnail" [src]="pdf.logo"
                  onError="this.src='./assets/img/admin/logo/store_logo.png';" style="cursor:pointer; " />
                <div class="change-logo" (click)="changeLogo()">
                  Change Logo
                </div>
              </div>
              <p class="mtop-2 text-left">
                <button *ngIf="pdf.logo" class="btn btn-sm btn-danger" (click)="doNotShow()">
                  Don't Show
                </button>
                <button *ngIf="!pdf.logo" class="btn btn-sm btn-dark" (click)="showLogo()">
                  Show Logo
                </button>
              </p>
            </div>

            <div class="col-md-6 sales-reciept">
              <div class="row">
                <div class="col-md-12">
                  <input type="text" [(ngModel)]="report_name" class="form-control m-input float-right" />
                </div>
                <div class="col-md-12 text-right">
                  <p class="pt-2 mt-0">Date: Order Date</p>
                </div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <h6>Store Address</h6>
              <app-summernote [className]="'address'" [value]="pdf.store_address" [height]="180"
                (changeValue)="pdf.store_address = $event"></app-summernote>
            </div>
            <div class="col-md-1"></div>
            <div class="col-md-5">
              <h6 class="text-right">General</h6>
              <app-summernote [className]="'general'" [value]="pdf.general" [height]="180"
                (changeValue)="pdf.general = $event"></app-summernote>
            </div>
          </div>
          <div style="margin-top: 30px;">
            <div class="table-responsive">
              <table class="table" style="padding-bottom: 10px;">
                <thead>
                  <tr>
                    <th *ngFor="let h of staticData.head">{{ h }}</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let b of staticData.body">
                    <td>{{ b.qty }}</td>
                    <td>{{ b.id }}</td>
                    <td>
                      <div>{{ b.name }}</div>
                      <div>Rent Start: {{ b.start }}</div>
                      <div>Rent End: {{ b.end }}</div>
                    </td>
                    <td>{{ b.unit }}</td>
                    <td>{{ b.tax }}</td>
                    <td>{{ b.sub }}</td>
                  </tr>
                </tbody>
              </table>
              <form [formGroup]="form" (submit)="submit()" class="pdfsubtotal-form">
                <div class="text-right d-flex mb-3 pd">
                  <label class="m-checkbox mt-2">
                    <input type="checkbox" name="subTotal" formControlName="sub_total"> <span></span>
                  </label>
                  <input type="text" class="form-control" formControlName="sub_total_text">
                  <div class="mt-2 amount">
                    $188.50
                  </div>
                </div>

                <div class="text-right d-flex mb-3">
                  <label class="m-checkbox mt-2">
                    <input type="checkbox" name="delivery_charge" formControlName="delivery_charge"> <span></span>
                  </label>
                  <input type="text" class="form-control" formControlName="delivery_charge_text">
                  <div class="mt-2 amount">
                    $18.00
                  </div>
                </div>

                <div class="text-right d-flex mb-3">
                  <label class="m-checkbox mt-2">
                    <input type="checkbox" name="delivery_tax" formControlName="delivery_tax"> <span></span>
                  </label>
                  <input type="text" class="form-control" formControlName="delivery_tax_text">
                  <div class="mt-2 amount">
                    $8.00
                  </div>
                </div>

                <div class="text-right d-flex mb-3">
                  <label class="m-checkbox mt-2">
                    <input type="checkbox" name="discount" formControlName="discount"> <span></span>
                  </label>
                  <input type="text" class="form-control" formControlName="discount_text">
                  <div class="mt-2 amount">
                    $0.00
                  </div>
                </div>

                <div class="text-right d-flex mb-3">
                  <label class="m-checkbox mt-2">
                    <input type="checkbox" name="sales_tax" formControlName="sales_tax"> <span></span>
                  </label>
                  <input type="text" class="form-control" formControlName="sales_tax_text">
                  <div class="mt-2 amount">
                    $0.00
                  </div>
                </div>

                <div class="text-right d-flex mb-3">
                  <label class="m-checkbox mt-2">
                    <input type="checkbox" name="deposite_amount" formControlName="deposite_amount"> <span></span>
                  </label>
                  <input type="text" class="form-control" formControlName="deposite_amount_text">
                  <div class="mt-2 amount">
                    $0.00
                  </div>
                </div>

                <div class="text-right d-flex mb-3">
                  <label class="m-checkbox mt-2">
                    <input type="checkbox" name="additional_charge" formControlName="additional_charge"> <span></span>
                  </label>
                  <input type="text" class="form-control" formControlName="additional_charge_text">
                  <div class="mt-2 amount">
                    $0.00
                  </div>
                </div>

                <div class="text-right d-flex mb-3">
                  <label class="m-checkbox mt-2">
                    <input type="checkbox" name="total" formControlName="total"> <span></span>
                  </label>
                  <input type="text" class="form-control" formControlName="total_text">
                  <div class="mt-2 amount">
                    $214.50
                  </div>
                </div>

                <!-- <button type="submit">submit</button> -->
              </form>
            </div>
          </div>
          <div class="row mtop-2">
            <div class="col-md-12">
              <h6>Rental Terms & Conditions</h6>
              <app-summernote [className]="'terms-conditions'" [value]="pdf.terms_conditions" [height]="220"
                (changeValue)="pdf.terms_conditions = $event"></app-summernote>
            </div>
          </div>
          <div class="row mtop-2">
            <div class="col-md-12">
              <h6>Client Message Field #1</h6>
              <app-summernote [className]="'messageField-1'" [value]="pdf.message_1" [height]="180"
                (changeValue)="pdf.message_1 = $event"></app-summernote>
            </div>
          </div>
          <div class="row mtop-2">
            <div class="col-md-12">
              <h6>Client Message Field #2</h6>
              <app-summernote [className]="'messageField-2'" [value]="pdf.message_2" [height]="180"
                (changeValue)="pdf.message_2 = $event"></app-summernote>
            </div>
          </div>

          <div class="mtop-2">
            <button class="btn btn-dark" [ngClass]="{
                'm-loader m-loader--light m-loader--right': loading
              }" (click)="submit()">
              Submit
            </button>
          </div>
        </div>
        <div class="col-md-3">
          <h5>Receipt Variables</h5>
          <table class="table table-hover table-striped">
            <tr *ngFor="let v of pdfvariables">
              <td>{{ v.label }}</td>
              <td>{{ v.value }}</td>
            </tr>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>