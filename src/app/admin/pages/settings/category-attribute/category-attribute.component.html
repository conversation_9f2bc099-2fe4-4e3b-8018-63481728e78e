<div class="custom-alert" #hasCusAlert></div>

<div class="m-subheader">
  <div class="d-flex align-items-center">
      <div class="mr-auto">
          <h3 class="m-subheader__tvariantitle m-subheader__title--separator">
              Manage Categories Variant
          </h3>
          <ul class="m-subheader__breadcrumbs m-nav m-nav--inline">
            <li class="m-nav__item m-nav__item--home">
              <a routerLink="/admin" class="m-nav__link m-nav__link--icon">
                <i class="m-nav__link-icon la la-home"></i>
              </a>
            </li>
            <li class="m-nav__separator">
              <i class="fa fa-angle-right"></i>
            </li>
            <li class="m-nav__item">
              <a class="m-nav__link">
                <span class="m-nav__link-text">
                  Settings
                </span>
              </a>
            </li>
            <li class="m-nav__separator" style="padding-left: 10px">
              <i class="fa fa-angle-right"></i>
            </li>
            <li class="m-nav__item">
              <a class="m-nav__link">
                <span class="m-nav__link-text">
                  Categories-Variant
                </span>
              </a>
            </li>
          </ul>
      </div>
  </div>
</div>
<!-- END: Subheader -->
<div class="m-content animated fadeIn">
  <div class="m-portlet row">
    <div class="m-portlet__body col-md-6 border-line">
        <div class="form-group row">
          <h4 class="colorPurpel col-12" for="ct">Selected Category</h4>
          <div class="col-md-8">
            <select name="ct" id="ct" class="form-control" #main (change)="callList(main.value)" style="cursor: pointer;">
              <option *ngFor="let a of mainCate" [value]="a.id">{{a.text}}</option>
            </select>
          </div>
        </div>
      <!--begin::Section-->
        <div class="table-responsive">
					<table class="table table-hover">
						<thead>
							<tr>
								<th>
                  Category 
								</th>
								<th>
								  Variant
                </th>
							</tr>
						</thead>
						<tbody *ngIf="CategoriesAttributes.length>0; else noData">
							<tr *ngFor="let list of CategoriesAttributes; let id='index'; trackBy: trackAttribute">
								<td>
									{{list?.name}}
								</td>
								<td>
                  <div class="row" *ngFor="let attr of list?.attribute_sets; let i='index'">
                    <div class="col-8">{{attr.name}}</div>
                    <div class="col-4">        
                      <a id="m_quick_sidebar_toggle" (click)="deleteAttribute(list.id, attr.id)"
                        class="m-portlet__nav-link btn m-btn m-btn--hover-danger m-btn--icon m-btn--icon-only m-btn--pill">
                        <i class="la la-trash"></i>
                      </a>
                    </div>
                  </div>
								</td>
               </tr>
            </tbody>
            <ng-template #noData>
              <tbody>
                <tr>
                  <td colspan="3">
                    <h5 class="text-center">
                      No Data Found
                    </h5>
                  </td>
                </tr>
              </tbody>
            </ng-template>
          </table>
				</div>
      <!--end::Section-->  
    </div>

    <div class="m-portlet__body col-md-6">
      <!--begin::Section-->
      <h4 class="colorPurpel">Add Categories Variant</h4>
      <div class="row" style="padding-top: 20px;">
        <div class="col-7">
          <div id="categoryAttr-jsTree" class="tree-demo"></div>
        </div>
        <div class="col-5">
          <div class="form-group">
            <label for="attr">Select Variant Set</label>
            <select name="attr" id="attr" class="form-control" [(ngModel)]="sendData.attribute_set_id" style="cursor: pointer;">
              <option value="null">-Select Attribute Set-</option>
              <option *ngFor="let a of attributeSets" [value]="a.id">{{a.name}}</option>
            </select>
          </div>
          <div class="form-group">
            <div *ngIf="loader; else button" class="m-loader m-loader--brand" 
              style="width: 30px; display: inline-block;"></div>
            <ng-template #button>
              <button type="button" [disabled]="!sendData.attribute_set_id || sendData.categories.length == 0" class="btn btn-brand" (click)="addCateAttr()">Add</button>
            </ng-template>
          </div>
        </div>
      </div>
      <!--end::Section-->  
    </div>
  </div>
</div>


