import {
  Component,
  OnInit,
  ViewChild,
  <PERSON>ement<PERSON>ef,
  HostListener,
  ViewContainerRef,
  ComponentFactoryResolver,
  ComponentRef
} from "@angular/core";
import { AlertService } from "../../../../modules/alert/alert.service";
import { SettingService } from "../setting-service/setting.service";
import { SidebarService } from "../../sidebar-service/sidebar.service";
import { Gateway } from "../models/settings.models";
import { map, catchError } from "rxjs/operators";
import { of } from "rxjs";
import { AddFormComponent } from "./add-form/add-form.component";
import { AddAdditionalGatewayComponent } from "./add-additional-gateway/add-additional-gateway.component";
import { ActivatedRoute } from "@angular/router";

@Component({
  selector: "app-payment-gateway",
  templateUrl: "./payment-gateway.component.html",
  styleUrls: ["./payment-gateway.component.css"]
})
export class PaymentGatewayComponent implements OnInit {
  loader: boolean;
  gateways: Gateway[] = [];
  sideBaropen: boolean;
  gatewayObj;
  componentRef: ComponentRef<any>;

  @ViewChild("hasCusAlert", {static: true}) alertContainer: ElementRef;
  @ViewChild("addFormCard", { read: ViewContainerRef })
  cardForm: ViewContainerRef;

  @HostListener("window:resize", ["$event"])
  onResize(event) {
    if (this.sideBaropen) {
      $(".native-routing").css("display", "block");
      this.openSidebar();
    }
  }

  constructor(
    private alert: AlertService,
    private settingS: SettingService,
    private sidebarS: SidebarService,
    private route: ActivatedRoute,
    private resolver: ComponentFactoryResolver
  ) {
    this.getGatewaySettings();
  }

  ngOnInit() {
    this.getGateways();

    this.route.queryParams.subscribe(params => {
      if (params.code) {
        this.settingS
          .setStripeToken({code:params.code})
          .then(res => {
            if (res.status === "OK") {
              this.alert.success(this.alertContainer,res.result.message,true,5000);
              this.editGateway(res.result.data);
            }
            else{
              this.alert.error(this.alertContainer,res.result.message,true,5000);
            }
          })
          .catch(err => {
            
            console.log(err)
          });
      }
    });
  }

  ngAfterViewInit() {
    //window.scrollTo(0, 0);
    this.closeSidebar();
    this.executeAction();
  }

  trackGateway(index, gate) {
    return gate.id ? gate.id : null;
  }

  editGateway(data: Gateway) {
    this.openSidebar({ edit: true, data: data, type: data.type });
  }
  addGateway() {
    this.openSidebar({ edit: false, data: {}, type: "online" });
  }
  addOfflineGateway() {
    this.openSidebar({ edit: false, data: {}, type: "offline" });
  }

  openSidebar(data?) {
    // let w =
    //   $(".global-sidebar-wrapper").width() +
    //   ($(window).width() > 992 ? 25 : 0) +
    //   "px";
    // this.sideBaropen = true;
    // $(".native-routing").css("display", "block");
    // this.sidebarS.openSidebar(w);
    if (data) {
      this.settingS.editGateForm.next(data);
      this.createComponent(data);
    }
  }

  closeSidebar() {
    $(".close-sidebar").click(e => {
      e.preventDefault();
      this.executeAction();
    });
    $(".close-sidebar-upper").click(e => {
      e.preventDefault();
      this.executeAction();
    });
  }

  executeAction() {
    this.sideBaropen = false;
    this.sidebarS.removeSidebar();
    $(".native-routing").css("display", "none");
    if (this.componentRef) {
      this.componentRef.destroy();
    }
  }

  allertShow(e) {
    if (e.message) {
      if (e.status) {
        this.alert.success(this.alertContainer, e.message, true, 5000);
        this.executeAction();
        this.getGateways();
      } else {
        this.alert.error(this.alertContainer, e.message, true, 5000);
      }
    }
  }

  private getGatewaySettings() {
    this.settingS
      .getGatewaySettings()
      .pipe(
        map(res => {
          return res.result.data;
        }),
        catchError(err => {
          return of([]);
        })
      )
      .subscribe(res => {
        this.gatewayObj = this.settingS.formatGateWay(res);
      });
  }

  getGateways() {
    this.loader = true;
    this.settingS
      .getGateways()
      .pipe(
        map(res => {
          return res.result.data;
        }),
        catchError(err => {
          this.loader = false;
          return of([]);
        })
      )
      .subscribe(res => {
        this.loader = false;
        this.gateways = res;
      });
    this.executeAction();
  }

  isRealPayment(gateway) {
    if (gateway.type !== 'offline') {
      return 'Yes';
    } else {
      if (gateway.config.hasOwnProperty('is_paid')) {
        const str = gateway.config.is_paid ? 'Yes' : 'No';
        return str;
      } else {
        return 'No';
      }
    }
  }

  private createComponent(data) {
    console.log(data);
    if (data.type === "online") {
      const factory = this.resolver.resolveComponentFactory(AddFormComponent);
      this.cardForm.clear();
      this.componentRef = this.cardForm.createComponent(factory);
      this.componentRef.instance.gatewayAll = this.gatewayObj["all"];
      this.componentRef.instance.methods = this.gatewayObj["list"];
      this.componentRef.instance.allertShow.subscribe(e => this.allertShow(e));
    } else {
      const factory = this.resolver.resolveComponentFactory(
        AddAdditionalGatewayComponent
      );
      this.cardForm.clear();
      this.componentRef = this.cardForm.createComponent(factory);
      // this.componentRef.instance.gatewayAll = this.gatewayObj['all'];
      // this.componentRef.instance.methods = this.gatewayObj['list'];
      this.componentRef.instance.allertShow.subscribe(e => this.allertShow(e));
    }
  }
}
