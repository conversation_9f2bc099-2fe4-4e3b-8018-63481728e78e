import { Component, OnInit, EventEmitter, Output } from "@angular/core";
import { FormGroup, FormBuilder, Validators } from "@angular/forms";
import { SettingService } from "../../setting-service/setting.service";
import { HttpService } from "../../../../../modules/http-with-injector/http.service";
import { ContentService } from "../../setting-service/contents.service";

declare let $: any;

@Component({
  selector: "app-add-additional-gateway",
  templateUrl: "./add-additional-gateway.component.html",
  styleUrls: ["./add-additional-gateway.component.css"]
})
export class AddAdditionalGatewayComponent implements OnInit {
  @Output("allertShow") allertShow = new EventEmitter();
  form: FormGroup;
  allStatus = [{ name: "Active", value: 1 }, { name: "Inactive", value: 0 }];
  editMode: boolean;
  loader: boolean;
  editId: number;
  editData: any;
  constructor(
    private http: HttpService,
    private fb: FormBuilder,
    private service: SettingService,
    private contentService: ContentService
  ) {
    this.form = this.fb.group({
      name: ["", Validators.required],
      instructions: [''],
      add_note: [true],
      is_online: [false],
      is_admin: [false],
      status: [this.allStatus[0].value],
      is_paid: [false],
    });

    this.service.editGateForm.subscribe(res => {
      if (res.edit) {
        this.editMode = true;
        this.form.patchValue(res.data);
        this.editId = res.data.id;
        this.editData = res.data;
        this.form.get('add_note').setValue(res.data.config.add_note);
        this.form.get('is_online').setValue(res.data.is_online);
        this.form.get('is_admin').setValue(res.data.is_admin);
        this.form.get('is_paid').setValue(res.data.config.hasOwnProperty('is_paid') ? res.data.config.is_paid : false);
        this.form.get('instructions').setValue(res.data.config.instructions);
      }
    });
  }

  ngOnInit() { }
  submit() {
    this.loader = true;
    const obj = this.form.getRawValue();
    obj["config"] = { 
      add_note: obj.add_note,
      instructions: obj.instructions,
      is_paid: obj.is_paid
    };
    delete obj.add_note;
    delete obj.instructions;
    delete obj.is_paid;
    this.service
      .additionalGateway(this.editMode, this.editId, obj, this.allertShow)
      .then(res => (this.loader = false));
  }

  ngAfterViewInit() {
    this._description();
  }

  private _description = () => {
    $('.summernote-content').summernote(this.contentService.summarNote());

      $('.summernote-content').on('summernote.blur', () => {
        this.form.get('instructions').setValue($('.summernote-content').summernote('code'))
      });
      if (this.editMode && this.editId) {
        $('.summernote-content').summernote('code', this.editData.config.instructions);
      }
  }
}
