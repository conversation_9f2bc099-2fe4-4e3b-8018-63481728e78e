<div class="col-md-12 mt-5 ">
  <h3>Manual payment method</h3>
  <form [formGroup]="form" class="row" (submit)="submit()">
    <div class="form-group col-md-7">
      <label for="method_name">Payment Method Name </label>
      <input 
        formControlName="name"
        placeholder="Name of your payment method"
        type="text" 
        name="method_name" 
        id="method_name" 
        class=" form-control m-input">
    </div>
    <div class="form-group col-md-7">
      <label for="method_name label-ins">Instructions </label>
      <small class="small-label">Instructions to your customer on how the order will be processed.</small>
        <div class="summernote-content"></div>
    </div>
    <div class="form-group col-md-12">
      <label class="m-checkbox">
        <input formControlName="add_note" type="checkbox" name="add_note" [checked]="form.get('add_note').value">
        Add Note ?
        <span></span>
      </label>
    </div>

    <!-- <div class="form-group col-md-12">
      <label class="m-checkbox">
        <input formControlName="is_paid" type="checkbox" name="is_paid" [checked]="form.get('is_paid').value">
        Consider order paid ?
        <span></span>
      </label>
    </div> -->


    <div class="form-group col-md-4">
      <label class="m-checkbox">
        <input type="checkbox" name="is_online"  formControlName="is_online">
        Show in Online Store/WP Plugin
        <span></span>
      </label>
    </div>

    <div class="form-group col-md-8">
      <label class="m-checkbox">
        <input type="checkbox" name="is_admin"  formControlName="is_admin">
        Show in Admin/POS
        <span></span>
      </label>
    </div>

    <div class="form-group col-md-8">
      <div class="order-shipped row">
        <div class="col-xl-4 col-lg-4 col-md-4 col-sm-12">
          <label>Consider order paid?</label>
        </div>
        <div class="col-xl-8 col-lg-8 col-md-8 col-sm-12">
          <div class="m-radio-inline">
            <label class="m-radio">
              <input type="radio" name="is_paid" formControlName="is_paid"
                [value]="true" />
                Real payment <span></span>
            </label>
            <label class="m-radio">
              <input type="radio" name="is_paid" formControlName="is_paid"
                [value]="false" />
                Promise to pay <span></span>
            </label>
          </div>
        </div>
      </div>
    </div>

   

    <div class="form-group col-md-7" *ngIf="editMode">
      <label for="status">Select Status</label>
      <select formControlName="status" id="status" class="form-control m-select" name="status">
        <option *ngFor="let item of allStatus" [value]="item.value">{{item.name}}</option>
      </select>
    </div>
    <div class="m-portlet__foot m-portlet__foot--fit col-md-7">
      <div class="m-form__actions m-form__actions" style="padding: 20px 0px;">
        <div class="m-loader m-loader--brand" *ngIf="loader; else btnSubmit" style="width: 30px; padding-left: 30px; display: inline-block;"></div>
        <ng-template #btnSubmit>
          <button type="submit" class="btn btn-brand" [disabled]="!form.valid">
            <i class="fa fa-save"></i>
            <span style="padding-left:10px;">Submit</span>
          </button>
        </ng-template>
      </div>
    </div>
  </form>
</div>
