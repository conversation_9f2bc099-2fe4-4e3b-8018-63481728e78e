<div class="custom-alert card-connect-alert" #hasCusAlert></div>

<h5 *ngIf="!stripeConfigDisabled">{{gateway.name}}</h5>
<form class="mt-3 m-form m-form--fit m-form--label-align-right" [formGroup]="gateForm" (ngSubmit)="save()">
  <div class="row" *ngIf="!stripeConfigDisabled">
    <div class="col-md-6 form-group" *ngFor="let field of fields; let i=index" style="position: relative;">
      <label *ngIf="field.toLowerCase() !=='connect'" class="label-upper" [attr.for]="field">{{field}}*</label>
      <input *ngIf="field.toLowerCase() !=='connect'" id="{{field}}" #forPassword
        type="{{field.toLowerCase()=='password' ? 'password' : 'text'}}" class="form-control m-input" name="{{field}}"
        formControlName="{{field}}" autocomplete="nope">
      <span class="showPassword" *ngIf="field.toLowerCase()=='password'" (click)="showPass(forPassword)">
        <i class="fa fa-eye"></i>
      </span>
    </div>
  </div>

  <div class="m-portlet__foot m-portlet__foot--fit">
    <div class="m-form__actions m-form__actions" style="padding: 20px 0px;">
      <div class="m-loader m-loader--brand" *ngIf="loader; else btnSubmit"
        style="width: 30px; padding-left: 30px; display: inline-block;"></div>
      <ng-template #btnSubmit>
        <span *ngIf="gateway?.name=='Stripe' && stripeConfigDisabled">
          <a (click)="onClickConnectStripe()" *ngIf="is_showDisconnectBtn==false;else showDisconnectBtn" class="btn stripe-btn stripe-btn-top">
          <span>S</span>Connect with stripe
        </a>&nbsp;
        <ng-template #showDisconnectBtn>
          <a style="color: #ffffff;" (click)="onClickDisconnectStripe()" class="btn btn-danger">
            Disconnect stripe
          </a>&nbsp;
        </ng-template>
      </span>
        <button type="submit" class="btn btn-brand" [disabled]="!gateForm.valid">
          <i class="fa fa-save"></i>
          <span style="padding-left:10px;">Submit</span>
        </button>
        <div *ngIf="gateway?.name=='Stripe' && !stripeConfigDisabled">
          <div *ngIf="is_showDisconnectBtn==false">
            <br />
            <span class="or">OR</span>
            <br /> <br />
            <a (click)="onClickConnectStripe()" class="btn stripe-btn">
              <span>S</span>Connect with stripe
            </a>
          </div>

          <div *ngIf="is_showDisconnectBtn">
            <br />
            <span class="or">OR</span>
            <br /> <br />
            <a style="color: #ffffff;" (click)="onClickDisconnectStripe()" class="btn btn-danger">
              Disconnect stripe
            </a>
          </div>
        </div>
      </ng-template>
    </div>
  </div>

</form>