import { Component, OnInit, Input, Output, EventE<PERSON>ter, OnDestroy, ViewChild, ElementRef } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { SettingService } from '../../../setting-service/setting.service';
import { Gateway } from '../../../models/settings.models';
import { getSubdomain } from '../../../../../../globals/_classes/functions';
import { environment } from '../../../../../../../environments/environment';
import { AlertService } from '../../../../../../modules/alert/alert.service';
import { Helpers } from '../../../../../../helpers';

@Component({
  selector: 'app-card-conect',
  templateUrl: './card-conect.component.html',
  styleUrls: ['./card-conect.component.css']
})
export class CardConectComponent implements OnInit {

  gateForm: FormGroup;
  loader: boolean;
  is_showDisconnectBtn=false;
  

  @Input('gateway') gateway: any;
  @Input('fields') fields: string[];
  @Input('id') id;
  @Input('edit') edit: boolean;
  @Output('submit') submit:EventEmitter <any> = new EventEmitter();

  @ViewChild("hasCusAlert") alertContainer: ElementRef;
  stripeConfigDisabled = false;


  constructor(
    private service: SettingService, 
    private fb: FormBuilder,
    private settingS: SettingService,
    private alert: AlertService
    ) {}

  ngOnInit() {
    console.log(this.gateway, this.fields);
    this.gateForm = this.fb.group(this.gateway.config);
    for (let [key, value] of Object.entries(this.gateway.config)) {
      this.gateForm.get(key).setValidators([Validators.required]);
      this.gateForm.get(key).updateValueAndValidity();
    }
    if (this.gateway.name === 'Stripe' &&  this.gateway.config.secret_key == ''){
      this.stripeConfigDisabled = true;
      this.gateForm.get('publishable_key').setValidators([]);
      this.gateForm.get('publishable_key').updateValueAndValidity();
      this.gateForm.get('secret_key').setValidators([]);
      this.gateForm.get('secret_key').updateValueAndValidity();
    } else {
      this.stripeConfigDisabled = false;
    }
    if(this.edit) {
      this.is_showDisconnectBtn=(this.gateway.config.hasOwnProperty('connect') && this.gateway.config['connect'].hasOwnProperty('stripe_user_id')) ? true:false;
      this.gateForm.patchValue(this.gateway.config);
    }
  }

  showPass(pass: any) {
    const type = pass.getAttribute('type');
    if(type==='password') {
      pass.setAttribute('type', 'text');
    } else {
      pass.setAttribute('type', 'password');
    }
  }


  save() {
    this.loader = true;
    this.gateway.status = +this.gateway.status;
    this.gateway.config = this.gateForm.value;
    // console.log(this.gateway);
    this.service.addUpdateGateway(this.gateway, this.id, this.edit, this.submit).then(
      res => this.loader = false
    );
    
  }


  onClickConnectStripe(){
    Helpers.setLoading(true);
    let redirect_url=`${environment.protocal}${environment.patnerHoshName}/auth/stripe`;
    this.settingS
          .setStoreNameForStripe({key:'store_name',value:getSubdomain()})
          .then(res => {
            if (res.status === "OK") {
              //window.open('https://connect.stripe.com/oauth/authorize?response_type=code&client_id='+environment.stripe_client_id+'&scope=read_write&redirect_uri='+redirect_url,"_self");
            }
            Helpers.setLoading(false);
          })
          .catch(err => {
            console.log(err)
          });
  
  }


  onClickDisconnectStripe(){
    Helpers.setLoading(true);
    this.settingS
          .disconnectStripe()
          .then(res => {
            if (res.status === "OK") {
              this.is_showDisconnectBtn=false;
               this.alert.success(this.alertContainer,res.result.message,true,5000);
            }
            else{
              this.is_showDisconnectBtn=true;
              this.alert.error(this.alertContainer,res.result.message,true,5000);
            }
            Helpers.setLoading(false);
          })
          .catch(err => {
            console.log(err)
          });
  
  }

 

}
