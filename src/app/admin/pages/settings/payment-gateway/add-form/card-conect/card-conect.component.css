.label-upper {
    text-transform: capitalize;
}
.showPassword {
    position: absolute;
    display: flex;
    width: 30px;
    height: 37px;
    align-items: center;
    top: 30px;
    right: 10px;
    cursor: pointer;
}

.or{
    padding: 10px;
    border-radius: 50%;
    border: 1px solid #bbbbbb;
}
.stripe-btn {
    color: #fff !important;
    background-image: linear-gradient(#039df5, #0479bb, #039df5);
}
.stripe-btn span {
    border-right: 1px solid #067abd;
    padding-right: 10px;
    margin-right: 10px;
    font-weight: bold;
    font-size: 16px;
    color: #fff;
}
.stripe-btn:hover,
.stripe-btn:focus,
.stripe-btn:active {
    color: #fff !important;
    background-image: linear-gradient(#039df5, #0479bb, #039df5);
}
.btn.stripe-btn:not(:disabled):not(.disabled):active, 
.btn.stripe-btn:not(:disabled):not(.disabled).active {
    color: #fff !important;
    background-image: linear-gradient(#039df5, #0479bb, #039df5) !important;
}
.card-connect-alert {
    position: fixed;
    right: 0;
    top: 25px !important;
}
.stripe-btn-top {
    margin-right: 10px !important;
    padding: 7.5px !important;
}