
<section style="padding-top: 35px;">
  <div class="row">
    <div class="col-sm-6">
      <h4 *ngIf="edit; else heading">Update Gateway</h4>
      <ng-template #heading>
        <h4>Add Gateway</h4>
      </ng-template>
      <form class="mt-3 m-form m-form--fit m-form--label-align-right">
        <div class="row">
          <div class="col-sm-6"  *ngIf="edit; else methodSelection">
            <div class="form-group">
              <label for="name">Select Payment Method</label>
              <select id="name" class="form-control m-select" name="name" [(ngModel)]="selectedMethod" (change)="changeMethod()" [disabled]="edit">
                <option *ngFor="let m of methods" [value]="m">{{m=='goMerchant' ? 'RentMy Payments' : m}}</option>
              </select>
            </div>
          </div>
          <ng-template #methodSelection>
            <div class="form-group">
              <label for="name">Select Payment Method</label>
              <select id="name" class="form-control m-select" name="name" [(ngModel)]="selectedMethod" (change)="changeMethod()" [disabled]="edit">
                <option *ngFor="let m of methods" [value]="m">{{m=='goMerchant' ? 'RentMy Payments' : m}} </option>
              </select>
            </div>
          </ng-template>
          <div class="col-sm-6">
            <div class="form-group">
              <label for="status">Select Status</label>
              <select id="status" class="form-control m-select" name="status" [(ngModel)]="gateway.status">
                <option value="1">Active</option>
                <option value="0">Inactive</option>
              </select>
            </div>
          </div>
          <div class="col-sm-6">
            <div class="form-group">
              <label class="m-checkbox">
                <input type="checkbox" name="is_online" [(ngModel)]="gateway.is_online">
                Show in Online Store/WP Plugin
                <span></span>
              </label>
            </div>
          </div>

          <div class="col-sm-6">
            <div class="form-group">
              <label class="m-checkbox">
                <input type="checkbox" name="is_admin" [(ngModel)]="gateway.is_admin">
                Show in Admin/POS
                <span></span>
              </label>
            </div>
          </div>
        </div>
        
        <div style="padding-top:20px;">
          <ng-template #cardConect></ng-template>
        </div>
      </form>
    </div>
    <div class="col-sm-6">
      <app-instructions [name]="selectedMethod"></app-instructions>
    </div>
  </div>
</section>
