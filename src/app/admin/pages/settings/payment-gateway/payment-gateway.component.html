<div class="custom-alert" #hasCusAlert></div>

<div class="m-subheader">
  <div class="d-flex align-items-center">
    <div class="mr-auto">
      <h3 class="m-subheader__title m-subheader__title--separator">
        Payment Methods
      </h3>
      <ul class="m-subheader__breadcrumbs m-nav m-nav--inline">
        <li class="m-nav__item m-nav__item--home">
          <a routerLink="/admin" class="m-nav__link m-nav__link--icon">
            <i class="m-nav__link-icon la la-home"></i>
          </a>
        </li>
        <li class="m-nav__separator">
          <i class="fa fa-angle-right"></i>
        </li>
        <li class="m-nav__item">
          <a class="m-nav__link">
            <span class="m-nav__link-text">
              Settings
            </span>
          </a>
        </li>
        <li class="m-nav__separator" style="padding-left: 10px">
          <i class="fa fa-angle-right"></i>
        </li>
        <li class="m-nav__item">
          <a class="m-nav__link">
            <span class="m-nav__link-text">
              Payment Methods
            </span>
          </a>
        </li>
      </ul>
    </div>
  </div>
</div>
<!-- END: Subheader -->
<div class="m-content animated fadeIn">
  <div class="m-portlet m-portlet--tabs">
    <div class="m-portlet__body">
      <div style="padding-bottom: 20px;">
        <button class="btn btn-brand add-gateway" (click)="addGateway()">
          Add Online Gateway
        </button>
        <button class="btn btn-brand ml-5 add-gateway" (click)="addOfflineGateway()">
          Add Manual payments
        </button>
      </div>
      <div class="m-section">
        <div class="m-section__content price-table table-responsive" style="position:relative">
          <div *ngIf="loader" class="table-load m-loader m-loader--brand table-responsive" ></div>
          <table class="table table-hover" style="padding-bottom: 10px;">
            <thead>
              <tr>
                <th>Payment Method</th>
                <th>Manual payment</th>
                <th>Online Store</th>
                <th>Admin/POS</th>
                <th>Real Payment</th>
                <th>Status</th>
                <th>Action</th>
              </tr>
            </thead>
            <tbody *ngIf="gateways.length; else noFound">
              <tr *ngFor="let gate of gateways; let i = 'index'; trackBy: trackGateway; let o='odd'; let e='even'" [ngClass]="{'odd-tr':o, 'even-tr':e}">
                <th>{{gate.name == 'goMerchant' ? 'RentMy Payments' : gate.name }}</th>
                <td>{{gate.type=='online'? 'No':'Yes'}}</td>
                <td [ngClass]="{'success-mgs': gate.is_online, 'error': !gate.is_online}">{{gate?.is_online ? 'Yes' : 'No'}}</td>
                <td [ngClass]="{'success-mgs': gate.is_admin, 'error': !gate.is_admin}">{{gate?.is_admin ? 'Yes' : 'No'}}</td>
                <td [ngClass]="{'success-mgs': isRealPayment(gate) == 'Yes', 'error': isRealPayment(gate) == 'No'}">{{isRealPayment(gate)}}</td>
                <td [ngClass]="{'success-mgs': gate.status, 'error': !gate.status}">{{gate?.status ? 'Active' : 'Inactive'}}</td>
                <td>
                  <button (click)="editGateway(gate)" title="Edit Gateway" class="m-portlet__nav-link btn m-btn m-btn--hover-info m-btn--icon m-btn--icon-only m-btn--pill">
                    <i class="fa fa-edit"></i>
                  </button>
                </td>
              </tr>
            </tbody>
            <ng-template #noFound>
              <tbody>
                <tr *ngIf="!loader">
                  <td colspan="5"><h4 class="text-center">No Data Found</h4></td>
                </tr>
              </tbody>
            </ng-template>

          </table>
        </div>
      </div>
    </div>
  </div>
</div>


<!-- sidebar -->

<div class="native-routing animated">
  <button class="close-sidebar btn btn-sm btn-brand">
    <i class="fa fa-chevron-right"></i>
  </button>
  <span class="close-sidebar-upper">
    <i class="la la-close"></i>
  </span>
  <div class="native-routing-container">
    <ng-template #addFormCard></ng-template>
  </div>
</div>
