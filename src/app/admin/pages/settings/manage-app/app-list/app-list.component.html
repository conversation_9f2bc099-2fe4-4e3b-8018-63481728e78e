<div class="custom-alert" #hasAlert></div>
<!-- BEGIN: Subheader -->
<div class="m-subheader">
  <div class="d-flex align-items-center">
    <div class="mr-auto">
      <h3 class="m-subheader__title m-subheader__title--separator">
        App List
      </h3>
      <ul class="m-subheader__breadcrumbs m-nav m-nav--inline">
        <li class="m-nav__item m-nav__item--home">
          <a routerLink="/admin/dashboard" class="m-nav__link m-nav__link--icon">
            <i class="m-nav__link-icon la la-home"></i>
          </a>
        </li>
        <li class="m-nav__separator">
          <i class="fa fa-angle-right"></i>
        </li>
        <li class="m-nav__item">
          <a class="m-nav__link">
            <span class="m-nav__link-text">
              App List
            </span>
          </a>
        </li>
      </ul>
    </div>
  </div>
</div>
<!-- END: Subheader -->
<div class="m-content product-list animated fadeIn">
  <!--begin::Portlet-->

  <!--end::Portlet-->
  <!--begin::Portlet-->
  <div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__head">
      <div class="m-portlet__head-caption">
        <div class="m-portlet__head-title">
          <h3 class="m-portlet__head-text">
            App List
          </h3>
          <div class="button-midle float-right">
            <a [routerLink]="['/admin/settings/apps/add']" type="button" class="btn btn-dark btn-sm">
              Create App
            </a>
          </div>
        </div>
      </div>
    </div>

    <div class="m-portlet__body" style="position: relative;">
      <!--begin: Datatable -->
      <!--begin::Section-->
      <div class="m-section">
        <div class="m-section__content price-table">
          <div *ngIf="loader" class="table-load m-loader m-loader--brand"></div>
          <h5 class="text-left pb-4">Create and use apps to connect the RentMy platform to many different applications.<br>  The API key and secret are used to provide access to the RentMy API</h5>
          <div style="margin-bottom: 40px;">
            <h5>Related help articles - <a style="font-size: 15px;" href="https://help.rentmy.co/wordpress-rental-plugin-documentation/" target="_blank">RentMy wordpress plugin installation</a></h5>
          </div>
          <div class="table-responsive" style="margin-bottom: 10px;">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>
                    App Name
                  </th>
                  <th>Domain</th>
                  <th>Store Uid</th>
                  <th>
                    Api Key
                  </th>
                  <th>
                    Secret Key
                  </th>
                  <th>
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody *ngIf="apps.length<1; else table">
                <tr *ngIf="!loader">
                  <td colspan="5">
                    <h4 class="text-center">No Apps Found</h4>
                  </td>
                </tr>
              </tbody>
              <ng-template #table>
                <tbody>
                  <ng-template ngFor let-app let-i='index' let-o='odd' let-e='even' [ngForOf]="apps" [ngForTrackBy]="trackStore">
                    <tr [ngClass]="{'odd-tr':o, 'even-tr':e}">
                      <td>{{app.app_name}}</td>
                      <td>{{app.site_url}}</td>
                      <td>{{app.store_uid}}</td>
                      <td>{{app.api_key}}</td>
                      <td>
                        <button type="button" class="btn btn-sm btn-outline-dark" (click)="openSecret('app-id-'+ app.id, 'app-icon-'+ app.id)">Show Key <i class="fa fa-plus" id="{{'app-icon-'+app.id}}" style="margin-left: 5px;"></i></button>
                      </td>
                      <td>
                        <div *ngIf="deleteId == app.id; else deleteS" class="m-loader m-loader--brand"
                          style="width: 30px; display: inline-block;"></div>
                        <ng-template #deleteS>
                          <a [routerLink]="['/admin/settings/apps/edit', app.id]" class="m-portlet__nav-link btn m-btn m-btn--hover-info m-btn--icon m-btn--icon-only m-btn--pill"
                            title="Edit App">
                            <i class="fa fa-edit"></i>
                          </a>
                          <a (click)="deleteApp(app.id)" class="m-portlet__nav-link btn m-btn m-btn--hover-danger m-btn--icon m-btn--icon-only m-btn--pill"
                            title="Delete App">
                            <i class="fa fa-trash"></i>
                          </a>
                        </ng-template>
                      </td>
                    </tr>
                    <tr class="dis-none" id="{{'app-id-'+app.id}}">
                      <td colspan="4" class="text-center">
                        <small *ngIf="app.id == copiedId" class="success-mgs">Secret key is copied</small>
                        <input type="text" class="form-control copy-input" [value]="app.api_secret" readonly #copyText>
                      </td>
                      <td colspan="1">
                        <button type="button" class="btn btn-sm btn-outline-dark" (click)="copySecret(copyText, app.id)">Copy</button>
                      </td>
                    </tr>
                  </ng-template>
                </tbody>
              </ng-template>
            </table>
          </div>
          <!-- pagination Start-->
          <boot-pagination [totalSize]="pagi.total" [page]="pagi.page" [pagelimit]="pagi.limit" [listSize]="pagi.limit"
            (pageChange)="reloadTable($event)"></boot-pagination>
          <!-- pagination End-->
        </div>
      </div>
      <!--end::Section-->
      <!--end: Datatable -->
    </div>

  </div>
  <!--end::Portlet-->
</div>
