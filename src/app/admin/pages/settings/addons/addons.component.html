<div class="custom-alert" #hasCusAlert></div>

<div class="m-subheader">
    <div class="d-flex align-items-center">
        <div class="mr-auto">
            <h3 class="m-subheader__title m-subheader__title--separator">
                Addons
            </h3>
            <ul class="m-subheader__breadcrumbs m-nav m-nav--inline">
                <li class="m-nav__item m-nav__item--home">
                    <a routerLink="/admin" class="m-nav__link m-nav__link--icon">
                        <i class="m-nav__link-icon la la-home"></i>
                    </a>
                </li>
                <li class="m-nav__separator">
                    <i class="fa fa-angle-right"></i>
                </li>
                <li class="m-nav__item">
                    <a class="m-nav__link">
                        <span class="m-nav__link-text">
                            Settings
                        </span>
                    </a>
                </li>
                <li class="m-nav__separator" style="padding-left: 10px">
                    <i class="fa fa-angle-right"></i>
                </li>
                <li class="m-nav__item">
                    <a class="m-nav__link">
                        <span class="m-nav__link-text">
                            Add-ons
                        </span>
                    </a>
                </li>
            </ul>
        </div>
    </div>
</div>
<!-- END: Subheader -->
<div class="m-content animated fadeIn">
    <div class="m-portlet">
        <div class="m-portlet__head title-head">
            <div class="m-portlet__head-caption">
                <div class="m-portlet__head-title">
                    <h3 class="m-portlet__head-text colorPurpel">
                        Add-ons
                    </h3>
                    <div class="add-list-btn text-right">
                        <button class="btn btn-brand" (click)="AddAddon()">
                            Add
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="m-portlet__body">
            <!--begin::Section-->
            <div class="row">
                <div class="col-md-6 pb-4 mb-3">
                    <form #serForm="ngForm" class="addon-search-area">
                        <div class="input-group">
                                <input type="text" 
                                class="form-control" placeholder="Search Primary Item By Id, Name"  
                                #searchAll="ngModel" name="search"  
                                [(ngModel)]="search" style="display: inline;">
                                <div *ngIf="searchAll.dirty || search" (click)="reset(serForm)" style="margin: auto; padding: 5px;cursor: pointer;display: inline;">
                                        <i class="fa fa-close"></i>
                                        </div>
                                <div class="input-group-append ml-2">
                                    <button class="btn btn-sm btn-brand" type="button" (click)="searchList(serForm.value)">
                                        <i class="fa fa-search mr-2"></i>Search
                                    </button>
                                </div>
                            </div>


                        <!-- <div class="form-group ">
                            <input type="text" class="form-control" placeholder="Search by id, name"
                            #searchAll="ngModel" name="search" [(ngModel)]="search" style="display: inline;">
                            <div *ngIf="searchAll.dirty || search" (click)="reset(serForm)" style="margin: auto; padding: 5px;cursor: pointer;display: inline;">
                            <i class="fa fa-close"></i>
                            </div>
                            <button type="submit" class="btn btn-sm btn-brand" (click)="searchList(serForm.value)"><i class="fa fa-search"></i>Search</button>
                        </div>-->
                    </form> 
                 </div>
            </div>

            <div class="">
                <div class="m-section__content price-table table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>
                                   Primary Item
                                </th>
                                <th>
                                    Add-on products
                                </th>
                                <th>
                                   Quantity
                                </th>
                                <th>
                                   Type
                                </th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody *ngIf="addonsList.length>0; else noData">
                            <tr *ngFor="let addon of addonsList; let i = 'index'; trackBy: trackTag; let o='odd'; let e='even'"
                                [ngClass]="{'odd-tr':o, 'even-tr':e}">
                                <td>{{ getName(addon?.item_id) }} </td>
                                <td>
                                    {{ getName(addon?.product_id) }}
                                </td>
                                <td>
                                    {{addon?.min_quantity}}
                                </td>
                                <td>
                                    {{addon?.is_required == 1 ? 'Required' : 'Optional'}}
                                </td>
                                <td>
                                    <a (click)="editAddon(addon)" title="Edit"
                                        class="m-portlet__nav-link btn m-btn m-btn--hover-info m-btn--icon m-btn--icon-only m-btn--pill">
                                        <i class="fa fa-edit"></i>
                                    </a>
                                    <a (click)="deleteAddon(addon.uid, i)" title="Delete"
                                        class="m-portlet__nav-link btn m-btn m-btn--hover-danger m-btn--icon m-btn--icon-only m-btn--pill">
                                        <i class="fa fa-trash"></i>
                                    </a>
                                </td>
                            </tr>
                        </tbody>
                        <ng-template #noData>
                            <tbody>
                                <tr>
                                    <td colspan="3">
                                        <h5 class="text-center">No Data Found</h5>
                                    </td>
                                </tr>
                            </tbody>
                        </ng-template>
                    </table>
                </div>

                 <!-- pagination Start-->
                <boot-pagination [totalSize]="pagi.total" [page]="pagi.page" [listSize]="pagi.limit" [pagelimit]="pagi.limit"
                (pageChange)="reloadTable($event)"></boot-pagination>
                <!-- pagination End-->
            </div>
            <!--end::Section-->
        </div>
    </div>
</div>


<!-- sidebar -->

<div class="native-routing animated">
    <button class="close-sidebar btn btn-sm btn-brand">
        <i class="fa fa-chevron-right"></i>
    </button>
    <span class="close-sidebar-upper">
        <i class="la la-close"></i>
    </span>
    <div class="native-routing-container">
        <app-add-addons  (submitForm)="SubmitAddons($event)"></app-add-addons>
    </div>

</div>