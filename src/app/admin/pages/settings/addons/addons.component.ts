import { <PERSON>mponent, OnInit, OnDestroy, ElementRef, ViewChild, HostListener, AfterViewInit } from '@angular/core';
import { Addons, Tag } from '../models/settings.models';
import { Helpers } from '../../../../helpers';
import { DialogBoxComponent } from '../../../../modules/dialog-box/dialog-box.component';
import { TagService } from '../tags/tag.setvice';
import { SidebarService } from '../../sidebar-service/sidebar.service';
import { SettingService } from '../setting-service/setting.service';
import { AlertService } from '../../../../modules/alert/alert.service';
import { ActivatedRoute } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { AddonsService } from './addons.service';
import { Pagi } from '../../../../modules/pagination/pagi.model';




@Component({
  selector: 'app-addons',
  templateUrl: './addons.component.html',
  styleUrls: ['./addons.component.css']
})
export class AddonsComponent implements OnInit, AfterViewInit {

  addonsList: Addons[] = [];
  sideBaropen: boolean;
  addon: Addons;
  EditMode: boolean;
  copyDone: boolean;
  pagi: Pagi = new Pagi();
  search='';

  @HostListener('window:resize', ['$event'])
  onResize(event) {
    if (this.sideBaropen) {
      $('.native-routing').css('display', 'block');
      this.sidebarS.openSidebar();
    }
  }

  @ViewChild('hasCusAlert') alertContainer: ElementRef;

  constructor(
    private addonsService: AddonsService,
    private sidebarS: SidebarService,
    private settingS: SettingService,
    private alertS: AlertService,
    private activeRoute: ActivatedRoute,
    private modalService: NgbModal
  ) {
    const list = this.activeRoute.snapshot.data['list'];
    if (list) {
      this.addonsList = list.data;
      this.pagi.page=parseInt(list.page);
      this.pagi.limit=parseInt(list.limit);
      this.pagi.total=parseInt(list.limit);
    }
  }

  ngOnInit() {}

  ngAfterViewInit() {
    //window.scrollTo(0, 0);
    this.closeSidebar();
    this.executeAction();
  }


  getAddonList(page,limit,searchTerm?){
     Helpers.setLoading(true);

      this.settingS.getAddons(page,limit,searchTerm).subscribe(res=>{
        Helpers.setLoading(false);
        this.addonsList=res.data;
        this.pagi.page=parseInt(res.page);
        this.pagi.limit=parseInt(res.limit);
      },err=>{
        Helpers.setLoading(false);
      })
    
  }

  trackTag(index, tag) {
    return tag ? tag.id : null;
  }

  getName(data:any=[]){
    let nameList='';
    let index=0;

    if(data.length>0)
    {
      data.map(x=>{
        index++;
        nameList=nameList+x.name
        nameList= data.length==index ? nameList+'' : nameList+', '

      })
    }
    return nameList;
  }

  editAddon(addon) {
    this.openSidebar();
    this.EditMode = true;
    this.addon = addon;
   this.addonsService.addonsReload.next({ reload: false, editMode: true, addons: addon });
  }

  AddAddon() {
    this.openSidebar();
    this.addon = new Addons();
    this.EditMode = false;
    this.addonsService.addonsReload.next({ reload: true, editMode: false});
  }

  searchList(vlaue){
    if(vlaue.search !=undefined && vlaue.search !=null)
    {
      this.getAddonList(this.pagi.page,this.pagi.limit,vlaue.search.trim());
    }
     
  }

  reset(f) {
    this.getAddonList(1,20);
    f.form.reset();
  }


  reloadTable(e) {
    this.getAddonList(e.page,e.limit);
  }
  
  openSidebar() {
    $('.native-routing').css('display', 'block');
    this.sidebarS.openSidebar();
    this.sideBaropen = true;
  }

  deleteAddon(id, i) {
    const modalRef = this.modalService.open(DialogBoxComponent, {
      centered: true,
      size: 'sm',
      windowClass: 'animated fadeIn'
    });
    modalRef.componentInstance.massage = 'Are you sure you want to delete?';
    modalRef.result
      .then((result) => {
        if (result) {
          Helpers.setLoading(true);
          this.archiveAddons(id, i);
        }
      }, (res) => {
        console.log(res);
      });

  }

  archiveAddons(id, i) {
    this.settingS.deleteAddons(id).then(
      res => {
        if(res.status=="OK")
        {
          this.addonsList.splice(i, 1);
          this.alert({error: false, message: res.result.message});
        }
        else{
          this.alert({error: true, message: res.result.message});
        }
       
        
      }
    ).catch (
      err => {
        console.log(err);
        this.alert({error: true, message: 'Something wrong! Addons has been not deleted'});
      }
    )
  }

  SubmitAddons(event) {
    if (!event.alert.error) {
      if (this.EditMode) {
        this.addon.is_required = event.data.is_required;
        this.addon.min_quantity = event.data.min_quantity;
        this.addon.status = event.data.status;
      } else {
        this.addonsList.push(event.data);
      }
      this.executeAction();
    }
    // console.log(event);
    this.alert(event.alert);
  }

  closeSidebar() {
    $('.close-sidebar').click((e) => {
      e.preventDefault();
      this.executeAction();
    });
    $('.close-sidebar-upper').click((e) => {
      e.preventDefault();
      this.executeAction();
    });
  }

  executeAction() {
    this.sideBaropen = null;
    this.sidebarS.removeSidebar();
    $('.native-routing').css('display', 'none');
  }

  alert(data) {
    Helpers.setLoading(false);
    if (data.error) {
      this.alertS.error(this.alertContainer, data.message, true, 5000);
    } else {
      this.alertS.success(this.alertContainer, data.message, true, 5000);
    }
  }

 


}