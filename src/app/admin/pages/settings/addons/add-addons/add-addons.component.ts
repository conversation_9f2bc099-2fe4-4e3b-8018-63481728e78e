import { Component, OnInit, OnD<PERSON>roy, ViewChild, ElementRef, Input, Output, EventEmitter } from '@angular/core';
import { Addons } from '../../models/settings.models';
import { AddonsService } from '../addons.service';
import { SettingService } from '../../setting-service/setting.service';

@Component({
  selector: 'app-add-addons',
  templateUrl: './add-addons.component.html',
  styleUrls: ['./add-addons.component.css']
})
export class AddAddonsComponent implements OnInit, OnDestroy {

   loader = false;
   addons:Addons
   edit=false;
   selectedItem=[];
   selectedAddonsProduct=[];


  @Output() submitForm = new EventEmitter();
  
  constructor(
    private addonsService:AddonsService,
    private settingService:SettingService
  ) {

  }

  ngOnInit() {
   
    this.addonsService.addonsReload.subscribe(val => {
      if (val.reload) {
        this.addons=new Addons();
        this.edit = val.editMode;
      }
      if (val.editMode) {
        this.edit = val.editMode;
        this.addons = val.addons;
        this.selectedItem=this.addons.item_id;
        this.selectedAddonsProduct=this.addons.product_id;
      }
      else{
        this.addons=new Addons();
        this.selectedItem=[];
        this.selectedAddonsProduct=[];
      }
    });
  }

  ngOnDestroy() {

  }


  selectProduct(product) {
    this.selectedItem = [];

    let prod = {
      id: product.product_id,
      name: product.name
    }
    this.selectedItem.push(prod);

  }



  selectAddonsProduct(product) {
    let prod = {
      id: product.product_id,
      name: product.name
    }
    this.selectedAddonsProduct.push(prod);

  }

  deleteAddonsProduct(id) {
    var index = this.selectedAddonsProduct.findIndex(x => x.id == id);
    if (index > -1) {
      this.selectedAddonsProduct.splice(index, 1);
    }
  }


  submit() {
    this.loader=true;
    let sendData = {
      type: 1,
      item_id: this.selectedItem.map(d => { return d.id }),
      product_id: this.selectedAddonsProduct.map(d => { return d.id }),
      is_required: this.addons.is_required,
      min_quantity: this.addons.min_quantity,
      status: true,
      label: this.addons.label
    };


    this.loader=true;

    this.settingService.addAddons(sendData).then(res=>{
      if(res.status=="OK")
      {
        this.addons.uid= res.result.data.uid;
        this.addons.item_id=this.selectedItem;
        this.addons.product_id=this.selectedAddonsProduct;


        this.sendEmitedData(this.addons, false, res.result.message);
      }
      else{
        this.sendEmitedData([], true, res.result.message);
      }
      this.loader=false;
    }).catch(err=>{
      this.sendEmitedData([], true, 'Something wrong! Addons has been not added');
      this.loader=false;
    })

  
  }


  update() {
    this.loader = true;

    let sendData = {
      uid:this.addons.uid,
      type: this.addons.type,
      item_id: this.selectedItem.map(d => { return d.id }),
      product_id: this.selectedAddonsProduct.map(d => { return d.id }),
      is_required: this.addons.is_required,
      min_quantity: this.addons.min_quantity,
      status: true,
      label:this.addons.label
    };

    this.settingService.updateAddons(sendData)
    .then(
      res => {
        if(res.status=="OK")
        {
        this.addons.item_id=this.selectedItem;
        this.addons.product_id=this.selectedAddonsProduct;
        this.sendEmitedData( this.addons, false, res.result.message)
        }
        else{
          this.sendEmitedData([], true, res.result.message)
        }
        
        this.loader = false;
      }
    ).catch(
      err => {
        console.log(err);
        this.sendEmitedData([], true, 'Something wrong! Addons has been not updated')
      }
    );
  }

  sendEmitedData(result, error, message) {
    this.loader = false;
    // console.log(result);
    const emit_data = {data: result, alert: {error: error, message: message}}
    this.submitForm.emit(emit_data);
  }

}
