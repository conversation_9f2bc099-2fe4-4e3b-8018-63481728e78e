<div style="padding-top: 35px;">
  <div *ngIf="openForm">
    <h5 class="colorPurpel" *ngIf="edit; else add">Update Variant Value
      <button class="btn btn-brand btn-sm" (click)="initAddAttribute()">
        Add Variant Value
      </button>
    </h5>
    <ng-template #add class="colorPurpel">
      <h5 class="colorPurpel">Add Variant Value</h5>
    </ng-template>
    <form class="m-form m-form--fit m-form--label-align-right" #addForm="ngForm">
      <div class="form-group m-form__group">
        <label for="title">
            Variant Value Name
        </label>
        <input type="text" class="form-control m-input" placeholder="Enter name" id="title"
        name="name" [(ngModel)]="attribute.name" autocomplete="off">
      </div>
      <div class="m-portlet__foot m-portlet__foot--fit text-right">
        <div class="m-form__actions m-form__actions" style="padding: 30px 0px;">
          <div *ngIf="loader; else button" class="m-loader m-loader--brand"
      				style="width: 30px; padding-left: 30px; display: inline-block;"></div>
          <ng-template #button>
            <button type="button" class="btn btn-brand btn-sm" (click)="updateAttribute()" *ngIf="edit; else addbtn">
              <i class="fa fa-save"></i> <span style="padding-left:10px;">Update</span>
            </button>
            <ng-template #addbtn>
              <button type="button" class="btn btn-brand btn-sm" (click)="submitAttribute()">
                <i class="fa fa-save"></i> <span style="padding-left:10px;">Submit</span>
                </button>
            </ng-template>
          </ng-template>
        </div>
      </div>
    </form>
  </div>

  <div *ngIf="!openForm" style="padding: 20px 0px;">
    <button class="btn btn-brand btn-sm" (click)="initAddAttribute()">
      Add Variant Value
    </button>
  </div>

  <h5>Variant Value List of <strong class="colorPurpel">{{attributeSet.name}}</strong></h5>
  <div class="m-section">
    <div class="m-section__content price-table table-responsive">
      <table class="table table-hover">
        <thead>
          <tr>
            <th>
              Name
            </th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody *ngIf="attributeSet.variants.length>0; else noDate">
          <tr *ngFor="let att of attributeSet.variants; let i='index'; trackBy: trackAtt">
            <td>
              {{att.name}}
            </td>
            <td>     
              <a id="m_quick_sidebar_toggle" (click)="editAttribute(att,i)"
                class="m-portlet__nav-link btn m-btn m-btn--hover-info m-btn--icon m-btn--icon-only m-btn--pill">
                <i class="fa fa-edit"></i>
              </a>
              <!-- <a id="m_quick_sidebar_toggle" (click)="deleteAttribute(att.id, i)"
                class="m-portlet__nav-link btn m-btn m-btn--hover-danger m-btn--icon m-btn--icon-only m-btn--pill">
                <i class="fa fa-trash"></i>
              </a> -->
            </td>
          </tr>
        </tbody>
        <ng-template #noDate>
          <tbody><tr><td colspan="2"><h5 class="text-center">No Variant Value Found</h5></td></tr></tbody>
        </ng-template>
      </table>
    </div>

  </div>
</div>
  