<div style="padding-top: 35px;">
	<h5 class="colorPurpel" *ngIf="edit; else add">Update Variant Set</h5>
	<ng-template #add class="colorPurpel">
		<h5 class="colorPurpel">Add Variant Set</h5>
	</ng-template>
	<form class="m-form m-form--fit m-form--label-align-right" #addForm="ngForm">
		<div class="form-group m-form__group">
			<label for="title">
				Variant Set Label
			</label>
			<input type="text" class="form-control m-input" placeholder="Enter Label" id="title"
			name="name" [(ngModel)]="attributeSet.name" autocomplete="off">
		</div>
		<div class="form-group m-form__group">
			<label for="slug">
				Slug
			</label>
			<input type="text" class="form-control m-input" placeholder="Enter Slug" id="slug"
			name="slug" [(ngModel)]="attributeSet.slug" autocomplete="off">
			<small class="info">Slug should be unique</small>
		</div>
		<div class="m-portlet__foot m-portlet__foot--fit text-right">
			<div class="m-form__actions m-form__actions" style="padding: 30px 0px;">
				<div *ngIf="loader; else button" class="m-loader m-loader--brand"
      				style="width: 30px; padding-left: 30px; display: inline-block;"></div>
				<ng-template #button>
					<button type="button" class="btn btn-brand" (click)="updateAttributeSet()" *ngIf="edit; else addbtn">
					  <i class="fa fa-save"></i> <span style="padding-left:10px;">Update</span>
					</button>
					<ng-template #addbtn>
						<button type="button" class="btn btn-brand" (click)="submitAttributeSet()">
							<i class="fa fa-save"></i> <span style="padding-left:10px;">Submit</span>
						</button>
					</ng-template>
				</ng-template>
			</div>
		</div>
	</form>
</div>	

