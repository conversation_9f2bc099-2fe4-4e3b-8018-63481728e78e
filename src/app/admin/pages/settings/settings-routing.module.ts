import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";
import { SettingsComponent } from "./settings.component";
import { AdminService } from "../../admin.service";
import { PagesComponent } from "../pages.component";
import { SettingResolveService } from "./setting-service/setting-resolve.service";

const routes: Routes = [
  {
    path: "",
    component: PagesComponent,
    children: [
      {
        path: "",
        component: SettingsComponent,
        canActivate: [AdminService],
        children: [
          {
            path: "dashboard",
            loadChildren:
              () => import('./settings-dashboard/settings-dashboard.module').then(m => m.SettingsDashboardModule),
              data : { title:'Setting Dashboard'}
          },
          {
            path: "manage-categories",
            loadChildren:
              () => import('./categories/categories.module').then(m => m.CategoriesModule),
              data : { title:'Manage Categories'}
          },
          {
            path: "variant",
            loadChildren:
              () => import('./attribute/attribute.module').then(m => m.AttributeModule),
            resolve: { list: SettingResolveService },
            data : { title:'Variant'}
          },
          {
            path: "tag",
            loadChildren:
              () => import('./tags/tags.module').then(m => m.TagsModule),
            resolve: { list: SettingResolveService },
            data : { title:'Tag List'}
          },
          {
            path: "addons",
            loadChildren:
              () => import('./addons/addons.module').then(m => m.AddonsModule),
            resolve: { list: SettingResolveService },
            data : { title:'Addons'}
          },
          {
            path: "custom-checkout",
            loadChildren:
              () => import('./customCheckout/customCheckout.module').then(m => m.CustomCheckoutModule),
            resolve: { list: SettingResolveService },
            data : { title:'Custom Checkout List'}
          },
          {
            path: "coupon",
            loadChildren:
              () => import('./coupon/coupon.module').then(m => m.CouponModule),
            resolve: { list: SettingResolveService },
            data : { title:'Coupon List'}
          },
          {
            path: "supplier",
            loadChildren:
              () => import('./supplier/supplier.module').then(m => m.SupplierModule),
            resolve: { list: SettingResolveService },
            data : { title:'Supplier List'}
          },
          {
            path: "video-wall-config",
            loadChildren:
              () => import('./video-wall-config/video-wall-config.module').then(m => m.VideoWallConfigModule),
            resolve: { list: SettingResolveService },
            data : { title:'Video Wall Configuration'}
          },
          {
            path: "contents",
            loadChildren:
              () => import('./all-contents/all-contents.module').then(m => m.AllContentsModule),
              data : { title:'Store Contents'}
          },
          {
            path: "navigations",
            loadChildren:
              () => import('./menu-settings/menu-settings.module').then(m => m.MenuSettingsModule),
              data : { title:'Navigation List'}
          },
          {
            path: "location",
            loadChildren:
              () => import('./bolt-terminal/bolt-terminal.module').then(m => m.BoltTerminalModule),
              data : { title:'Location List'}
          },
          {
            path: "widgets",
            loadChildren:
              () => import('./widgets/widgets.module').then(m => m.WidgetsModule),
              data : { title:'Widgets List'}
          },
          {
            path: "delivery",
            loadChildren:
              () => import('./delivery/delivery.module').then(m => m.DeliveryModule),
            resolve: { conditions: SettingResolveService },
            data : { title:'Delivery Settings'}
          },
          {
            path: "order-receipt",
            loadChildren:
              () => import('./order-receipt/order-receipt.module').then(m => m.OrderReceiptModule),
              data : { title:'Order Receipt Settings'}
          },
          {
            path: "hours-holidays",
            loadChildren:
              () => import('./hours-holiday/hours-holiday.module').then(m => m.HoursHolidayModule),
            resolve: { weeks: SettingResolveService },
            data : { title:'Store Hours'}
          },
          {
            path: "categories-variant",
            loadChildren:
              () => import('./category-attribute/category-attribute.module').then(m => m.CategoryAttributeModule)
          },
          {
            path: "payment-gateways",
            loadChildren:
              () => import('./payment-gateway/payment-gateway.module').then(m => m.PaymentGatewayModule),
              data : { title:'Payment Gateways'}
          },
          {
            path: "shipping-settings",
            loadChildren:
              () => import('./shipping-settings/shipping-settings.module').then(m => m.ShippingSettingsModule),
              data : { title:'Shipping Settings'}
          
            },
          {
            path: "apps",
            loadChildren:
              () => import('./manage-app/manage-app.module').then(m => m.ManageStoreAppModule),
              data : { title:'App List'}
          },
          {
            path: "advance-settings",
            loadChildren:
              () => import('./advance-settings/advance-settings.module').then(m => m.AdvanceSettingsModule),
            resolve: { leadLag: SettingResolveService },
            data : { title:'Advance Settings'}
          },
          {
            path: "quick-book",
            loadChildren:
              () => import('./quick-book/quick-book.module').then(m => m.QuickBookModule),
              data : { title:'Quickbook Integration'}
          },
          {
            path: "mailchimp",
            loadChildren:
              () => import('./mailchimp/mailchimp.module').then(m => m.MailchimpModule),
              data : { title:'Mailchimp'}
          },
          {
            path: "atrium",
            loadChildren:
              () => import('./atrium/atrium.module').then(m => m.AtriumModule),
              data : { title:'Atrium'}
          },
          {
            path: "business-info",
            loadChildren: () => import('./business-info/business-info.module').then(m => m.BusinessInfoModule)
          },
          {
            path: "sales-tax",
            loadChildren: () => import('./sales-tax/sales-tax.module').then(m => m.SalesTaxModule)
          },
          {
            path: "**",
            redirectTo: "manage-categories",
            pathMatch: "full"
          }
        ]
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SettingsRoutingModule {}
