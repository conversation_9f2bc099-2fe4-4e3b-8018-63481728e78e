import {
  Component,
  OnInit,
  Host<PERSON>istener,
  ViewChild,
  ElementRef,
  AfterContentInit
} from "@angular/core";
import { Stores } from "../models/settings.models";
import { SidebarService } from "../../sidebar-service/sidebar.service";
import { SettingService } from "../setting-service/setting.service";
import { AlertService } from "../../../../modules/alert/alert.service";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { DialogBoxComponent } from "../../../../modules/dialog-box/dialog-box.component";
import { Helpers } from "../../../../helpers";
import { AdminService } from "../../../admin.service";
import { Router } from "@angular/router";

@Component({
  selector: "app-bolt-terminal",
  templateUrl: "./bolt-terminal.component.html",
  styleUrls: ["./bolt-terminal.component.css"]
})
export class BoltTerminalComponent implements OnInit, AfterContentInit {
  terminals: Stores[] = [];
  terminal: Stores = new Stores();
  edit: boolean;
  sideBaropen: boolean;
  sideBarName: string = null;

  @ViewChild("hasCusAlert", {static: true}) alertContainer: ElementRef;

  @HostListener("window:resize", ["$event"])
  onResize(event) {
    if (this.sideBaropen) {
      this.openSidebar();
    }
  }

  constructor(
    private router: Router,
    private sidebarS: SidebarService,
    private settingS: SettingService,
    private alertS: AlertService,
    private adminS: AdminService,
    private modalService: NgbModal
  ) {}

  ngOnInit() {
    //window.scrollTo(0, 0);
    this.getTerminal();
  }

  ngAfterContentInit() {
    this.closeSidebar();
    this.executeAction();
  }

  closeSidebar() {
    $(".close-sidebar").click(e => {
      e.preventDefault();
      this.executeAction();
    });
    $(".close-sidebar-upper").click(e => {
      e.preventDefault();
      this.executeAction();
    });
  }

  executeAction() {
    this.sideBaropen = null;
    this.sidebarS.removeSidebar();
    $(".native-routing").css("display", "none");
  }

  openSidebar() {
    $(".native-routing").css("display", "block");
    this.sidebarS.openSidebar();
    this.sideBaropen = true;
  }

  trackTerminal(index, co) {
    return co ? co.id : null;
  }

  getTerminal() {
    this.settingS.getterminals().subscribe(
      res => {
        // console.log(res);
        this.terminals = res.data;
        if (this.terminals) {
          this.adminS.changeStore(this.terminals);
        }
      },
      err => {
        this.terminals = [];
        console.log(err);
        this.alert({
          error: true,
          message: "Something wrong!!! Please try again."
        });
      }
    );
  }

  addTerminal() {
    this.chekingSubsPlan();
  }
  chekingSubsPlan() {
    let message = "";
    const user = JSON.parse(localStorage.getItem("currentUser"));
    if (user.subscription.account_type === "FREE" || user.subscription.account_type === "Free") {
      message =
      "You need to upgrade your billing plan to add an additional location. Would you like to upgrade now?";
    } else {
      message = "Each additional location will incur an adjustment to your subscription plan. Do you wish to proceed?";
    }
    const modalRef = this.modalService.open(DialogBoxComponent, {
      centered: true,
      // size: "lg",
      // width:'500px',
      windowClass: "animated fadeIn"
    });
    modalRef.componentInstance.massage = message;
    modalRef.result.then(
      result => {
        if (result) {
          if (user.subscription.account_type === "FREE") {
            this.router.navigateByUrl("/admin/plans");
          } else {
           // Helpers.setLoading(true);
            this.terminal = new Stores();
            this.edit = false;
            this.openSidebar();
            this.sideBarName = "add";
          }
        }
      },
      res => {
        console.log(res);
      }
    );
  }
  editTerminal(ter) {
    this.terminal = new Stores();
    this.terminal = Object.assign({}, ter);
    this.edit = true;
    this.openSidebar();
    this.sideBarName = "add";
  }

  submitTerminal(event) {
    // console.log(event);
    if (!event.error) {
      this.getTerminal();
      this.executeAction();
    }
    this.alert(event);
  }

  deleteTerminal(id) {
    const modalRef = this.modalService.open(DialogBoxComponent, {
      centered: true,
      size: "sm",
      windowClass: "animated fadeIn"
    });
    modalRef.componentInstance.massage = "Are you sure you want to delete?";
    modalRef.result.then(
      result => {
        if (result) {
          Helpers.setLoading(true);
          this.archiveTerminal(id);
        }
      },
      res => {
        console.log(res);
      }
    );
  }

  archiveTerminal(id) {
    this.settingS
      .deleteTerminal(id)
      .then(res => {
        this.getTerminal();
        this.alert({ error: false, message: "Location has been deleted" });
      })
      .catch(err => {
        console.log(err);
        this.alert({
          error: true,
          message: "Something wrong! Location has been not deleted"
        });
      });
  }

  alert(data) {
    Helpers.setLoading(false);
    if (data.error) {
      this.alertS.error(this.alertContainer, data.message, true, 5000);
    } else {
      this.alertS.success(this.alertContainer, data.message, true, 5000);
    }
  }

  viewTerminal(ter) {
    this.terminal = new Stores();
    this.terminal = Object.assign({}, ter);
    this.openSidebar();
    this.sideBarName = "view";
  }
}
