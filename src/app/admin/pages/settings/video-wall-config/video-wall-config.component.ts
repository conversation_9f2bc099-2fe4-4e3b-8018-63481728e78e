import {
  <PERSON>mpo<PERSON>,
  OnInit,
  HostL<PERSON>ener,
  ViewChild,
  ElementRef
} from "@angular/core";
import { SidebarService } from "../../sidebar-service/sidebar.service";
import { SettingService } from "../setting-service/setting.service";
import { AlertService } from "../../../../modules/alert/alert.service";
import { ActivatedRoute } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { Pagi } from "../../../../modules/pagination/pagi.model";
import { DialogBoxComponent } from "../../../../modules/dialog-box/dialog-box.component";
import { Helpers } from "../../../../helpers";
import { VideoWall } from "../models/settings.models";

@Component({
  selector: "app-video-wall-config",
  templateUrl: "./video-wall-config.component.html",
  styleUrls: ["./video-wall-config.component.css"]
})
export class VideoWallConfigComponent implements OnInit {
  videoWallList = [];
  sideBaropen: boolean;
  videoWallData: VideoWall;
  typeList = [];
  loader: boolean;
  pagi: Pagi = new Pagi();

  @HostListener("window:resize", ["$event"])
  onResize(event) {
    if (this.sideBaropen) {
      $(".native-routing").css("display", "block");
      this.sidebarS.openSidebar();
    }
  }

  @ViewChild("hasCusAlert") alertContainer: ElementRef;

  constructor(
    private sidebarS: SidebarService,
    private settingS: SettingService,
    private alertS: AlertService,
    private activeRoute: ActivatedRoute,
    private modalService: NgbModal
  ) {
    this.videoWallData = new VideoWall();
    const list = this.activeRoute.snapshot.data["list"];
    // console.log(list);
    if (list) {
      this.dataList(list);
    }
  }

  ngOnInit() {

  }

  ngAfterViewInit() {
    //window.scrollTo(0, 0);
    this.closeSidebar();
    this.executeAction();
  }

  trackVideoWall(index, co) {
    return co ? co.id : null;
  }

  reloadTable(e) {
    // console.log(e);
    this.getVideoWallList();
  }

  getVideoWallList() {
    this.loader = true;
    this.dataRender();
  }

  private dataRender() {
    this.settingS.getVideoWallList().subscribe(
      res => {
        this.dataList(res);
        this.loader = false;
      },
      err => console.log(err)
    );
  }

  private dataList(res) {
    this.videoWallList = res.data;
    this.pagi.total = res["total"] || 0;
    this.pagi.page = parseInt(res["page"]) || 1;
    this.pagi.limit = parseInt(res["limit"]) || 10;
  }

  closeSidebar() {
    $(".close-sidebar").click(e => {
      e.preventDefault();
      this.executeAction();
    });
    $(".close-sidebar-upper").click(e => {
      e.preventDefault();
      this.executeAction();
    });
  }

  executeAction() {
    this.sideBaropen = null;
    this.sidebarS.removeSidebar();
    $(".native-routing").css("display", "none");
  }

  initAddVideoWall() {
    this.settingS.addEditChange({ open: true, edit: false });
    this.sideBaropen = true;
    this.videoWallData = new VideoWall();
    this.typeList = [];

    this.videoWallData.show_availability = "no";
    this.videoWallData.show_price = "yes";
    this.videoWallData.interval = 5;
    this.videoWallData.display_type = -1;

    $(".native-routing").css("display", "block");
    this.sidebarS.openSidebar();
  }

  editVideoWall(cop) {
    this.settingS.addEditChange({ open: true, edit: true });
    this.sideBaropen = true;

    this.videoWallData.id = cop.id;
    this.videoWallData.name = cop.name;
    this.videoWallData.display_type = cop.display_type;
    this.videoWallData.show_availability = cop.settings.show_availability;
    this.videoWallData.show_price = cop.settings.show_price;
    this.videoWallData.interval = cop.settings.interval;

    this.typeList = cop.items;

    this.videoWallData = Object.assign({}, this.videoWallData);
    $(".native-routing").css("display", "block");
    this.sidebarS.openSidebar();
  }

  openSidebar() {
    $(".native-routing").css("display", "block");
    this.sidebarS.openSidebar();
    this.sideBaropen = true;
  }

  deleteVideoWall(id) {
    const modalRef = this.modalService.open(DialogBoxComponent, {
      centered: true,
      size: "sm",
      windowClass: "animated fadeIn"
    });
    modalRef.componentInstance.massage = "Are you sure you want to delete?";
    modalRef.result.then(
      result => {
        if (result) {
          Helpers.setLoading(true);
          this.archiveVideoWall(id);
        }
      },
      res => {
        console.log(res);
      }
    );
  }

  archiveVideoWall(id) {
    this.settingS
      .deleteVideoWall(id)
      .then(res => {
        if (res.status === "OK") {
          this.dataRender();
          this.alert({ error: false, message: res.result.message });
        } else {
          this.alert({ error: true, message: res.result.error });
        }
      })
      .catch(err => {
        console.log(err);
        this.alert({ error: true, message: "Something wrong!" });
      });
  }

  submitVideoWall(event) {
    // console.log(event);
    if (!event.alert.error) {
      this.dataRender();
      this.executeAction();
    }
    this.alert(event.alert);
  }

  alert(data) {
    Helpers.setLoading(false);
    if (data.error) {
      this.alertS.error(this.alertContainer, data.message, true, 5000);
    } else {
      this.alertS.success(this.alertContainer, data.message, true, 5000);
    }
  }

  getDate(d) {
    return new Date(d);
  }
}
