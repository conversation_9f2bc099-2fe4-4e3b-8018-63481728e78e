import {
  Component,
  OnInit,
  Output,
  Input,
  EventEmitter,
  OnDestroy
} from "@angular/core";
import { VideoWall } from "../../models/settings.models";
import { SettingService } from "../../setting-service/setting.service";
import { ContentService } from "../../setting-service/contents.service";
import { GET_USER } from "../../../../../globals/_classes/functions";
import { Subscription } from "rxjs";

declare let $: any;

@Component({
  selector: "app-add-video-wall-config",
  templateUrl: "./add-video-wall-config.component.html",
  styleUrls: ["./add-video-wall-config.component.css"]
})
export class AddVideoWallConfigComponent implements OnInit, OnDestroy {
  loader: boolean;
  edit: boolean;
  sub: Subscription;

  selectedSearchType = -1;
  tagList = [];
  categoryList = [];

  @Input("videoWall") videoWall;
  @Input("typeListData") typeListData = [];

  @Output("submit") submitForm: EventEmitter<any> = new EventEmitter();

  constructor(
    private settingS: SettingService,
    private contentS: ContentService
  ) {
    console.log(this.typeListData)
  }

  ngOnInit() {
    this.getTags();
    this.getCategoryList();
    this.sub = this.settingS.addEditOpen.subscribe(
      val => {
        if (val.open) {
          // console.log(val);
          if (val.edit) {
            this.edit = val.edit;
          } else {
            this.videoWall = new VideoWall();
            this.edit = false;
          }

        }
      }
    );
  }

  ngOnDestroy() {
    this.sub.unsubscribe();
  }


  sendEmitedData(error, message) {
    this.loader = false;
    // console.log(result);
    const emit_data = { alert: { error: error, message: message } };
    this.submitForm.emit(emit_data);
  }

  update() {
    this.loader = true;
    // console.log(this.supplier);

    let sendData = {
      name: this.videoWall.name,
      store_id: GET_USER().store_id,
      display_type: Number(this.videoWall.display_type),
      settings: {
        show_price: this.videoWall.show_price,
        show_availability: this.videoWall.show_availability,
        interval: this.videoWall.interval
      },
      options: Object.assign({}, this.typeListData.map(v => v.id))
    };

    this.settingS
      .updateVideoWall(sendData, this.videoWall.id)
      .then(res => {
        if (res.status === "OK") {
          this.sendEmitedData(false, res.result.message);
          this.videoWall = new VideoWall();
          this.selectedSearchType = -1;
          this.typeListData = [];
        } else {
          this.sendEmitedData(true, res.result.error);
        }
      })
      .catch(err => {
        console.log(err);
        this.sendEmitedData(
          true,
          "Something wrong!"
        );
      });
  }

  getTags() {
    this.contentS.getTags().subscribe(res => {
      if (res) {
        this.tagList = res;
      }
    });
  }

  getCategoryList() {
    this.contentS.getCategories().subscribe(res => {
      if (res) {
        this.categoryList = res;
      }
    });
  }


  deleteType(id) {

    var index = this.typeListData.findIndex(x=>x.id==id);
    if (index > -1) {
      this.typeListData.splice(index, 1);
    }

  }

  onChangeType(type) {
    console.log(type);
    if(Number(type) !=-1)
    {
      this.selectedSearchType = type;
      this.typeListData = [];
    }
    
  }

  onSelectTag(id) {
    console.log(id);
    if(id !="null")
    {
      const tag = this.tagList.find(x => x.id == id);
      this.typeListData.push(tag);
    }
   
  }

  onSelectCategory(id) {
    console.log(id);
    if(id !="null")
    {
      const category = this.categoryList.find(x => x.id == id);
      this.typeListData.push(category);
    }
   
  }

  selectProduct(product) {
    let prod={
      id:product.product_id,
      name:product.name
    }
    this.typeListData.push(prod);
  }

  save(f) {
    this.loader = true;

    const sendData = {
      name: this.videoWall.name,
      store_id: GET_USER().store_id,
      display_type: Number(this.videoWall.display_type),
      settings: {
        show_price: this.videoWall.show_price,
        show_availability: this.videoWall.show_availability,
        interval: this.videoWall.interval
      },
      options: Object.assign({}, this.typeListData.map(v => v.id))
    };

    console.log(sendData);

    this.settingS
      .addVideoWall(sendData)
      .then(res => {
        console.log(res);
        if (res.status === "OK") {
          this.sendEmitedData(false, res.result.message);
          this.videoWall = new VideoWall();
          this.selectedSearchType = -1;
          this.typeListData = [];
          f.form.reset();
        } else {
          this.sendEmitedData(true, res.result.error);
        }
      })
      .catch(err => {
        console.log(err);
        this.sendEmitedData(true, "Something wrong!");
      });
  }
}
