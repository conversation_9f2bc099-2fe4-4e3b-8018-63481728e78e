import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BusinessInfoComponent } from './business-info.component';
import { Routes, RouterModule } from '@angular/router';
import { SettingResolveService } from '../setting-service/setting-resolve.service';

const routes: Routes = [
  {
    path: '',
    component: BusinessInfoComponent,
    children: [
      {
          path: 'store-logo',
          loadChildren: () => import('../all-contents/store-logo/store-logo.module').then(m => m.StoreLogoModule)
      },
      {
          path: 'store-config',
          loadChildren: () => import('../all-contents/time-zone/time-zone.module').then(m => m.TimeZoneModule),
          resolve: { 'list': SettingResolveService }
      },
      {
        path: '**',
        redirectTo: 'store-logo',
        pathMatch: 'full',
      }
    ]
  }
]

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes)
  ],
  declarations: [BusinessInfoComponent]
})
export class BusinessInfoModule { }
