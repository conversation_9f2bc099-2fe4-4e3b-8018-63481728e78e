.custom-alert {
    position: fixed;
    top: 90px;
    right: 30px;
    z-index: 5000;
}

.m-5px {
    margin: 5px;
}

.flex-div {
    display: flex;
    align-items: center;
}

.icon {
    width: 50px;
    text-align: center;
}

.shipAccount {
    border-top: 1px solid #f2f2f2;
    padding: 10px 0px;
}

.shipDisLoader {
    width: 30px;
    padding-left: 30px;
    display: inline-block;
    top: -4px;
}

b {
    font-weight: 500;
}

.api-key-area {
    display: flex;
}

.service-view-icon {
    display: inline-block;
    border: 1px solid black;
    padding: 4px 10px;
    margin-right: 10px;
    cursor: pointer;
}

.m-portlet__body h4 {
    font-size: 18px;
}

.shipping-title {
    padding: 15px 15px;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
}

.shipping-title h4 {
    font-size: 22px;
}

hr {
    border-top: none;
    margin-top: 0;
    margin-bottom: 0;
}

.connect-shipper-btn {
    padding: 12px 50px;
}