<div class="custom-alert" #hasCusAlert></div>

<div class="m-subheader">
    <div class="d-flex align-items-center">
        <div class="mr-auto">
            <h3 class="m-subheader__title m-subheader__title--separator">
                Shipping Settings
            </h3>
            <ul class="m-subheader__breadcrumbs m-nav m-nav--inline">
                <li class="m-nav__item m-nav__item--home">
                    <a routerLink="/admin" class="m-nav__link m-nav__link--icon">
                        <i class="m-nav__link-icon la la-home"></i>
                    </a>
                </li>
                <li class="m-nav__separator">
                    <i class="fa fa-angle-right"></i>
                </li>
                <li class="m-nav__item">
                    <a class="m-nav__link">
                        <span class="m-nav__link-text">
              Settings
            </span>
                    </a>
                </li>
                <li class="m-nav__separator" style="padding-left: 10px">
                    <i class="fa fa-angle-right"></i>
                </li>
                <li class="m-nav__item">
                    <a class="m-nav__link">
                        <span class="m-nav__link-text">
              Shipping Settings
            </span>
                    </a>
                </li>
            </ul>
        </div>
    </div>
</div>
<!-- END: Subheader -->
<div class="m-content animated fadeIn">
    <div class="m-portlet m-portlet--tabs">
        <div class="m-portlet__body">
            <div class="row">
                <div class="col-md-4">
                    <h4>Shipping origin</h4>
                    <span>Used to calculate shipping rates at checkout.</span>
                </div>
                <div class="col-md-8">
                    <h5>Shipping from</h5>
                    <div class="flex-div m-5px" *ngFor="let loc of locations">
                        <!-- <div class="icon">
                            <i class="fa fa-map-marker"></i>
                        </div> -->
                        <div style="flex: 1">
                            <h6>
                                {{loc.name}}
                                <span *ngIf="loc.is_online" class="m-badge m-badge--wide m-badge--primary m-5px">online</span>
                                <span *ngIf="inStoreLocation == loc.id" class="m-badge m-badge--wide m-badge--info m-5px">in store</span>
                                <span class="cursor-pointer m-5px" (click)="addLocation(loc)"><i class="fa fa-edit"></i></span>
                            </h6>
                            <span>{{formatAddress(loc)}}</span><br><span>{{loc.phone}}</span>
                            <p style="color: red;">{{checkValidShippingSetting(loc)}}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <!-- Shipping option area -->
        <div class="m-portlet__body p-0">
            <div class="row shipping-title m-0">
                <div class="col-md-12">
                    <h4 class="mb-0">Shipping Options</h4>
                </div>
            </div>
        </div>

        <div class="m-portlet__body">
            <div class="row align-items-center">
                <div class="col-md-4">
                    <h4>Flat-Rate Pricing</h4>
                    <span>Add a flat fee to shipping orders</span>
                </div>
                <div class="col-md-8">
                    <form [formGroup]="flatshippingForm" (ngSubmit)="saveFlatShipping()">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="input-group m-input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">
                                        {{currency && currency.symbol ? currency.symbol : "$"}}
                                    </span>
                                    </div>
                                    <input numberOnly formControlName="flat_rate" class="form-control m-input" type="text" numberOnly placeholder="Rate" autocomplete="off">
                                    <div class="input-group-append">
                                        <small class="input-group-text">
                                            {{currency && currency.code ? currency.code : "USD"}}
                                        </small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <button type="submit" [disabled]="!flatshippingForm.valid" class="btn btn-md btn-dark">Submit
                                </button>
                            </div>

                        </div>
                    </form>
                </div>
            </div>
        </div>
        <hr/>




        <div class="m-portlet__body">
            <div class="row align-items-center">
                <div class="col-md-4">
                    <h4>Variable (distance) Pricing</h4>
                    <span>Charge for Shipping based on order weight x distance x multiplier</span>
                </div>
                <div class="col-md-8">
                    <form [formGroup]="shippingConfigForm" (ngSubmit)="saveStandardRate()">
                        <!-- <div class="row">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <input class="form-control m-input" type="text" placeholder="Multiplier" autocomplete="off">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <button type="submit" [disabled]="!shippingConfigForm.valid || standard_shipping_error" class="btn btn-md btn-dark">Submit
                                </button>
                            </div>
                        </div>
                        <div class="row" id="standard_rate_container" style="display: none;">
                            <div class="col-md-6">
                                <label>Shipping Constant Rate</label>
                                <div class="input-group m-input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">
                                        {{currency && currency.symbol ? currency.symbol : "$"}}
                                    </span>
                                    </div>
                                    <input numberOnly formControlName="freight_rate" class="form-control m-input" type="text" numberOnly (ngModelChange)="changeRate()" placeholder="Rate" autocomplete="off">
                                    <div class="input-group-append">
                                        <small class="input-group-text">
                                            {{currency && currency.code ? currency.code : "USD"}}
                                        </small>
                                    </div>
                                </div>
                                <span *ngIf="standard_shipping_error">
                                    <small class="error">Rate should be a valid number</small>
                                </span>
                            </div>

                        </div> -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <div class="m-radio-inline">
                                        <label>Do you want to activate standard shipping options ?</label>&nbsp;&nbsp;
                                        <label class="m-radio">
                                            <input type="radio" name="allow_standard_shipping" (click)="showRate(true)"
                                                   formControlName="allow_standard_shipping"
                                                   [value]="true"/>
                                            Yes <span></span>
                                        </label>
                                        <label class="m-radio">
                                            <input type="radio" name="allow_standard_shipping" (click)="showRate(false)" (change)="changeAllowedValue()"
                                                   formControlName="allow_standard_shipping"
                                                   [value]="false"/>
                                            No <span></span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row" id="standard_rate_container" style="display: none;">
                            <div class="col-md-6">
                                <!-- <label>Shipping Constant Rate</label> -->
                                <div class="input-group m-input-group">
                                    <div class="input-group-prepend">
                                    <span class="input-group-text">
                                        {{currency && currency.symbol ? currency.symbol : "$"}}
                                    </span>
                                    </div>
                                    <input numberOnly formControlName="freight_rate" class="form-control m-input"
                                           type="text" numberOnly (ngModelChange)="changeRate()"
                                           placeholder="Multiplier" autocomplete="off">
                                    <div class="input-group-append">
                                        <small class="input-group-text">
                                            {{currency && currency.code ? currency.code : "USD"}}
                                        </small>
                                    </div>
                                </div>
                                <span *ngIf="standard_shipping_error">
                                    <small class="error">Rate should be a valid number</small>
                                </span>
                            </div>

                        </div>
                        <div class="row">
                            <div class="col-md-12 mt-2">
                                <button type="submit" [disabled]="!shippingConfigForm.valid || standard_shipping_error"
                                        class="btn btn-sm btn-dark">Submit
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <hr/>


        <div class="m-portlet__body">
            <div class="row">
                <div class="col-md-4">
                    <h4>Premium Shipping</h4>
                    <span>
                        Calculated shipping fees based on carrier shipping method.<br>
                        Print outbound and return labels, provide shipping status to cutomers and more
                    </span>
                </div>

                <div class="col-md-8">
                    <div class="row">
                        <div class="col-md-12 mb-4" *ngIf="is_connect_shipper_visible">
                            <button type="button" (click)="is_connect_shipper_visible = !is_connect_shipper_visible" class="btn btn-md btn-dark connect-shipper-btn">Connect Shipper</button>
                        </div>
                        <div class="col-md-12" *ngIf="!is_connect_shipper_visible">
                            <h5>Carrier accounts</h5>
                            <p>Connect your carriers with ShipEngine. Add ShipEngine API here. <a href="https://shipengine.com" target="_blank">Click here to connect with ShipEngine</a></p>

                            <div class="api-key-area">
                                <input class="form-control mr-2" type="text" name="api_key" placeholder="Please enter shipEngine api key" [(ngModel)]="shipping_api_key">

                                <button [disabled]="shipping_api_key == ''" class="btn btn-dark btn-md" title="Connect with your shipengine carrier." (click)="onClickSyncShippingCarrier()">
                                    Connect
                                </button>
                            </div>

                            <div style="margin: 10px 0px;" *ngIf="shippingList.length > 0; else noCarrier">
                                <div class="flex-div shipAccount" *ngFor="let ship of shippingList">
                                    <div style="width: 100px;">
                                        <img *ngIf="ship?.img" class="carrier-service-logo" alt="UPS® logo" [src]="ship?.img" width="64" height="64">
                                    </div>
                                    <div style="flex: 1">
                                        <h6>{{ship?.name | uppercase}}</h6>
                                        <div>
                                            <p>
                                                Carrier Id: <b>{{ship?.carrier_id}}</b><br> Account: <b>{{ship?.config.account_number}}</b>
                                            </p>
                                        </div>
                                    </div>
                                    <div class="text-right" style="flex: 1">
                                        <div class="service-view-icon" (click)="openService(ship)" title="View all services">
                                            <span class="fa fa-eye"></span>
                                        </div>
                                        <button class="btn btn-sm btn-danger" (click)="disconnectCarrier(ship.name)">
                                            Disconnect Carrier
                                            <div class="m-loader m-loader--light shipDisLoader"
                                                *ngIf="deleteId == ship.name"></div>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <ng-template #noCarrier>
                                <p>No carrier has been selected.</p>
                            </ng-template>
                        </div>
                    </div>

                </div>
            </div>
        </div>
        <hr/>


        <div class="m-portlet__body">
            <div class="row">
                <div class="col-md-4">
                    <h4>Shipping Configuration</h4>
                </div>
                <div class="col-md-8">
                    <app-shipping-configuration-form [shippingList]="shippingList"></app-shipping-configuration-form>
                </div>
            </div>
        </div>

    </div>
</div>


<!-- sidebar -->

<div class="native-routing animated">
    <button class="close-sidebar btn btn-sm btn-brand">
        <i class="fa fa-chevron-right"></i>
    </button>
    <span class="close-sidebar-upper">
    <i class="la la-close"></i>
  </span>
    <div class="native-routing-container">
        <ng-template #addFormCard></ng-template>
    </div>
</div>