import { Component, OnInit, Input, OnChanges, ElementRef, ViewChild } from '@angular/core';
import { SettingService } from '../../setting-service/setting.service';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { AlertService } from './../../../../../modules/alert/alert.service';
import { Helpers } from './../../../../../helpers';

@Component({
  selector: 'app-view-services-modal',
  templateUrl: './view-services-modal.component.html',
  styleUrls: ['./view-services-modal.component.css']
})
export class ViewServicesModalComponent implements OnInit {

  @Input() shipping: any;
  servicesList: any[] = [];
  @ViewChild("hasCusAlert") alertContainer: ElementRef;

  constructor(
    private service: SettingService,
    public activeModal: NgbActiveModal,
    private alert: AlertService
  ) { }

  ngOnInit() {
    console.log(this.shipping)
    if (this.shipping && this.shipping.id) {
      this.getAvailableServices();
    }
  }

  getAvailableServices() {
    Helpers.setLoading(true);
    this.service.getAvailableShippingServices(this.shipping.id)
      .then(res => {
        Helpers.setLoading(false);
        if (res.status === 'OK') {
          this.servicesList = res.result.data.services;
        } else {
          this.servicesList = [];
        }
      })
      .catch(err => {
        Helpers.setLoading(false);
        this.servicesList = [];
      })
  }

  submit(type: string) {
    Helpers.setLoading(true);
    let data = {};
    data['type'] = type;
    if (type === 'selected') {
      data['services'] = this.servicesList;
    }
    this.service.saveShippingServices(data, this.shipping.id)
      .then( res => {
        Helpers.setLoading(false);
        if (res.status === "OK") {
          this.alert.success(
            this.alertContainer,
            "Successfully saved.",
            true,
            2000
          );
          setTimeout(() => {
            this.activeModal.close();
          }, 2000);
        } else {
          this.alert.error(this.alertContainer, 'Somthing went wrong!!!', true, 3000);
        }
      })
      .catch(err => {
        Helpers.setLoading(false);
        this.alert.error(
          this.alertContainer,
          "Someting went wrong!!! Please try again.",
          true,
          3000
        );
      })
  }

}
