import { Helpers } from './../../../../helpers';
import {
    Component,
    OnInit,
    ComponentRef,
    ElementRef,
    ViewChild,
    ViewContainerRef,
    HostListener,
    ComponentFactoryResolver
} from "@angular/core";
import {Shipping} from "../models/settings.models";
import {AlertService} from "../../../../modules/alert/alert.service";
import {SettingService} from "../setting-service/setting.service";
import {SidebarService} from "../../sidebar-service/sidebar.service";
import {map, catchError} from "rxjs/operators";
import {of} from "rxjs";
import {MainFormComponent} from "./main-form/main-form.component";
import {DialogBoxComponent} from "../../../../modules/dialog-box/dialog-box.component";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {AddressModalComponent} from "./address-modal/address-modal.component";
import {GET_USER} from "../../../../globals/_classes/functions";
import {FormBuilder, FormGroup, Validators} from "@angular/forms";
import { ViewServicesModalComponent } from './view-services-modal/view-services-modal.component';

@Component({
  selector: "app-shipping-settings",
  templateUrl: "./shipping-settings.component.html",
  styleUrls: ["./shipping-settings.component.css"]
})
export class ShippingSettingsComponent implements OnInit {
  loader: boolean;
  shippings = {
    fedex: null,
    ups: null,
    usps: null
  };
  componentRef: ComponentRef<any>;
  inStoreLocation = GET_USER().location_id;
  shippingConfigForm: FormGroup;
  flatshippingForm: FormGroup;
  sideBaropen: boolean;
  deleteId: string;
  standard_shipping_error = false;
  locations = [];
  currency;
  shippingObj;
  shipping_api_key: string;

  @ViewChild("hasCusAlert") alertContainer: ElementRef;
  @ViewChild("addFormCard", { read: ViewContainerRef })
  cardForm: ViewContainerRef;
  shippingList: any = [];
  is_connect_shipper_visible: boolean;

  @HostListener("window:resize", ["$event"])
  onResize(event) {
    if (this.sideBaropen) {
      $(".native-routing").css("display", "block");
      this.openSidebar();
    }
  }

  constructor(
    private alert: AlertService,
    private settingS: SettingService,
    private sidebarS: SidebarService,
    private modalService: NgbModal,
    private resolver: ComponentFactoryResolver,
    private fb: FormBuilder
  ) {
    this.getLocationWithAddress();
    this.currency = JSON.parse(localStorage.getItem("currency"));
    this.flatshippingForm = this.fb.group({
      flat_rate: ['', Validators.required]
    });
  }

  ngOnInit() {
    this.getShippins();
    this.shippingConfigForm = this.fb.group({
      allow_standard_shipping: ["", Validators.required],
      freight_rate: ["", Validators.required]
    });
    this.getStandardRate();
  }

  ngAfterViewInit() {
    //window.scrollTo(0, 0);
    this.closeSidebar();
    this.executeAction();
  }

  saveFlatShipping() {
    console.log(this.flatshippingForm.getRawValue());
    this.settingS
      .updateStoreConfig("shipping", this.flatshippingForm.getRawValue())
      .then(res => {
        this.loader = false;
        this.alert.success(this.alertContainer, "Successful", true, 5000);
      });
  }

  getLocationWithAddress() {
    this.settingS.getLocationWithAddress().subscribe(res => {
      this.locations = res;
    });
  }

  formatAddress(loc) {
    const { address, city, country, state, zipcode } = loc;
    return [address, city, country, state, zipcode].filter(f => f).join(", ");
  }

  editCarrier(type) {
    const data = this.shippings[type];
    this.openSidebar({ edit: true, type: type, data: data });
  }

  addCarrier(type) {
    this.openSidebar({ edit: false, type: type });
  }

  openSidebar(data?) {
    // let w =
    //   $(".global-sidebar-wrapper").width() +
    //   ($(window).width() > 992 ? 25 : 0) +
    //   "px";
    // this.sideBaropen = true;
    // $(".native-routing").css("display", "block");
    //this.sidebarS.openSidebar(w);
    if (data) {
      this.settingS.editShippingForm.next(data);
      this.createComponent();
    }
  }

  closeSidebar() {
    // $(".close-sidebar").click(e => {
    //   e.preventDefault();
    //   this.executeAction();
    // });
    // $(".close-sidebar-upper").click(e => {
    //   e.preventDefault();
    //   this.executeAction();
    // });
  }

  executeAction() {
    this.sideBaropen = false;
    this.sidebarS.removeSidebar();
    $(".native-routing").css("display", "none");
    if (this.componentRef) {
      this.componentRef.destroy();
    }
  }

  allertShow(e) {
    if (e.message) {
      if (e.status) {
        this.alert.success(this.alertContainer, e.message, true, 5000);
        this.executeAction();
        this.getShippins();
      } else {
        this.alert.error(this.alertContainer, e.message, true, 5000);
      }
    }
  }

  getShippins() {
    this.loader = true;
    this.settingS
      .getShippings()
      .pipe(
        map(res => {
          return res.result.data;
        }),
        catchError(err => {
          this.loader = false;
          return of([]);
        })
      )
      .subscribe(res => {
        this.loader = false;
        const names = [];
        if (res && res.length > 0) {
          this.is_connect_shipper_visible = false;
          this.shippingList = res;
          this.shippingList.map(ship => {
            if (ship.name === "stamps_com") {
              ship["img"] = "./assets/img/admin/shipping/stamps.svg";
            } else if (ship.name === "usps") {
              ship["img"] = "./assets/img/admin/shipping/usps.svg";
            } else if (ship.name === "fedex") {
              ship["img"] = "./assets/img/admin/shipping/fedex.svg";
            } else if (ship.name === "ups") {
              ship["img"] = "./assets/img/admin/shipping/ups.svg";
            } else {
              ship["img"] = "";
            }
          });
          // for (const s of res) {
          //     const name = s.name === 'stamps_com' ? 'usps' : s.name.toLowerCase();
          //     names.push(name);
          //     if (name == 'fedex') {
          //         this.shippings.fedex = s;
          //     } else if (name == 'ups') {
          //         this.shippings.ups = s;
          //     } else {
          //         this.shippings.usps = s;
          //     }
          // }
          // for (let s in this.shippings) {
          //     if (!names.includes(s)) {
          //         this.shippings[s] = null;
          //     }
          // }
        } else {
          this.shippingList = [];
          this.is_connect_shipper_visible = true;
        }
      });
    this.executeAction();
  }

  getStandardRate() {
    this.settingS.getStoreConfig("shipping").then(res => {
      if (res.status === "OK") {
        const allow_standard_shipping = res.result.data.hasOwnProperty(
          "allow_standard_shipping"
        )
          ? res.result.data.allow_standard_shipping
          : false;
        const rate = res.result.data.hasOwnProperty("freight_rate")
          ? res.result.data.freight_rate
          : "";
        const flat_rate = res.result.data.hasOwnProperty("flat_rate")
          ? res.result.data.flat_rate
          : "";
        this.shippingConfigForm
          .get("allow_standard_shipping")
          .setValue(allow_standard_shipping);
        this.shippingConfigForm.get("freight_rate").setValue(rate);
        this.flatshippingForm.get("flat_rate").setValue(flat_rate);
        if (allow_standard_shipping) {
          this.showRate(true);
        } else {
          this.showRate(false);
        }
      }
    });
  }

  saveStandardRate() {
    console.log(this.shippingConfigForm.value);
    this.loader = true;
    if (this.shippingConfigForm.value.allow_standard_shipping) {
      this.settingS
        .updateStoreConfig("shipping", this.shippingConfigForm.value)
        .then(res => {
          this.loader = false;
          this.alert.success(this.alertContainer, "Successful", true, 5000);
        });
    } else {
      const whenNo = this.shippingConfigForm.value;
      delete whenNo.freight_rate;
      this.settingS.updateStoreConfig("shipping", whenNo).then(res => {
        this.loader = false;
        this.alert.success(this.alertContainer, "Successful", true, 5000);
      });
    }
  }

  showRate(is_display) {
    if (is_display) {
      document.getElementById("standard_rate_container").style.display =
        "block";
    } else {
      document.getElementById("standard_rate_container").style.display = "none";
    }
  }

  changeRate() {
    const rate = this.shippingConfigForm.get("freight_rate").value;
    const allowed_value = this.shippingConfigForm.get("allow_standard_shipping")
      .value;
    if (allowed_value) {
      if (rate <= 0) {
        this.standard_shipping_error = true;
      } else {
        this.standard_shipping_error = false;
      }
    }
  }

  changeAllowedValue() {
    const allowed_value = this.shippingConfigForm.get("allow_standard_shipping")
      .value;
    if (!allowed_value) {
      this.standard_shipping_error = false;
    }
  }

  private createComponent() {
    const factory = this.resolver.resolveComponentFactory(MainFormComponent);
    this.cardForm.clear();
    this.componentRef = this.cardForm.createComponent(factory);
    this.componentRef.instance.allertShow.subscribe(e => this.allertShow(e));
  }

  disconnectCarrier(type) {
    const modalRef = this.modalService.open(DialogBoxComponent, {
      centered: true,
      size: "sm",
      windowClass: "animated fadeIn"
    });
    modalRef.componentInstance.massage = "Are you sure you want to delete?";
    modalRef.result.then(
      result => {
        if (result) {
          this.archiveCoupon(type);
        }
      },
      res => {
        console.log(res);
      }
    );
  }

  archiveCoupon(type) {
    const data = this.shippingList.find(ship => ship.name === type);
    if (!data) return;
    this.deleteId = type;
    this.settingS
      .deleteShipping(data["carrier_id"])
      .then(res => {
        if (res.status === "OK" && res.result.message) {
          this.allertShow({ status: true, message: res.result.message });
        } else {
          this.allertShow({ status: false, message: res.result.error });
        }
        this.deleteId = null;
      })
      .catch(err => {
        console.log(err);
        this.deleteId = null;
        this.allertShow({
          status: false,
          message: "Something went wrong!!! Please try again."
        });
      });
  }

  addLocation(loc) {
    const modalRef = this.modalService.open(AddressModalComponent, {
      centered: true,
      size: "lg",
      backdrop : 'static',
      keyboard : false
    });
    modalRef.componentInstance.locationId = loc.id;
    modalRef.result.then(
      result => {
        this.getLocationWithAddress();
      },
      res => {
        console.log(res);
      }
    );
  }

  onClickSyncShippingCarrier() {
    let sendData = {
      "api-key": this.shipping_api_key,
      "name": "all"
    };
    Helpers.setLoading(true);

    this.settingS
      .syncShippingEngine(sendData)
      .then(res => {
        Helpers.setLoading(false);
        if (res.status == "OK") {
          this.allertShow({ message: res.result.message, status: true });
        } else {
          this.allertShow({ message: res.result.message, status: false });
        }
      })
      .catch(err => {
        Helpers.setLoading(false);
        this.allertShow({ message: "Somthing went wrong", status: false });
      });
  }

  openService(shipping) {
    const modalRef = this.modalService.open(ViewServicesModalComponent, {
      centered: true,
      size: "lg"
    });
    modalRef.componentInstance.shipping = shipping;
  }

  checkValidShippingSetting(loc): string {
    for(let value of Object.values(loc)) {
      if (value == undefined || value == '' || value == null) {
        return 'Invalid address.'
      }
    }
  }
}
