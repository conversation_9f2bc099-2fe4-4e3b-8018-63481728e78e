import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AddressModalComponent } from './address-modal.component';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { Select2NormalModule } from '../../../../../modules/select2-normal/select2-normal.module';

@NgModule({
  imports: [
    CommonModule,
    NgbModule,
    FormsModule,
    ReactiveFormsModule,
    Select2NormalModule
  ],
  exports: [AddressModalComponent],
  declarations: [AddressModalComponent]
})
export class AddressModalModule { }
