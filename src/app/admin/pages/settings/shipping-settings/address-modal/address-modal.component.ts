import { Component, OnInit, ViewChild, ElementRef, Input } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { SettingService } from "../../setting-service/setting.service";
import { AlertService } from "../../../../../modules/alert/alert.service";
import { allCountry } from "../../../../../globals/_classes/country";
import { algoliaPlaceSearchConfig } from "../../../../../globals/_classes/functions";
//import * as places from 'places.js';
import * as $ from 'jquery';

@Component({
  selector: "app-address-modal",
  templateUrl: "./address-modal.component.html",
  styleUrls: ["./address-modal.component.css"]
})
export class AddressModalComponent implements OnInit {
  loading: boolean;
  loader: boolean;
  distanceForm: FormGroup;
  countries = allCountry;
  allState;
  countryCode = 230;
  stateCode;
  placesAutocomplete: any;
  @Input("locationId") locationId;
  @ViewChild("hasCusAlert") alertContainer: ElementRef;
  selectedCountry = 'us';

  constructor(
    public activeModal: NgbActiveModal,
    private fb: FormBuilder,
    private service: SettingService,
    private alert: AlertService
  ) {}

  ngOnInit() {
    this.getLocationData();
    this.distanceForm = this.fb.group(this.initForm());
    this.countries.sort((a, b) => a.name.localeCompare(b.name));
    this.countryCode = 230;
    const storeCountry = 'US'; //default us
    this.placesAutocomplete = algoliaPlaceSearchConfig('city', storeCountry, this.selectedCountry)
    this.placesAutocomplete.on("change", function resultSelected(e) {
      document.body.click();
      $('#zipcode').val(e.suggestion.postcode || '');
      $('#state').val(e.suggestion.administrative  || '');
    });
    this.placesAutocomplete.configure({ countries: [storeCountry] });
  }

  private initForm() {
    return {
      address: ["", Validators.required],
      city: ["", Validators.required],
      state: ["", Validators.required],
      zipcode: ["", Validators.required],
      country: ["", Validators.required],
      phone: ["", Validators.required]
    };
  }

  getLocationData() {
    this.loader = true;
    this.service.getDeliveryDistance(this.locationId).subscribe(res => {
      if (res) {
        this.distanceForm.patchValue(res);
        const country = this.countries.find(
          f => f.code.toUpperCase() == res.country
        );
        this.countryCode = country ? country.id : 230;
        if(!country) {
          this.distanceForm.get('country').setValue('US');
        }
      
      } else {
        this.distanceForm.reset();
      }
      this.loader = false;
      this.getState();
    });
  }

  selectLocation() {
    this.getLocationData();
  }

  submitForm() {
    this.loading = true;
    this.service
      .saveDeliveryDistance(this.distanceForm.value, this.locationId)
      .then(res => {
        this.loading = false;
        if (res.status === "OK" && res.result.message) {
          this.alert.success(
            this.alertContainer,
            res.result.message,
            true,
            2000
          );
          setTimeout(() => {
            this.activeModal.close();
          }, 2000);
        } else {
          this.alert.error(this.alertContainer, res.result.error, true, 3000);
        }
      })
      .catch(err => {
        this.loading = false;
        this.alert.error(
          this.alertContainer,
          "Someting went wrong!!! Please try again.",
          true,
          3000
        );
      });
  }

  private getState() {
    this.service.getState(this.countryCode).subscribe(res => {
      this.allState = res;
      const state = this.distanceForm.get("state");
      if (this.allState && state.value) {
        const data = this.allState.find(
          f => f.code.toUpperCase() == state.value
        );
        this.stateCode = data ? data.id : null;
        if (!data) {
          state.setValue("");
        }
      }
    });
  }

  changeCountry(e) {
    this.countryCode = e.id ? e.id : 230;
    const selectedCountry = this.countries.find(c => c.id === this.countryCode)
    this.getState();
    const country = this.countries.find(f => f.id === this.countryCode);
    this.distanceForm.get("country").setValue(country.code.toUpperCase());
    this.placesAutocomplete.configure({ countries: [selectedCountry.code] });

    $("#city").val('')
    $("#state").val('')
    $("#zipcode").val('')
    this.distanceForm.get("city").setValue('');
    this.distanceForm.get("state").setValue('');
    this.distanceForm.get("zipcode").setValue('');
  }

  onChangeData() {
    this.distanceForm.get("city").setValue($('#city').val().toString());
    this.distanceForm.get("state").setValue($('#state').val().toString());
    this.distanceForm.get("zipcode").setValue($('#zipcode').val().toString());
  }

  changeState(e) {
    if (e.id) {
      this.stateCode = e.id;
      const state = this.allState.find(f => f.id === e.id);
      this.distanceForm.get("state").setValue(state.code.toUpperCase());
    } else {
      this.distanceForm.get("state").setValue("");
    }
  }
}
