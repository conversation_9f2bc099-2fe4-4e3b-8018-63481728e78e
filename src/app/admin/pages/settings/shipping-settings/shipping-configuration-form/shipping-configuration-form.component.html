<div class="custom-alert alert-area" #hasCusAlert></div>

<form class="m-form" [formGroup]="shippingConfigurationForm">
    <div class="row">
        <div class="form-group col-md-12">
            <div class="row order-shipped">
                <div class="col-xl-6 col-lg-6 col-md-6 col-sm-6">
                    <label>Default shipping carrier/method for shipping to customer</label>
                </div>
                <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
                    <select class="form-control m-select" formControlName="shipping_carrier_to_customer">
                      <option value="null">-- None --</option>
                      <option *ngFor="let ship of shippingList" [value]="ship.id">{{ship.name}}</option>
                      <!-- <option *ngIf="shippingList['fedex']" [value]="shippingList['fedex'].id">{{shippingList['fedex'].name}}
                      </option>
                      <option *ngIf="shippingList['ups']" [value]="shippingList['ups'].id">{{shippingList['ups'].name}}</option> -->
                    </select>
                </div>
            </div>
        </div>

        <!-- <div class="form-group col-md-12">
      <div class="row ">
        <div class="col-xl-6 col-lg-6 col-md-6 col-sm-6">
          <label>Default shipping carrier/method for shipping from customer</label>
        </div>
        <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
          <select class="form-control m-select" formControlName="shipping_carrier_from_customer">
            <option value="">--Select--</option>
            <option *ngIf="shippingList['fedex']" [value]="shippingList['fedex'].id">{{shippingList['fedex'].name}}
            </option>
            <option *ngIf="shippingList['ups']" [value]="shippingList['ups'].id">{{shippingList['ups'].name}}</option>
          </select>
        </div>

      </div>
    </div> -->

        <div class="form-group col-md-12">
            <div class="row order-shipped">
                <div class="col-xl-6 col-lg-6 col-md-6 col-sm-6">
                    <label>Time from order creation until shipped </label>
                </div>
                <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
                    <select class="form-control m-select" formControlName="shipping_time_type">
            <option value="day">Day</option>
            <option value="time">Time</option>
          </select>
                </div>
                <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
                    <input type="number" class="form-control m-input" placeholder="Value" formControlName="shipping_time_value">
                </div>
            </div>
        </div>


        <div class="form-group col-md-12">
            <div class="row order-shipped">
                <div class="col-xl-6 col-lg-6 col-md-6 col-sm-6">
                    <label>Does customer pay two-way shipping?</label>
                </div>
                <div class="col-xl-6 col-lg-6 col-md-6 col-sm-6">
                    <div class="m-radio-inline">
                        <label class="m-radio">
              <input type="radio" name="shipping_is_two_way_shipping" formControlName="shipping_is_two_way_shipping"
                [value]="true" />
              Yes <span></span>
            </label>
                        <label class="m-radio">
              <input type="radio" name="shipping_is_two_way_shipping" formControlName="shipping_is_two_way_shipping"
                [value]="false" />
              No <span></span>
            </label>
                    </div>
                </div>
            </div>
        </div>


        <div class="form-group m-form__group col-md-12">
            <div class="row order-shipped">
                <div class="col-xl-6 col-lg-6 col-md-6 col-sm-6">
                    <label>Shipping & handling amount</label>
                </div>
                <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
                    <div class="input-group m-input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text" id="table-cost-addon">
                {{ currency.symbol? currency.symbol : '$'}}
              </span>
                        </div>
                        <input numberOnly class="form-control m-input" formControlName="shipping_amount" type="text" placeholder="0.00" autocomplete="off">
                        <div class="input-group-append">
                            <small class="input-group-text" id="table-cost-addon">
                {{ currency.code? currency.code : 'USD'}}
              </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- <div class="form-group m-form__group col-md-12">
      <div class="row order-shipped">
        <div class="col-xl-6 col-lg-6 col-md-6 col-sm-6">
          <label>Shipping discounts</label>
        </div>
        <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
          <div class="input-group m-input-group">
            <input numberOnly class="form-control m-input" type="text" formControlName="shipping_discount"
              placeholder="0.00" autocomplete="off">
            <div class="input-group-append">
              <span class="input-group-text" id="table-cost-addon">
                %
              </span>
            </div>
          </div>
        </div>
      </div>
    </div> -->



        <div class="col-md-6 col-md-offset-6">
            <button class="btn btn-brand" (click)="onClickSave()">Save</button>
        </div>
    </div>
</form>