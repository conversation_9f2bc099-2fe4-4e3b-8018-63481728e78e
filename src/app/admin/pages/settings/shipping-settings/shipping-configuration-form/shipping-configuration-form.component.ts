import { Component, OnInit, Input, ViewChild, ElementRef } from '@angular/core';
import { FormGroup, FormBuilder } from '@angular/forms';
import { HttpService } from '../../../../../modules/http-with-injector/http.service';
import { AlertService } from '../../../../../modules/alert/alert.service';

@Component({
  selector: 'app-shipping-configuration-form',
  templateUrl: './shipping-configuration-form.component.html',
  styleUrls: ['./shipping-configuration-form.component.css']
})
export class ShippingConfigurationFormComponent implements OnInit {


  shippingConfigurationForm: FormGroup;
  currency;
  @Input() shippingList;

  @ViewChild("hasCusAlert") alertContainer: ElementRef;

  constructor(
    private formBuilder: FormBuilder,
    private http: HttpService,
    private alertS: AlertService
  ) { }

  ngOnInit() {
    this.shippingConfigurationForm = this.formBuilder.group({
      shipping_carrier_to_customer: [null],
      shipping_carrier_from_customer: [""],
      shipping_time_type: ["day"],
      shipping_time_value: [""],
      shipping_is_two_way_shipping: [false],
      shipping_amount: [""],
      shipping_discount: [""]
    })

    this.currency = JSON.parse(localStorage.getItem("currency"));

    this.getShippingConfiguration();
  }


  getShippingConfiguration() {
    this.http.get('store-config/shipping').subscribe(res => {
      const data = res.result.data ? res.result.data : null;
      if (data != null) {
        let shipping_config = {
          shipping_carrier_to_customer: data.carrier_to_customer ? data.carrier_to_customer : null,
          shipping_carrier_from_customer: data.carrier_from_customer,
          shipping_time_type: data.lead_time_type,
          shipping_time_value: data.lead_time_value,
          shipping_is_two_way_shipping: data.is_two_way_shipping,
          shipping_amount: data.handling_amount,
          shipping_discount: data.discount
        }

        this.shippingConfigurationForm.patchValue(shipping_config);
      }
    })
  }


  onClickSave() {
    let send_data = {
      shipping_carrier_to_customer: parseInt(this.shippingConfigurationForm.get('shipping_carrier_to_customer').value),
      shipping_time_type: this.shippingConfigurationForm.get('shipping_time_type').value,
      shipping_time_value: parseInt(this.shippingConfigurationForm.get('shipping_time_value').value ? this.shippingConfigurationForm.get('shipping_time_value').value : 0),
      shipping_is_two_way_shipping: this.shippingConfigurationForm.get('shipping_is_two_way_shipping').value,
      shipping_amount: parseFloat(this.shippingConfigurationForm.get('shipping_amount').value ? this.shippingConfigurationForm.get('shipping_amount').value : 0),
    }

    this.http.post('store-config/update/shipping', send_data).subscribe(res => {
      if (res.status == "OK") {
        this.alertS.success(this.alertContainer, "Successfully saved", true, 5000);
      }
      else {
        this.alertS.error(this.alertContainer, "Something Wrong!!", true, 5000);
      }
    }, err => {
      console.log(err)
    })
  }

}
