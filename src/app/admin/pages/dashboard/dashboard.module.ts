import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { Routes, RouterModule } from "@angular/router";
import { LayoutModule } from "../../layouts/layout.module";
import { DashboardComponent } from "./dashboard.component";
import { PagesComponent } from "../pages.component";
import { DashboarServiceModule } from "./dashboard.service/dashboard-service.module";
import { BusinessCateComponent } from "./business-cate/business-cate.component";
import { GridListComponent } from "./grid-list/grid-list.component";
import { OptionalServicesComponent } from "./optional-services/optional-services.component";
import { SafeHtmlModule } from "../../../modules/safe-html/safe-html.pipe";
import { ContentService } from "../settings/setting-service/contents.service";
import { CurrencyFormatModule } from "../../../modules/currency-format/currency-format.pipe";
import { ReactiveFormsModule, FormsModule } from "@angular/forms";
import { DialogBoxSubscriptionComponent } from "../../../modules/dialog-box-subscription/dialog-box-subscription.component";
import { DialogBoxSubscriptionModule } from "../../../modules/dialog-box-subscription/dialog-box-subscription.module";
import { PricingService } from "../pricing/pricing.service";
import { OnboardingWizardComponent } from "../onboarding-wizard/onboarding-wizard.component";
import { OnboardingWizardModule } from "../onboarding-wizard/onboarding-wizard.module";

const routes: Routes = [
  {
    path: "",
    component: PagesComponent,
    children: [{ path: "", component: DashboardComponent }]
  }
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    LayoutModule,
    ReactiveFormsModule,
    DashboarServiceModule.forRoot(),
    SafeHtmlModule,
     CurrencyFormatModule,
     FormsModule,
     OnboardingWizardModule,
     DialogBoxSubscriptionModule
  ],
  exports: [RouterModule],
  declarations: [
    DashboardComponent,
    BusinessCateComponent,
    GridListComponent,
    OptionalServicesComponent
  ],
  providers: [ContentService,PricingService],
  entryComponents:[OnboardingWizardComponent,DialogBoxSubscriptionComponent]
})
export class DashboardkModule {}
