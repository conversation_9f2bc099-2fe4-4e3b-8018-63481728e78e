.custom-alert {
    position: fixed;
    top: 90px;
    right: 30px;
    z-index: 5000;
}

.paragraph-body {
    padding-top: 20px;
}

.img-body {
    padding: 20px;
}

.img-body iframe {
    width: 100%;
    height: 300px;
}

.btn-group-dashboard button {
    margin: 5px;
}

.dashboard-box {
    display: table;
    width: 100%;
    background-image: linear-gradient(to right, #9B468A, #6D1987);
    min-height: 150px;
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.10);
    cursor: pointer;
    padding: 15px;
}

.inventory-box {
    background-image: linear-gradient(to right, #9B468A, #6D1987);
}

.creatorder-box {
    background-image: linear-gradient(to right, #F46D54, #D4422E);
}

.reservation-box {
    background-image: linear-gradient(to right, #2FA8A8, #1F7F78);
}

.pointsale-box {
    background-image: linear-gradient(to right, #3AA8CD, #2698B4);
}

.dashboard-box:hover {
    box-shadow: none !important;
}

.dashboard-box span {
    display: table-cell;
    vertical-align: middle;
}

.dashboard-box span i {
    font-size: 112px;
}

.inventory-box span i {
    color: #A94BAD;
}

.creatorder-box span i {
    color: #ea7d6d;
}

.reservation-box span i {
    color: #39bbb5;
}

.pointsale-box span i {
    color: #40B1D2;
}

.dashboard-box h5 {
    display: table-cell;
    vertical-align: middle;
    text-align: right;
    font-size: 25px;
    font-weight: 400;
    color: #EEFBFB;
    /*text-transform: uppercase;*/
    line-height: 35px;
    padding-right: 10px;
}

.publish-btn {
    background-color: #fff;
    font-size: 16px;
    font-weight: 500;
    color: #3f4047;
    border-radius: 5px !important;
    padding: 10px 20px;
    box-shadow: 0px 1px 15px 1px rgba(69, 65, 78, 0.1) !important;
}

.publish-btn:hover {
    background-color: #fff !important;
    box-shadow: none;
    border: 1px solid #fff;
}

@media (min-width: 1350px) and (max-width: 1530px) {
    .dashboard-box {
        min-height: 160px;
    }
    .dashboard-box span i {
        font-size: 60px;
    }
    .dashboard-box h5 {
        font-size: 18px;
        line-height: 25px
    }
}

@media (min-width: 1200px) and (max-width: 1349px) {
    .dashboard-box {
        min-height: 150px;
    }
    .dashboard-box span i {
        font-size: 55px;
    }
    .dashboard-box h5 {
        font-size: 16px;
        line-height: 25px
    }
}

@media (min-width: 992px) and (max-width: 1199px) {
    .dashboard-box {
        margin-bottom: 30px;
    }
}

@media (min-width: 768px) and (max-width: 992px) {
    .dashboard-top-area {
        margin-bottom: 0 !important;
    }
    .dashboard-box {
        min-height: 120px;
        margin-bottom: 30px;
    }
    .dashboard-box span i {
        font-size: 60px;
    }
    .dashboard-box h5 {
        font-size: 20px;
        line-height: 22px
    }
}

@media (min-width: 576px) and (max-width: 767px) {
    .dashboard-top-area {
        margin-bottom: 0 !important;
    }
    .dashboard-box {
        min-height: 140px;
        margin-bottom: 30px;
    }
    .dashboard-box span i {
        font-size: 50px;
    }
    .dashboard-box h5 {
        font-size: 16px;
        line-height: 25px
    }
}

@media (min-width: 200px) and (max-width: 575px) {
    .dashboard-top-area {
        margin-bottom: 0 !important;
    }
    .dashboard-box {
        min-height: 150px;
        margin-bottom: 30px;
    }
    .dashboard-box span i {
        font-size: 65px;
    }
    .dashboard-box h5 {
        font-size: 20px;
        line-height: 25px
    }
}


/* new css */

.dashboard-gettingstart .m-portlet .m-portlet__body {
    /* padding: 0 !important; */
    padding: 15px 2.2rem 5px !important;
}

.gettingstart-close {
    position: absolute;
    top: 25px;
    right: 40px;
    cursor: pointer;
}

.gettingstart-close i {
    font-size: 22px;
}


/* gettingstart new css */

.rentmy-gettingstart-box2 {
    /* background-color: #fff; */
    text-align: center;
    margin: 10px 0 20px;
    transition: all .5s;
    position: relative;
    border-radius: 2px;
    box-shadow: 0px 0px 8px -1px rgba(190, 189, 189, 0.4);
    overflow: hidden;
}

.rentmy-gettingstart-box2.gettingstart-box {
    /* background-color: #e8929a; */
    background-image: linear-gradient(to right, #e8929a, #e8747f);
}

.rentmy-gettingstart-box2.gettingstart-box2 {
    /* background-color: #51b1ca; */
    background-image: linear-gradient(to right, #51b1ca, #2fa5c3);
}

.rentmy-gettingstart-box2.gettingstart-box3 {
    /* background-color: #46C373; */
    background-image: linear-gradient(to right, #46C373, #1fbb58);
}

.rentmy-gettingstart-body {
    display: table;
}

.rentmy-gettingstart-icon,
.rentmy-gettingstart-text {
    display: table-cell;
    vertical-align: middle;
}

.rentmy-gettingstart-icon {
    padding: 25px;
}


/* .gettingstart-box .rentmy-gettingstart-icon {
    background-color: #e8929a;
}

.gettingstart-box2 .rentmy-gettingstart-icon {
    background-color: #51b1ca;
}

.gettingstart-box3 .rentmy-gettingstart-icon {
    background-color: #46C373;
} */

.rentmy-gettingstart-text {
    width: 100%;
    position: relative;
}


/* .rentmy-gettingstart-box2 .gettingstart-text {
    border-left: 5px solid #ce7e85;
    background-color: #e8929a;
}

.rentmy-gettingstart-box2 .gettingstart-text2 {
    border-left: 5px solid #3a9ab3;
    background-color: #51b1ca;
}

.rentmy-gettingstart-box2 .gettingstart-text3 {
    border-left: 5px solid #31a95c;
    background-color: #46C373;
} */

.rentmy-gettingstart-box2:hover {
    box-shadow: 0px 0px 10px #eee;
}

.core-icon-image {
    width: 70px;
    display: inline-block;
}

.rentmy-gettingstart-box2 h4 {
    font-size: 20px;
    font-weight: 400;
    padding: 0;
    color: #fff;
    line-height: 25px;
    display: inline-block;
    vertical-align: middle;
    display: inline-block;
    text-align: center;
    width: 100%;
    margin: auto;
}

.rentmy-gettingstart-onhover2 {
    position: absolute;
    left: 0;
    width: 100.5%;
    height: 100%;
    padding: 15px;
    display: none;
    transition: all .3s;
    bottom: -200px;
}

.gettingstart-box .rentmy-gettingstart-onhover2 {
    background-color: #e8929a;
    transition: all .5s;
}

.gettingstart-box2 .rentmy-gettingstart-onhover2 {
    background-color: #51b1ca;
    transition: all .5s;
}

.gettingstart-box3 .rentmy-gettingstart-onhover2 {
    background-color: #46C373;
    transition: all .5s;
}

.rentmy-gettingstart-onhover2 a {
    display: table;
    height: 100%;
}

.rentmy-gettingstart-onhover-inner2 {
    vertical-align: middle;
    display: table-cell;
    transition: all .5s;
}

.rentmy-gettingstart-onhover2 p {
    color: #fff;
    width: 100%;
    display: inline-block;
    margin-bottom: 0;
}

.rentmy-gettingstart-box2:hover .rentmy-gettingstart-onhover2 {
    display: table;
    transition: all .5s;
    bottom: 0px;
}

.rentmy-gettingstart-icon img {
    transition: all .3s;
}

.rentmy-gettingstart-box2:hover .rentmy-gettingstart-icon img {
    transform: scale(1.1);
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
}

@media (min-width: 1500px) and (max-width: 1599px) {
    .rentmy-gettingstart-onhover2 p {
        font-size: 13px;
    }
}

@media (min-width: 1400px) and (max-width: 1499px) {
    .rentmy-gettingstart-onhover2 p {
        font-size: 12px;
    }
}

@media (min-width: 1200px) and (max-width: 1399px) {
    .rentmy-gettingstart-icon {
        padding: 25px 15px;
    }
    .rentmy-gettingstart-onhover2 {
        padding: 10px;
    }
    .rentmy-gettingstart-onhover2 p {
        font-size: 12px;
    }
}

@media (min-width: 1200px) and (max-width: 1350px) {
    .rentmy-gettingstart-box2 h4 {
        font-size: 16px;
    }
    .rentmy-gettingstart-onhover2 p {
        font-size: 11px;
    }
}

@media (min-width: 993px) and (max-width: 1199px) {
    .rentmy-gettingstart-icon {
        padding: 25px 15px;
    }
}

@media (min-width: 576px) and (max-width: 1199px) {
    .rentmy-gettingstart-box2 h4 {
        font-size: 16px;
    }
    .rentmy-gettingstart-onhover2 p {
        font-size: 12px;
    }
}

@media (min-width: 576px) and (max-width: 767px) {
    .rentmy-gettingstart-icon {
        padding: 25px 15px;
    }
    .rentmy-gettingstart-onhover2 {
        padding: 10px;
    }
    .rentmy-gettingstart-onhover2 p {
        font-size: 9px;
    }
}

@media (max-width: 575px) {
    .dashboard-gettingstart .m-portlet .m-portlet__body {
        padding: 15px 15px 5px !important;
    }
    .rentmy-gettingstart-box2 h4 {
        font-size: 16px;
    }
    .rentmy-gettingstart-icon {
        padding: 15px;
    }
    .rentmy-gettingstart-onhover2 p {
        font-size: 11px;
    }
}

.website-setting-video h5 {
    padding-top: 6px;
    cursor: pointer;
}

.website-setting-video img {
    width: 62px;
}

.website-setting-video h5 a {
    font-size: 14px;
    color: #333;
    padding-left: 10px;
}

.website-setting-video h5 a:hover {
    color: #111;
}

.dashboard-gettingstart .m-portlet__head-text {
    width: unset !important;
}

@media (min-width: 768px) {
    .website-setting-video {
        margin-left: -140px;
    }
}

@media (max-width: 767px) {
    .dashboard-gettingstart .m-portlet__head-text {
        width: 120px !important;
    }
    .website-setting-video h5 {
        padding-top: 18px;
    }
    .website-setting-video img {
        width: 30px;
    }
    .website-setting-video h5 a {
        font-size: 12px;
    }
}

@media (max-width: 575px) {
    .website-setting-video h5 a {
        display: none;
    }
}

.youtube-video-modal {
    background-color: rgba(0, 0, 0, .5);
}

.youtube-video-modal .modal-body {
    padding: 0 !important;
}

.youtube-video-modal .modal-dialog {
    max-width: 700px;
    margin-top: -12px;
}

.youtube-video-modal .modal-body {
    height: 500px;
}

.youtube-video-modal .modal-body iframe {
    width: 100%;
    height: 500px;
}

.youtube-video-modal .close {
    position: absolute;
    top: -25px;
    right: 0;
    font-weight: bold;
    color: #000 !important;
    opacity: unset;
    text-shadow: none;
}

.youtube-video-modal .close span {
    font-size: 20px;
    color: #fff;
}

@media (max-width: 575px) {
    .youtube-video-modal .modal-body {
        height: 300px;
    }
    .youtube-video-modal .modal-body iframe {
        height: 100%;
    }
}