import {
  Component,
  OnInit,
  ViewEncapsulation,
  AfterViewInit,
  HostListener,
  OnDestroy,
  ViewChild,
  ElementRef
} from "@angular/core";
import { Location } from "@angular/common";
import { SidebarService } from "../sidebar-service/sidebar.service";
import { Router, NavigationEnd } from "@angular/router";
import { Subscription } from "rxjs";
import { DashboarService } from "./dashboard.service/dashboard.service";
import { AlertService } from "../../../modules/alert/alert.service";
import { MetaService } from "../../../_services/meta.service";
import { NgbModal, NgbModalOptions } from "@ng-bootstrap/ng-bootstrap";
import { DialogBoxSubscriptionComponent } from "../../../modules/dialog-box-subscription/dialog-box-subscription.component";
import { PricingService } from "../pricing/pricing.service";
import { getSubscriptionPlan, eLogin, encrypt } from "../../../globals/_classes/functions";
import { Title } from "@angular/platform-browser";
import { SettingService } from "../settings/setting-service/setting.service";
import { OnboardingWizardComponent } from "../onboarding-wizard/onboarding-wizard.component";
import { domainName } from "../../../globals/endPoint/config";

declare let $: any;

@Component({
  selector: "app-blank",
  templateUrl: "./dashboard.component.html",
  styleUrls: ["./dashboard.component.css"],
  encapsulation: ViewEncapsulation.None
})
export class DashboardComponent implements OnInit, AfterViewInit, OnDestroy {
  sidebarOpen: boolean;
  sub: Subscription[] = [];
  fromDash: boolean;
  loader: boolean;
  subs_plan:any;
  show_start_btn = false;

  @ViewChild("hasCusAlert") alertContainer: ElementRef;

  @HostListener("window:resize", ["$event"])
  onResize(event) {
    if (this.sidebarOpen && this.fromDash) {
      this.createNewOrder();
    }
  }

  constructor(
    private metaService: MetaService,
    private sideBarS: SidebarService,
    private router: Router,
    private alertS: AlertService,
    private dashS: DashboarService,
    private priceS: PricingService,
    private location: Location,
    private modalService: NgbModal,
    private title: Title,
    private settingService: SettingService
  ) {
    const path = this.location.path();
    if (path.includes("?")) {
      this.location.replaceState(path.split("?")[0]);
    }
    this.getCurrency();

    this.subs_plan = getSubscriptionPlan();

  }

  ngOnInit() {
    this.sub[0] = this.sideBarS.sidebarOpen.subscribe(val => {
      this.sidebarOpen = val;
      this.fromDash = false;
      this.sideBarS.closeCartSidebar();
    });
    this.sub[1] = this.router.events.subscribe(route => {
      if (route instanceof NavigationEnd) {
        this.sidebarOpen = false;
      }
    });

    let storeName = localStorage.getItem("storeName");

    if(storeName !=undefined && storeName !='undefined' && storeName !=null)
    {
      this.title.setTitle(JSON.parse(localStorage.getItem("storeName"))+":: Dashboard");
    }

    
  }

  getCurrency() {
    this.dashS.getTimeZone().subscribe(res => {
      const metaContent = res.timezone.meta_contents;
      if (metaContent) {
        this.addMetaTag(metaContent);
      }
      const currency = res.timezone.currency_format;
      if (currency) {
        localStorage.setItem("currency", JSON.stringify(currency));
      }


      this.settingService.onOrderLimitChange({updateOrderLimit:true,total_orders:res.timezone.order? res.timezone.order.total_order : 0});

      this.settingService.onStoreContentChange({updateSidebar:true,store_content_settings:res.timezone});

    });
  }

  addMetaTag(data) {
    this.metaService.addTagsForAdmin(data);
  }

  hide_start_btn() {
    this.show_start_btn = !this.show_start_btn;
    const data = {
      type: "hide_start_buttons", 
      value: true
    }
    this.dashS.saveStoreOptions(data);
  }
  
  ngAfterViewInit() {
    this.sideBarS.closeCartSidebar();

    let ngbModalOptions: NgbModalOptions = {
      backdrop: "static",
      keyboard: false,
      windowClass:'onboard-modal-window',
      size: "lg",
    };

    this.dashS.getOnboardingQuestionnaire().subscribe(res => {
      if (res.length== 0) {
        this.show_start_btn = true;
        // setTimeout(() => {
        //   const modalRef = this.modalService.open(
        //     OnboardingWizardComponent,
        //     ngbModalOptions
        //   );

        //   modalRef.componentInstance.name = "OnboardingWizard";
        // });
      } else {
        if (res.hasOwnProperty('hide_start_buttons') && res.hide_start_buttons == 1) {
          this.show_start_btn = false;
        } else {
          this.show_start_btn = true;
        }
        // let is_exist = res.hasOwnProperty("first_login");
        // if (is_exist === false) {
        //   setTimeout(() => {
        //     const modalRef = this.modalService.open(
        //       OnboardingWizardComponent,
        //       ngbModalOptions
        //     );

        //     modalRef.componentInstance.name = "OnboardingQuestionnaire";
        //   });
        // } 
      }
    });
  }

  ngOnDestroy() {
    this.sub.forEach(s => {
      s.unsubscribe();
    });
  }

  createNewOrder() {
    this.fromDash = true;
    this.sidebarOpen = true;
    let w =
      $(".global-sidebar-wrapper").width() +
      ($(window).width() > 992 ? 25 : 0) +
      "px";
    this.sideBarS.openCartSidebar(w);
  }

  goToReturnedList() {
    this.subs_plan = getSubscriptionPlan();
   if (this.subs_plan ==false) {
    this.router.navigateByUrl("admin/inventory/add");
  } else {
    this.priceS.getSubscriptionInfo().subscribe(res => {
      console.log(res);
      if (res.hasOwnProperty("newInventory") && res.newInventory) {
        this.router.navigateByUrl("admin/inventory/add");
      } else {
        const modalRef = this.modalService.open(
          DialogBoxSubscriptionComponent,
          {
            centered: true,
            windowClass: "animated fadeIn"
          }
        );
        modalRef.componentInstance.massage =
          "Adding new inventory will put you in a higher tier, do you want to upgrade your plan now or enter trial mode?";
        modalRef.result.then(
          result => {
            if (result) {
              this.router.navigateByUrl("/admin/plans");
            } else {
              sessionStorage.setItem('showSubscriptionAlert',"yes")
              sessionStorage.setItem('subscription',JSON.stringify(res));
              this.router.navigateByUrl("admin/inventory/add");
            }
          },
          res => {
            console.log(res);
          }
        );
      }
    });
  }

  }
  goToUploadLogo(){
    this.router.navigate(["admin/settings/contents/store-logo"]);
  }

  showAlert(e) {
    if (e) {
      if (e.status) {
        this.alertS.success(this.alertContainer, e.message, true, 3000);
      } else {
        this.alertS.error(this.alertContainer, e.message, true, 3000);
      }
    }
  }

  swithToPosScreen(): void {
    this.dashS.getUserDataForPos()
      .then(
        res => {
          if (res.status === 'OK') {
            const returnUrl = "/cash-register";
            const user = res.result.data;
            const a = eLogin(
              encrypt(encrypt(encrypt(JSON.stringify(user), "upper"), "lower"))
            );
            const href = domainName(user.store_name, "store") + `${returnUrl}?user=${a}`;
           // window.open(href, "_self");
          } else {
            this.alertS.error(this.alertContainer, 'Something went wrong!', true, 5000);
          }
        })
      .catch(
        err => {
          this.alertS.error(this.alertContainer, 'Something went wrong!', true, 5000);
        });
  }
}
