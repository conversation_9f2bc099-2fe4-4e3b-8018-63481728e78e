<div class="m-portlet m-portlet--full-height ">
  <div class="m-portlet__body pb-0">
    <div class="row">
      <div class="col-xl-3 col-md-4 col-sm-6" *ngFor="let grid of gridLinks">
        <a routerLink="{{grid.link}}">
          <div class="menu-box text-center">
            <!-- <i class="fa flaticon-pie-chart"></i> -->
            <img src="assets/img/admin/icon/{{grid.icon}}" class="img-fluid">
            <h5>{{grid.name}} </h5>
            <!-- {{grid.soon}} -->
          </div>
        </a>
      </div>
    </div>
  </div>
</div>
