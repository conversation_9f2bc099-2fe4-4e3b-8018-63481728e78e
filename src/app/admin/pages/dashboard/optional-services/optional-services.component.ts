import { Component, OnInit, Output, EventEmitter } from "@angular/core";
import { services } from "../models/dashboar-models";
import { DashboarService } from "../dashboard.service/dashboard.service";

@Component({
  selector: "app-optional-services",
  templateUrl: "./optional-services.component.html",
  styleUrls: ["./optional-services.component.css"]
})
export class OptionalServicesComponent implements OnInit {
  optionalServices = services;
  services = [];
  loader: boolean;

  @Output("alert") alert = new EventEmitter();

  constructor(private dashS: DashboarService) {}

  ngOnInit() {}

  selectService(e, i) {
    this.services = [];
    if (e.target.checked) {
      this.optionalServices[i].check = true;
    } else {
      this.optionalServices[i].check = false;
    }
    this.services = this.optionalServices.filter(f => f.check);
    console.log(this.optionalServices);
  }

  submit() {
    this.loader = true;
    const sendData = {
      service: this.services.map(m => m.service)
    };
    if (this.services.length > 0) {
      this.dashS
        .sendOptionalService(sendData)
        .then(res => {
          this.loader = false;
          this.alert.emit({
            status: res.result.message ? true : false,
            message: res.result.message ? res.result.message : res.result.error
          });
        })
        .catch(err => {
          this.loader = false;
          this.alert.emit({
            status: false,
            message: "Something went wrong!!! Please try again"
          });
        });
    }
  }
}
