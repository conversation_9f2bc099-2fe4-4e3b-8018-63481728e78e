import {
  Component,
  OnInit,
  HostListener,
  Output,
  EventEmitter
} from "@angular/core";
import { DashboarService } from "../dashboard.service/dashboard.service";
import { environment } from "../../../../../environments/environment";
import { formatSlideValue } from "../../../../globals/_classes/functions";

declare let $: any;

@Component({
  selector: "app-business-cate",
  templateUrl: "./business-cate.component.html",
  styleUrls: ["./business-cate.component.css"]
})
export class BusinessCateComponent implements OnInit {
  url: string = environment.api_url;
  actualValue = [];
  types = [];
  grid = { s: 1, c: 12 };
  importing: number = null;

  @Output("alert") alert = new EventEmitter();

  @HostListener("window:resize", ["$event"])
  onResize(event) {
    this.formatValue();
  }

  constructor(private dashS: DashboarService) {
    this.getAllType();
  }

  ngOnInit() {}

  private getAllType() {
    this.dashS.getSetUp().subscribe(res => {
      this.actualValue = res;
      this.formatValue();
    });
  }

  private formatValue() {
    const { type, grid } = formatSlideValue(this.actualValue);
    this.grid = grid;
    this.types = type;
  }

  importProduct(id) {
    this.importing = id;
    this.dashS
      .importProduct(id)
      .then(res => {
        this.importing = null;
        this.alert.emit({
          status: res.result.message ? true : false,
          message: res.result.message ? res.result.message : res.result.error
        });
      })
      .catch(err => {
        this.importing = null;
        this.alert.emit({
          status: false,
          message: "Something went wrong!!! Please try again"
        });
      });
  }

  openAdvanceSearch() {
    $(".search-panel#advanceSearch").toggleClass("dis-block");
    $("#advanceSearch").toggleClass("la-angle-up la-angle-down");
    this.formatValue();
  }

  checkHasUrl(id) {
    if (id === 3 || id === 4 || id === 6 || id === 7 || id === 20) {
      return true;
    } else {
      return false;
    }
  }

  onClickViewStore(id) {
    // if (id === 3) {
    //   window.open("http://bounce.rentmy.co", "_blank");
    // } else if (id === 4) {
    //   window.open("http://audio.rentmy.co", "_blank");
    // } else if (id === 6) {
    //   window.open("http://my-texas-totes.rentmy.co", "_blank");
    // } else if (id === 7) {
    //   window.open("http://water.rentmy.co", "_blank");
    // } else if (id === 20) {
    //   window.open("http://camping.rentmy.co", "_blank");
    // }
  }
}
