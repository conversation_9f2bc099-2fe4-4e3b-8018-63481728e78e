<div class="m-portlet m-portlet--mobile">
  <div
    class="m-portlet__head"
    (click)="openAdvanceSearch()"
    style="cursor: pointer;"
  >
    <div class="m-portlet__head-caption">
      <div class="m-portlet__head-title m-product-title" style="width:100%;">
        <h3 class="m-portlet__head-text text-left" style="width:92%;">
          Let’s grow your rental business!
        </h3>
        <h3
          class="icon-head text-right"
          style="margin: auto;padding: 20px 5px;"
        >
          <i
            class="bounce la la-angle-down"
            id="advanceSearch"
            style="font-size: 1.8rem;"
          ></i>
        </h3>
      </div>
    </div>
  </div>
  <div
    class="m-portlet__body search-panel dis-none animated fadeIn"
    id="advanceSearch"
  >
    <div
      id="carouselExampleControls"
      class="carousel slide "
      data-ride="carousel"
      data-interval="false"
    >
      <div class="carousel-inner">
        <div
          class="carousel-item"
          [ngClass]="{ active: i === 0 }"
          *ngFor="let t of types; let i = index"
        >
          <!-- Carusel Iiem #1-->
          <div class="row" style="padding-top: 3rem;">
            <div class="pb-5" [ngClass]="'col-' + grid.c" *ngFor="let a of t">
              <div class="slider-box-item" style="cursor: pointer;">
                <img class="" [src]="url + a.image_data" alt="" />
                <h4>{{ a.category_name }}</h4>
                <div class="imag-hover">
                    <div class="btn-group business-group-morebtn">
                        <button type="button" class="btn btn-secondary dropdown-toggle business-more-btn" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                            <i class="la la-ellipsis-h"></i>
                        </button>
                        <div class="dropdown-menu" x-placement="top-start" style="position: absolute; transform: translate3d(0px, -193px, 0px); top: 0px; left: 0px; will-change: transform;">
                            <a class="dropdown-item" (click)="importProduct(a.id)">Add Samples</a>
                            <a class="dropdown-item" routerLink="/admin/bulk-demo-import/{{ a.id }}">View Details</a>
                            <a *ngIf="checkHasUrl(a.id)" class="dropdown-item" (click)="onClickViewStore(a.id)" target="_blank">Visit Sample Store</a>
                        </div>
                    </div>
                  <!-- <a
                   *ngIf="checkHasUrl(a.id)"
                    class="btn btn-sm btn-light view-store"
                    title="Visit sample store"
                    (click)="onClickViewStore(a.id)"
                    target="_blank"
                    ><i class="fa fa-arrow-right"></i>
                  </a>
                  <button
                    class="btn btn-sm btn-light"
                    (click)="importProduct(a.id)"
                    style="white-space: pre-wrap; width: 116px"
                  >
                    Add Samples
                  </button>
                  <a
                    routerLink="/admin/bulk-demo-import/{{ a.id }}"
                    class="btn btn-sm btn-light"
                    style="margin-left: 5px; width: 116px; white-space: pre-wrap;"
                    >View Details</a
                  >
                </div> -->
                <div class="loader-part" *ngIf="importing == a.id">
                  <div
                    *ngIf="importing == a.id"
                    class="table-load m-loader m-loader--brand"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <a
        class="carousel-control-prev"
        href="#carouselExampleControls"
        role="button"
        data-slide="prev"
      >
        <i class="fa fa-chevron-left"></i>
        <span class="carousel-control-prev-icon" aria-hidden="true"></span>
        <span class="sr-only">Previous</span>
      </a>
      <a
        class="carousel-control-next"
        href="#carouselExampleControls"
        role="button"
        data-slide="next"
      >
        <i class="fa fa-chevron-right"></i>
        <span class="carousel-control-next-icon" aria-hidden="true"></span>
        <span class="sr-only">Next</span>
      </a>
    </div>
  </div>
</div>
