import { Observable } from 'rxjs/Observable';
import { Injectable, Optional } from "@angular/core";
import { HttpService } from "../../../../modules/http-with-injector/http.service";
import { map, catchError, tap } from "rxjs/operators";
import { DashboardServiceConfig } from "../models/dashboar-models";
import { of, BehaviorSubject } from "rxjs";

@Injectable()
export class DashboarService {
  config: DashboardServiceConfig;

  private logo = new BehaviorSubject<string>('assets/img/company/logo.png');
  public logoUrl = this.logo.asObservable();

  constructor(
    @Optional() config: DashboardServiceConfig,
    private http: HttpService
  ) {
    this.config = config;
  }

  getUserDataForPos() {
    return this.http.get('users/token?source=pos').toPromise();
  }

  getSetUp() {
    return this.http.get("setup/type").pipe(
      map(m => m.result.data),
      catchError(err => of([]))
    );
  }

  sendOptionalService(data) {
    return this.http.post("service-request", data).toPromise();
  }

  sendOnboardingQuestionnaire(data) {
    return this.http.post("questionaries", data).toPromise();
  }

  sendQ(data) {
    this.logo.next('new image');
    return this.http.post('questionaries', data).pipe(
      tap((res) => {
        if (res.result.data.logo) {
          this.logo.next(res.result.data.logo);
          this.logoUrl.subscribe(res => console.log(res))
        }
      }
      )
    )
  }

  getOnboardingQuestionnaire() {
    return this.http.get("questionaries").pipe(
      map(m => m.result.data),
      catchError(err => of([]))
    );
  }

  saveStoreOptions(data: any) {
    return this.http.post("save-store-options", data).toPromise();
  }

  importProduct(id) {
    return this.http.get("import-sample/" + id).toPromise();
  }

  getTimeZone() {
    return this.http.get("timezones").pipe(
      map(res => res.result),
      catchError(e => of({ timezone: "" }))
    );
  }

  getCurrencyConfig() {
    return this.http.get("currency-config").pipe(
      map(res => {
        return res.result.data;
      }),
      catchError(err => of([]))
    );
  }

  getPaymentConfig() {
    return this.http.get(`store-config/payments`).pipe(map(res => res.result));
  }
}
