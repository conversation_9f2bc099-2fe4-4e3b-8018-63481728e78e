import { Component, AfterViewInit, Output, EventEmitter, Input, OnDestroy } from '@angular/core';
import { TransactionReport } from '../../models/report-models';
import { FORMAT_SEARCH } from '../../../../../globals/_classes/functions';
import { Subscription } from 'rxjs';
import { ActivatedRoute } from '@angular/router';

declare let $:any;
declare var moment: any;

@Component({
  selector: 'transactions-report-filter',
  templateUrl: './filter.component.html',
  styleUrls: ['./filter.component.css']
})
export class FilterComponent implements AfterViewInit, OnDestroy {

  transactionReport: TransactionReport;
  filter;
  search: boolean;
  sub: Subscription;

  @Input('status') status;
  @Input('stores') stores;
  @Input('terminals') terminals;
  @Output('loadList') loadList: EventEmitter<any> = new EventEmitter();

  constructor(private route: ActivatedRoute,) { 
    this.transactionReport = new TransactionReport();
    this.sub = this.route.paramMap.subscribe(
      val => {
        this.reset();
      }
    );
  }

  ngAfterViewInit() {
    this.transactionReport.start_date = moment().format('YYYY-MM-DD');
    this.transactionReport.end_date = moment().format('YYYY-MM-DD');
    
    $('#m_daterangepicker_3 .form-control').val( 'From ' + moment().format('YYYY-MM-DD') + ' To ' + moment().format('YYYY-MM-DD'));
    this._dateRange();
  }

  ngOnInit() {
    $(function () {
      $(".search-title").on("click", function () {
          $(".search-content-body").slideToggle();
      });
    })
  }

  ngOnDestroy() {
    this.sub.unsubscribe();
  }

  searchTransactionReport(){
    // console.log(this.salesReport);
    // this.filter = FORMAT_SEARCH(this.transactionReport);
    this.filter = this.transactionReport;
    // console.log(this.filter);
    if(this.filter) {
      this.loadList.emit(this.filter);
      this.search = true;
    }
  }

  resetSearch(){
    this.reset();
    this.filter = null;
    if(this.search) {
      this.loadList.emit('');
      this.search = false;
    }
  }

  reset() {
    this.transactionReport = new TransactionReport();
    this.transactionReport.start_date = null;
    this.transactionReport.end_date = null;
  }

  private _dateRange() {
    $('#m_daterangepicker_3').daterangepicker({
      opens: 'left',
      startDate: moment(),
      endDate: moment(),
      ranges: {
        'Today': [moment(), moment()],
        'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
        'Last 7 Days': [moment().subtract(6, 'days'), moment()],
        'Last 30 Days': [moment().subtract(29, 'days'), moment()],
        'This Month': [moment().startOf('month'), moment().endOf('month')],
        'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
      },
      autoUpdateInput: true,
      buttonClasses: 'm-btn btn',
      applyClass: 'btn-brand',
      cancelClass: 'btn-danger'
    }, (start, end, label) => {
      this.transactionReport.start_date = start.format('YYYY-MM-DD');
      this.transactionReport.end_date = end.format('YYYY-MM-DD');
        $('#m_daterangepicker_3 .form-control').val( 'From ' + start.format('YYYY-MM-DD') + ' To ' + end.format('YYYY-MM-DD'));
      });
  }
}
