<div class="custom-alert" #hasAlert></div>
<!-- BEGIN: Subheader -->
<div class="m-subheader">
  <div class="d-flex align-items-center">
    <div class="mr-auto">
      <h3 class="m-subheader__title m-subheader__title--separator">
        Transaction Report
      </h3>
      <ul class="m-subheader__breadcrumbs m-nav m-nav--inline">
        <li class="m-nav__item m-nav__item--home">
          <a routerLink="/admin" class="m-nav__link m-nav__link--icon">
            <i class="m-nav__link-icon la la-home"></i>
          </a>
        </li>
        <li class="m-nav__separator">
          <i class="fa fa-angle-right"></i>
        </li>
        <li class="m-nav__item">
          <a class="m-nav__link">
            <span class="m-nav__link-text">
              Reports
            </span>
          </a>
        </li>
        <li class="m-nav__separator" style="padding-left: 10px">
          <i class="fa fa-angle-right"></i>
        </li>
        <li class="m-nav__item">
          <a class="m-nav__link">
            <span class="m-nav__link-text">
              Transaction Report
            </span>
          </a>
        </li>
      </ul>
    </div>
  </div>
</div>
<!-- END: Subheader -->
<div class="m-content product-list animated fadeIn">
  <!--begin::Portlet-->
  <transactions-report-filter
    [status]="status"
    [stores]="stores"
    [terminals]="terminals"
    (loadList)="filterList($event)"
  ></transactions-report-filter>
  <!--end::Portlet-->
  <!--begin::Portlet-->
  <div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__head">
      <div class="m-portlet__head-caption">
        <div class="m-portlet__head-title">
          <h3 class="m-portlet__head-text">
            Transaction Report
          </h3>
          <!-- <div class="button-midle float-right">
            <div
              *ngIf="loaderExport; else ExportBTN"
              class="m-loader m-loader--brand"
              style="width: 30px; display: inline-block;"
            ></div>
            <ng-template #ExportBTN>
              <button
                type="button"
                class="btn btn-success btn-sm"
                (click)="exportSales()"
              >
                <i class="m-nav__link-icon la la-cloud-download"></i>
                <span class="m-nav__link-text">
                  Export Report
                </span>
              </button>
            </ng-template>
          </div> -->
        </div>
      </div>
    </div>

    <div class="m-portlet__body" style="position: relative;">
      <!--begin: Datatable -->
      <!--begin::Section-->
      <div class="m-section">
        <div class="m-section__content price-table">
          <div *ngIf="loader" class="table-load m-loader m-loader--brand"></div>
          <div class="table-responsive" style="margin-bottom: 10px;">
            <table class="table table-hover">
             
              <tbody *ngIf="transactionReport.length < 1; else table">
                <tr *ngIf="!loader">
                  <td colspan="10">
                    <h4 class="text-center">No Data Found</h4>
                  </td>
                </tr>
              </tbody>
              <ng-template #table>
                <tbody
                  *ngFor="
                    let transaction of transactionReport;
                    let i = index;
                    let o = odd;
                    let e = even;
                    trackBy: trackReport
                  "
                >

                  <tr [ngClass]="{ 'odd-tr': o, 'even-tr': e }">
                    <th >Order</th>
                    <td>
                      <a
                        routerLink="/admin/reservations/{{
                          transaction.order_id
                        }}/details"
                        >{{ transaction.order_id }}
                      </a>
                    </td>
                    <td>Customer Name :{{ transaction.name }}</td>
                    <td>Total :{{ transaction.total | currency }}</td>
                  </tr>

                  <tr>
                      <td class="description-fields w-100" colspan="4">
                        <div class="row">

                            <div class="col-md-12 p-0">
                                <table class="w-100"  *ngIf="transaction.payments.length >0">
                                    <tbody>
                                        <tr>
                                            <th class="tablesize-100">Payments</th>
                                            <th>Gateway Name</th>
                                            <th>Transaction Id</th>
                                            <th>Amount</th>
                                            <th>Payment Method</th>
                                          </tr>
                                      <tr
                                        *ngFor="let payment of transaction.payments"
                                      >
                                      <td></td>
                                        <td>{{ payment?.payment_gateway }}</td>
                                        <td>{{ payment?.transaction_id }}</td>
                                        <td>
                                          {{ payment?.payment_amount | currency }}
                                        </td>
                                        <td>{{ payment?.payment_method }}</td>
                                      </tr>
                                    </tbody>
                                </table>
                              </div>
                              
                          <div class="col-md-12 p-0">
                            <table class="w-100" *ngIf="
                            transaction.products.length >0">
                                <tbody>
                                    <tr>
                                        <th class="tablesize-100">Products</th>
                                        <th>Id</th>
                                        <th>Name</th>
                                        <th>Quantity</th>
                                        <th>Sub Total</th>
                                        <th>Total</th>
                                      </tr>
                                  <tr *ngFor="let prod of transaction.products">
                                    <td></td>
                                    <td>
                                      <a
                                        routerLink="/admin/inventory/{{
                                          prod?.id
                                        }}/details"
                                      >
                                        {{ prod?.id }}
                                      </a>
                                    </td>
                                    <td>{{ prod?.name }}</td>
                                    <td>{{ prod?.quantity }}</td>
                                    <td>{{ prod?.sub_total | currency }}</td>
                                    <td>{{ prod?.total | currency }}</td>
                                  </tr>
                                </tbody>
                            </table>
                          </div>

                       
                        </div>
                      </td>
                  </tr>
                </tbody>
              </ng-template>
            </table>
          </div>
          <!-- pagination Start-->
          <boot-pagination
            [totalSize]="pagi.total"
            [page]="pagi.page"
            [pagelimit]="pagi.limit"
            [listSize]="pagi.limit"
            (pageChange)="reloadTable($event)"
          ></boot-pagination>
          <!-- pagination End-->
        </div>
      </div>
      <!--end::Section-->
      <!--end: Datatable -->
    </div>
  </div>
  <!--end::Portlet-->
</div>
