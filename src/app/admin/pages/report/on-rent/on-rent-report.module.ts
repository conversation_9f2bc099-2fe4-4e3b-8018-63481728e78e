import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FilterComponent } from './filter/filter.component';
import { RouterModule, Routes } from '@angular/router';
import { PaginationModule } from '../../../../modules/pagination/pagination.module';
import { FormsModule } from '@angular/forms';
import { CurrencyFormatModule } from '../../../../modules/currency-format/currency-format.pipe';
import { OnRentReportComponent } from './on-rent-report.component';
import { ProductSearchModule } from '../../../../modules/product-search/product-search.module';

const route: Routes = [
  {
    path: '',
    component: OnRentReportComponent
  }
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(route),
    FormsModule,
    PaginationModule,
    CurrencyFormatModule,
    ProductSearchModule
  ],
  declarations: [OnRentReportComponent, FilterComponent]
})
export class OnRentReportModule { }
