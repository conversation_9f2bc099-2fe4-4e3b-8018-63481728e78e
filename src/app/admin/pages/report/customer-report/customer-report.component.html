<div class="custom-alert" #hasAlert></div>
<!-- BEGIN: Subheader -->
<div class="m-subheader">
  <div class="d-flex align-items-center">
    <div class="mr-auto">
      <h3 class="m-subheader__title m-subheader__title--separator">
        Customer Report
      </h3>
      <ul class="m-subheader__breadcrumbs m-nav m-nav--inline">
        <li class="m-nav__item m-nav__item--home">
          <a routerLink="/admin" class="m-nav__link m-nav__link--icon">
            <i class="m-nav__link-icon la la-home"></i>
          </a>
        </li>
        <li class="m-nav__separator">
          <i class="fa fa-angle-right"></i>
        </li>
        <li class="m-nav__item">
          <a class="m-nav__link">
            <span class="m-nav__link-text">
              Reports
            </span>
          </a>
        </li>
        <li class="m-nav__separator" style="padding-left: 10px">
          <i class="fa fa-angle-right"></i>
        </li>
        <li class="m-nav__item">
          <a class="m-nav__link">
            <span class="m-nav__link-text">
              Customer Report
            </span>
          </a>
        </li>
      </ul>
    </div>
  </div>
</div>
<!-- END: Subheader -->
<div class="m-content product-list animated fadeIn">
  <!--begin::Portlet-->
  <customer-report-filter
    [status]="status"
    [stores]="stores"
    [terminals]="terminals"
    (loadList)="filterList($event)"
  ></customer-report-filter>
  <!--end::Portlet-->
  <!--begin::Portlet-->
  <div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__head">
      <div class="m-portlet__head-caption">
        <div class="m-portlet__head-title">
          <h3 class="m-portlet__head-text">
            Customer Report
          </h3>
          <!-- <div class="button-midle float-right">
            <div
              *ngIf="loaderExport; else ExportBTN"
              class="m-loader m-loader--brand"
              style="width: 30px; display: inline-block;"
            ></div>
            <ng-template #ExportBTN>
              <button
                type="button"
                class="btn btn-success btn-sm"
                (click)="exportSales()"
              >
                <i class="m-nav__link-icon la la-cloud-download"></i>
                <span class="m-nav__link-text">
                  Export Report
                </span>
              </button>
            </ng-template>
          </div> -->
        </div>
      </div>
    </div>

    <div class="m-portlet__body" style="position: relative;">
      <!--begin: Datatable -->
      <!--begin::Section-->
      <div class="m-section">
        <div class="m-section__content price-table">
          <div *ngIf="loader" class="table-load m-loader m-loader--brand"></div>
          <div class="table-responsive" style="margin-bottom: 10px;">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th></th>
                  <th>
                    First Name
                  </th>
                  <th>
                    First Name
                  </th>
                  <th>
                    Email
                  </th>
                  <th>
                    Deposited Amount
                  </th>
                  <th>
                    Shipping Charge
                  </th>
                  <th>
                    Sub Total
                  </th>
                  <th>
                    Tax
                  </th>
                  <th>
                    Total
                  </th>
                </tr>
              </thead>
              <tbody *ngIf="customerReport.length < 1; else table">
                <tr *ngIf="!loader">
                  <td colspan="10">
                    <h4 class="text-center">No Data Found</h4>
                  </td>
                </tr>
              </tbody>
              <ng-template #table>
                <tbody
                  *ngFor="
                    let customer of customerReport;
                    let o = odd;
                    let e = even;
                    trackBy: trackReport
                  "
                >
                  <tr [ngClass]="{ 'odd-tr': o, 'even-tr': e }">
                    <td style="max-width: 50px;">
                      <span
                        id="product_sidebar_toggle"
                        (click)="
                          openSummary(
                            'order-id-' + customer.id,
                            'order-icon-' + customer.id
                          )
                        "
                        class="m-portlet__nav-link btn m-btn m-btn--hover-brand m-btn--icon m-btn--icon-only m-btn--pill"
                        title="View Details"
                      >
                        <i
                          class="fa fa-plus-circle"
                          id="{{ 'order-icon-' + customer.id }}"
                        ></i>
                      </span>
                    </td>

                    <td>{{ customer.first_name }}</td>
                    <td>{{ customer.last_name }}</td>
                    <td>{{ customer.email }}</td>
                    <td>{{ customer.deposited_amount | currency }}</td>
                    <td>{{ customer.shipping_charge | currency }}</td>
                    <td>{{ customer.sub_total | currency }}</td>
                    <td>{{ customer.tax | currency }}</td>
                    <td>{{ customer.total | currency }}</td>
                  </tr>

                  <tr class="dis-none" id="{{ 'order-id-' + customer.id }}">
                    <td colspan="9">
                      <div class="description-fields w-100">
                        <div class="row">
                          <div class="col-md-12">
                            <table>
                              <thead>
                                <tr>
                                  <th>Name</th>
                                  <th>Quantity</th>
                                  <th>Rent Start</th>
                                  <th>Rent End</th>
                                  <th>Sales Tax Price</th>
                                  <th>Sub Total</th>
                                  <th>Total</th>
                                </tr>
                              </thead>
                              <tbody>
                                <tr *ngFor="let prod of customer.products">
                                  <td>{{ prod?.name }}</td>
                                  <td>{{ prod?.quantity }}</td>
                                  <td>
                                    {{
                                      prod.rental_type != "buy"
                                        ? (getDate(prod?.rent_start)
                                          | date: "MM-dd-yyyy hh:mm:ss a")
                                        : ""
                                    }}
                                  </td>
                                  <td>
                                    {{
                                      prod.rental_type != "buy"
                                        ? (getDate(prod?.rent_end)
                                          | date: "MM-dd-yyyy hh:mm:ss a")
                                        : ""
                                    }}
                                  </td>
                                  <td>
                                    {{ prod?.sales_tax_price | currency }}
                                  </td>
                                  <td>{{ prod?.sub_total | currency }}</td>
                                  <td>{{ prod?.total | currency }}</td>
                                </tr>
                              </tbody>
                            </table>
                          </div>
                        </div>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </ng-template>
            </table>
          </div>
          <!-- pagination Start-->
          <boot-pagination
            [totalSize]="pagi.total"
            [page]="pagi.page"
            [pagelimit]="pagi.limit"
            [listSize]="pagi.limit"
            (pageChange)="reloadTable($event)"
          ></boot-pagination>
          <!-- pagination End-->
        </div>
      </div>
      <!--end::Section-->
      <!--end: Datatable -->
    </div>
  </div>
  <!--end::Portlet-->
</div>
