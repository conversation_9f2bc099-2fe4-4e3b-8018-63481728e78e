<div class="m-portlet m-portlet--mobile">
  <div class="m-portlet__head search-title">
    <div class="m-portlet__head-caption">
      <div class="m-portlet__head-title">
        <h3 class="m-portlet__head-text">
          <i class="la la-search"></i>
          Search
        </h3>
      </div>
    </div>
  </div>
  <div class="m-portlet__body search-panel search-content-body">
    <!--begin::Form-->
    <form class="m-form m-form--fit m-form--label-align-right second-form" 
      #form="ngForm" (ngSubmit)="searchCustomerReport()">
      <div class="row">
        <div class="col-md-3 col-sm-6">
          <div class="form-group m-form__group">
            <input class="form-control m-input" type="text" placeholder="Customer Name" name="name" [(ngModel)]="customerReport.first_name" autocomplete="off">
          </div>
        </div>

        <div class="col-md-3 col-sm-6">
          <div class="form-group m-form__group">
            <input class="form-control m-input" type="email" placeholder="Customer Email" name="email" [(ngModel)] ="customerReport.email" autocomplete="off">
          </div>
        </div>
      
        <div class="col-md-6 col-sm-6">
          <div class="form-group m-form__group">
            <button type="submit" class="btn m-btn--pill m-btn--air btn-brand btn-sm"
            style="margin-right:10px">
              <i class="fa fa-calendar-check-o"></i>
                Search
            </button>
            <button type="reset" class="btn m-btn--pill m-btn--air btn-danger btn-sm"
            (click)="resetSearch()">
              <i class="fa fa-history"></i>
                Reset
            </button>
          </div>
        </div>
      </div>
    </form>
    <!--end::Form-->
  </div>
</div>