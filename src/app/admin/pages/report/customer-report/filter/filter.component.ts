import { Component, AfterViewInit, Output, EventEmitter, Input, OnDestroy } from '@angular/core';
import {  CustomerReport } from '../../models/report-models';
import { FORMAT_SEARCH } from '../../../../../globals/_classes/functions';
import { Subscription } from 'rxjs';
import { ActivatedRoute } from '@angular/router';

declare let $:any;


@Component({
  selector: 'customer-report-filter',
  templateUrl: './filter.component.html',
  styleUrls: ['./filter.component.css']
})
export class FilterComponent implements AfterViewInit, OnDestroy {

  customerReport: CustomerReport;
  filter;
  search: boolean;
  sub: Subscription;

  @Input('status') status;
  @Input('stores') stores;
  @Input('terminals') terminals;
  @Output('loadList') loadList: EventEmitter<any> = new EventEmitter();

  constructor(private route: ActivatedRoute,) { 
    this.customerReport = new CustomerReport();
    this.sub = this.route.paramMap.subscribe(
      val => {
        this.reset();
      }
    );
  }

  ngAfterViewInit() {

  }

  ngOnInit() {
    $(function () {
      $(".search-title").on("click", function () {
          $(".search-content-body").slideToggle();
      });
    })
  }

  ngOnDestroy() {
    this.sub.unsubscribe();
  }

  searchCustomerReport(){
    // console.log(this.salesReport);
    // this.filter = FORMAT_SEARCH(this.salesReport);
    this.filter =this.customerReport;
    // console.log(this.filter);
    if(this.filter) {
      this.loadList.emit(this.filter);
      this.search = true;
    }
  }

  resetSearch(){
    this.reset();
    this.filter = null;
    if(this.search) {
      this.loadList.emit('');
      this.search = false;
    }
  }

  reset() {
    this.customerReport = new CustomerReport();
  }


}
