import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { Pagi } from '../../../../modules/pagination/pagi.model';
import { ReportService } from '../report.service/report.service';
import { SalesReportList } from '../models/report-models';
import { downloadFile, getStoreName } from '../../../../globals/_classes/functions';
import { AlertService } from '../../../../modules/alert/alert.service';


declare var moment: any;


@Component({
  selector: 'app-sales-report',
  templateUrl: './sales-report.component.html',
  styleUrls: ['./sales-report.component.css']
})
export class SalesReportComponent implements OnInit {

  salesReport: SalesReportList[] = [];
  pagi: Pagi = new Pagi();
  loader: boolean;
  filter: string = '';
  status = [];
  stores = [];
  terminals = [];
  loaderExport: boolean;
  currentDate;

  @ViewChild('hasCusAlert') alertContainer: ElementRef;
  modStatusArray: any[] = [];

  constructor(
    private alertS: AlertService,
    private reportS: ReportService
  ) {
    this.currentDate=moment().format('YYYY-MM-DD');
    this.getSalesReport(1, 20);
  }

  ngOnInit() {
    //window.scrollTo(0, 0);
    this.getStatus();
    this.getlocation();

   
  }

  trackReport(index, tran) {
    return tran ? tran.id : null;
  }

  getDate(d) {
    if (d) {
      return new Date(d);
    }
    return '';
  }

  reloadTable(e) {
    this.getSalesReport(e.page, e.limit);
  }

  getSalesReport(p?, l?) {
    this.loader = true;
    const params = this.filter
    ?
    '?page_no='+ p +'&limit='+ l+'&start_date=' + this.filter['start_date'] + '&end_date=' + this.filter['end_date'] + '&status=' + this.filter['status'] + '&user_id=' + this.filter['user_id'] + '&source=' + this.filter['source'] + '&location=' + this.filter['location']
    : '?page_no=1&limit=20&start_date=' + moment().subtract(1, 'month').startOf('month').format('YYYY-MM-DD') + '&end_date=' + moment().subtract(1, 'month').endOf('month').format('YYYY-MM-DD');

    this.reportS.getSalesReport(params).subscribe(
      res => {
        // console.log(res);
        this.dataList(res);
        this.loader = false;
      },
      err => console.log(err)
    );
  }

  private dataList(res) {
    this.salesReport = res.data;
    this.pagi.total = res['total'] || 0;
    this.pagi.page = parseInt(res['page_no']) || 1;
    this.pagi.limit = parseInt(res['limit']) || 20;
  }

  filterList(e) {
    this.filter = e;
    // console.log(e);
    this.getSalesReport(1, 20);
  }

  getStatus() {
    this.reportS.getOrderStatus().subscribe(
      res => {
        this.modStatusArray = res;
      },
      err => console.log(err)
    );
  }

  getlocation() {
    this.terminals = [];
    this.reportS.getterminals().subscribe(
      res => {
        if (res.status == 'OK') {
          this.stores = res.result.data;
          for (let s of res.result.data) {
            for (let t of s.stores_terminals) {
              const obj = {};
              obj['id'] = t.id;
              obj['name'] = s.store_name + ' - ' + t.name;
              this.terminals.push(obj);
            }
          }
        }
      }, err => console.log(err)
    );
  }
  getTotal(s, t, de, di) {
    return (s ? s : 0) + (t ? t : 0) + (de ? de : 0) - (di ? di : 0);
  }

  exportSales() {
    this.loaderExport = true;
    const params = this.filter
      ?
      '?page_no=1&limit=20&start_date=' + this.filter['start_date'] + '&end_date=' + this.filter['end_date'] + '&status=' + this.filter['status'] + '&user_id=' + this.filter['user_id'] + '&source=' + this.filter['source'] + '&location=' + this.filter['location']+'&export=true'
      : '?page_no=1&limit=20&start_date=' + this.currentDate + '&end_date=' +this.currentDate+'&export=true';
    // console.log(params);
    this.reportS.exportSales(params).then(
      res => {
        let fileName=getStoreName()+'_sales';
       downloadFile(res,fileName);
        this.loaderExport = false;
      },
      err => {
        console.log(err);
        this.loaderExport = false;
        this.alertS.error(this.alertContainer, 'Something wrong!!! Please try again', true, 3000);
      }
    );
  }


}
