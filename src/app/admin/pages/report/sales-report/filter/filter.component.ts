import { Component, AfterViewInit, Output, EventEmitter, Input, OnDestroy } from '@angular/core';
import { SalesReport } from '../../models/report-models';
import { FORMAT_SEARCH } from '../../../../../globals/_classes/functions';
import { Subscription } from 'rxjs';
import { ActivatedRoute } from '@angular/router';
import { OrderService } from '../../../reservations/order.service/order.service';

declare let $:any;
declare var moment: any;

@Component({
  selector: 'sales-report-filter',
  templateUrl: './filter.component.html',
  styleUrls: ['./filter.component.css']
})
export class FilterComponent implements AfterViewInit, OnDestroy {

  salesReport: SalesReport;
  filter;
  search: boolean;
  sub: Subscription;
  location=[];
  cashierList=[];

  @Input('status') status;
  @Input('stores') stores;
  @Input('terminals') terminals;
  @Output('loadList') loadList: EventEmitter<any> = new EventEmitter();
  mod_status: any[] = [];

  constructor(
    private route: ActivatedRoute,
     private orderS: OrderService
    ) { 
    this.salesReport = new SalesReport();
    this.sub = this.route.paramMap.subscribe(
      val => {
        this.reset();
      }
    );
  }

  ngAfterViewInit() {
    this.salesReport.start_date = moment().subtract(1, 'month').startOf('month').format('YYYY-MM-DD');
    this.salesReport.end_date = moment().subtract(1, 'month').endOf('month').format('YYYY-MM-DD');
    
    $('#m_daterangepicker_3 .form-control').val( 
      'From ' + 
      moment().subtract(1, 'month').startOf('month').format('YYYY-MM-DD') + 
      ' To ' + 
      moment().subtract(1, 'month').endOf('month').format('YYYY-MM-DD')
    );
    this._dateRange();
  }

  ngOnInit() {
    $(function () {
      $(".search-title").on("click", function () {
          $(".search-content-body").slideToggle();
      });
    })

    this.getLocation();
    this.getCashierList();
  }

  ngOnDestroy() {
    this.sub.unsubscribe();
  }

  ngOnChanges(): void { }

  getStatusValue($event) {
    this.salesReport.status = $event;
  }

  getLocation() {
    this.orderS.getLocations().subscribe(
      res => {
        if (res.status == "OK") {
          this.location = res.result.data.map(m => {
            return { id: m.id, name: m.name };
          });
        } else {
          this.location = [];
        }
      },
      err => {
        this.location = [];
        console.log(err);
      }
    );
  }


  getCashierList() {
    this.orderS.getCashiers().subscribe(
      res => {
        if (res.status == "OK") {
          this.cashierList = res.result.data.users;
        } else {
          this.cashierList = [];
        }
      },
      err => {
        this.cashierList = [];
        console.log(err);
      }
    );
  }


  searchSalesReport(){
    // console.log(this.salesReport);
    // this.filter = FORMAT_SEARCH(this.salesReport);
    this.filter = this.salesReport;
    // console.log(this.filter);
    if(this.filter) {
      this.loadList.emit(this.filter);
      this.search = true;
    }
  }

  resetSearch(){
    this.reset();
    this.filter = null;
    if(this.search) {
      this.loadList.emit('');
      this.search = false;
    }
  }

  reset() {
    this.salesReport = new SalesReport();
    this.salesReport.start_date = null;
    this.salesReport.end_date = null;
  }

  private _dateRange() {
    $('#m_daterangepicker_3').daterangepicker({
      opens: 'left',
      startDate: moment().subtract(1, 'month').startOf('month'),
      endDate: moment().subtract(1, 'month').endOf('month'),
      ranges: {
        'Today': [moment(), moment()],
        'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
        'Last 7 Days': [moment().subtract(6, 'days'), moment()],
        'Last 30 Days': [moment().subtract(29, 'days'), moment()],
        'This Month': [moment().startOf('month'), moment().endOf('month')],
        'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
      },
      autoUpdateInput: true,
      buttonClasses: 'm-btn btn',
      applyClass: 'btn-brand',
      cancelClass: 'btn-danger'
    }, (start, end, label) => {
      this.salesReport.start_date = start.format('YYYY-MM-DD');
      this.salesReport.end_date = end.format('YYYY-MM-DD');
        $('#m_daterangepicker_3 .form-control').val( 'From ' + start.format('YYYY-MM-DD') + ' To ' + end.format('YYYY-MM-DD'));
      });
  }

}
