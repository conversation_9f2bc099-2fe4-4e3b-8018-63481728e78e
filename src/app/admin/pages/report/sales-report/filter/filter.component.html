<div class="m-portlet m-portlet--mobile">
  <div class="m-portlet__head search-title">
    <div class="m-portlet__head-caption">
      <div class="m-portlet__head-title">
        <h3 class="m-portlet__head-text">
          <i class="la la-search"></i>
          Search
        </h3>
      </div>
    </div>
  </div>
  <div class="m-portlet__body search-panel search-content-body">
    <!--begin::Form-->
    <form class="m-form m-form--fit m-form--label-align-right second-form" 
      #form="ngForm" (ngSubmit)="searchSalesReport()">
      <div class="row">

        <div class="col-md-4 col-sm-4">
          <div class="form-group m-form__group">
            <div class="m-input-icon pull-right" id="m_daterangepicker_3">
              <input type="text" class="form-control m-input" placeholder="Select Date Range" autocomplete="off" readonly>
              <span class="m-input-icon__icon m-input-icon__icon--right">
                <span>
                  <i class="la la-calendar-check-o"></i>
                </span>
              </span>
            </div>
          </div>
        </div>

       

        <div class="col-md-4 col-sm-4">
          <div class="form-group m-form__group">
            <select class="form-control m-input" name="location" [(ngModel)] ="salesReport.location">
              <option value=''>-Location-</option>
              <option *ngFor="let s of location" [value]="s.id">{{s.name}}</option>
            </select>
          </div>
        </div>

        <div class="col-md-4 col-sm-4">
          <div class="form-group m-form__group">
            <select class="form-control m-input" name="user_id" [(ngModel)] ="salesReport.user_id">
              <option value=''>-Sales Person-</option>
              <option *ngFor="let cashier of cashierList" [value]="cashier.id">{{cashier.first_name}} {{ cashier.last_name}}</option>
            </select>
          </div>
        </div>

        <div class="col-md-4 col-sm-4">
          <div class="form-group m-form__group">
            <select class="form-control m-input" name="source" [(ngModel)] ="salesReport.source">
              <option value=''>-Order Source-</option>
              <option value="Admin">Admin</option>
              <option value="Online">Online</option>
              <option value="POS">POS</option>
            </select>
          </div>
        </div>

        <!-- <div class="col-md-4 col-sm-4">
          <div class="form-group m-form__group">
            <select class="form-control m-input" name="status" [(ngModel)] ="salesReport.status">
              <option value=''>-Status-</option>
              <option *ngFor="let s of mod_status; let i = 'index'" [value]="s.id">{{s.label}}</option>
            </select>
          </div>
        </div> -->

        <div class="col-md-4 col-sm-4" *ngIf="status">
          <div class="form-group m-form__group">
              <app-status-option 
                [status]="status" 
                (onSelectStatus)="getStatusValue($event)">
              </app-status-option>
          </div>
        </div>

      
        <div class="col-md-4 col-sm-4">
          <div class="form-group m-form__group">
            <button type="submit" class="btn m-btn--pill m-btn--air btn-brand btn-sm"
            style="margin-right:10px">
              <i class="fa fa-calendar-check-o"></i>
                Search
            </button>
            <button type="reset" class="btn m-btn--pill m-btn--air btn-danger btn-sm"
            (click)="resetSearch()">
              <i class="fa fa-history"></i>
                Reset
            </button>
          </div>
        </div>
      </div>
    </form>
    <!--end::Form-->
  </div>
</div>