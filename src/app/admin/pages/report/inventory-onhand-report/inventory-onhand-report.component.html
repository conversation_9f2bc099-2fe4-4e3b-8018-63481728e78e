<div class="custom-alert" #hasAlert></div>
<!-- BEGIN: Subheader -->
<div class="m-subheader">
  <div class="d-flex align-items-center">
    <div class="mr-auto">
      <h3 class="m-subheader__title m-subheader__title--separator">
        Inventory On Hand Report
      </h3>
      <ul class="m-subheader__breadcrumbs m-nav m-nav--inline">
        <li class="m-nav__item m-nav__item--home">
          <a routerLink="/admin" class="m-nav__link m-nav__link--icon">
            <i class="m-nav__link-icon la la-home"></i>
          </a>
        </li>
        <li class="m-nav__separator">
          <i class="fa fa-angle-right"></i>
        </li>
        <li class="m-nav__item">
          <a class="m-nav__link">
            <span class="m-nav__link-text">
              Reports
            </span>
          </a>
        </li>
        <li class="m-nav__separator" style="padding-left: 10px">
          <i class="fa fa-angle-right"></i>
        </li>
        <li class="m-nav__item">
          <a class="m-nav__link">
            <span class="m-nav__link-text">
              Inventory On Hand Report
            </span>
          </a>
        </li>
      </ul>
    </div>
  </div>
</div>
<!-- END: Subheader -->
<div class="m-content product-list animated fadeIn">
  <!--begin::Portlet-->
  <inventory-onhand-report-filter
    [status]="status"
    [stores]="stores"
    [terminals]="terminals"
    (loadList)="filterList($event)"
  ></inventory-onhand-report-filter>
  <!--end::Portlet-->
  <!--begin::Portlet-->
  <div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__head">
      <div class="m-portlet__head-caption">
        <div class="m-portlet__head-title">
          <h3 class="m-portlet__head-text">
            Inventory On Hand Report
          </h3>
          <!-- <div class="button-midle float-right">
            <div
              *ngIf="loaderExport; else ExportBTN"
              class="m-loader m-loader--brand"
              style="width: 30px; display: inline-block;"
            ></div>
            <ng-template #ExportBTN>
              <button
                type="button"
                class="btn btn-success btn-sm"
                (click)="exportSales()"
              >
                <i class="m-nav__link-icon la la-cloud-download"></i>
                <span class="m-nav__link-text">
                  Export Report
                </span>
              </button>
            </ng-template>
          </div> -->
        </div>
      </div>
    </div>

    <div class="m-portlet__body" style="position: relative;">
      <!--begin: Datatable -->
      <!--begin::Section-->
      <div class="m-section">
        <div class="m-section__content price-table">
          <div *ngIf="loader" class="table-load m-loader m-loader--brand"></div>
          <div class="table-responsive" style="margin-bottom: 10px;">
            <table class="table table-hover table-striped">
              <thead>
                <tr>
                  <th>
                    Product Name
                  </th>

                  <th>
                    Supplier Name
                  </th>
                  <th>
                    Supply Id
                  </th>
                  <th>
                      Barcode
                    </th>
                  <th>
                    Total Quantity
                  </th>
                  <th>
                    Available
                  </th>
                </tr>
              </thead>
              <tbody *ngIf="InventoryOnhandReport.length < 1; else table">
                <tr *ngIf="!loader">
                  <td colspan="10">
                    <h4 class="text-center">No Data Found</h4>
                  </td>
                </tr>
              </tbody>
              <ng-template #table>
                <tbody *ngFor="
                let productGroup of InventoryOnhandReport;
                let o = odd;
                let e = even;
                trackBy: trackReport
              ">
                  <tr
                   
                  >
                    <td>
                      {{ productGroup[0]?.name }}
                    </td>

                    <td>{{ productGroup[0]?.supplier_name }}</td>
                    <td>{{ productGroup[0]?.supply_id }}</td>
                    <td><span *ngIf="productGroup[0]?.chain =='1'">{{ productGroup[0]?.barcode }}</span></td>
                    <td><span *ngIf="productGroup[0]?.chain =='1'">{{ productGroup[0]?.quantity }}</span></td>
                    <td><span *ngIf="productGroup[0]?.chain =='1'">{{ productGroup[0]?.available }}</span></td>
                  
                  </tr>
                  <tr *ngIf="productGroup[0]?.chain =='1' else show"></tr>

                  <ng-template #show>
                      <tr  class="vairint-table" *ngFor="
                      let variant of productGroup;
                     ">
                        <td><i class="fa fa-long-arrow-right ml-2 mr-2"></i>{{variant?.chain_name}}</td>
                        <td></td>
                        <td></td>
                        <td>{{ variant?.barcode }}</td>
                        <td>{{ variant?.quantity }}</td>
                        <td>{{ variant?.available }}</td>
                      </tr>
                  </ng-template>
               
                  
                </tbody>
              </ng-template>
            </table>
          </div>
          <!-- pagination Start-->
          <boot-pagination
            [totalSize]="pagi.total"
            [page]="pagi.page"
            [pagelimit]="pagi.limit"
            [listSize]="pagi.limit"
            (pageChange)="reloadTable($event)"
          ></boot-pagination>
          <!-- pagination End-->
        </div>
      </div>
      <!--end::Section-->
      <!--end: Datatable -->
    </div>
  </div>
  <!--end::Portlet-->
</div>
