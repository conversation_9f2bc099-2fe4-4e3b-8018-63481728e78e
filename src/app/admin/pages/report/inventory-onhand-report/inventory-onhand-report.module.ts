import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FilterComponent } from './filter/filter.component';
import { RouterModule, Routes } from '@angular/router';
import { PaginationModule } from '../../../../modules/pagination/pagination.module';
import { FormsModule } from '@angular/forms';
import { CurrencyFormatModule } from '../../../../modules/currency-format/currency-format.pipe';
import { InventoryOnHandReportComponent } from './inventory-onhand-report.component';

const route: Routes = [
  {
    path: '',
    component: InventoryOnHandReportComponent
  }
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(route),
    FormsModule,
    PaginationModule,
    CurrencyFormatModule
  ],
  declarations: [InventoryOnHandReportComponent, FilterComponent]
})
export class InventoryOnHandReportModule { }
