
export class ReportServiceConfig {
    settings: any;
}

export class VoucherSearch {
    order_id: number;
    transaction_id: number;
    transaction_start: string;
    transaction_end: string;
    amount: string;
    amount_type: string;
}

export class Report {
    name: string;
    email: string;
    date_start: string;
    date_end: string;
    location: number = null;
    terminal: number = null;
    transaction_type: string = null;
    payment_type: number = null;
    payment_status: number = null;
    order_status: number = null;
    coupon_code: string;
}

export class SalesReport {
    start_date: string;
    end_date: string;
    status: string='';
    user_id: string='';
    source: string='';
    location: string='';
}

export class OnRentReport {
    start_date: string;
    end_date: string;
    product_id:number;
}

export class TransactionReport {
    start_date: string;
    end_date: string;
    order_id:number;
    transaction_id:number;
}

export class InventoryOnhandReport {
    start_date: string;
    end_date: string;
    location: number = null;
}


export class CustomerReport {
    first_name: string;
    email: string;
}


export interface SalesReportList {
    id: number;
    created: string;
    total_quantity: number;
    status: string;
    sub_total: number;
    tax: number;
    total_discount: number
    total_deposit: number
    payments: SalesPayment;
}

export interface SalesPayment {
    id: number;
    order_id: number;
    type: number;
    payment_amount: number;
    payment_method: string;
    content_id: number
    terminal_id: number
    content_method: string;
}

