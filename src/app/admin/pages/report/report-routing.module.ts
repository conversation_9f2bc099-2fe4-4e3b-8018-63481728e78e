import {NgModule} from '@angular/core';
import {Routes, RouterModule} from '@angular/router';
import { AdminService } from '../../admin.service';
import { PagesComponent } from '../pages.component';
import { ReportComponent } from './report.component';
import { AdminRoleGaurdService } from '../../admin-role-gaurd.service';


const routes: Routes = [
  {
    path: '',
    component: PagesComponent,
    children: [{
        path: '',
        component: ReportComponent,
        canActivate: [AdminService],
        children: [
          {
            path: 'dashboard',
            loadChildren: () => import('./report-dashboard/report-dashboard.module').then(m => m.ReportDashboardModule)
          },
            {
                path: 'order-voucher',
                loadChildren: () => import('./order-voucher/order-voucher.module').then(m => m.OrderVoucherModule)
            },
            {
              path: 'sales-report',
              loadChildren: () => import('./sales-report/sales-report.module').then(m => m.SalesReportModule),
              canActivate: [AdminRoleGaurdService]
            },
            {
              path: 'on-rent-report',
              loadChildren: () => import('./on-rent/on-rent-report.module').then(m => m.OnRentReportModule),
              canActivate: [AdminRoleGaurdService]
            },
            {
              path: 'transactions-report',
              loadChildren: () => import('./transactions-report/transactions-report.module').then(m => m.TransactionsReportModule),
              canActivate: [AdminRoleGaurdService]
            },
            {
              path: 'inventory-onhand-report',
              loadChildren: () => import('./inventory-onhand-report/inventory-onhand-report.module').then(m => m.InventoryOnHandReportModule),
              canActivate: [AdminRoleGaurdService]
            },
            {
              path: 'customer-report',
              loadChildren: () => import('./customer-report/customer-report.module').then(m => m.CustomerReportModule),
              canActivate: [AdminRoleGaurdService]
            }
        ]
    }]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ReportRoutingModule {}