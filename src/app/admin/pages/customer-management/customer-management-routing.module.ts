import { ClientService } from './client-service/client.service';
import { CustomerManagementComponent } from './customer-management.component';
import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AdminService } from '../../admin.service';
import { PagesComponent } from '../pages.component';

const routes: Routes = [
  {
    path: '',
    component: PagesComponent,
    children: [{
        path: '',
        component: CustomerManagementComponent,
        canActivate: [AdminService],
        children:[
            {
                path: 'all',
                loadChildren: () => import('./all-user/all-user.module').then(m => m.AllUserModule),
                resolve: { 'user_list': ClientService }
            },
            {
                path: ':user_id',
                loadChildren: () => import('./user-profile/user-profile.module').then(m => m.UserProfileModule),
            },
            {
                path: '**',
                redirectTo: 'all'
            },
        ]
    }]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CustomerManagementRoutingModule {}