import { ClientResolveService } from './../client-service/client-resolve.service';
import {NgModule} from '@angular/core';
import {Routes, RouterModule} from '@angular/router';
import { UserProfileComponent } from './user-profile.component';


const routes: Routes = [
  {
    path: '',
    component: UserProfileComponent,
    children:[
      {
        path:'account-settings',
        loadChildren:() => import('./account-settings/account-settings.module').then(m => m.AccountSettingsModule)
      },
      {
        path:'profile',
        loadChildren:() => import('./overview/overview.module').then(m => m.OverviewModule),
        resolve: { 'user_info': ClientResolveService}
      },
      {
        path: '**',
        redirectTo: 'profile'
      },
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class UserProfileRoutingModule {}