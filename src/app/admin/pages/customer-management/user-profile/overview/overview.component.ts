import { ClientService } from './../../client-service/client.service';
import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { Helpers } from '../../../../../helpers';
import { AlertService } from '../../../../../modules/alert/alert.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { AddCustomerAddressComponent } from './add-address/add-address.component';
import { DialogBoxComponent } from '../../../../../modules/dialog-box/dialog-box.component';

const feed=[
  {id:1514,type_action:'login-success',content_id:712,ip:'*************',os:'Android',browser:'Chorme',device:'Mobile',date:'5/15/18 13:22'},
  {id:1515,type_action:'login-reject',content_id:712,ip:'*************',os:'Android',browser:'Handheld',device:'Mobile',date:'5/15/18 13:22'}
]

@Component({
  selector: 'app-overview',
  templateUrl: './overview.component.html',
  styleUrls: ['./overview.component.css']
})
export class OverviewComponent implements OnInit {

  feeds = feed;
  userId:number;
  user_info;
  cards;
  address = [];

  @ViewChild("hasCusAlert") alertContainer: ElementRef;

  constructor(
    private router: Router,
     private route:ActivatedRoute,
    private userS: ClientService,
    private alertS: AlertService,
    private modalService: NgbModal
  ) {
    this.userId = this.route.parent.parent.snapshot.params['user_id'];
    this.getAddress();
   }

  ngOnInit() {
    //window.scrollTo(0,0);
    this.user_info = this.route.snapshot.data['user_info'].data;
    // this.user_info = this.userS.formateUser(this.user_info);
    this.getCards();
  }

  getAddress(){
    this.userS.getCustomerAddress(this.userId).then(res => {
      res.status === 'OK' ? this.address = res.result.data : this.address = [];
    }).catch(err => this.address = []);
  }

  goToPersonalInfo(){
    this.router.navigate(["admin/customer/" + this.userId + "/account-settings"]);
  }

  getCards(){
    this.userS.getCardTotal(this.userId).subscribe(
      res=>{
        this.cards = res.data;
      },
      err => console.log(err)
    );
  }

  findUserType(data){
    return this.userS.findUserType(data);
  }

  checkStatus(data){
    return this.userS.checkStatus(data);
  }

  getText(t) {
    if(t) {
      return t.replace(/<(?:.|\n)*?>/gm, '');
    }
    return '';
  }



  openAddress(type: string, data?: any) {
    const modalRef = this.modalService.open(AddCustomerAddressComponent, {
      centered: true,
      windowClass: "animated fadeIn",
      backdropClass: "modal-backdrop-cus",
      size: "lg",
      backdrop : 'static',
      keyboard : false
    });
    modalRef.componentInstance.addressType = type;
    modalRef.componentInstance.customer_id = this.userId;
    if (type === 'edit' && data) {
      modalRef.componentInstance.addressData = data;
    }
    modalRef.componentInstance.response.subscribe(res => {
      if (res.status === 'OK') {
        this.getAddress();
        this.alertS.success(
          this.alertContainer,
          res.result.message,
          true,
          3000
        );
      } else {
        this.alertS.error(
          this.alertContainer,
          "Something went wrong!!",
          true,
          5000
        );
      }
    })
  }

  delete(data) {
    const modalRef = this.modalService.open(DialogBoxComponent, {
      centered: true,
      windowClass: "animated fadeIn",
      backdropClass: "modal-backdrop-cus",
      size: "sm"
    });
    modalRef.componentInstance.massage = "Do you want delete this address?";
    modalRef.result.then(
      result => {
        if (result) {
          Helpers.setLoading(true);
          this.userS.deleteCustomerAddress(data.id).then(
            res => {
              Helpers.setLoading(false);
              if (res.status === 'OK') {
                this.getAddress();
                this.alertS.success(
                  this.alertContainer,
                  res.result.message,
                  true,
                  3000
                );
              } else if (res.status === 'NOK') {
                this.alertS.error(
                  this.alertContainer,
                  res.result.message,
                  true,
                  3000
                );
              }else {
                this.alertS.error(
                  this.alertContainer,
                  "Something went wrong!!",
                  true,
                  5000
                );
              }
            }
          ).catch(
            err => {
              Helpers.setLoading(false);
              this.alertS.error(
                this.alertContainer,
                err.error.result
                  ? err.error.result.message
                  : err.message,
                true,
                5000
              );
            }
          )
        }
      },
      res => {
        console.log(res);
      }
    );
  }


}
