
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Component, OnInit, Input, ViewChild, ElementRef, EventEmitter, Output } from '@angular/core';
import { ClientService } from '../../../client-service/client.service';
import { AlertService } from '../../../../../../modules/alert/alert.service';
import { allCountry } from '../../../../../../globals/_classes/country';
import { Helpers } from '../../../../../../helpers';

@Component({
  selector: 'app-add-client-address',
  templateUrl: './add-address.component.html',
  styleUrls: ['./add-address.component.css']
})
export class AddCustomerAddressComponent implements OnInit {

  form: FormGroup;
  countries;
  countryCode;
  submitted = false;
  @Input() public addressType;
  @Input() public customer_id;
  @Input() public addressData;
  @Output() response: EventEmitter<any> = new EventEmitter();
  @ViewChild("hasAlert") alertContainer: ElementRef;

  constructor(
    private formBuilder: FormBuilder,
    private service: ClientService,
    public activeModal: NgbActiveModal,
    private alertService: AlertService
  ) {
    this.countryCode = 'us';
    this.form = this.formBuilder.group({
      address_line1: ['', Validators.required],
      city: ['', Validators.required],
      state: [''],
      zipcode: [''],
      country: [this.countryCode],
      type: ['']
    })
  }

  ngOnInit() {
    console.log(this.addressData)
    if (this.addressData) {
      this.form.patchValue(this.addressData);
    }
    this.countries = allCountry;
    this.countries.sort((a, b) => a.name.localeCompare(b.name));
  }

  submit() {
    this.submitted = true;
    if (this.form.invalid) {
      return;
    }
    Helpers.setLoading(true);
    if (this.addressType === 'add') {
      this.service.addNewAddress(this.form.getRawValue(),this.customer_id).then(res => {
        Helpers.setLoading(false);
        if (res.status === 'OK') {
          this.dismiss(true);
          this.response.emit(res);
        } else if (res.status === 'NOK') {
          this.alertService.error(this.alertContainer, res.result.message, true, 5000);
        } else {
          this.alertService.error(this.alertContainer, 'Something went wrong!', true, 5000);
        }
      }).catch(err => {
        Helpers.setLoading(false);
        this.alertService.error(
          this.alertContainer,
          err.error.result
            ? err.error.result.message
            : err.message,
          true,
          5000
        );
      })
    } else if (this.addressType === 'edit') {
      this.service.updateAddress(this.form.getRawValue(), this.addressData.id,this.customer_id).then(res => {
        Helpers.setLoading(false);
        if (res.status === 'OK') {
          this.dismiss(true);
          this.response.emit(res);
        } else if (res.status === 'NOK') {
          this.alertService.error(this.alertContainer, res.result.message, true, 5000);
        } else {
          this.alertService.error(this.alertContainer, 'Something went wrong!', true, 5000);
        }
      }).catch(err => {
        Helpers.setLoading(false);
        this.alertService.error(
          this.alertContainer,
          err.error.result
            ? err.error.result.message
            : err.message,
          true,
          5000
        );
      })
    }
  }

  dismiss(e) {
    if (e) {
      this.activeModal.dismiss();
    }
  }

}
