import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { ClientService } from '../../../client-service/client.service';
import { Router, ActivatedRoute } from '@angular/router';
import { AlertService } from './../../../../../../modules/alert/alert.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ImagePopupComponent } from '../../../../../../modules/image-popup/image-popup.component';
import { DialogBoxComponent } from '../../../../../../modules/dialog-box/dialog-box.component';

class note {
  id: number;
  text: string = ''
  file: File
}

@Component({
  selector: 'app-notes',
  templateUrl: './notes.component.html',
  styleUrls: ['./notes.component.css']
})
export class NotesComponent implements OnInit {

  userId: number;
  user_info;
  cards;
  address = [];
  loader: boolean = false;
  btn_text: string = 'Save';
  note: note;
  noteList = [];
  is_show_noteForm = false;
  fileName:string='';

  @ViewChild("hasCusAlert") alertContainer: ElementRef;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private userS: ClientService,
    private alertS: AlertService,
    private modalService: NgbModal
  ) { 
    this.userId = this.route.parent.parent.parent.snapshot.params['user_id'];
  }

  ngOnInit() {
    this.note = new note();
    this.AddNote();
    this.userS.getClientNotes(this.userId).then(
      res => {
        if (res.status === 'OK') {
          this.noteList = res.result.data;
        } else {
          this.noteList = [];
        }
      }
    ).catch(
      err => this.noteList = []
    );
  }

  getText(t) {
    if (t) {
      return t.replace(/<(?:.|\n)*?>/gm, '');
    }
    return '';
  }

  onClickCancelNote() {
    this.is_show_noteForm = false;
  }

  AddNote() {
    this.note=new note();
    this.note.id=null;
    this.btn_text="Save";
    this.is_show_noteForm = true;
    this.fileName='';
  }


  onChangeNoteFile(e) {
    let file = e.target.files[0];
    this.note.file = file;
    this.fileName=file.name;
  }

  showBigImage(img) {
    const modalImage = this.modalService.open(ImagePopupComponent, {
      centered: true
    });
    modalImage.componentInstance.image = img;
  }


  saveNote() {
    const sendData = new FormData();
    sendData.append("content_id", this.userId.toString());
    sendData.append("content_type", 'customer');
    sendData.append("text", this.note.text);
    sendData.append("file", this.note.file);

    if (this.note.id) {
      this.userS.updateClientNotes(this.note.id, sendData).then(res => {
        if (res.status == "OK") {
          const note=res.result.data;
          this.noteList.map(item => {
            if (item.id == note.id) {
              item.text = note.text
              item.file_type = note.file_type
              item.file_path = note.file_path
            }
            return item;
          })

          // this.is_show_noteForm=false;
          this.fileName='';

          this.alertS.success(
            this.alertContainer,
            res.result.message,
            true,
            5000
          );
        }
        else {

          this.alertS.error(
            this.alertContainer,
            res.result.message,
            true,
            5000
          );
        }
      }).catch(err => {

      })
    }
    else {
      this.userS.addClientNotes(sendData).then(res => {
        if (res.status == "OK") {

          this.noteList.push(res.result.data);
          // this.is_show_noteForm=false;
          this.fileName='';

          this.alertS.success(
            this.alertContainer,
            res.result.message,
            true,
            5000
          );
        }
        else {
          this.alertS.error(
            this.alertContainer,
            res.result.message,
            true,
            5000
          );
        }
      }).catch(err => {

      })
    }


  }

  editNote(note) {
    this.note.id = note.id;
    this.note.text = note.text;
    this.is_show_noteForm = true;
    this.btn_text = "Update";
  }


  onDeleteNote(id, index) {
    const modalRef = this.modalService.open(DialogBoxComponent, {
      centered: true,
      size: "sm",
      windowClass: "animated fadeIn"
    });
    modalRef.componentInstance.massage = "Are you sure you want to delete?";
    modalRef.result.then(
      result => {
        this.deleteNote(id, index);
      },
      reason => {
        console.log(reason);
      }
    );
  }

  deleteNote(id, index) {
    this.userS.deleteClientNotes(id).then(res => {
      if (res.status == "OK") {
        this.noteList.splice(index, 1);
        this.alertS.success(
          this.alertContainer,
          res.result.message,
          true,
          5000
        );
      }
      else {
        this.alertS.error(
          this.alertContainer,
          res.result.message,
          true,
          5000
        );
      }
    }).catch(err => {

    })

  }

}
