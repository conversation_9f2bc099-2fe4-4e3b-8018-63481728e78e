import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AccountSettingsComponent } from './account-settings.component';
import { Routes, RouterModule } from '@angular/router';
import { PersonalInfoComponent } from './personal-info/personal-info.component';
import { ChangeAvatarComponent } from './change-avatar/change-avatar.component';
import { ChangePasswordComponent } from './change-password/change-password.component';
import { DragDropModule } from '../../../../../modules/drag-drop/drag-drop.module';
import { PasswordStrengthBarModule } from '../../../../../modules/password-strength-bar/password-strength-bar.module';
import { FormsModule } from '@angular/forms';
import { DialogBoxModule } from '../../../../../modules/dialog-box/dialog-box.module';
import { DialogBoxComponent } from '../../../../../modules/dialog-box/dialog-box.component';
import { AddPinComponent } from './add-pin/add-pin.component';
import { ClientResolveService } from '../../client-service/client-resolve.service';
import { NotesComponent } from './notes/notes.component';
import { ImagePopupModule } from '../../../../../modules/image-popup/image-popup.module';
import { ImagePopupComponent } from '../../../../../modules/image-popup/image-popup.component';

const route:Routes= [
  {
    path: '',
    component: AccountSettingsComponent,
    children:[
      {
        path: 'personal-info',
        component: PersonalInfoComponent,
        resolve:{
          'user': ClientResolveService
        }
      },
      {
        path: 'notes',
        component: NotesComponent
      },
      {
        path: 'change-password',
        component: ChangePasswordComponent
      },
      {
        path: '**',
        redirectTo:'personal-info'
      }
    ]
  }
]

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(route),
    DragDropModule,
    FormsModule,
    PasswordStrengthBarModule,
    DialogBoxModule,
    ImagePopupModule
  ],
  entryComponents: [DialogBoxComponent, ImagePopupComponent],
  exports:[RouterModule],
  declarations: [
    AccountSettingsComponent, 
    PersonalInfoComponent, 
    ChangeAvatarComponent, 
    ChangePasswordComponent,
    AddPinComponent,
    NotesComponent
  ]
})
export class AccountSettingsModule { }
