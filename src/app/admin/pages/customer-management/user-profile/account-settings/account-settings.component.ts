import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { Component, OnInit } from '@angular/core';
import { PLATFORM_ID,Inject } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';

@Component({
  selector: 'app-account-settings',
  templateUrl: './account-settings.component.html',
  styleUrls: ['./account-settings.component.css']
})
export class AccountSettingsComponent implements OnInit {

  subs: Subscription[] = [];
  from_customer_list: boolean = false;
  userId: any;
  is_customer: boolean;

  constructor(
    @Inject(PLATFORM_ID) private platform: Object,
    private route: ActivatedRoute,
    private router: Router
  ) {
    this.userId = this.route.parent.parent.snapshot.params['user_id'];
    const contents = localStorage.getItem('contents') ? JSON.parse(localStorage.getItem('contents')) : null;
    this.is_customer = contents.site_specific.confg.hasOwnProperty('customer')
      ? contents.site_specific.confg.customer.hasOwnProperty('active')
        ? contents.site_specific.confg.customer.active
        : false
      : false;
  }

  ngOnInit() {
    // if (isPlatformBrowser(this.platform)) {
    // window.scrollTo(0,0);
    // }
    this.subs.push(
      this.route.queryParams.subscribe(query => {
        if (query.source) {
          this.from_customer_list = true;
        }
      })
    );
  }

  ngOnDestroy(): void {
    this.subs.forEach(sub => sub.unsubscribe());
  }

  backToList(): void {
    if (this.from_customer_list) {
      this.router.navigate(['/admin/customers']);
    } else {
      this.router.navigate(['/admin/customer/all']);
    }
  }

  goToNotes(): void {
    if (this.from_customer_list) {
      console.log(this.userId)
      this.router.navigate(
        [`/admin/customer/${this.userId}/account-settings/notes`],
        {
          queryParams: {
            source: "customer-list"
          }
        }
      );
    } else {
      this.router.navigate([`/admin/customer/${this.userId}/account-settings/notes`]);
    }
  }

}
