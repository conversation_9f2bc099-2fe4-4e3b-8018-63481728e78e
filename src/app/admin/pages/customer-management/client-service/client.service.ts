import { Injectable, Optional } from "@angular/core";
import { map, debounceTime } from "rxjs/operators";
import {
  Router,
  Resolve,
  ActivatedRouteSnapshot,
  RouterStateSnapshot
} from "@angular/router";
import { Observable, BehaviorSubject } from "rxjs";
import { HttpService } from "../../../../modules/http-with-injector/http.service";
import { UserServiceConfig } from "./../models/user.models";
import { FORMAT_SEARCH } from "../../../../globals/_classes/functions";

@Injectable()
export class ClientService implements Resolve<any> {
  config: UserServiceConfig;

  private USER_Image = new BehaviorSubject<string>(null);
  user_image = this.USER_Image.asObservable();

  private USER_Status = new BehaviorSubject<any>(null);
  user_status = this.USER_Status.asObservable();

  changeImage(image: string) {
    this.USER_Image.next(image);
  }

  changeProfileStatus(st) {
    this.USER_Status.next(st);
  }

  constructor(
    @Optional() config: UserServiceConfig,
    private http: HttpService,
    private router: Router
  ) {
    this.config = config;
  }

  resolve(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<any> {
    return this.getUserList(1, 10, "");
  }

  getUserList(page?, limit?, filter?) {
    return this.http
      .get("customers?type=1&page_no=" + page + "&limit=" + limit + filter)
      .pipe(map(res => res.result));
  }

  proccessFilterParam(param) {
    return FORMAT_SEARCH(param);
  }

  findUserType(type) {
    switch (true) {
      case type == 1:
        return "Customer";
      case type == 2:
        return "Client";
      case type == 3:
        return "Store Admin";
      case type == 4:
        return "Salesperson";
    }
  }

  checkStatus(state) {
    if (state == 1) {
      return "Active";
    }
    return "Inactive";
  }

  register(data) {
    return this.http.post("customers?source=admin", data).toPromise();
  }

  deleteUser(id) {
    return this.http.delete(`customers/${id}`).toPromise();
  }

  getuserProfileInfo(id) {
    return this.http.get(`customers/${id}`).pipe(map(res => res.result));
  }

  updateUserInfo(id, data) {
    return this.http.post(`customers/${id}?source=admin`, data).toPromise();
  }

  addUserAddress(data) {
    return this.http.post(`users/address`, data).toPromise();
  }

  updateUserAddress(ad_id, data) {
    return this.http.post(`users/address/${ad_id}`, data).toPromise();
  }

  deleteUserAddress(ad_id) {
    return this.http.delete(`addresses/${ad_id}/delete`).toPromise();
  }


  addNewAddress(data,customer_id) {
    return this.http.post(`customers/address?type=${data.type}&customer_id=${customer_id}&source=admin`, data).toPromise();
  }

  updateAddress(data, id,customer_id) {
    return this.http.post(`customers/address/${id}?type=${data.type}&customer_id=${customer_id}&source=admin`, data).toPromise();
  }

  deleteCustomerAddress(ad_id) {
    return this.http.delete(`customers/address/${ad_id}?source=admin`).toPromise();
  }

  getuserBasicInfo(id) {
    return this.http.get(`customers/${id}`).pipe(map(res => res.result));
  }

  getCustomerAddress(id) {
    return this.http.get(`customers/address?customer_id=${id}&source=admin`).toPromise();
  }

  getCardTotal(id) {
    return this.http.get(`users/${id}/dashboard`).pipe(map(res => res.result));
  }

  resendPassword(data,type?) {
    return this.http.post(`customers/forgot-password?type=${type ? type : 'change'}`, data).toPromise();
  }

  changePassword(user_id, data) {
    return this.http.post(`customers/auth/change-password/${user_id}`, data).toPromise();
  }

  changePin(data) {
    return this.http.post(`users/update-pin`, data).toPromise();
  }

  authorizePin(data) {
    return this.http.post(`users/authorize-pin`, data).toPromise();
  }

  formateUser(user_info) {
    for (let a of user_info.addresses) {
      if (a.is_primary == 1) {
        user_info["phone"] = a.phone;
        user_info["mobile"] = a.mobile;
        break;
      }
    }

    return user_info;
  }

  updateClientNotes(id,data) {
    return this.http.post(`comments/${id}`,data).toPromise();
  }

  
  addClientNotes(data) {
    return this.http.post(`comments`,data).toPromise();
  }

  deleteClientNotes(id) {
    return this.http.delete(`comments/${id}`).toPromise();
  }

  getClientNotes(id) {
    return this.http.get(`comments?type=customer&id=${id}`).toPromise();
  }
}
