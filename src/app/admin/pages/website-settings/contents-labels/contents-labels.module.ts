import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ContentsLabelsComponent } from './contents-labels.component';
import { Routes, RouterModule } from '@angular/router';
import { SettingResolveService } from '../../settings/setting-service/setting-resolve.service';

const routes: Routes = [
  {
    path: '',
    component: ContentsLabelsComponent,
    children: [
      {
          path: 'custom-css',
          loadChildren: () => import('../../settings/all-contents/custom-css/custom-css.module').then(m => m.CustomCssModule),
          resolve: { 'list': SettingResolveService }
      },
      {
          path: 'banner',
          loadChildren: () => import('../../settings/all-contents/banner/banner.module').then(m => m.BannerModule),
          resolve: {'content': SettingResolveService}
      },
      {
          path: 'grid',
          loadChildren: () => import('../../settings/all-contents/grid/grid.module').then(m => m.GridModule)
      },
      {
          path: 'site-specific',
          loadChildren: () => import('../../settings/all-contents/site-specific/site-specific.module').then(m => m.SiteSpecificModule),
      },
      {
          path: 'custom-content',
          loadChildren: () => import('../../settings/all-contents/custom-contents/contents.module').then(m => m.ContentsModule)
      },
      {
          path: 'layout-contents',
          loadChildren: () => import('../../settings/all-contents/contents/contents.module').then(m => m.ContentsModule)
      },
      {
          path: '**',
          redirectTo: 'banner',
          pathMatch: 'full',
      }
  ]
  }
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes)
  ],
  declarations: [ContentsLabelsComponent]
})
export class ContentsLabelsModule { }
