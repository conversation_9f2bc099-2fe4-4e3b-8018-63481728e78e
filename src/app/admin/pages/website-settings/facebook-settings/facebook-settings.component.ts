import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { getdomain, getSubdomain, isJson } from './../../../../globals/_classes/functions';
import { FormGroup, FormBuilder } from '@angular/forms';
import { ContentService } from '../../settings/setting-service/contents.service';
import { AlertService } from './../../../../modules/alert/alert.service';
import { HttpService } from './../../../../modules/http-with-injector/http.service';

@Component({
  selector: 'app-facebook-settings',
  templateUrl: './facebook-settings.component.html',
  styleUrls: ['./facebook-settings.component.css']
})
export class FacebookSettingsComponent implements OnInit {
  url: string;
  timeForm: FormGroup;
  data = {};
  loading: boolean;

  @ViewChild("hasCusAlert") alertContainer: ElementRef;

  constructor(
    public fb: FormBuilder,
    private service: ContentService,
    private alert: AlertService,
    public http: HttpService,
  ) { }

  ngOnInit() {
    const domain = getdomain();
    const subDomain = getSubdomain();
    this.url = subDomain.includes("localhost")
      ? "http://" + domain
      : "https://" + subDomain + ".rentmy.co";
    this.timeForm = this.fb.group({
      fb_tracking_id: "",
      fb_page_id: [''],
    });
    this.getFormData();
  }

  getFormData() {
    this.service.getTimeZone().subscribe(
      res => {
        this.data = {
          fb_tracking_id: res.timezone.hasOwnProperty("fb_tracking_id")
            ? res.timezone.fb_tracking_id
            : "",
          fb_page_id: res.timezone.hasOwnProperty("fb_page_id")
            ? res.timezone.fb_page_id
            : ""
        };
        this.timeForm
          .get("fb_tracking_id")
          .setValue(this.data['fb_tracking_id']);
        this.timeForm
          .get("fb_page_id")
          .setValue(this.data['fb_page_id']);
      },
      err => console.error(err)
    );
  }

  save() {
    this.loading = true;
    this.service
      .saveTimeZone(this.timeForm.value)
      .then(res => {
        this.loading = false;
        if (res.result) {
          this.alert.error(this.alertContainer, res.result.error, true, 3000);
        } else {
          this.getFormData();
          this.setContent();
          this.alert.success(
            this.alertContainer,
            "Settings Updated",
            true,
            3000
          );
        }
      })
      .catch(err => {
        this.loading = false;
        this.alert.error(
          this.alertContainer,
          "Something went wrong!!! Please try again.",
          true,
          3000
        );
      });
  }

  setContent(){
    this.http.get('contents').toPromise()
        .then(res => {
            if (res.status === 'OK' && res.result.data.length > 0) {
                const content = {};
                const data = res.result.data.filter(f => {
                    return f['config'].status === 1;
                });

                for (const c of data) {
                    const tag = this.formatTag(c.config.tag);
                    content[tag] = isJson(c.contents) ? JSON.parse(c.contents) : c.contents;
                }
                localStorage.setItem('contents', JSON.stringify(content));
                return content;
            } else {
                return {};
            }
        }).catch(err => console.log(err));
  }

  formatTag(text: String) {
    return text.replace(/(\-[a-z])/g, function ($1) { return $1.toUpperCase().replace('-', ''); });
  }

}
