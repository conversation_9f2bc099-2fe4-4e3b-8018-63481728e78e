import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FacebookSettingsComponent } from './facebook-settings.component';
import { RouterModule, Routes } from '@angular/router';
import { ReactiveFormsModule } from '@angular/forms';

const routes: Routes = [
  {
    path: '',
    component: FacebookSettingsComponent
  }
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    ReactiveFormsModule
  ],
  declarations: [FacebookSettingsComponent]
})
export class FacebookSettingsModule { }
