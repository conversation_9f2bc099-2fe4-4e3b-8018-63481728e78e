<div class="m-subheader">
    <div class="d-flex align-items-center">
        <div class="mr-auto">
            <h3 class="m-subheader__title m-subheader__title--separator">
                Facebook
            </h3>
            <ul class="m-subheader__breadcrumbs m-nav m-nav--inline">
                <li class="m-nav__item m-nav__item--home">
                    <a routerLink="/admin" class="m-nav__link m-nav__link--icon">
                        <i class="m-nav__link-icon la la-home"></i>
                    </a>
                </li>
                <li class="m-nav__separator">
                    <i class="fa fa-angle-right"></i>
                </li>
                <li class="m-nav__item">
                    <a class="m-nav__link">
                        <span class="m-nav__link-text">
                Website Settings
              </span>
                    </a>
                </li>
                <li class="m-nav__separator" style="padding-left: 10px">
                    <i class="fa fa-angle-right"></i>
                </li>
                <li class="m-nav__item">
                    <a class="m-nav__link">
                        <span class="m-nav__link-text">
                Facebook
              </span>
                    </a>
                </li>
            </ul>
        </div>
    </div>
</div>
<!-- END: Subheader -->
<div class="m-content fadeIn">
    <div class="m-portlet m-portlet--tabs">
        <div class="m-portlet__head title-head">
            <div class="m-portlet__head-caption">
                <div class="m-portlet__head-title">
                    <h3 class="m-portlet__head-text">Facebook</h3>
                </div>
            </div>
        </div>
        <div class="m-portlet__body">
            <div class="tab-content">
                <div class="custom-alert" #hasCusAlert></div>
                <form [formGroup]="timeForm" (ngSubmit)="save()">
                    <div class="row">
                        <div class="col-md-3">
                            <h5>
                                Facebook Pixels <br />
                                <small style="font-size:12px;">Facebook Pixel helps you create ad campaigns to find new customers
                                    that look most like your buyers.
                                </small>
                            </h5>
                        </div>
                        <div class="col-md-9">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <div class="m-radio-inline">
                                            <label>Facebook Pixel ID </label>&nbsp;&nbsp;
                                            <input class="form-control" class="form-control col-md-6" formControlName="fb_tracking_id" type="text" placeholder="Paste your Facebook Pixel ID here" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-5">
                        <div class="col-md-3">
                            <h5>
                                Facebook Messenger <br />
                                <small style="font-size:12px;">Facebook Messenger chat helps you coomunicate with your customers through
                                    your store.
                                </small>
                            </h5>
                        </div>
                        <div class="col-md-9">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <div class="m-radio-inline">
                                            <label>Facebook Page ID </label>&nbsp;&nbsp;
                                            <input class="form-control" class="form-control col-md-9" formControlName="fb_page_id" type="text" placeholder="Paste your 15 digit Facebook page ID here, eg: 123456789012345" />
                                            <small>Note: To configure your Facebook page for chatting, go to Facebook page settings >
                                                Messaging and then
                                                "Add Messenger to your website" and add this domain "{{url}}" to "ADD WEBSITE DOMAIN
                                                NAME" and click Finish button.
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="m-portlet__foot m-portlet__foot--fit">
                        <div class="m-form__actions m--margin-top-30">
                            <button type="submit" [disabled]="loading" class="btn btn-dark m-btn--icon" [ngClass]="{ 'm-loader m-loader--light m-loader--right': loading }">
                                Submit
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>