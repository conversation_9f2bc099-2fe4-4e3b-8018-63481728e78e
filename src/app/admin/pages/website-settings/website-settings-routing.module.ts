import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";
import { AdminService } from "../../admin.service";
import { PagesComponent } from "../pages.component";
import { WebsiteSettingsComponent } from "./website-settings.component";
import { SettingResolveService } from "../settings/setting-service/setting-resolve.service";

const routes: Routes = [
  {
    path: "",
    component: PagesComponent,
    children: [
      {
        path: "",
        component: WebsiteSettingsComponent,
        canActivate: [AdminService],
        children: [
          {
            path: "navigations",
            loadChildren:
              () => import('./../settings/menu-settings/menu-settings.module').then(m => m.MenuSettingsModule),
              data : { title:'Navigation List'}
          },
          {
            path: "contents-labels",
            loadChildren: () => import('./contents-labels/contents-labels.module').then(m => m.ContentsLabelsModule)
          },
          {
            path: "facebook",
            loadChildren: () => import('./facebook-settings/facebook-settings.module').then(m => m.FacebookSettingsModule)
          },
          {
            path: 'layout',
            loadChildren: () => import('./../settings/all-contents/layout/layout.module').then(m => m.LayoutConentModule),
            resolve: { 'layout': SettingResolveService }
          },
          {
            path: "pages",
            loadChildren: () => import('./../settings/all-contents/pages/pages.module').then(m => m.PagesModule)
          },
          {
            path: "**",
            redirectTo: "layout",
            pathMatch: "full"
          }
        ]
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class WebsiteSettingsRoutingModule {}
