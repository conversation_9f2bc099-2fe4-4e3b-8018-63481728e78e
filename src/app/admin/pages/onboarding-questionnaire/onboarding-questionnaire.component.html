
<div class="custom-alert" #hasCusAlert></div>

<div class="onboarding-modal-body">
  <div class="onboarding-modal-header">
    <h3 class="mb-0">Initial Setup</h3>
    <button aria-label="Close" (click)="onClickClose()" class="close" type="button">
      <i class="la la-close"></i>
    </button>
  </div>

  <form [formGroup]="questionnareForm" (ngSubmit)="save()">
    <!-- step-1 -->
    <div *ngIf="currentStep == 1" class="row">
      <div class="col-md-12">
        <div class="row">
          <div class="col-lg-6 col-md-12">
            <div class="form-group">
              <label class="main-label">My Business is</label>
              <br />
              <label class="m-radio mr-5">
                <input type="radio" class="form-control" value="A twinkle in my eye" formControlName="my_business"
                  (change)="onChangeBusiness($event.target.value)" />
                A twinkle in my eye
                <span></span>
              </label>
              <br />
              <label class="m-radio mr-5">
                <input type="radio" class="form-control" value="A startup" formControlName="my_business"
                  (change)="onChangeBusiness($event.target.value)" />
                A startup
                <span></span>
              </label>
              <br />
              <label class="m-radio mr-5">
                <input type="radio" class="form-control" value="Established" formControlName="my_business"
                  (change)="onChangeBusiness($event.target.value)" />
                Established
                <span></span>
              </label>
            </div>
          </div>
        </div>
      </div>

      <div *ngIf="selected_business_name == 'Established'" class="col-md-12">
        <div class="row">
          <div class="col-lg-6 col-md-12">
            <div class="form-group">
              <label class="main-label">How do you manage your inventory now?</label>
              <br />
              <label class="m-radio mr-5">
                <input type="radio" class="form-control" value="Paper" formControlName="my_business_info"
                  (change)="onChangeBusinessInfo($event.target.value)" />
                Paper
                <span></span>
              </label>
              <br />
              <label class="m-radio mr-5">
                <input type="radio" class="form-control" value="Spreadsheets" formControlName="my_business_info"
                  (change)="onChangeBusinessInfo($event.target.value)" />
                Spreadsheets
                <span></span>
              </label>
              <br />
              <label class="m-radio mr-5">
                <input type="radio" class="form-control" value="Software/app" formControlName="my_business_info"
                  (change)="onChangeBusinessInfo($event.target.value)" />
                Software/app
                <span></span>
              </label>
            </div>

            <div *ngIf="selected_business_info == 'Software/app'" class="form-group">
              <label class="main-label">Software/app name</label>

              <input type="text" class="form-control" formControlName="software_app_name" />
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-12">
        <div class="row">
          <div class="col-lg-6 col-md-12">
            <div class="form-group">
              <label class="main-label">What is your role?</label>
              <br />
              <label class="m-radio mr-5">
                <input type="radio" class="form-control" value="Business owner/manager" formControlName="business_role"
                  (change)="onChangeBusinessRole($event.target.value)" />
                Business owner/manager
                <span></span>
              </label>
              <br />
              <label class="m-radio mr-5">
                <input type="radio" class="form-control" value="Web/App Developer" formControlName="business_role"
                  (change)="onChangeBusinessRole($event.target.value)" />
                Web/App Developer
                <span></span>
              </label>
              <br />
              <label class="m-radio mr-5">
                <input type="radio" class="form-control" value="Marketing Agency" formControlName="business_role"
                  (change)="onChangeBusinessRole($event.target.value)" />
                Marketing Agency
                <span></span>
              </label>
              <br />
              <label class="m-radio mr-5">
                <input type="radio" class="form-control" value="Other" formControlName="business_role"
                  (change)="onChangeBusinessRole($event.target.value)" />
                Other
                <span></span>
              </label>
            </div>
            <div *ngIf="selected_business_role == 'Other'" class="form-group">
              <input type="text" class="form-control" formControlName="business_role_other" />
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-12">
        <div class="row">
          <div class="col-lg-6 col-md-12">
            <div class="form-group">
              <label class="main-label">I plan to use RentMy for</label>
              <br />
              <label class="m-radio mr-5">
                <input type="radio" class="form-control" value="Backoffice management"
                  formControlName="use_rentmy_for" />
                Backoffice management
                <span></span>
              </label>
              <br />
              <label class="m-radio mr-5">
                <input type="radio" class="form-control" value="Point of Sale" formControlName="use_rentmy_for" />
                Point of Sale
                <span></span>
              </label>
              <br />
              <label class="m-radio mr-5">
                <input type="radio" class="form-control" value="Through a website" formControlName="use_rentmy_for" />
                Through a website
                <span></span>
              </label>
              <br />
              <label class="m-radio mr-5">
                <input type="radio" class="form-control" value="All of ‘em!" formControlName="use_rentmy_for" />
                All of ‘em!
                <span></span>
              </label>
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-12">
        <div class="row">
          <div class="col-lg-6 col-md-12">
            <div class="form-group">
              <label class="main-label">What best describes your category?</label>
              <select class="form-control m-input dropdown-cls" 
              formControlName="store_category"
              (change)="onChangeCategory($event.target.value)">
                <option value="Audio/Visual">Audio/Visual (microphones, cameras, headphones, etc)</option>
                <option value="Autos & Accessories">Autos & Accessories</option>
                <option value="Bounce Houses/Inflatables">Bounce Houses/Inflatables</option>
                <option value="Camping & Outdoor">Camping & Outdoor</option>
                <option value="Cleaning & Maintenance">Cleaning & Maintenance</option>
                <option value="Educational">Educational</option>
                <option value="Fashion">Fashion (including accessories & costumes)</option>
                <option value="Medical/Heath">Medical/Heath</option>
                <option value="Meeting/Event Spaces">Meeting/Event Spaces</option>
                <option value="Moving (including trailers & crates)">Moving (including trailers & crates)</option>
                <option value="Party & Event">Party & Event</option>
                <option value="Real Estate/Property">Real Estate/Property</option>
                <option value="Sports">Sports</option>
                <option value="Tools & Equipment">Tools & Equipment</option>
                <option value="Toys & Games">Toys & Games</option>
                <option value="Vehicles">Vehicles (including bicycles, golf carts, etc)</option>
                <option value="Watersports">Watersports (jet skis, kayaks, etc.)</option>
                <option value="Something else">Something else</option>
              </select>
            </div>
            <div *ngIf="is_show_category_others" class="form-group">
              <label class="main-label">Others</label>
              <input type="text" class="form-control" formControlName="category_others" />
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-12">
        <div class="row">
          <div class="col-lg-6 col-md-12">
            <div class="form-group">
              <label class="main-label">Which country do you serve?</label>
              <select class="form-control m-input dropdown-cls" formControlName="country">
                <option [value]="item.name" *ngFor="let item of countries">{{
              item.name
            }}</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-12">
        <div class="row">
          <div class="col-lg-6 col-md-12">
            <div class="form-group">
              <label class="main-label">Where did you hear about us?</label>
              <select class="form-control m-input dropdown-cls"
               formControlName="hear_about_us"
               (change)="onChangeHearAboutUs($event.target.value)">
                <option value="Social media">Social media</option>
                <option value="Word of mouth">Word of mouth</option>
                <option value="Salesperson">Salesperson</option>
                <option value="Search engine">Search engine</option>
                <option value="Other">Other</option>
              </select>
               <br />
              <div *ngIf="is_show_hear_about_us_other" class="form-group">
                <input type="text" class="form-control" placeholder="Enter text" formControlName="hear_about_us_other" />
              </div>

            </div>
          </div>

          
        </div>
      </div>
      
    </div>

    <!-- step-2 -->
    <div *ngIf="currentStep == 2" class="row">
      <div class="col-lg-6 col-md-12">
        <div class="form-group">
          <label class="main-label">Add your first product?</label>
          <br />
          <label class="m-radio mr-5">
            <input type="radio" class="form-control" value="yes" formControlName="add_first_product"
              (change)="onChangeFirstProductOption($event)" />
            Yes
            <span></span>
          </label>
          <br />
          <label class="m-radio mr-5">
            <input type="radio" class="form-control" value="Not now" formControlName="add_first_product"
              (change)="onChangeFirstProductOption($event)" />
            Not now
            <span></span>
          </label>

          <div *ngIf="is_show_first_product_section" id="addProduct" formArrayName="first_product">
            <div *ngFor="
                let address of questionnareForm.controls.first_product.controls;
                let i = index
              ">
              <div [formGroupName]="i">
                <div class="row">
                  <div class="col-md-12">
                      <div class="form-group">
                          <label for="exampleInputEmail1">
                            Product Image
                          </label>
                          <div class="custom-file">
                            <input class="custom-file-input" type="file" id="image" name="image"
                            (change)="onChangeProductImage($event)" accept="image/*" >
                            <label class="custom-file-label" for="image">
                              Choose file
                            </label>
                            <input type="hidden" name="imgHidden" formControlName="image" />
                          </div>
                        </div>
                  </div>
                  <div class="col-md-12">
                    <div *ngIf="product_image !=''" class="img-show-area">
                      <a class="img-close" (click)="onImageClose()"><i class="fa fa-times"></i></a>
                      <img [src]="product_image" alt="" width="80" style="object-fit: contain;" />
                    </div>
                  </div>
                </div>

                <div class="form-group">
                  <label>Product Name</label>
                  <input type="text" class="form-control" formControlName="name" />
                </div>
                <div class="row">
                  <div class="col-md-12">
                    <div class="form-group">
                      <label>Product Quantity</label>
                      <input type="text" class="form-control" formControlName="qty" />
                    </div>
                  </div>
                  <div class="col-md-12">
                    <div class="form-group">
                      <label>Product Rental Price</label>
                      <input type="number" class="form-control" formControlName="price" />
                    </div>
                  </div>
                  <div class="col-md-12">
                    <div class="form-group">
                      <label>Product Price Term</label>
                      <select class="form-control m-input dropdown-cls" formControlName="price_term">
                        <option value="hourly">Hourly</option>
                        <option value="daily">Daily</option>
                        <option value="weekly">Weekly</option>
                        <option value="monthly">Monthly</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- step-3 -->
    <div *ngIf="currentStep == 3" class="row">
      <div class="col-lg-6 col-md-12">
          <div class="form-group">
              <label for="exampleInputEmail1">
                  Add your logo
              </label>
              <div class="custom-file">
                <input class="custom-file-input" type="file" id="logo" name="logo" (change)="onChangeLogo($event)" accept="image/*" >
                <label class="custom-file-label" for="logo">
                  Choose file
                </label>
                <input type="hidden" name="logoHidden" formControlName="logo" />
              </div>
            </div>

      </div>
      <div class="col-lg-12 col-md-12">
        <div *ngIf="logo !=''" class="img-show-area">
            <a class="img-close" (click)="onLogoClose()"><i class="fa fa-times"></i></a>
            <img [src]="logo" alt="" width="80" style="object-fit: contain;" />
          </div>
      </div>

      <div class="col-md-12">
        <div class="form-group">
          <label class="main-label">Would you like to use our import tool to add your inventory?</label>
          <br />
          <label class="m-radio mr-5">
            <input type="radio" class="form-control" value="Yes" formControlName="add_import_tool"
              (change)="onChangeAddImportTool($event.target.value)" />
            Yes
            <span></span>
          </label>
          <br />
          <label class="m-radio mr-5">
            <input type="radio" class="form-control" value="No" formControlName="add_import_tool"
              (change)="onChangeAddImportTool($event.target.value)" />
            No
            <span></span>
          </label>
        </div>
      </div>

      <div *ngIf="show_import_tool == 'Yes'" class="col-md-12">
        <label>
          Please download sample excel file for importing and fill the necessary
          data.
        </label>
        <div class="mb-20">
          <a [attr.href]="downloadSample" title="Download Sample"
            style="color:#fff;">Download Sample</a>
        </div>
      </div>

      <div *ngIf="show_import_tool == 'Yes'" class="col-md-12">
        <label class="w-100 mt-2 mb-3">Watch helpful</label>
        <iframe style="border: none;" src="https://www.youtube.com/embed/mi6Jr2-j-0U">
        </iframe>
      </div>
    </div>

    <div *ngIf="currentStep == 3" class="row">
      <div class="col-md-6 text-center mt-3">
        <button style="cursor: pointer" class="btn btn-info" type="submit">
          Submit
        </button>
        &nbsp;
        <button (click)="onClickClose()" style="cursor: pointer;" class="btn btn-danger" type="submit">
          Skip
        </button>
      </div>
    </div>
  </form>

  <div class="row m-0 back-next-area">
    <div class="col-md-12">
      <button *ngIf="currentStep != 1" (click)="onClickBack(currentStep)" style="cursor: pointer" class="btn btn-brand">
        <i class="fa fa-chevron-left mr-2"></i>Back
      </button>
      <button *ngIf="currentStep != 3" (click)="onClickNext(currentStep)" style="cursor: pointer;"
        class="btn btn-brand float-right">
        Next<i class="fa fa-chevron-right ml-2"></i>
      </button>
    </div>
  </div>
</div>