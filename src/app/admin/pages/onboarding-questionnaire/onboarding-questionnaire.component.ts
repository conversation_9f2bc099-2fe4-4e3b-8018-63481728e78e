import { AdminService } from './../../admin.service';
import { Component, OnInit, ViewChild, ElementRef } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { FormGroup, FormBuilder, Validators, FormArray } from "@angular/forms";
import { allCountry } from "../../../globals/_classes/country";
import { DashboarService } from "../dashboard/dashboard.service/dashboard.service";
import { SidebarService } from "../sidebar-service/sidebar.service";
import { Helpers } from "../../../helpers";
import { EndPoint } from "../../../globals/endPoint/config";
import { HttpEventType, HttpErrorResponse } from "@angular/common/http";
import { HttpService } from "../../../modules/http-with-injector/http.service";
import { AlertService } from "../../../modules/alert/alert.service";
import { changeUser, GET_USER } from '../../../globals/_classes/functions';

declare var $: any;

@Component({
  selector: "app-onboarding-questionnaire",
  templateUrl: "./onboarding-questionnaire.component.html",
  styleUrls: ["./onboarding-questionnaire.component.css"]
})
export class OnboardingQuestionnaireComponent implements OnInit {
  questionnareForm: FormGroup;
  countries = allCountry.sort((a, b) => a.name.localeCompare(b.name));
  is_show_first_product_section;
  selected_business_name;
  selected_business_info;
  selected_business_role;
  logo;
  product_image;
  show_import_tool;
  downloadSample;
  currentStep = 1;
  newLogo;
  safeURL;
  is_show_hear_about_us_other=false;
  is_show_category_others=false;

  @ViewChild("hasCusAlert", {static: false}) alertContainer: ElementRef;

  constructor(
    public activeModal: NgbActiveModal,
    private fb: FormBuilder,
    private dashS: DashboarService,
    private sidebarS: SidebarService,
    private http: HttpService,
    private alertS: AlertService,
    private adminS: AdminService
  ) {
    this.downloadSample = EndPoint + "products/download/sample";
  
    this.questionnareForm = this.fb.group({
      my_business: [""],
      my_business_info: [""],
      software_app_name: [""],
      business_role: [""],
      business_role_other: [""],
      use_rentmy_for: [""],
      store_category: ["Audio/Visual"],
      category_others: [""],
      country: ["United States of America"],
      hear_about_us: ["Social media"],
      hear_about_us_other: [""],
      logo: [""],
      add_first_product: ["yes"],
      first_product: this.fb.array([this.initProduct()]),
      add_import_tool: ["Yes"]
    });
  }

  ngOnInit() {
    this.is_show_first_product_section = true;
    this.show_import_tool = 'Yes';
    this.logo='';
    this.product_image='';
    this.loadQuestionnare();
  }

  initProduct() {
    return this.fb.group({
      image: [""],
      name: [""],
      qty: [""],
      price: [""],
      price_term: [""]
    });
  }


  onChangeCategory(selectedCategory){
    this.is_show_category_others=  selectedCategory=='Something else'? true:false;
   
  }

  onChangeFirstProductOption(first_product_add_option) {
    let value = first_product_add_option.target.value;
    if (value === "yes") {
      this.is_show_first_product_section = true;
    } else {
      this.is_show_first_product_section = false;
    }
  }

  onChangeHearAboutUs(value){
    if (value === "Other") {
      this.is_show_hear_about_us_other = true;
    } 
    else{
      this.is_show_hear_about_us_other=false;
    }
  }

  loadQuestionnare() {
    let questionnareInfo = this.questionnareForm.value;
    //todo::call api for Questionnaries
    questionnareInfo = JSON.parse(sessionStorage.getItem("questionaries"));

    let data = [];
    if (questionnareInfo != null) {
      data = questionnareInfo.map(x => {
        return x.answer;
      });
    }

    const patchData = {
      my_business: data[0],
      my_business_info: data[1],
      software_app_name: data[2],
      business_role: data[3],
      business_role_other: data[4],
      use_rentmy_for: data[5],
      store_category: data[6],
      category_others: data[7],
      country: data[8],
      hear_about_us: data[9],
      hear_about_us_other: data[10],
      add_first_product: data[11].answer,
      first_product: data[11].product,
      logo: data[12],
      add_import_tool: data[13]
    };

    patchData.store_category === "Something else"
    ? (this.is_show_category_others = true)
    : (this.is_show_category_others = false);

    patchData.add_first_product === "yes"
      ? (this.is_show_first_product_section = true)
      : (this.is_show_first_product_section = false);

      patchData.hear_about_us === "Other"
      ? (this.is_show_hear_about_us_other = true)
      : (this.is_show_hear_about_us_other = false);

    this.selected_business_name = patchData.my_business;
    this.selected_business_info = patchData.my_business_info;
    this.selected_business_role = patchData.business_role;
    this.show_import_tool = patchData.add_import_tool;

    this.logo=patchData.logo;
    if(patchData.first_product !=undefined)
    {
      this.product_image=patchData.first_product[0].image;
    }
   

    this.questionnareForm.patchValue(patchData);
  }

  onClickNext(step: number) {
    this.currentStep = step + 1;
  }

  onClickBack(step: number) {
    this.currentStep = step - 1;
  }

  onChangeBusiness(business_name) {
    this.selected_business_name = business_name;
  }

  onChangeBusinessInfo(business_info) {
    this.selected_business_info = business_info;
  }

  onChangeBusinessRole(business_role) {
    this.selected_business_role = business_role;
  }

  onChangeLogo(e) {
    let file = e.target.files[0];
    console.log(e.target.files[0]);
    let reader = new FileReader();
    reader.onload = e => {
      this.logo = reader.result;
    };
    reader.readAsDataURL(e.target.files[0]);

    this.onUpload(file, "Logo");
  }

  onChangeProductImage(e) {
    let file = e.target.files[0];
    console.log(e.target.files[0]);
    let reader = new FileReader();
    reader.onload = e => {
      this.product_image = reader.result;
    };
    reader.readAsDataURL(e.target.files[0]);

    this.onUpload(file, "Image");
  }

  onUpload(inputFile: File, type) {
    Helpers.setLoading(true);
    const formData = new FormData();
    formData.append("file", inputFile);
    formData.append("type", type);

    this.http
      .post("media/upload", formData)
      .subscribe(res => {
        if (res.status=="OK") {
          Helpers.setLoading(false);
          console.log(res.result.data);

          if(type=="Image")
          {
            const control = <FormArray>(
              this.questionnareForm.controls["first_product"]
            );
            control.controls[0]["controls"]["image"].setValue(
              res.result.data.filename
            );
          }
          else{
            this.questionnareForm.controls["logo"].setValue( res.result.data.filename);
          }
        
        }
        else{
          this.alertS.error(this.alertContainer, res.result.message, true, 3000);
        }
      });
  }

  onImageClose(){
    this.product_image="";
    const control = <FormArray>(
      this.questionnareForm.controls["first_product"]
    );
    control.controls[0]["controls"]["image"].setValue(
      this.product_image
    );
  }

  onLogoClose(){
    this.logo="";
    this.questionnareForm.controls["logo"].setValue(this.logo);
  }

  onClickClose() {
    this.activeModal.close("Close click");
    let skip = true;
    this.saveQuestionnaire(skip);

    sessionStorage.setItem("is_skip", JSON.stringify(skip));
    this.sidebarS.questionnaireSkip(skip);
  }

  onChangeAddImportTool(value) {
    this.show_import_tool = value;
  }

  save() {
    Helpers.setLoading(true);
    let skip = false;
    this.saveQuestionnaire(skip);

    sessionStorage.setItem("is_skip", JSON.stringify(skip));
    this.sidebarS.questionnaireSkip(skip);

    setTimeout(() => {
      this.activeModal.close("");
    }, 2000);
  }

  saveQuestionnaire(is_skip) {

    const sendData = {
      questionaries: [
        {
          question: "My Business is",
          answer: this.questionnareForm.get("my_business").value
        },
        {
          question: "How do you manage your inventory now?",
          answer: this.questionnareForm.get("my_business_info").value
        },
        {
          question: "Software/app name",
          answer: this.questionnareForm.get("software_app_name").value
        },
        {
          question: "What is your role?",
          answer: this.questionnareForm.get("business_role").value
        },
        {
          question: "Other role",
          answer: this.questionnareForm.get("business_role_other").value
        },
        {
          question: "I plan to use RentMy for",
          answer: this.questionnareForm.get("use_rentmy_for").value
        },
        {
          question: "What best describes your category?",
          answer: this.questionnareForm.get("store_category").value
        },
        {
          question: "Others category",
          answer: this.questionnareForm.get("category_others").value
        },
        {
          question: "Which country do you serve?",
          answer: this.questionnareForm.get("country").value
        },
        {
          question: "Where did you hear about us?",
          answer: this.questionnareForm.get("hear_about_us").value
        },
        {
          question: "about us other",
          answer: this.questionnareForm.get("hear_about_us_other").value
        },
        {
          question: "Add your first product",
          answer: {
            answer: this.questionnareForm.get("add_first_product").value,
            product: this.questionnareForm.get("first_product").value
          }
        },
        {
          question: "Add your logo",
          answer: this.questionnareForm.get("logo").value
        },
        {
          question:
            "Would you like to use our import tool to add your inventory?",
          answer: this.questionnareForm.get("add_import_tool").value
        }
      ],
      questionaries_skip: is_skip
    };


    this.adminS.sendQ(sendData).subscribe(
      res => {
        this.newLogo = res.result.data.logo;
        changeUser('logo', this.newLogo);
        this.adminS.changeUser(GET_USER());
        Helpers.setLoading(false);
        sessionStorage.setItem(
          "questionaries",
          JSON.stringify(sendData.questionaries)
        );
        console.log(res);
      }, (err: HttpErrorResponse) => {
        console.log(err);
      }
    )
  }
}
