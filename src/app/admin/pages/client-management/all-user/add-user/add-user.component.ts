import { ClientService } from './../../client-service/client.service';
import { Component, OnInit, Output, EventEmitter, Input } from '@angular/core';
import { UserSignUp } from '../../models/user.models';
import { AlertService } from '../../../../../modules/alert/alert.service';
import { chcekPassowrd } from '../../../../../globals/_classes/functions';


@Component({
  selector: 'app-add-user',
  templateUrl: './add-user.component.html',
  styleUrls: ['./add-user.component.css']
})
export class AddUserComponent implements OnInit {

  
  password:string = '';
  loader:boolean;

  @Input('signUp') signUp: UserSignUp;
  @Output('submit') submit: EventEmitter<any> = new EventEmitter();

  constructor(
    private alert: AlertService,
    private userS: ClientService
  ) {
      
   }

  ngOnInit() {
  }


  // checkPassword(pass,conf){
  //   return chcekPassowrd(pass,conf);
  // }

  createAccount(f){
    this.loader = true;
    this.userS.register(this.signUp).then(
      data => {
        this.loader = false;
        if (data.status === 'NOK') {
          this.submit.emit({off: true, success: false, msg: data.result.message});
        } else if (data.status === 'OK') {
          f.form.reset();
          this.submit.emit({off: true, success: true, msg: 'New user has been created successfully', userId: data.result.data.id});
        } else {
          this.submit.emit({off: true, success: false, msg: 'Something went wrong!!'});
        }
      },
      error => {
        if(error.error.result.error.email) {
          this.submit.emit({off: true, success: false, msg: error.error.result.error.email});
        } else {
          this.submit.emit({off: true, success: false, msg: error.error.result.error});
        }
        this.loader = false; 
    });
  }

}
