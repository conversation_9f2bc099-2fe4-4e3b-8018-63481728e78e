import { Component, OnInit } from '@angular/core';
import { PLATFORM_ID,Inject } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';

@Component({
  selector: 'app-account-settings',
  templateUrl: './account-settings.component.html',
  styleUrls: ['./account-settings.component.css']
})
export class AccountSettingsComponent implements OnInit {

  constructor(@Inject(PLATFORM_ID) private platform: Object) { }

  ngOnInit() {
    // if (isPlatformBrowser(this.platform)) {
    //  window.scrollTo(0,0);
    // }
  }

}
