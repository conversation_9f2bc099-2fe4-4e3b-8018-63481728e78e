import { ClientService } from './../../client-service/client.service';
import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { forkJoin } from 'rxjs';
import { AlertService } from '../../../../../modules/alert/alert.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { DialogBoxComponent } from '../../../../../modules/dialog-box/dialog-box.component';
import { ImagePopupComponent } from '../../../../../modules/image-popup/image-popup.component';
import { Helpers } from '../../../../../helpers';
import { AddClientAddressComponent } from './add-address/add-address.component';
import { PLATFORM_ID,Inject } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { ContentResoveService } from 'src/app/home/<USER>/contetn-resolve.service';

class note {
  id: number;
  text: string = ''
  file: File
}

@Component({
  selector: 'app-overview',
  templateUrl: './overview.component.html',
  styleUrls: ['./overview.component.css']
})
export class OverviewComponent implements OnInit {
  contents: any = {};
  userId: number;
  user_info;
  cards;
  address = [];
  loader: boolean = false;
  btn_text: string = 'Save';
  note: note;
  noteList = [];
  is_show_noteForm = false;
  fileName:string='';

  @ViewChild("hasCusAlert") alertContainer: ElementRef;

  constructor(
    @Inject(PLATFORM_ID) private platform: Object,
    private router: Router,
    private route: ActivatedRoute,
    private userS: ClientService,
    private alertS: AlertService,
    private modalService: NgbModal,
    private contentService: ContentResoveService
  ) {
    this.userId = this.route.parent.parent.snapshot.params['user_id'];
  }

  ngOnInit() {
    this.contents = this.contentService.contents;
    this.note = new note();
    // if (isPlatformBrowser(this.platform)) {
    // window.scrollTo(0, 0);
    // }

    forkJoin(this.userS.getClientAddress(this.userId), this.userS.getClientNotes(this.userId)).subscribe(resultList => {
      if (resultList.length > 0) {
        console.log(resultList[0]);
        (resultList[0].status === 'OK' && resultList[0]) ? this.address = resultList[0].result.data : this.address = [];
        (resultList[1].status === 'OK' && resultList[1]) ? this.noteList = resultList[1].result.data : this.noteList = [];
      }

    }, err => {
      this.address = []
      this.noteList = [];
    })

    this.user_info = this.route.snapshot.data['user_info'].data;
    // this.user_info = this.userS.formateUser(this.user_info);
    this.getCards();
  }

  goToPersonalInfo() {
    this.router.navigate(["admin/client/" + this.userId + "/account-settings"]);
  }

  getCards() {
    this.userS.getCardTotal(this.userId).subscribe(
      res => {
        this.cards = res.data;
      },
      err => console.log(err)
    );
  }

  findUserType(data) {
    return this.userS.findUserType(data);
  }

  checkStatus(data) {
    return this.userS.checkStatus(data);
  }

  getText(t) {
    if (t) {
      return t.replace(/<(?:.|\n)*?>/gm, '');
    }
    return '';
  }

  onClickCancelNote() {
    this.is_show_noteForm = false;
  }

  AddNote() {
    this.note=new note();
    this.note.id=null;
    this.btn_text="Save";
    this.is_show_noteForm = true;
    this.fileName='';
  }


  onChangeNoteFile(e) {
    let file = e.target.files[0];
    this.note.file = file;
    this.fileName=file.name;
  }

  showBigImage(img) {
    const modalImage = this.modalService.open(ImagePopupComponent, {
      centered: true
    });
    modalImage.componentInstance.image = img;
  }


  saveNote() {
    // let sendData = {
    //   content_id: parseInt(this.userId.toString()),
    //   content_type: 'client',
    //   text: this.note.text
    // }

    const sendData = new FormData();
    sendData.append("content_id", this.userId.toString());
    sendData.append("content_type", 'client');
    sendData.append("text", this.note.text);
    sendData.append("file", this.note.file);

    if (this.note.id) {
      this.userS.updateClientNotes(this.note.id, sendData).then(res => {
        if (res.status == "OK") {
          console.log(res);
          const note=res.result.data;

          this.noteList.map(item => {
            if (item.id == note.id) {
              item.text = note.text
              item.file_type = note.file_type
              item.file_path = note.file_path
            }
            return item;
          })

          this.is_show_noteForm=false;
          this.fileName='';

          this.alertS.success(
            this.alertContainer,
            res.result.message,
            true,
            5000
          );
        }
        else {

          this.alertS.error(
            this.alertContainer,
            res.result.message,
            true,
            5000
          );
        }
      }).catch(err => {

      })
    }
    else {
      this.userS.addClientNotes(sendData).then(res => {
        if (res.status == "OK") {

          this.noteList.push(res.result.data);
          this.is_show_noteForm=false;
          this.fileName='';

          this.alertS.success(
            this.alertContainer,
            res.result.message,
            true,
            5000
          );
        }
        else {
          this.alertS.error(
            this.alertContainer,
            res.result.message,
            true,
            5000
          );
        }
      }).catch(err => {

      })
    }


  }

  editNote(note) {
    this.note.id = note.id;
    this.note.text = note.text;
    this.is_show_noteForm = true;
    this.btn_text = "Update";
  }


  onDeleteNote(id, index) {
    const modalRef = this.modalService.open(DialogBoxComponent, {
      centered: true,
      size: "sm",
      windowClass: "animated fadeIn"
    });
    modalRef.componentInstance.massage = "Are you sure you want to delete?";
    modalRef.result.then(
      result => {
        this.deleteNote(id, index);
      },
      reason => {
        console.log(reason);
      }
    );
  }

  deleteNote(id, index) {
    this.userS.deleteClientNotes(id).then(res => {
      if (res.status == "OK") {
        this.noteList.splice(index, 1);
        this.alertS.success(
          this.alertContainer,
          res.result.message,
          true,
          5000
        );
      }
      else {
        this.alertS.error(
          this.alertContainer,
          res.result.message,
          true,
          5000
        );
      }
    }).catch(err => {

    })

  }

  getAddress(){
    this.userS.getClientAddress(this.userId).then(res=>{
      (res.status === 'OK' && res.result) ? this.address = res.result.data : this.address = [];
    })
  }


  openAddress(type: string, data?: any) {
    const modalRef = this.modalService.open(AddClientAddressComponent, {
      centered: true,
      windowClass: "animated fadeIn",
      backdropClass: "modal-backdrop-cus",
      size: "lg",
      backdrop : 'static',
      keyboard : false
    });
    modalRef.componentInstance.addressType = type;
    modalRef.componentInstance.customer_id = this.userId;
    if (type === 'edit' && data) {
      modalRef.componentInstance.addressData = data;
    }
    modalRef.componentInstance.response.subscribe(res => {
      if (res.status === 'OK') {
        this.getAddress();
        this.alertS.success(
          this.alertContainer,
          res.result.message,
          true,
          3000
        );
      } else {
        this.alertS.error(
          this.alertContainer,
          "Something went wrong!!",
          true,
          5000
        );
      }
    })
  }

  delete(data) {
    const modalRef = this.modalService.open(DialogBoxComponent, {
      centered: true,
      windowClass: "animated fadeIn",
      backdropClass: "modal-backdrop-cus",
      size: "sm"
    });
    modalRef.componentInstance.massage = "Do you want delete this address?";
    modalRef.result.then(
      result => {
        if (result) {
          Helpers.setLoading(true);
          this.userS.deleteClientAddress(data.id).then(
            res => {
              Helpers.setLoading(false);
              if (res.status === 'OK') {
                this.getAddress();
                this.alertS.success(
                  this.alertContainer,
                  res.result.message,
                  true,
                  3000
                );
              } else if (res.status === 'NOK') {
                this.alertS.error(
                  this.alertContainer,
                  res.result.message,
                  true,
                  3000
                );
              }else {
                this.alertS.error(
                  this.alertContainer,
                  "Something went wrong!!",
                  true,
                  5000
                );
              }
            }
          ).catch(
            err => {
              Helpers.setLoading(false);
              this.alertS.error(
                this.alertContainer,
                err.error.result
                  ? err.error.result.message
                  : err.message,
                true,
                5000
              );
            }
          )
        }
      },
      res => {
        console.log(res);
      }
    );
  }


}
