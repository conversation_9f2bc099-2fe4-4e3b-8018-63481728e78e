
<div class="custom-alert alert-position" #hasCusAlert></div>
  
<div class="m-portlet__head title-head">
  <div class="m-portlet__head-caption">
    <div class="m-portlet__head-title w-100">
      <h3 class="m-portlet__head-text colorPurpel">
        Personal Info
      </h3>
      <div class="add-list-btn text-right">
        <button (click)="goToPersonalInfo()"
          class="btn btn-secondary m-btn m-btn--icon m-btn--icon-only m-btn--pill m-btn--air float-right">
          <i class="la la-edit"></i>
        </button>
      </div>
    </div>
  </div>
</div>
<div class="m-section card-section info-section animated fadeIn pb-4">
  <div class="m-section__content">
    <div class="row">
      <p class="col-md-6">
        <strong>Name: </strong> {{user_info?.first_name}} {{user_info?.last_name}}
      </p>
      <p class="col-md-6">
        <strong>Email: </strong> {{user_info?.email}}
      </p>
    </div>
    <div class="row">
      <p class="col-md-6">
        <strong>User Type: </strong> {{findUserType(user_info?.type)}}
      </p>
      <p class="col-md-6">
        <strong>Phone: </strong> {{user_info?.phone}}
      </p>
    </div>
    <div class="row">
      <p class="col-md-6">
        <strong>Mobile: </strong> {{user_info?.mobile}}
      </p>
      <p class="col-md-6">
        <strong>Status: </strong> {{checkStatus(user_info?.status)}}
      </p>
    </div>
    <div class="row">
      <p class="col-md-6">
        <strong>Company: </strong> {{user_info?.company}}
      </p>
    </div>
  </div>
</div>

<div class="address-area">
  <div class="address-title w-100">
    <h5>Address</h5>
    <div class="float-right text-center note-add">
      <button  type="button" (click)="openAddress('add')" class="btn btn-secondary m-btn m-btn--icon m-btn--icon-only m-btn--pill m-btn--air float-right"><i class="fa fa-plus"></i></button>
    </div>
  </div>
  <div class="address-list" *ngFor="let address of address">
    <span class="w-100"><b>{{address.type}}</b></span>
    <label>{{address?.full_address}}
    </label>

    <button (click)="delete(address)" class="btn btn-sm biling-address-edit float-right"><i class="fa fa-trash"></i></button>
    <button (click)="openAddress('edit', address)" class="btn btn-sm biling-address-edit float-right"><i class="fa fa-pencil"></i></button>
  </div>
</div>

<div class="note mt-4 mb-2">
  <div class="row address-title w-100 ml-0 mr-0">
      <h5>Note</h5>
      <div class="float-right text-center note-add">
        <button *ngIf="!is_show_noteForm" type="button" (click)="AddNote()" class="btn btn-secondary m-btn m-btn--icon m-btn--icon-only m-btn--pill m-btn--air float-right"><i class="fa fa-plus"></i></button>
      </div>
    </div>
  <form *ngIf="is_show_noteForm" class="m-form m-form--fit">
    <div class="row m-0 pl-3 pr-3">
      <div class="col-md-8">
        
        <div class="form-group">
         <label>Note</label>
          <textarea rows="2" cols="80" class="form-control m-input" id="note" name="note" [(ngModel)]="note.text">
          </textarea>
        </div>

        <div class="custom-file">
          <input
            class="custom-file-input"
            type="file"
            id="file"
            name="file"
            (change)="onChangeNoteFile($event)"
          />
          <label class="custom-file-label" for="file">
            Choose file
          </label>
        </div>
        <br /><br />
        <p *ngIf="fileName !=''">{{contents?.site_specific?.others?.lbl_file || 'File'}} : {{ fileName }}</p>


        <div class="form-group">
          <div *ngIf="loader; else button" class="m-loader m-loader--brand" style="width: 30px; display: inline-block;">
          </div>
          <ng-template #button>
            <button type="button" [disabled]="note.text==''" (click)="saveNote()"
              class="btn btn-brand btn-sm">{{btn_text}}</button>
              &nbsp;&nbsp;
              <button type="button" (click)="onClickCancelNote()"
              class="btn btn-danger btn-sm">Cancel</button>
          </ng-template>
        </div>
      </div>
    </div>
  </form>


  <div class="note-table table-responsive">
    <table class="table">
      <tbody *ngIf="noteList.length > 0; else noDate">
        <tr *ngFor="
            let note of noteList;
            let i = index;
          ">
          <td class="text-justify pr-4">
            {{ note.text }}

            <br>
            <small> {{ note.created }}</small>
          </td>
          <td>
            <a 
            *ngIf="note?.file_type =='application' && note?.file_path !=''" 
            [attr.href]="note?.file_path"
            target="_blank"
            title="Download file">Download</a>

            <img 
            *ngIf="note?.file_type =='image' && note?.file_path !=''" 
             class="img pointer" style="cursor: pointer" 
              [src]="note?.file_path" height="40px" (click)="showBigImage(note?.file_path)" />
          </td>
          <td class="text-right w-70">
            <a id="m_quick_sidebar_toggle" (click)="editNote(note)" title="Edit Note"
              class="m-portlet__nav-link btn m-btn m-btn--hover-info m-btn--icon m-btn--icon-only m-btn--pill">
              <i class="fa fa-edit"></i>
            </a>
            <a id="m_quick_sidebar_toggle" (click)="onDeleteNote(note.id,i)"
              class="m-portlet__nav-link btn m-btn m-btn--hover-danger m-btn--icon m-btn--icon-only m-btn--pill">
              <i class="fa fa-trash"></i>
            </a>
          </td>
        </tr>
      </tbody>
      <ng-template #noDate>
        <tbody>
          <tr *ngIf="!loader">
            <td colspan="5">
              <h5 class="text-center">No Note Found</h5>
            </td>
          </tr>
        </tbody>
      </ng-template>
    </table>
  </div>

</div>