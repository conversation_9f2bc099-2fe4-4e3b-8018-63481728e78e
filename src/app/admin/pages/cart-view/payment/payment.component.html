<div class="custom-alert" #hasCusAlert></div>

<div *ngIf="!orderDone; else orderCreated" class="content">
  <div style="margin: 15px 0px;" *ngIf="!inStore.instore">
      <app-billing-address [from]="true" (getNewAddress)="getNewAddress()"></app-billing-address>
  </div>
  <h4>
    Select Payment Type
  </h4>
  <div class="m-form__group form-group">
    <div class="m-radio-inline">
      <label class="m-radio" *ngIf="allGateways.length > 0">
        <input type="radio" name="main" [value]="1" [(ngModel)]="mode" (click)="changeMode(1)">
        Credit Card Entry
        <span></span>
      </label>
      <label class="m-radio" *ngIf="bolt">
        <input type="radio" name="main" [value]="2" [(ngModel)]="mode" (click)="changeMode(2)">
        Card Swipe
        <span></span>
      </label>
      <label class="m-radio">
        <input type="radio" name="main" [value]="3" [(ngModel)]="mode" (click)="changeMode(3)">
        Cash
        <span></span>
      </label>
    </div>
  </div>
  <div style="width: 100%; height: 100px;" *ngIf="loaderApi">
    <div *ngIf="loaderApi" class="table-load m-loader m-loader--brand"></div>
  </div>
  <div *ngIf="!loaderApi">
    <div *ngIf="is_online; else OFFline">
      <div class="m-portlet__body form-panel" style="margin: 0px;">
        <div *ngIf="mode==1">
          <ng-template #cardConect></ng-template>
        </div>

        <div *ngIf="mode == 2">
          <div *ngIf="loader; else bttnSWIPE" class="m-loader m-loader--brand" style="width: 30px;display: inline-block;"></div>
          <ng-template #bttnSWIPE>
            <button *ngIf="responseText" type="button" (click)="submitSwipe(form)" class="btn btn-brand">Procced To
              Order</button>
            <button *ngIf="!responseText" type="button" (click)="changeMode(2)" class="btn btn-dark">Try Again</button>
          </ng-template>
        </div>
      </div>
    </div>

    <ng-template #OFFline>
      <div class="m-portlet__body form-panel" style="margin: 0px;">
        <form #formOffline="ngForm" class="row">
          <div class="row" style="margin: 0px;">
            <div class="form-group col-sm-12">
              <h5>
                Cart Total: {{cartItems.total | currency}}
              </h5>
            </div>
            <div class="form-group col-sm-6">
              <label for="paymethod" class="colorPurpel">
                Payment Method*
              </label>
              <select class="form-control m-input" name="paymethod" id="paymethod" [(ngModel)]="payment_method"
                required>
                <option *ngFor="let m of oflinePaymentmethod; let i ='index'" [value]="m">{{m}}</option>
              </select>
            </div>
            <div class="form-group col-sm-6">
              <label for="amount" class="colorPurpel">
                Amount tendered*
              </label>
              <div class="input-group m-input-group">
                <div class="input-group-prepend">
                  <span class="input-group-text" id="table-cost-addon">
                    $
                  </span>
                </div>
                <input id="cost" class="form-control m-input" id="amount" type="text" placeholder="0.00" name="amount"
                  [maxlength]="maxLength" numberOnly [(ngModel)]="payment_amount" (ngModelChange)="checkPayAmount()"
                  autocomplete="off" required [disabled]="payment_method == 1">
                <div class="input-group-append">
                  <span class="input-group-text" id="table-cost-addon">
                    USD
                  </span>
                </div>
              </div>
              <span *ngIf="payment_error">
                <small class="error">Amount should be equal or greater than cart total</small>
              </span>
            </div>
            <div class="form-group col-sm-6">
              <h5>
                Change Amount: {{changeAmount | currency}}
              </h5>
            </div>
            <div class="form-group col-sm-12">
              <div *ngIf="loader; else bttnOff" class="m-loader m-loader--brand" style="width: 30px;display: inline-block;"></div>
              <ng-template #bttnOff>
                <button type="button" (click)="offlinePayment()" [disabled]="!formOffline.form.valid || payment_error"
                  class="btn btn-brand">Submit</button>
              </ng-template>
            </div>
          </div>
        </form>
      </div>
    </ng-template>
  </div>

  
</div>

<ng-template #orderCreated>

</ng-template>
