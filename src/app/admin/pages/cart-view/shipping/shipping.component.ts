import {
  Component,
  OnChanges,
  Output,
  Input,
  EventEmitter,
  OnInit
} from "@angular/core";
import { CartService } from "../../../cart-service/cart.service";
import { GET_USER, forPickUp } from "../../../../globals/_classes/functions";
import { map, catchError } from "rxjs/operators";
import { of } from "rxjs";

@Component({
  selector: "app-shipping",
  templateUrl: "./shipping.component.html",
  styleUrls: ["./shipping.component.css"]
})
export class ShippingComponent implements OnChanges, OnInit {
  @Input("allShipping") allShipping;
  zone = [];
  shipping = [];
  isRequaird: boolean;
  byZone: boolean;
  gotoNext: boolean;
  instoreSelect;
  zoneSelect;
  allLocation = [];
  loader: boolean;
  // inStorePickupDisabled: boolean;

  @Output() openBilling = new EventEmitter();

  constructor(private cartS: CartService) {}

  ngOnChanges() {
    if (this.allShipping) {
      const { delivery, shipping } = this.allShipping;
      this.isRequaird = delivery.delivery_settings.is_requiered;
      // if (delivery.delivery_settings.hasOwnProperty('disable_instore_pickup') && delivery.delivery_settings.disable_instore_pickup) {
      //   this.inStorePickupDisabled = true;
      // } else {
      //   this.inStorePickupDisabled = false;
      // }
      if (this.isRequaird) {
        this.byZone = delivery.delivery_settings.charge_by_zone;
        this.zone = delivery.location;
      }

      for(let s in shipping) {
        this.shipping.push(shipping[s]);
      }
    }
  }

  ngOnInit() {
    this.getLocation();
  }

  getLocation() {
    this.cartS
      .getterminals()
      .pipe(
        map(res => res.result.data),
        catchError(e => of([]))
      )
      .subscribe(res => {
        this.allLocation = res.filter(f => f.status);
      });
  }

  getDate(d) {
    if (d) {
      return new Date(d);
    }
    return "";
  }

  private inStoreSelection(s, m) {
    if (m === 1) {
      this.zoneSelect = null;
      this.instoreSelect = "in";
      forPickUp(true, s.id, true);
    } else {
      this.instoreSelect = null;
      const pick = JSON.parse(sessionStorage.getItem("inStore"));
      if (pick.instore || pick.pickup) {
        forPickUp(false, null, true);
      }
    }
  }

  private zoneSelection(m) {
    if (m === 2 && this.zone.length > 0) {
      this.instoreSelect = null;
      this.zoneSelect = "zone";
    } else {
      this.zoneSelect = null;
    }
  }

  sendSelectData(s, m) {
    this.loader = true;
    const token = this.cartS.getSessionData("cartToken");
    this.inStoreSelection(s, m);
    this.zoneSelection(m);
    const sendData = {
      shipping_method: m,
      token,
      shipping_cost: s && s.charge ? s.charge : 0,
      tax: s && s.tax ? s.tax : 0
    };
    this.cartS
      .addDeliveryCharge(sendData)
      .then(res => {
        this.loader = false;
        if (res.status == "OK" && res.result.data) {
          if (s) {
            this.cartS.setSessionData("deliveryCharge", s);
          }
          this.cartS.setSessionData("cartList", res.result.data);
          this.gotoNext = true;
        } else {
          this.gotoNext = false;
        }
      })
      .catch(err => {
        this.loader = false;
        console.error(err);
        this.gotoNext = false;
      });
  }

  submit() {
    if (this.gotoNext) {
      this.openBilling.emit(true);
      this.cartS.goToPayment(true);
    }
  }

  back() {
    this.openBilling.emit(false);
  }
}
