import { Component, OnInit, EventEmitter, Output } from "@angular/core";
import { CheckOut } from "../../../cart-service/cart.models";
import { GET_USER } from "../../../../globals/_classes/functions";
import { CartService } from "../../../cart-service/cart.service";
import { allCountry } from "../../../../globals/_classes/country";

@Component({
  selector: "app-check-out",
  templateUrl: "./check-out.component.html",
  styleUrls: ["./check-out.component.css"]
})
export class CheckOutComponent implements OnInit {
  paymentMethods = ["Online", "Offline"];
  termsCondition = true;
  billingForm: CheckOut;
  errorFiled: boolean;
  instore: boolean;
  countries = allCountry;
  countryCode = 230;
  stateCode;
  allState;

  @Output("openShipping") openShipping: EventEmitter<any> = new EventEmitter();

  constructor(private cartS: CartService) {}

  ngOnInit() {
    this.addInfoToBill();
    if (!this.instore) {
      this.setShippingCountryAndState();
    }
  }

  setShippingCountryAndState() {
    if (this.billingForm.shipping_country) {
      this.countryCode = this.countries.find(
        f => f.code.toUpperCase() == this.billingForm.shipping_country
      ).id;
    }
    this.getState();
  }
  addInfoToBill() {
    this.cartS.checkout.subscribe(val => {
      if (val) {
        let bill = this.cartS.getSessionData("billInfo");
        if (bill) {
          this.billingForm = bill;
        } else {
          this.billingForm = new CheckOut();
          this.billingForm.type = 1;
        }
        this.billingForm.pickup = this.cartS.getSessionData("inStore")
          ? this.cartS.getSessionData("inStore").pickup
          : null;
        this.billingForm.delivery_charge = this.cartS.getSessionData(
          "cartList"
        ).delivery_charge;
        this.billingForm.token = this.cartS.getSessionData("cartToken");
        this.billingForm.driving_license_required = this.checkDrivingLicense();
        this.errorFiled = false;
        this.instore = this.cartS.getSessionData("inStore")
          ? this.cartS.getSessionData("inStore").instore
          : false;
        if (this.instore) {
          setTimeout(() => {
            this.resetShipping();
          }, 100);
        }
      }
    });
  }

  submitBilling() {
    this.billingForm.shipping_first_name = this.billingForm.first_name;
    this.billingForm.shipping_last_name = this.billingForm.last_name;
    this.billingForm.shipping_mobile = this.billingForm.mobile;
    if (
      this.billingForm.driving_license_required &&
      !this.billingForm.driving_license
    ) {
      this.errorFiled = true;
    } else {
      this.billingForm.salesman = GET_USER().user_id;
      this.cartS.setSessionData("billInfo", this.billingForm);
       this.openShipping.emit(this.billingForm);
     // console.log(this.billingForm);
    }
  }

  checkDrivingLicense() {
    const cart = this.cartS.getSessionData("cartList");
    for (let c of cart) {
      if (c.driving_license_required) {
        return true;
      }
    }
    return false;
  }

  resetShipping() {
    this.billingForm.shipping_first_name = null;
    this.billingForm.shipping_last_name = null;
    this.billingForm.shipping_address1 = null;
    this.billingForm.shipping_address2 = null;
    this.billingForm.shipping_city = null;
    this.billingForm.shipping_mobile = null;
    this.billingForm.shipping_state = null;
    this.billingForm.shipping_zipcode = null;
  }

  changeCountry(e) {
    const id = e.id ? e.id : 230;
    this.countryCode = id;
    this.getState();
    const country = this.countries.find(f => f.id === id);
    this.billingForm.shipping_country = country.code.toUpperCase();
  }

  changeState(e) {
    if (e.id) {
      this.stateCode = e.id;
      const state = this.allState.find(f => f.id === e.id);
      this.billingForm.shipping_state = state.code.toUpperCase();
    } else {
      this.billingForm.shipping_state = "";
    }
  }

  private getState() {
    this.cartS.getCountryState(this.countryCode).subscribe(res => {
      this.allState = res;
      if (this.allState && this.billingForm.shipping_state) {
        const data = this.allState.find(
          f => f.code.toUpperCase() == this.billingForm.shipping_state
        );
        this.stateCode = data ? data.id : null;
        if (!data) {
          this.billingForm.shipping_state = "";
        }
      }
    });
  }
  onSubmitCustomer(customer) {
    console.log(customer);
    if (customer) {
      for (let key in customer) {
        if (!customer[key]) {
          delete customer[key];
        }
      }
      this.billingForm = customer;
      this.setShippingCountryAndState();
    }
  }
}
