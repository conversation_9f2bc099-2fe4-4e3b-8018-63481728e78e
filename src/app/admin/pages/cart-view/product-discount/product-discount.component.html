<div class="col-md-12 mt-3 ">
    <div class="" #hasAlert></div>
 </div>
<div class="discount">
    <button  aria-label="Close" (click)="activeModal.dismiss('')" class="close" type="button">
      <span  aria-hidden="true">×</span></button>
  <div class="discount-form">
    <form action="">
      <div class="row">
        <div class="form-group col-md-4 ">
          <label>Percent off  </label>
        </div>
        <div class="col-md-8">
          <div class="row">
            <div class="form-group col-md-4">
              <label class="m-radio">
                <input (change)="selectDiscount('10')" type="radio" name="customRadioo">
                <span></span>
                10%
              </label>
            </div>
            <div class="form-group col-md-4">
              <label class="m-radio">
                <input (change)="selectDiscount('15')" type="radio" name="customRadioo">
                <span></span>
                15%
              </label>
            </div>
            <div class="form-group col-md-4">
              <label class="m-radio">
                <input (change)="selectDiscount('20')" type="radio" name="customRadioo">
                <span></span>
                20%
              </label> 
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="form-group col-md-12">
         <div class="row">
            <label for="" class="col-md-4 ml-3 mt-3">Amount Off</label>
           <input class="form-control" (ngModelChange)="getDiscaountValue(subTotal.value)" numberOnly class="form-control col-md-6" [formControl]="subTotal" placeholder="0.00" type="text" >
         </div>
         <div class="row mt-4 ">
          <label  class="col-md-4 ml-3" for=""> Subtotal <br> (Discounted) </label>
          <div class="col-md-6">
            <p>{{calculated_price | currency}}</p>
          </div>
         </div>
          </div>
      </div>
      <div class="row">
        <div class="form-group col-md-12">
          <label for="" style="font-size: 15px;">Note * </label>
          <textarea class="form-control" name="" id="" [formControl]="note"  cols="30" class="form-control" rows="10"></textarea>
        </div>
      </div>
      <div class="row">
        <div class="m-loader m-loader--brand" *ngIf="loader"  style="width: 30px;margin-left: 10px; padding-left: 30px; display: inline-block;"></div>
        <button [disabled]=" !subTotal.value || !note.value" class="btn btn-brand ml-3" (click)="submit()">Proceed</button>
        <button (click)="activeModal.dismiss('')" class="btn btn-sm btn-danger col-md-2  ml-4" >Cancel</button>
      </div>
    </form>
  </div>
</div>