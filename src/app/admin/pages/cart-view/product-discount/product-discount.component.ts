import { Component, OnInit, Input, OnChanges, ViewChild, ElementRef } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { FormGroup, FormBuilder, FormControl } from '@angular/forms';
import { HttpService } from '../../../../modules/http-with-injector/http.service';
import { CartItem } from '../../../cart-service/cart.models';
import { AlertService } from '../../../../modules/alert/alert.service';
import { CartService } from '../../../cart-service/cart.service';

@Component({
  selector: 'app-product-discount',
  templateUrl: './product-discount.component.html',
  styleUrls: ['./product-discount.component.css']
})
export class ProductDiscountComponent implements OnInit, OnChanges {
  discount_value: number;
  subTotal = new FormControl('');
  note = new FormControl('');
  @ViewChild('hasAlert') alertContainer: ElementRef;
  @Input() price: number;
  @Input() cart;
  @Input() basePrice;
  @Input() token;
  @Input() order;
  @Input() orderId;

  calculated_price: number;
  loader: boolean;
  item_log = [];
  cart_item;
  endpoint: string;

  constructor(
    private service: CartService,
    private alert: AlertService,
    private http: HttpService,
    private fb: FormBuilder,
    public activeModal: NgbActiveModal) {


  }

  ngOnInit() {
    if (this.order) {
      this.endpoint = 'orders/add-discount';
    } else {
      this.endpoint = 'carts/add-discount';
    }
    this.calculated_price = this.basePrice;
    const price = 0;
    this.subTotal.setValue(price.toFixed(2));
    this.note.setValue(this.cart.additional ? this.cart.additional.subtaintial_note : '');

    // if (sessionStorage.getItem('item_log')) {
    //   this.item_log = JSON.parse(sessionStorage.getItem('item_log'));
    // }
    // if (this.item_log.length) {
    //   this.cart_item = this.item_log.find(item => {
    //     if (item.id === this.cart.id) {
    //       return true;
    //     }

    //   });
    //   if (this.cart_item) {
    //     this.note.setValue(this.cart_item.note);
    //   }
    // }

  }
  ngOnChanges() {
    if (this.order) {
      this.endpoint = 'orders/add-discount';
    } else {
      this.endpoint = 'carts/add-discount';
    }
  }

  submit() {
    this.loader = true;
    if (this.order) {
      this.http.post(this.endpoint,
         {
           order_id: this.orderId, 
           order_item_id: this.cart.id, 
           off_amount: this.discount_value, 
           sub_total: this.calculated_price,
           note: this.note.value 
          }
         )
      .toPromise()
      .then(res => {
        if (res.status === 'OK') {
          if (res.result.data) {
            this.activeModal.close(res.result.data);
          }
          // this.service.cartDiscount.next({ reload: true, cart: res.result.data.cart });
          // tslint:disable-next-line:max-line-length
          // this.saveLogInStorage({ token: this.token, id: this.cart.id, product_id: this.cart.product.id, note: this.note.value, off_amount: this.discount_value, amount: this.calculated_price });
          this.alert.success(this.alertContainer, 'Submitted successfully', true, 5000);
          setTimeout(() => {
            this.activeModal.close(false);
          }, 2000);
        } else {
          this.alert.error(this.alertContainer, res.result.data.message, true, 5000);
        }
        this.loader = false;
      }).catch(err => {
        this.alert.error(this.alertContainer, 'Something wrong Please try again !!!', true, 5000);
        this.loader = false;
        console.log(err);
      });
    } else {
      this.http.post(this.endpoint,
         {
           token: this.token,
           cart_item_id: this.cart.id,
            sub_total: this.calculated_price,
            off_amount: this.discount_value,
            note: this.note.value
          }
         )
      .toPromise()
      .then(res => {
        if (res.status === 'OK') {
          this.service.cartDiscount.next({ reload: true, cart: res.result.data.cart });
          // tslint:disable-next-line:max-line-length
          // this.saveLogInStorage({ token: this.token, id: this.cart.id, product_id: this.cart.product.id, note: this.note.value, off_amount: this.discount_value, amount: this.calculated_price });
          this.alert.success(this.alertContainer, res.result.data.message, true, 5000);
          setTimeout(() => {
            this.activeModal.close(false);
          }, 2000);
        } else {
          this.alert.error(this.alertContainer, res.result.data.message, true, 5000);
        }
        this.loader = false;
      }).catch(err => {
        this.alert.error(this.alertContainer, 'Something wrong Please try again !!!', true, 5000);
        this.loader = false;
        console.log(err);
      });
    }
  }
  selectDiscount(value) {
    this.discount_value = Number(value);
    const sub_total = (this.basePrice * (this.discount_value / 100));
    this.subTotal.setValue(sub_total.toFixed(2));
    this.calculated_price = this.basePrice - sub_total;
  }

  getDiscaountValue(value) {
    this.calculated_price = this.basePrice - Number(value);
  }

  saveLogInStorage(data) {
    this.cart_item = data;
    const index = this.item_log.findIndex(item => {
      if (item.id === this.cart.id) {
        return true;
      }
    });
    if (index > -1) {
      this.item_log[index] = this.cart_item;
    } else {
      this.item_log.push(data);
    }

    sessionStorage.setItem('item_log', JSON.stringify(this.item_log));
    console.log(this.item_log);
  }
}
