import { Component, OnInit, ViewChild, ElementRef, Output, EventEmitter, SimpleChanges, Input } from '@angular/core';
import { FormGroup, FormBuilder, FormArray, Validators } from '@angular/forms';
import { Helpers } from '../../../../helpers';
import { HttpService } from '../../../../modules/http-with-injector/http.service';
import { AlertService } from '../../../../modules/alert/alert.service';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { SidebarService } from '../../sidebar-service/sidebar.service';

@Component({
  selector: 'app-setp-two',
  templateUrl: './setp-two.component.html',
  styleUrls: ['./setp-two.component.css']
})
export class SetpTwoComponent implements OnInit {

  questionnareForm: FormGroup;
  product_image;
  is_next_click=false;

  @Input('isStep2Complete') isStep2Complete;
  @Output('onSubmit') onSubmit: EventEmitter<any> = new EventEmitter();
  @ViewChild("hasCusAlert") alertContainer: ElementRef;

  constructor(
    private fb: FormBuilder,
    private http: HttpService,
    private sidebarS: SidebarService,
    private alertS: AlertService,
    public activeModal: NgbActiveModal
  ) { }

  ngOnInit() {

    this.questionnareForm = this.fb.group({
      product_name: ["Sample",Validators.required],
      image: [""],
      product_id: [""],
      how_many: ["100"],
      product_category: [""],
      price_type: ["3"],
      sell_price: [""],
      basic_price: this.fb.group({
        price: [""]
      }),
      standard_price: this.fb.group({
        type: ["hourly"],
        price: [""],
        duration: [""]
      }),
      advanced_price: this.fb.group({
        type: ["hourly"],
        price: [""],
        duration: [""],
        range: ["1"],
        additional_price: [""],
        additional_duration: [""]
      })
    });

    this.product_image = '';
  }

  ngOnChanges(changes:SimpleChanges){
    this.is_next_click=!changes.isStep2Complete.currentValue;
  }


  onChangeProductImage(e) {
    let file = e.target.files[0];
    console.log(e.target.files[0]);
    let reader = new FileReader();
    reader.onload = e => {
      this.product_image = reader.result;
    };
    reader.readAsDataURL(e.target.files[0]);

    this.onUpload(file, "Image");
  }

  onUpload(inputFile: File, type) {
   // Helpers.setLoading(true);
    const formData = new FormData();
    formData.append("file", inputFile);
    formData.append("type", type);

    this.http
      .post("media/upload", formData)
      .subscribe(res => {
        if (res.status == "OK") {
          //Helpers.setLoading(false);
          console.log(res.result.data);

          if (type == "Image") {
            this.questionnareForm.get('image').setValue(
              res.result.data.filename
            );
          }


        }
        else {
          this.alertS.error(this.alertContainer, res.result.message, true, 3000);
        }
      });
  }

  onImageClose() {
    this.product_image = "";
    this.questionnareForm.get('image').setValue(
      this.product_image
    );
  }


  onClickNext() {
    let sendData = this.questionnareForm.value;
    let price = {};

    if (this.questionnareForm.get('price_type').value == 2) {
      delete sendData.standard_price;
      delete sendData.advanced_price;
      price = sendData.basic_price

      delete sendData.basic_price;
    }
    else if (this.questionnareForm.get('price_type').value == 3) {
      delete sendData.basic_price;
      delete sendData.advanced_price;

      price = sendData.standard_price
      delete sendData.standard_price;
    }
    else if (this.questionnareForm.get('price_type').value == 4) {
      delete sendData.basic_price;
      delete sendData.standard_price;

      price = sendData.advanced_price
      delete sendData.advanced_price;
    }

    price['price_type'] = parseInt(this.questionnareForm.get('price_type').value);
    sendData['price'] = price;
    delete sendData.price_type;
    

    this.is_next_click=true;
    this.onSubmit.emit(this.questionnareForm.value)
  }

  onClickSkip() {
    let skip = true;

    let sendData=this.questionnareForm.value;
    sendData['skip']=skip
    this.onSubmit.emit(sendData)

    sessionStorage.setItem("is_skip", JSON.stringify(skip));
    this.sidebarS.questionnaireSkip(skip);
    this.activeModal.dismiss();
  }

}
