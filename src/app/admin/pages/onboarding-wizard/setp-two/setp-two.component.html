<form [formGroup]="questionnareForm">
  <div class="onboarding-middle-body-inner">
    <div class="row">
      <div class="col-md-12">
        <div class="row">
          <div class="col-lg-12 col-md-12">
            <div class="form-group">
              <label class="main-label">Product name*</label>
              <input type="text" class="form-control" placeholder="Product name" formControlName="product_name" />
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-12">
        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label class="main-label">
                Product image
              </label>
              <div class="custom-file">
                <input class="custom-file-input" type="file" id="image" name="image"
                  (change)="onChangeProductImage($event)" accept="image/*">
                <label class="custom-file-label" for="image">
                  Choose file
                </label>
                <input type="hidden" name="imgHidden" formControlName="image" />
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div *ngIf="product_image !=''" class="img-show-area">
              <a class="img-close cursor-pointer" (click)="onImageClose()"><i class="fa fa-times"></i></a>
              <img [src]="product_image" alt="" width="80" style="object-fit: contain;" />
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-12">
        <div class="row">
          <div class="col-xl-6 col-lg-6 col-md-6">
            <div class="form-group">
              <label class="main-label">How many do you have?</label>
              <input type="number" class="form-control" placeholder="" formControlName="how_many" />
            </div>
          </div>

          <div class="col-xl-6 col-lg-6 col-md-6">
            <div class="form-group">
              <label class="main-label">Product id</label>
              <input type="text" class="form-control" placeholder="" formControlName="product_id" />
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-12">
       <div class="row">
        <div class="col-xl-12 col-lg-12 col-md-12">
          <div class="form-group">
            <label class="main-label">Name this product's group or category</label>
            <input type="text" class="form-control" placeholder="" formControlName="product_category" />
          </div>
        </div>
       </div>
      </div>

      <div class="col-md-12">
        <div class="row">
          <div class="col-xl-6 col-lg-6 col-md-6">
            <div class="form-group">
              <label class="main-label">Rental price choose your pricing preference</label>
              <select class="form-control m-input dropdown-cls" formControlName="price_type">
                <option value="2">Basic</option>
                <option value="3">Standard</option>
                <option value="4">Advanced</option>
              </select>
            </div>
          </div>

          <div class="col-xl-6 col-lg-6 col-md-6">
            <div class="form-group">
              <label class="main-label">Sell price</label>
              <input type="number" class="form-control" placeholder="" formControlName="sell_price" />
            </div>
          </div>
        </div>
      </div>

      <!-- Fixed Price -->
      <div class="w-100" formGroupName="basic_price">
        <div *ngIf="questionnareForm.get('price_type').value==2" class="col-md-12">
          <div class="row">
            <div class="col-xl-6 col-lg-12 col-md-12">
              <h6>Fixed price</h6>
              <div class="row">
                <div class="form-group m-form__group col-md-12 mb-0">
                  <div class="input-group m-input-group">
                    <div class="input-group-prepend">
                      <span class="input-group-text"> $ </span>
                    </div>
                    <input autocomplete="off" class="form-control m-input" placeholder="Amount" type="number"
                      formControlName="price" />
                    <div class="input-group-append">
                      <small class="input-group-text"> USD </small>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>

      <!-- standard Price -->
      <div formGroupName="standard_price">
        <div *ngIf="questionnareForm.get('price_type').value==3" class="col-md-12">
          <div class="row">
            <div class="col-md-12">
              <div class="row">
                <div class="form-group m-form__group col-md-3">
                  <h6>Type</h6>
                  <div class="input-group m-input-group">
                    <select class="form-control m-input dropdown-cls" formControlName="type">
                      <option value="hourly">Hourly</option>
                      <option value="daily">Daily</option>
                      <option value="weekly">Weekly</option>
                      <option value="monthly">Monthly</option>
                    </select>
                  </div>
                </div>
                <div class="form-group m-form__group col-md-4">
                  <h6>Duration</h6>
                  <div class="input-group m-input-group">
                    <input autocomplete="off" class="form-control m-input" formControlName="duration" numberonly=""
                      placeholder="Duration" type="number" />
                  </div>
                </div>
                <div class="form-group m-form__group col-md-5">
                  <h6>Amount</h6>
                  <div class="input-group m-input-group">
                    <div class="input-group-prepend"><span class="input-group-text"> $ </span></div>
                    <input autocomplete="off" class="form-control m-input" formControlName="price" numberonly=""
                      placeholder="Amount" type="number" ng-reflect-name="price" />
                    <div class="input-group-append"><small class="input-group-text"> USD </small></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Advanced Price -->
      <div formGroupName="advanced_price" class="w-100">
        <div *ngIf="questionnareForm.get('price_type').value==4" class="col-md-12">
          <div class="row">
            <div class="form-group m-form__group  col-xl-2 col-lg-6 col-md-6 col-sm-6">
              <h6>Type</h6>
              <div class="input-group m-input-group">
                <select class="form-control m-input dropdown-cls" formControlName="type">
                  <option value="hourly">Hourly</option>
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                  <option value="monthly">Monthly</option>
                </select>
              </div>
            </div>
            <div class="form-group m-form__group  col-xl-4 col-lg-6 col-md-6 col-sm-6">
              <ng-template #popContentRange>Enter the base(minimum) term for the rental in the
                Duration field. Add the number of base periods that this pricing applies to.
                <br/>For example, if you offer a one day rental
                for {{15  | currency}}, and you charge that price for up to 5 days, enter 1 in the 'Duration' field and 15 in the 'Amount' field.</ng-template>
            <h6><span triggers="mouseenter:mouseleave" [ngbPopover]="popContentRange">Duration / Range ? </span></h6>
              <div class="input-group m-input-group">
                <input autocomplete="off" class="form-control m-input" placeholder="Duration" type="number"
                  formControlName="duration" />
                <input autocomplete="off" class="form-control m-input" placeholder="Range" type="number"
                  formControlName="range" />
              </div>
            </div>
            <div class="form-group m-form__group  col-xl-2 col-lg-6 col-md-6 col-sm-6">
              <h6>Amount</h6>
              <div class="input-group m-input-group">
                <input autocomplete="off" class="form-control m-input" placeholder="Amount" type="number"
                  formControlName="price" />
              </div>
            </div>
            <div class="form-group m-form__group  col-xl-4 col-lg-6 col-md-6 col-sm-6">
              <ng-template #popContentAdditional>If you offer a price break after the base period, use these fields.
              </ng-template>
              <h6><span  placement="left" triggers="mouseenter:mouseleave" [ngbPopover]="popContentAdditional">Additional Price / Duration ?</span></h6>
              <div class="input-group m-input-group ">
                  <input autocomplete="off" class="form-control m-input" placeholder="Amount" type="number"
                  formControlName="additional_price" />
                <input autocomplete="off" class="form-control m-input ml-2" placeholder="Duration" type="number"
                  formControlName="additional_duration" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- step footer -->
  <div class="onboarding-footer w-100">
    <button class="btn btn-gray" (click)="onClickSkip()">
      Skip
    </button>
    <button [disabled]="!questionnareForm.valid || is_next_click" style="cursor: pointer;" class="btn btn-brand float-right" (click)="onClickNext()">
      Next Step<i class="fa fa-chevron-right ml-2"></i>
    </button>
  </div>

</form>