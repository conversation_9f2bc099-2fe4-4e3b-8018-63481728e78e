.onboarding-middle-body-inner {
    padding: 35px 40px 40px;
}
.m-widget4 .m-widget4__item {
    display: table;
    padding-top: 1.15rem;
    padding-bottom: 1.25rem;
    min-height: 110px;
    width: 100%;
    padding-left: 1.15rem;
    padding-right: 1.15rem;
    background-color: #fff;
    margin-bottom: 20px;
    /* box-shadow: 0px 1px 5px 1px rgba(69, 65, 78, 0.08); */
    border: 1px solid #f2f3f8;
    border-radius: 2px;
}
.m-widget4 .m-widget4__item:hover {
    box-shadow: 0px 2px 15px 4px rgba(69, 65, 78, 0.08);
}
.m-widget4__item {
    border: none;
}
.m-widget4 .m-widget4__item .m-widget4__img {
    display: table-cell;
    vertical-align: middle;
}
.m-widget4 .m-widget4__item .m-widget4__img i {
    font-size: 45px;
    color: #575962;
}
.m-widget4 .m-widget4__item .m-widget4__info {
    display: table-cell;
    width: 100%;
    padding-left: 1.2rem;
    padding-right: 1.2rem;
    font-size: 1rem;
    vertical-align: middle;
}
.m-widget4 .m-widget4__item .m-widget4__info .m-widget4__title {
    font-size: 15px;
    font-weight: 600;
}
.m-widget4__item .m-widget4__title {
    color: #575962;
}
.btn-brand {
    border-radius: 3px;
    padding: 0.65rem 15px;
    background-color: #4B9AFA;
    border-color: #4B9AFA;
}
.btn-brand:focus,
.btn-brand:active,
.btn-brand:hover {
    background-color: #3b7dce  !important;
    border-color: #3b7dce  !important;
}
.onboarding-footer {
    border-top: 1px solid #f2f3f8;
    padding: 15px 30px;
    height: 68px;
}
.btn-continue {
    background-color: #655FB5;
    color: #fff;
}
.btn-continue:hover {
    background-color: rgb(123, 116, 218);
    color: #fff;
}
@media (max-width: 991px) {
    .m-widget4 .m-widget4__item .m-widget4__img i {
        font-size: 25px;
    }
    .m-widget4 .m-widget4__item {
        height: 80px;
        min-height: auto;
    }
    .m-widget4 .m-widget4__item .m-widget4__info {
        padding-left: 10px;
        padding-right: 10px;
    }
    .m-widget4 .m-widget4__item .m-widget4__info .m-widget4__title {
        font-size: 12px;
        font-weight: 600;
        line-height: 19px;
    }
}
@media (max-width: 767px) {
    .onboarding-middle-body-inner {
        padding: 15px 20px;
    }
    .onboarding-footer {
        padding: 15px 20px;
    }
}
@media (max-width: 575px) {
    .m-widget4 .m-widget4__item {
        height: 85px;
        min-height: auto;
    }
    .m-widget4 .m-widget4__item .m-widget4__img i {
        font-size: 25px;
    }
    .m-widget4 .m-widget4__item .m-widget4__info .m-widget4__title {
        font-size: 15px;
    }
}

.btn.btn-gray {
    background-color: #f2f3f8;
    border-radius: 3px;
    padding: 0.65rem 20px;
}
.btn.btn-gray:active,
.btn.btn-gray:hover,
.btn.btn-gray:hover {
    background-color: #eee;
    border-radius: 3px;
    padding: 0.65rem 20px;
}