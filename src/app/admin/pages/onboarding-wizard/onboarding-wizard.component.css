.onboarding-rightside-bg {
    background-color: #655fb5;
    position: absolute;
    top: 0;
    right: 0;
    width: 35%;
    height: 100%;
}

@media (max-width: 1699px) {
    .onboarding-rightside-bg {
        width: 33%;
    }
}
@media (max-width: 1499px) {
    .onboarding-rightside-bg {
        width: 30%;
    }
}
@media (max-width: 1350px) {
    .onboarding-rightside-bg {
        width: 26%;
    }
    .onboarding-modal-header h4 {
        font-size: 20px;
    } 
}
@media (max-width: 1199px) {
    .onboarding-rightside-bg {
        width: 24%;
    }
    .onboarding-modal-header h4 {
        font-size: 18px;
    }
}
@media (max-width: 992px) {
    .onboarding-rightside-bg {
        width: 23%;
    }

}

.onboarding-modal-body {
    background-color: #f2f3f8;
    height: 100vh;
    position: relative;
}
.onboarding-middle-body {
    background-color: #fff;
    border-radius: 5px;
}
.onboarding-nextstep {
    position: absolute;
    width: 250px;
    height: 100%;
    z-index: 1;
    list-style: none;
    right: -205px;
}
.onboarding-nextstep-inner {
    display: flex;
    height: 100%;
    width: 100%;
    align-items: center;
    justify-content: center;
}
.onboarding-nextstep-inner ul {
    list-style: none;
    margin-left: 0;
    margin: 0;
    padding-left: 0;
    width: 100%;
}
.onboarding-nextstep-inner ul li {
    margin: 60px 0;
    position: relative;
}
.onboarding-nextstep-inner ul li a {
    width: 30px;
    height: 30px;
    text-align: center;
    line-height: 30px;
    border-radius: 50px;
    display: inline-block;
    font-size: 12px;
    margin-left: 15px;
    transition: all .3s;
}
.onboarding-circle a {
    background-color: #fff;
    border: 1px solid #eee;
}
.onboarding-active a {
    background-color: #4B9AFA;
    border: 1px solid #4B9AFA;
    color: #fff !important;
}
.onboarding-success a {
    background-color: #655fb5;
    border: 1px solid #655fb5;
    color: #fff !important;
}
.onboarding-circle a .onboarding-success-icon {
    display: none;
}
.onboarding-success a .onboarding-no {
    display: none;
}
.onboarding-success a .onboarding-success-icon {
    display: block;
}
.onboarding-success a .onboarding-success-icon i {
    font-size: 12px;
}


.onboarding-circle .onboarding-nextstep-text {
    display: inline-block;
    color: #9390b7;
    margin-left: 10px;
    position: absolute;
    right: 0;
    left: 50px;
    font-size: 10px;
    font-weight: 400;
    transition: all .3s;
}
.onboarding-circle .onboarding-nextstep-text b {
    display: inline-block;
    width: 100%;
    font-size: 15px;
    letter-spacing: 0.5px;
    font-weight: 500;
}


.onboarding-active .onboarding-nextstep-text {
    color: #eee;
}
.onboarding-success .onboarding-nextstep-text {
    color: #b3afdc;
}

.onboarding-middle-body-inner {
    padding: 45px 50px 40px;
}
.onboarding-modal-header h3{
    text-align: center;
}
.onboarding-modal-body form {
    /* padding: 30px 30px;
    height: 100vh; */
    /* overflow: auto; */
}
label {
    font-weight: 400;
    
}
.h-100 {
    height: 100%;
}
.main-label {
    font-weight: 600 !important;
    font-size: 15px;
}
app-onboarding-questionnaire {
    padding-bottom: 0;
}
.back-next-area {
    padding: 12px 15px;
    border-top: 1px solid #eee;
    background-color: #fff;
}
.btn > i {
    font-size: 10px;
}
.btn {
    border-radius: 20px;
    padding: 0.65rem 35px;
}
.btn-brand {
    border-radius: 3px;
    padding: 0.65rem 15px;
    background-color: #4B9AFA;
    border-color: #4B9AFA;
}
.btn-brand:focus,
.btn-brand:active,
.btn-brand:hover {
    background-color: #4B9AFA;
    border-color: #4B9AFA;
}
iframe {
    width: 50%;
    padding-bottom: 15px;
    height: 279px;
    border: 1px solid #eee!important;
}
.download-btn {
    border-radius: 5px;
    font-size: 12px;
    margin: 10px 0;
}

a{
    color: black !important;
    text-decoration: underline;
    font-size: 18px;
}
.img-show-area {
    width: auto;
    position: relative;
    width: 105px;
}
.img-close {
    position: absolute;
    width: 25px;
    height: 25px;
    background-color: #fff;
    box-shadow: 0px 0px 10px #eee;
    top: 0;
    right: 15px;
}


.onboarding-footer {
    border-top: 1px solid #f2f3f8;
    padding: 20px 30px;
}
.onboarding-modal-header h3 {
    position: absolute;
    right: 0;
    left: 0;
    padding-top: 70px;
}
.onboarding-modal-header {
    padding: 20px 0 20px;
}

@media (max-width: 1580px) {
    .col-xl-6 {
        flex: 0 0 64%;
        max-width: 64%;
    }
}
@media (max-width: 1199px) {
    .onboarding-nextstep {
        width: 207px;
        right: -162px;
    }
}
@media (max-width: 992px){
    .onboarding-nextstep {
        width: 165px;
        right: -120px;
    }
}
@media (max-width: 767px){
    .onboarding-modal-header {
        padding: 0px 0 20px;
    }
    .onboarding-modal-header h4 {
        font-size: 18px;
    }
    .onboarding-rightside-bg {
        width: 100%;
        display: none;
    }
    .col-xl-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }
    .onboarding-collam {
        padding: 20px 30px;
    }
    .onboarding-collam {
        padding: 0px 30px;
        overflow: auto;
    }
    .onboarding-middle-body {
        height: 80vh;
        overflow: auto;
    }
    .onboarding-nextstep {
        position: relative;
        width: 100% !important;
        height: unset;
        right: unset !important;
        padding-top: 15px;
    }
    .onboarding-nextstep-inner ul {
        width: 300px;
    }
    .onboarding-nextstep-inner ul li {
        margin: 15px 23px;
        position: relative;
        float: left;
    }
    .onboarding-circle .onboarding-nextstep-text {
        display: none;
    }
    .onboarding-nextstep-inner ul li::after{
        content: "";
        position: absolute;
        width: 80px;
        height: 1px;
        background-color: #eee;
        top: 14px;
    }
    .onboarding-nextstep-inner ul li:last-child::after{
        display: none;
    }
    .onboarding-nextstep-inner ul li.onboarding-success::after{
        background-color: #655fb5;
    }
}
