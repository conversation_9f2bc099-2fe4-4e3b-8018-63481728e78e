import { AdminService } from './../../admin.service';
import { Component, OnInit, ViewChild, ElementRef } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { HttpErrorResponse } from "@angular/common/http";
import { HttpService } from "../../../modules/http-with-injector/http.service";
import { AlertService } from "../../../modules/alert/alert.service";
import { DashboarService } from '../dashboard/dashboard.service/dashboard.service';

declare var $: any;

@Component({
  selector: "app-onboarding-wizard",
  templateUrl: "./onboarding-wizard.component.html",
  styleUrls: ["./onboarding-wizard.component.css"]
})
export class OnboardingWizardComponent implements OnInit {

  currentStep = 1;
  isStep1Complete = false;
  isStep2Complete = false;
  isStep3Complete = false;

  @ViewChild("hasCusAlert") alertContainer: ElementRef;

  constructor(
    public activeModal: NgbActiveModal,
    private http: HttpService,
    private alertS: AlertService,
    private adminS: AdminService,
    public dashS: DashboarService
  ) {

  }

  ngOnInit() {

  }

  getCurrencyConfig() {
    this.dashS.getCurrencyConfig().subscribe(
      res => {
        const currency = res;
        if (currency) {
          localStorage.setItem("currency", JSON.stringify(currency));
        }
      }
    )
  }


  saveQuestionnaire(step, sendData) {
    sendData['step'] = this.currentStep;
    if (step == "one") {
      this.currentStep = 2;
      this.adminS.sendQ(sendData).subscribe(
        res => {
          if (res.status == "OK") {
            this.isStep1Complete = true;
            this.getCurrencyConfig();
          }

        }, (err: HttpErrorResponse) => {
          this.isStep1Complete = true;
          console.log(err);
        });

    }
    else if (step == "two") {
      this.currentStep = 3;
      this.adminS.sendQ(sendData).subscribe(
        res => {
          if (res.status == "OK") {
            this.isStep2Complete = true;
          }

        }, (err: HttpErrorResponse) => {
          this.isStep2Complete = true;
          console.log(err);
        });
    }
    else if (step == "three") {
      this.currentStep = 3;
      this.adminS.sendQ(sendData).subscribe(
        res => {
          if (res.status == "OK") {
            this.isStep3Complete = true;
          }
        }, (err: HttpErrorResponse) => {
          this.isStep3Complete = true;
          console.log(err);
        });
    }


  }
}
