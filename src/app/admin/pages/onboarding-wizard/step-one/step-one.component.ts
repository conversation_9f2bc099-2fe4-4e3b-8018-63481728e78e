import { Component, OnInit, ViewChild, ElementRef, Output, EventEmitter, Input, OnChanges, SimpleChanges } from '@angular/core';
import { allCountry } from '../../../../globals/_classes/country';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';

import { EndPoint } from '../../../../globals/endPoint/config';
import { GET_USER } from '../../../../globals/_classes/functions';
import { AlertService } from '../../../../modules/alert/alert.service';
import { AdminService } from '../../../../admin/admin.service';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { ContentService } from '../../settings/setting-service/contents.service';
import { SidebarService } from '../../sidebar-service/sidebar.service';

@Component({
  selector: 'app-step-one',
  templateUrl: './step-one.component.html',
  styleUrls: ['./step-one.component.css']
})
export class StepOneComponent implements OnInit,OnChanges {

  questionnareForm: FormGroup;
  countries = allCountry.sort((a, b) => a.name.localeCompare(b.name));

  logo: string;
  tempLogo: string;
  url: string;
  currencyList = [];
  is_next_click=false;

  @Input('isStep1Complete') isStep1Complete;
  @Output('onSubmit') onSubmit: EventEmitter<any> = new EventEmitter();
  @ViewChild('hasCusAlert') alertContainer: ElementRef;

  constructor(
    private fb: FormBuilder,
    private alert: AlertService,
    private adminS: AdminService,
    private sidebarS: SidebarService,
    private ContentService: ContentService,
    public activeModal: NgbActiveModal
  ) { }

  ngOnInit() {

    this.ContentService.getCurrencyList().subscribe(res => {
      this.currencyList = res.data;
    });

    this.url = EndPoint + 'stores/' + GET_USER().store_id + '/store-logo';
    this.logo = GET_USER().logo;
    let company_name=GET_USER().company_name ? GET_USER().company_name : "";

    this.questionnareForm = this.fb.group({
      company_name: [company_name ,Validators.required],
      location: ["Default location",Validators.required],
      business_address: ["",Validators.required],
      currency: ["USD"],
      business_role: ["Business owner/manager"],
      business_role_other: [""],
      country: ["us"],
      store_logo: [""]
    });

  }


  ngOnChanges(changes:SimpleChanges){
    this.is_next_click=!changes.isStep1Complete.currentValue;
  }

  uploadLogo(data) {
    console.log(data);
    this.logo = data.status.result.logo;
    this.questionnareForm.get('store_logo').setValue(this.logo);
    this.tempLogo = null;
    
    let currentUser=GET_USER();
    currentUser['logo']=this.logo;
    localStorage.setItem('currentUser',JSON.stringify(currentUser));

    this.adminS.changeUser(currentUser);

    //this.alert.success(this.alertContainer, data.status.result.message, true, 3000);
  }

  changeLogo() {
    this.tempLogo = this.logo;
    this.logo = null;
  }

  cancelUpload() {
    this.logo = this.tempLogo;
    this.tempLogo = null;
  }


  onClickNext() {
    this.is_next_click=true;
    this.onSubmit.emit(this.questionnareForm.value)
  }

  onClickSkip() {
    let skip = true;

    let sendData=this.questionnareForm.value;
    sendData['skip']=skip
    this.onSubmit.emit(sendData)

    sessionStorage.setItem("is_skip", JSON.stringify(skip));
    this.sidebarS.questionnaireSkip(skip);
    this.activeModal.dismiss();
  }


}
