<div class="custom-alert" #hasCusAlert></div>



<div class="onboarding-modal-body">
  <div class="onboarding-rightside-bg">
  </div>

  <div class="row justify-content-center align-items-center h-100">

    <div class="col-xl-6 onboarding-collam ">
      <div class="onboarding-modal-header">
        <h4 *ngIf="currentStep==1" class="text-center">Welcome to RentMy!</h4>
        <h4 *ngIf="currentStep==1" class="text-center">Let's customize your experience.</h4>
        <h4 *ngIf="currentStep==3" class="text-center">Great start! What would you like to do next? </h4>
      </div>

      <div class="onboarding-middle-body">
        <!-- next step by step section -->
        <div class="onboarding-nextstep">
          <div class="onboarding-nextstep-inner">
            <ul>
              <li [ngClass]="currentStep==1 ? 'onboarding-active' : (currentStep <= 3 ? 'onboarding-success' : '')"
                class="onboarding-circle">
                <a>
                  <span class="onboarding-no">1</span>
                  <span class="onboarding-success-icon"><i class="la la-check"></i></span>
                </a>
                <span class="onboarding-nextstep-text">
                  <b>Step 1 of 3</b>
                  Tell us about your business
                </span>
              </li>
              <li
                [ngClass]="currentStep==2 ? 'onboarding-active' : (currentStep > 1 && currentStep <= 3  ? 'onboarding-success' : '')"
                class="onboarding-circle">
                <a>
                  <span class="onboarding-no">2</span>
                  <span class="onboarding-success-icon"><i class="la la-check"></i></span>
                </a>
                <span class="onboarding-nextstep-text">
                  <b>Step 2 of 3</b>
                  Quickly add products with multiple pricing options and advanced settings
                </span>
              </li>
              <li [ngClass]="currentStep==3 ? 'onboarding-active' : ''" class="onboarding-circle">
                <a>
                  <span class="onboarding-no">3</span>
                  <span class="onboarding-success-icon"><i class="la la-check"></i></span>
                </a>
                <span class="onboarding-nextstep-text">
                  <b>Step 3 of 3</b>
                  Configure your store and tell us about your specific use case.
                </span>
              </li>
            </ul>
          </div>
        </div>
        <!-- end -->

        <!-- main body -->
        <div class="onboarding-middlecontent-body">

          <!-- Step1 -->
          <app-step-one *ngIf="currentStep == 1" [isStep1Complete]="" (onSubmit)="saveQuestionnaire('one',$event)">
          </app-step-one>

          <!-- step-2 -->
          <app-setp-two *ngIf="currentStep == 2" [isStep2Complete]="" (onSubmit)="saveQuestionnaire('two',$event)">
          </app-setp-two>

          <!-- Step3 -->
          <app-step-three *ngIf="currentStep == 3" (onSubmit)="saveQuestionnaire('three',$event)"></app-step-three>

        </div>

      </div>
    </div>
  </div>

</div>