import { Component, OnInit, ElementRef, ViewChild } from '@angular/core';
import { Pagi } from '../../../../modules/pagination/pagi.model';
import { UserService } from '../user.service/user.service';
import { getStoreName, downloadFile } from '../../../../globals/_classes/functions';
import { AlertService } from '../../../../modules/alert/alert.service';

@Component({
  selector: 'app-newsletter',
  templateUrl: './newsletter.component.html',
  styleUrls: ['./newsletter.component.css']
})
export class NewsletterComponent implements OnInit {

  pagi:Pagi = new Pagi();
  public loader: boolean;
  newsLetterList: any[] = [];
  @ViewChild('hasAlert') alertContainer: ElementRef;

  constructor(
    private userS: UserService,
    private alertS: AlertService
  ) {
    this.getNewsletter(1, 100);
  }

  ngOnInit() {
  }

  reloadTable(e) {
    this.getNewsletter(e.page, e.limit);
  }

  private getNewsletter(p, l) {
    this.loader = true;
    this.renderData(p,l);
  }

  private renderData(p, l) {
    this.userS.getNewsletterList(p,l).subscribe(
      res =>{
        this.dataList(res);
        this.loader = false;
      },
      err=> this.loader = false
    );
  }

  private dataList(res) {
    this.newsLetterList = res.data;
    this.pagi.total = res['total'] || 0;
    this.pagi.page = parseInt(res['page_no']) || 1;
    this.pagi.limit = parseInt(res['limit']) || 100;
  }

  exportNewsletter() {
    this.userS.exportNewsletter().then(
      res => {
        let fileName= getStoreName()+'_newsletter';
       downloadFile(res,fileName);
      },
      err => {
        console.log(err);
        this.alertS.error(this.alertContainer, 'Something wrong!!! Please try again', true, 3000);
      }
    );
  }

}
