<div class="m-subheader">
    <div class="d-flex align-items-center">
        <div class="mr-auto">
            <h3 class="m-subheader__title m-subheader__title--separator">Newsletter</h3>
            <ul class="m-subheader__breadcrumbs m-nav m-nav--inline">
                <li class="m-nav__item m-nav__item--home">
                    <a class="m-nav__link m-nav__link--icon" routerlink="/admin"><i class="m-nav__link-icon la la-home"></i></a>
                </li>
                <li class="m-nav__separator"><i class="fa fa-angle-right"></i></li>
                <li class="m-nav__item">
                    <a class="m-nav__link"><span class="m-nav__link-text"> Newsletter </span></a>
                </li>
            </ul>
        </div>
    </div>
</div>
<div class="custom-alert" #hasAlert></div>
<div class="m-content animated fadeIn">
    <div class="m-portlet m-portlet--mobile">
        <div class="m-portlet__head title-head">
            <div class="m-portlet__head-caption">
                <div class="m-portlet__head-title">
                    <h3 class="m-portlet__head-text">Newsletter List</h3>
                    <div class="add-list-btn text-right">
                        <button class="btn btn-dark btn-sm" type="button" (click)="exportNewsletter()">
                            <i class="m-nav__link-icon la la-cloud-download"></i>
                            <span class="m-nav__link-text"> Export </span>
                          </button>
                        <!-- <button class="btn btn-brand btn-sm" type="button" (click)="exportNewsletter()">
                            <i class="fa fa-plus"></i> Export
                        </button> -->
                    </div>
                </div>
            </div>
        </div>
        <div class="m-portlet__body" style="position: relative;">
            <div class="m-section">
                <div class="m-section__content price-table">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Email</th>
                                    <th>Created</th>
                                </tr>
                            </thead>
                            <tbody *ngIf="newsLetterList.length < 1; else table">
                                <tr *ngIf="!loader">
                                    <td colspan="10">
                                    <h4 class="text-center">No Data Found</h4>
                                    </td>
                                </tr>
                            </tbody>
                            <ng-template #table>
                                <tbody>
                                    <tr class="even-tr" *ngFor="let newsltr of newsLetterList">
                                        <td>{{newsltr?.email}}</td>
                                        <td>{{newsltr?.created | customDate}}</td>
                                    </tr>
                                </tbody>
                            </ng-template>
                        </table>
                    </div>
                    <boot-pagination 
                        [totalSize]="pagi.total" 
                        [page]="pagi.page" 
                        [pagelimit]="pagi.limit" 
                        [listSize]="pagi.limit"
                        (pageChange)="reloadTable($event)">
                    </boot-pagination>
                </div>
            </div>
        </div>
    </div>
</div>