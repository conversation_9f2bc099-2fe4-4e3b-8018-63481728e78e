
<div class="custom-alert" #hasCusAlert></div>
<div class="row">
  <div class="col-md-6 email-checkbox">
    <h4>Emails</h4>
    <div class="form-group">
      <label class="m-checkbox">
        <input type="checkbox" name="" [(ngModel)]="orders" id=""> Orders <span></span>
      </label>
    </div>

    <div class="form-group">
      <label class="m-checkbox">
        <input type="checkbox" name="" [(ngModel)]="contact" id=""> Contact <span></span>
      </label>
    </div>
  </div>
  <div class="col-md-6 email-checkbox">
    <h4>Text/SMS</h4>
    <div class="form-group">
      <label class="m-checkbox">
        <input type="checkbox" name="" [(ngModel)]="text_orders" id=""> Orders <span></span>
      </label>
    </div>

    <div class="form-group">
      <label class="m-checkbox">
        <input type="checkbox" name="" [(ngModel)]="text_contact" id=""> Contact <span></span>
      </label>
    </div>
  </div>

  <!-- <div class="col-md-12">
    <button type="submit" (click)="save()">Save</button>
  </div> -->

  <div class="col-md-12 mt-3">
    <div class="form-group">
      <div *ngIf="loader; else button" class="m-loader m-loader--brand" style="width: 30px; display: inline-block;"></div>
      <ng-template #button>
          <button type="button" (click)="save()"
          class="btn btn-brand btn-sm">Save Change</button>
      </ng-template>
    </div>
  </div>
  <div class="col-md-12 mt-3">
    <p style="font-style: italic;">*Note: Message & data rates charged by your carrier may apply.</p>
    <p style="font-style: italic;">Clients outside of the US & Canada, contact us for SMS function pricing.</p>
  </div>
</div>