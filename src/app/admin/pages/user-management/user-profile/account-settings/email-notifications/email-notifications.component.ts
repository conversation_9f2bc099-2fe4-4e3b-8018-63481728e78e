import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ElementRef, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { User } from '../../../models/user.models';
import { UserService } from '../../../user.service/user.service';
import { AlertService } from './../../../../../../modules/alert/alert.service';

@Component({
  selector: 'app-email-notifications',
  templateUrl: './email-notifications.component.html',
  styleUrls: ['./email-notifications.component.css']
})
export class EmailNotificationsComponent implements OnInit, OnDestroy {

  subs: Subscription[] = [];
  user: User;
  orders: boolean = false;
  contact: boolean = false;
  text_orders: boolean = false;
  text_contact: boolean = false;
  @ViewChild("hasCusAlert") alertContainer: ElementRef;
  loader: boolean;

  constructor(
    private activatedRoute: ActivatedRoute,
    private alertS: AlertService,
    private userS: UserService
  ) {
    this.subs.push(
      this.activatedRoute.data.subscribe((data: any) => {
        if (data.user) {
          this.user = data.user.data;
          if (this.user.email_notifications && this.user.email_notifications.length !== 0) {
            this.orders = this.user.email_notifications.email ? this.user.email_notifications.email.orders : false;
            this.contact = this.user.email_notifications.email ? this.user.email_notifications.email.contact : false;
            this.text_orders = this.user.email_notifications.sms ? this.user.email_notifications.sms.orders : false;
            this.text_contact = this.user.email_notifications.sms ? this.user.email_notifications.sms.contact : false;
          }
        }
      })
    )
  }

  ngOnInit(): void {
  }

  ngOnDestroy(): void {
    this.subs.forEach(sub => sub.unsubscribe());
  }

  save(): void {
    this.loader = true;
    const email_notifications = {
      'email': {
        'orders': this.orders,
        'contact': this.contact
      },
      'sms': {
        'orders': this.text_orders,
        'contact': this.text_contact
      }
    };
    this.userS.setEmailConfigtoUser(email_notifications, this.user.id)
      .then(
        res => {
          this.loader = false;
          if (res.status == 'OK') {
            this.alertS.success(this.alertContainer, 'Successfully Saved', true, 3000);
          } else {
            this.alertS.error(this.alertContainer, 'Something went wrong!', true, 3000);
          }
        }
      ).catch(err => {
        this.loader = false;
        this.alertS.error(this.alertContainer, 'Something went wrong!', true, 3000);
      })
  }
}
