.form-group {
    padding-bottom:1.2rem!important;
}
.add-list-btn{
    display: table-cell; vertical-align: middle;
}
.search-panel .row> div {
    padding-right: 0px;
    padding-left: 0px;;
}
.search-panel select option[data-default] {
    color: #888;
}
.search-panel select option[value=""][disabled] {
    display: none;
}

.custom-alert{
    position: fixed;
    top: 2%;
    right: 30px;
    z-index: 5000;
}

.img-resize{
    max-width: 60px!important;
    height: 60px!important;
    object-fit: contain;
}

/*-- addition all user --*/
@media (max-width: 575px) {    
    .m-portlet .m-portlet__body {        
        padding: 15px 15px;    
    }
}
