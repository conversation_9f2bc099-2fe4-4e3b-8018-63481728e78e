<div class="custom-alert" #hasCusAlert></div>

<div *ngIf="!orderDone; else orderCreated" class="content mt-0">
  <div class="row">
    <div class="col-md-11" *ngIf="!fromOrderDetails">
      <h2 class="ac-title pb-4">
        Payment
      </h2>
    </div>
    <div class="col-md-11">
      <h4>
        Select Payment Type
      </h4>
    </div>
  </div>

  <div class="row">
    <div class="col-md-12">
      <div class="m-form__group form-group">
        <label class="m-radio mr-5">
          <input
            type="radio"
            name="main"
            [value]="Cash"
            (click)="changeMode('Cash')"
            checked
          />
          Cash
          <span></span>
        </label>
        <label
          class="m-radio mr-5 mt-3"
          *ngFor="let gateway of showGateways; let i = index"
        >
          <input
            type="radio"
            name="main"
            [value]="gateway"
            (click)="changeMode(gateway)"
          />
          {{ gateway.name }}
          <span></span>
        </label>
        
      </div>
    </div>
  </div>

  <div class="row" *ngIf="(fromOrderDetails && !additional) || (fromOrderDetails && additional && is_paid)">
    <div class="col-xl-7 col-lg-8 col-md-12">
      <h5>
        Amount to pay*
      </h5>
      <div class="input-group m-input-group mb-4" *ngIf="fromOrderDetails">
        <div class="input-group-prepend">
          <span class="input-group-text" id="table-cost-addon">
            $
          </span>
        </div>
        <input
          id="cost"
          class="form-control m-input"
          id="amount"
          type="text"
          placeholder="0.00"
          name="amount"
          [maxlength]="maxLength"
          numberOnly
          [(ngModel)]="payment_amount_for_order"
          (ngModelChange)="checkAfterOrderPayAmount()"
          autocomplete="off"
          required
          [disabled]="payment_method == 1"
        />
        <div class="input-group-append">
          <span class="input-group-text" id="table-cost-addon">
            USD
          </span>
        </div>
      </div>
      <!-- <div class="form-group col-sm-12 mt-2 pl-0" *ngIf="fromOrderDetails">
        <h5>Change Amount: {{ changeAmount | currency }}</h5>
      </div> -->
    </div>
  </div>

  <div style="width: 100%; height: 100px;" *ngIf="loaderApi">
    <div *ngIf="loaderApi" class="table-load m-loader m-loader--brand"></div>
  </div>
  <div *ngIf="!loaderApi">
    <div *ngIf="is_online">
      <div class="m-portlet__body form-panel" style="margin: 0px;">
        <div *ngIf="mode == 1">
          <div *ngIf="(is_stripeGateway && fromOrderDetails) ||(is_stripeGateway && fromAdminStore)" class="row mt-3">
            <div class="col-md-8">
              <div class="m-form__group form-group">
                <label
                  *ngFor="let card of stripeOldCardList;let i=index"
                  class="m-radio mr-5"
                >
                  <input
                    type="radio"
                    name="stripeCard"
                    value="stripe"
                    (click)="onChangeCardType('oldCard',card.id)"
                  />
                  Pay using ****{{ stripeOldNumber(card) }}
                  <span></span>
                </label>
                <label *ngIf="customer_id != null" class="m-radio mr-5">
                  <input
                    type="radio"
                    name="stripeCard"
                    value="stripe"
                    (click)="onChangeCardType('newCard')"
                  />
                  Pay using new credit card
                  <span></span>
                </label>
                <br />
                <button
                  *ngIf="is_ShowOldCardSubmitbtn && fromOrderDetails"
                  class="btn btn-info"
                  [disabled]="payment_amount_for_order <= 0"
                  (click)="onClickOldCardNumberSubmit()"
                >
                  Submit
                </button>
                

                <div  *ngIf="is_ShowOldCardSubmitbtn && fromAdminStore">
                    <button
                    class="btn btn-info"
                    (click)="onClickOldCardNumberSubmit(false)"
                  >
                  Authorize
                  </button>
                  &nbsp;&nbsp;
                  <button
                  class="btn btn-info"
                  (click)="onClickOldCardNumberSubmit(true)"
                >
                Authorize & capture
                </button>
                </div>
                
              </div>
            </div>
          </div>

          <ng-template #cardConect></ng-template>
        </div>

        <div *ngIf="mode == 2">
          <div
            *ngIf="loader; else bttnSWIPE"
            class="m-loader m-loader--brand"
            style="width: 30px;display: inline-block;"
          ></div>
          <ng-template #bttnSWIPE>
            <div class="mt-3">
                <button
                *ngIf="responseText && !fromOrderDetails"
                type="button"
                (click)="submitSwipe(form)"
                class="btn btn-brand"
              >
                Procced To Order
              </button>
              <button
              *ngIf="fromOrderDetails"
              [disabled]="!payment_amount_for_order"
              type="button"
              (click)="submitSwipe()"
              class="btn btn-brand"
            >
              Process Payment
            </button>
               &nbsp;&nbsp;
              <button
                *ngIf="!responseText && !fromOrderDetails"
                type="button"
                (click)="changeMode({name:'BoltCardConnect',type:'online'})"
                class="btn btn-dark"
              >
                Try Again
              </button>
            </div>
            
          </ng-template>
        </div>
      </div>
    </div>

    <div *ngIf="!is_online">
      <div class="m-portlet__body form-panel" style="margin: 0px;">
        <div *ngIf="mode == 3; then PayLater; else Cash"></div>
      </div>
    </div>

    <ng-template #PayLater>
      <div class="m-portlet__body form-panel" style="margin: 0px;">
        <div class="row">
          <div class="pt-2 col-xl-7 col-lg-8 col-md-12" *ngIf="is_instructions">
            <div style="padding: 0 5px;" [innerHTML]="selectedGateway.config.instructions | safeHtml"></div>
          </div>

          <div class="form-group col-sm-6" *ngIf="!fromOrderDetails && selectedGateway?.config?.is_paid">
            <h5>
              Amount to pay*
            </h5>
            <div class="input-group m-input-group">
              <div class="input-group-prepend">
                <span class="input-group-text" id="table-cost-addon">
                  $
                </span>
              </div>
              <input
                id="cost"
                class="form-control m-input"
                id="amount"
                type="text"
                placeholder="0.00"
                name="amount"
                [maxlength]="maxLength"
                numberOnly
                [(ngModel)]="payment_amount"
                (ngModelChange)="checkPayAmount()"
                autocomplete="off"
                required
                [disabled]="payment_method == 1"
              />
              <div class="input-group-append">
                <span class="input-group-text" id="table-cost-addon">
                  USD
                </span>
              </div>
            </div>
            <!-- <span *ngIf="payment_error">
              <small class="error"
                >Amount should be equal or greater than cart total</small
              >
            </span> -->
          </div>

          <div class="form-group col-sm-6" *ngIf="!fromOrderDetails && selectedGateway?.config?.is_paid">
            <h5>
              Amount Tendered
            </h5>
            <div class="input-group m-input-group" >
              <div class="input-group-prepend">
                <span class="input-group-text" id="table-cost-addon">
                  $
                </span>
              </div>
              <input
                id="cost"
                class="form-control m-input"
                id="amount"
                type="text"
                placeholder="0.00"
                name="amount_tendered"
                numberOnly
                [(ngModel)]="amount_tendered"
                (ngModelChange)="checkPayAmount()"
                autocomplete="off"
                required />
              <div class="input-group-append">
                <span class="input-group-text" id="table-cost-addon">
                  USD
                </span>
              </div>
            </div>
            <span *ngIf="payment_error && !fromOrderDetails">
              <small class="error"
                >Amount should be equal or greater than cart total</small
              >
            </span>

            <div class="form-group col-sm-12 mrgn-cs" *ngIf="!fromOrderDetails">
              <h5>Change Amount: {{ changeAmount | currency }}</h5>
            </div>
          </div>

          <div class="form-group col-xl-7 col-lg-8 col-md-12" *ngIf="is_note_required">
            <label for="">Note </label>
            <input
              [(ngModel)]="note"
              autocomplete="off"
              maxlength="155"
              class="form-control"
              type="text"
              name="note"
            />
          </div>
          <div class="form-group col-sm-12">
            <div
              *ngIf="loader; else bttnOff"
              class="m-loader m-loader--brand"
              style="width: 30px;display: inline-block;"
            ></div>
            <ng-template #bttnOff>
              <button
                *ngIf="is_note_required"
                [disabled]="!note"
                type="button"
                (click)="offlinePayment(true)"
                class="btn btn-brand"
              >
                Submit
              </button>

              <button
                *ngIf="!is_note_required"
                type="button"
                (click)="offlinePayment(true)"
                class="btn btn-brand"
              >
                Submit
              </button>
            </ng-template>
          </div>
        </div>
      </div>
    </ng-template>

    <ng-template #Cash>
      <div class="m-portlet__body form-panel" style="margin: 0px;">
        <form #formOffline="ngForm" class="row">
          <div class="row" style="margin: 0px;">
            <div class="form-group col-sm-12" *ngIf="!fromOrderDetails">
              <h5>Cart Total: {{ cartItems.total | currency }}</h5>
            </div>
            <div class="form-group col-sm-6">
              <h5 *ngIf="!fromOrderDetails">
                Amount to pay*
              </h5>
              <div class="input-group m-input-group" *ngIf="!fromOrderDetails">
                <div class="input-group-prepend">
                  <span class="input-group-text" id="table-cost-addon">
                    $
                  </span>
                </div>
                <input
                  id="cost"
                  class="form-control m-input"
                  id="amount"
                  type="text"
                  placeholder="0.00"
                  name="amount"
                  [maxlength]="maxLength"
                  numberOnly
                  [(ngModel)]="payment_amount"
                  (ngModelChange)="checkPayAmount()"
                  autocomplete="off"
                  required
                  [disabled]="payment_method == 1"
                />
                <div class="input-group-append">
                  <span class="input-group-text" id="table-cost-addon">
                    USD
                  </span>
                </div>
              </div>
              <!-- <span *ngIf="payment_error && !fromOrderDetails">
                <small class="error"
                  >Amount should be equal or greater than cart total</small
                >
              </span> -->
            </div>

            <div class="form-group col-sm-6">
              <h5 *ngIf="!fromOrderDetails">
                Amount Tendered
              </h5>
              <div class="input-group m-input-group" *ngIf="!fromOrderDetails">
                <div class="input-group-prepend">
                  <span class="input-group-text" id="table-cost-addon">
                    $
                  </span>
                </div>
                <input
                  id="cost"
                  class="form-control m-input"
                  id="amount"
                  type="text"
                  placeholder="0.00"
                  name="amount_tendered"
                  numberOnly
                  [(ngModel)]="amount_tendered"
                  (ngModelChange)="checkPayAmount()"
                  autocomplete="off"/>
                <div class="input-group-append">
                  <span class="input-group-text" id="table-cost-addon">
                    USD
                  </span>
                </div>
              </div>
              <span *ngIf="payment_error && !fromOrderDetails">
                <small class="error"
                  >Amount should be equal or greater than cart total</small
                >
              </span>
            </div>

            <div class="form-group col-sm-12" *ngIf="!fromOrderDetails">
              <h5>Change Amount: {{ changeAmount | currency }}</h5>
            </div>
            <div class="form-group col-sm-12">
              <div
                *ngIf="loader; else bttnOff"
                class="m-loader m-loader--brand"
                style="width: 30px;display: inline-block;"
              ></div>
              <ng-template #bttnOff>
                <button
                  type="button"
                  (click)="offlinePayment()"
                  [disabled]="!formOffline.form.valid || payment_error"
                  class="btn btn-brand"
                >
                  Submit
                </button>
              </ng-template>
            </div>
          </div>
        </form>
      </div>
    </ng-template>
  </div>
  <div *ngIf="!fromOrderDetails">
    <app-order-summary
      [cartItems]="cartItems"
      [order_id]="order_id"
    ></app-order-summary>
  </div>
  <div class="row" *ngIf="!fromOrderDetails">
    <div class="col-md-1 text-right">
      <a
        (click)="backToShipping()"
        class="btn btn-dark caps mr-5 "
        style="margin-left:0px!important; float:left; color: #fff"
      >
        <i class="fa fa-backward"></i>&nbsp;Back</a
      >
    </div>
  </div>
</div>

<ng-template #orderCreated>
  <app-order-summary
    [quote]="quote"
    [cartItems]="cartItems"
    [order_id]="order_id"
  ></app-order-summary>
</ng-template>
