import { StripeCheckoutV3Component } from './../../../../../modules/stripe-checkout-v3/stripe-checkout-v3.component';
import { StripeCheckoutV3Module } from './../../../../../modules/stripe-checkout-v3/stripe-checkout-v3.module';
import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { PaymnetSettingsComponent } from "./paymnet-settings.component";
import { OrderSummaryComponent } from "./order-summary/order-summary.component";
import { OrderService } from "../../../reservations/order.service/order.service";
import { CardConnectCheckoutAdminModule } from "../../../../payment-gateway/card-connect-checkout/card-connect-checkout.module";
import { CardConnectCheckoutComponent } from "../../../../payment-gateway/card-connect-checkout/card-connect-checkout.component";
import { AuthorizeDotNetCheckoutAdminModule } from "../../../../payment-gateway/authorizeDotNet-checkout/authorizeDotNet-checkout.module";
import { AuthorizeDotNetCheckoutComponent } from "../../../../payment-gateway/authorizeDotNet-checkout/authorizeDotNet-checkout.component";
import { PaypalCheckoutAdminModule } from "../../../../payment-gateway/paypal-checkout/paypal-checkout.module";
import { PaypalCheckoutComponent } from "../../../../payment-gateway/paypal-checkout/paypal-checkout.component";
import { GoMerchantCheckoutModule } from "../../../../payment-gateway/go-merchant-checkout/go-merchant-checkout.module";
import { GoMerchantCheckoutComponent } from "../../../../payment-gateway/go-merchant-checkout/go-merchant-checkout.component";
import { SafeHtmlModule } from './../../../../../modules/safe-html/safe-html.pipe';
import { DateFormatModule } from './../../../../../modules/date-format/date-format-pipe';



@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    CardConnectCheckoutAdminModule,
    StripeCheckoutV3Module,
    PaypalCheckoutAdminModule,
    AuthorizeDotNetCheckoutAdminModule,
    GoMerchantCheckoutModule,
    SafeHtmlModule,
    DateFormatModule
  ],
  declarations: [PaymnetSettingsComponent,OrderSummaryComponent],
  exports: [PaymnetSettingsComponent,OrderSummaryComponent
  ],
  providers: [OrderService],
  entryComponents: [
    StripeCheckoutV3Component,
    PaypalCheckoutComponent,
    CardConnectCheckoutComponent,
    AuthorizeDotNetCheckoutComponent,
    GoMerchantCheckoutComponent
  ]
})
export class PaymentSettingCheckoutModule {}
