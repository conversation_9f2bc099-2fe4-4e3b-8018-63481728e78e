import { Component, OnInit, Input } from '@angular/core';
import { product_image, EndPoint } from '../../../../../../globals/endPoint/config';
import { Router } from '@angular/router';
import { CartService } from '../../../../../../admin/cart-service/cart.service';
import { removeCartDate, isJson } from '../../../../../../globals/_classes/functions';
import { HttpService } from '../../../../../../modules/http-with-injector/http.service';
import { SettingService } from '../../../../../../admin/pages/settings/setting-service/setting.service';

@Component({
  selector: 'app-order-summary',
  templateUrl: './order-summary.component.html',
  styleUrls: ['./order-summary.component.css']
})
export class OrderSummaryComponent implements OnInit {

  @Input('cartItems') cartItems;
  @Input('order_id') order_id;
  @Input() quote: boolean;
  api = EndPoint;
  img_url = product_image;

  constructor(
    private router: Router,
    private cartS: CartService,
    public http: HttpService,
    private settingService:SettingService
  ) { }

  ngOnInit() {
    this.setContent();
    sessionStorage.setItem('cartId',this.order_id);
    removeCartDate('adminOrder')

  }

  setContent(){
    this.http.get('contents').toPromise()
        .then(res => {
            if (res.status === 'OK' && res.result.data.length > 0) {
                const content = {};
                const data = res.result.data.filter(f => {
                    return f['config'].status === 1;
                });

                for (const c of data) {
                    const tag = this.formatTag(c.config.tag);
                    content[tag] = isJson(c.contents) ? JSON.parse(c.contents) : c.contents;
                }

                const total=content['site_specific']['confg']['order']['total_order'];
                this.settingService.onOrderLimitChange({updateOrderLimit:true,total_orders:total});

                return content;
            } else {
                return {};
            }
        }).catch(err => console.log(err));
  }

  formatTag(text: String) {
    return text.replace(/(\-[a-z])/g, function ($1) { return $1.toUpperCase().replace('-', ''); });
  }


  gotoDetails() {
    sessionStorage.setItem('cartId',null);
    sessionStorage.removeItem('cartList');
    this.router.navigate([`/admin/reservations/${this.order_id}/details`], {
      queryParams: {
        source: "admin_checkout"
      }
    });
  }


  get showStartTime() {
    const contents = localStorage.getItem("contents")
      ? JSON.parse(localStorage.getItem("contents"))
      : null;
    if (
      contents.site_specific.confg &&
      contents.site_specific.confg.hasOwnProperty("show_start_time")
    ) {
      return contents.site_specific.confg.show_start_time;
    }
    return true;
  }

  getDate(d) {
    return this.cartS.formateListDate(d);
  }

}
