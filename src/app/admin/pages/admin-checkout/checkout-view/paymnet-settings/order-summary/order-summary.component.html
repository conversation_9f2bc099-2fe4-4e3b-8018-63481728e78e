<section
  class="main-content mrt-md mrb-md animated fadeIn"
  [ngClass]="{ content: order_id }"
>
<!-- <h2 class="ac-title ac-ordertitle">
    Order Summery
</h2> -->
  <div class="container-fluid">
    <div class="row justify-content-end">
      <div class="table-responsive col-12" style="padding-top: 20px;">
        <table class="table cart">
          <thead>
            <tr>
              <th></th>
              <th>Product</th>
              <th>Unit Price</th>
              <th>Quantity</th>
              <!-- <th>Sales Tax</th> -->
              <th>Subtotal</th>
            </tr>
          </thead>
          <tbody>
            <ng-container
              class="list"
              *ngFor="let cart of cartItems.cart_items; let i = index"
            >
              <tr>
                <td>
                  <img
                    class="img-fluid img-avatar img-thumbnail"
                    *ngIf="cart.product.images.length > 0"
                    style="max-width:50px;"
                    src="{{ img_url }}{{ cart.store_id }}/{{
                      cart.product.id
                    }}/{{ cart.product.images[0].image_small }}"
                    onError="this.src='./assets/img/home/<USER>';"
                  />

                  <img
                    class="img-fluid img-avatar img-thumbnail"
                    *ngIf="cart.product.images.length == 0"
                    style="max-width:50px;"
                    src="./assets/img/home/<USER>"
                    alt="Product profile"
                  />
                </td>
                <td>
                  <div class="product">
                    {{ cart.product.name }}
                    <br />
                    <span *ngIf="
                    cart.product.variant_chain &&
                    cart.product.variant_chain != 'Unassigned: Unassigned'">{{cart.product.variant_chain}}</span>
                    <br />
                    <small *ngIf="cart?.rent_start">
                      From : {{  cart?.rent_start | customDate: 'rental' }}
                    </small>
                    <br />
                    <small *ngIf="cart?.rent_end">
                       To : {{  cart?.rent_end | customDate: 'rental' }}
                    </small>
                   
                      
                  </div>
                  <div *ngIf="cart.deposit_amount > 0" class="deposit">
                    Deposit Amount: {{ cart.deposit_amount | currency }}
                    {{ cart.deposite_tax == "true" ? "(Including Tax)" : "" }}
                  </div>
                  <!-- package Code  -->
                  <ng-container
                    *ngIf="cart.product_type === 2 && cart.products.length"
                  >
                    <h6 class="pt-3 pb-1">Package Includes</h6>
                    <div class="mb-2" *ngFor="let product of cart.products">
                      <div style="font-size:13px;">
                        {{ product.name }}({{ product.quantity }})
                      </div>
                      <small
                        *ngIf="
                          product.variant_chain !== 'Unassigned: Unassigned'
                        "
                        >{{ product.variant_chain }}</small
                      >
                    </div>
                  </ng-container>
                </td>
                <td>{{ cart.price | currency }}</td>
                <td>{{ cart.quantity }}</td>
                <td>
                  {{ cart.sub_total | currency }}
                </td>
              </tr>

              <ng-container *ngFor="let addon_item of cart?.products">
                <tr *ngIf="addon_item.product_type==3">
                  <td class="cart-img">
                    <img *ngIf="
                    addon_item.image != '';
                        else placeholder
                      " class="img-fluid img-avatar img-thumbnail img-resize" src="{{ img_url }}{{ item?.store_id }}/{{
                        addon_item?.id
                      }}/{{
                        addon_item?.image
                      }}" onError="this.src='./assets/img/home/<USER>';" />
                    <ng-template #placeholder>
                      <img class="img-fluid img-avatar img-thumbnail"
                        src="./assets/img/home/<USER>" />
                    </ng-template>
                  </td>
                  <td>
                    <div class="product p-0">
                      {{ addon_item.name }}
      
                      <div class="p_variants">
                        <span *ngIf="
                           addon_item.variant_chain !=
                              'Unassigned: Unassigned'
                            ">
                          <small>{{ addon_item.variant_chain }} </small>
                        </span>
                      </div>
                    </div>
                  </td>
                  <td></td>
                  <td>
                    <p>{{ addon_item?.quantity }}</p>
                  </td>
                  <td></td>
                </tr>
              </ng-container>
              
            </ng-container>
          </tbody>
        </table>
      </div>
      <div class="col-xl-6 col-lg-6 col-md-8 col-sm-10 col-12">
      <div class="order_summery">
        <div *ngIf="order_id" class="order-download-details">
          <a href="{{ api }}pages/pdf?order_id={{ order_id }}" class="btn-orange"><i class="fa fa-file-pdf-o"></i> Download</a>
          <a (click)="gotoDetails()" title="Order Details" class="btn-brand"><i class="fa fa-eye"></i> Details</a>
        </div>
        <h4 *ngIf="order_id">
          {{quote ? 'Quote' : 'Order'}} Summary
        </h4>
        <h4 *ngIf="!order_id" style="margin:auto;">Order Summary</h4>
        <div class="table-responsive">
          <table class="table cart">
            <tbody>
              <tr>
                <td>
                  Subtotal
                </td>
                <td>
                  <span class="cart_p">
                    {{ cartItems.sub_total | currency }}</span
                  >
                </td>
              </tr>
              <tr>
                <td>
                  Delivery Charge
                </td>
                <td>
                  <span class="cart_p">{{
                    cartItems.delivery_charge | currency
                  }}</span>
                </td>
              </tr>
              <tr>
                <td>
                  Discount
                </td>
                <td>
                  <span class="cart_p">{{
                    cartItems.total_discount | currency
                  }}</span>
                </td>
              </tr>
              <tr>
                <td>
                  Sales Tax
                </td>
                <td>
                  <span class="cart_p"> {{ cartItems.tax | currency }}</span>
                </td>
              </tr>
              <tr>
                <td>
                  Delivery Tax
                </td>
                <td>
                  <span class="cart_p">
                    {{ cartItems.delivery_tax | currency }}</span
                  >
                </td>
              </tr>
              <tr>
                <td>
                  Total Deposit Amount
                </td>
                <td>
                  <span class="cart_p">{{
                    cartItems.deposit_amount | currency
                  }}</span>
                </td>
              </tr>
              <tr>
                <td>
                  Additional charge
                </td>
                <td>
                  <span class="cart_p">{{
                    cartItems.additional_charge | currency
                  }}</span>
                </td>
              </tr>
              <tr>
                <td>
                  <h5>Total</h5>
                </td>
                <td>
                  <h5>
                    <span class="cart_p">
                      {{ cartItems.total | currency }}</span
                    >
                  </h5>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      </div>
    </div>
  </div>
</section>
