import { StripeCheckoutV3Component } from './../../../../../modules/stripe-checkout-v3/stripe-checkout-v3.component';
import {
  Component,
  OnInit,
  ViewChild,
  ElementRef,
  Output,
  Input,
  EventEmitter,
  OnDestroy,
  ViewContainerRef,
  ComponentFactoryResolver,
  Type,
  ComponentRef,
  OnChanges
} from "@angular/core";
import { Router } from "@angular/router";

import { Subscription } from "rxjs";
import { AlertService } from "../../../../../modules/alert/alert.service";
import { CartService } from "../../../../cart-service/cart.service";
import {
  formateRentType,
  GET_USER,
  iframe
} from "../../../../../globals/_classes/functions";
import { OrderService } from "./../../../reservations/order.service/order.service";
import { Helpers } from "../../../../../helpers";
// import { StripeCheckoutComponent } from "../../../../payment-gateway/stripe-checkout/stripe-checkout.component";
import { CardConnectCheckoutComponent } from "../../../../payment-gateway/card-connect-checkout/card-connect-checkout.component";
import { AuthorizeDotNetCheckoutComponent } from "../../../../payment-gateway/authorizeDotNet-checkout/authorizeDotNet-checkout.component";
import { PaypalCheckoutComponent } from "../../../../payment-gateway/paypal-checkout/paypal-checkout.component";
import { GoMerchantCheckoutComponent } from "../../../../payment-gateway/go-merchant-checkout/go-merchant-checkout.component";
import { PLATFORM_ID,Inject } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';

@Component({
  selector: "app-paymnet-settings",
  templateUrl: "./paymnet-settings.component.html",
  styleUrls: ["./paymnet-settings.component.css"]
})
export class PaymnetSettingsComponent implements OnInit, OnChanges {
  loader: boolean;
  cartItems: any;
  payment_amount: any = 0;
  payment_amount_for_order: any = 0;
  due_amount: any = 0;
  total_order_amount = 0;
  is_online = true;
  is_NoteAllow = true;
  mode = 3;
  paymentInfo = new Object();
  oflinePaymentmethod = ["Paid in Cash", "Paid in Credit"];
  any;
  allGateways = [];
  showGateways = [];
  order_id: number;
  orderDone: boolean;
  payment_method = "Paid in Cash";
  sessionEmpty = [
    "cartList",
    "cartToken",
    "billInfo",
    "orderId",
    "inStore",
    "log",
    "item_log",
    "coupon",
    "deliveryCharge",
    "return_to"
  ];
  payment_error: boolean;
  sub: Subscription[] = [];
  changeAmount = 0;
  responseText;
  loaderApi: boolean;
  token: number;
  maxLength: number = 15;
  terminalHSN;
  boltSub: Subscription;
  bolt: boolean;
  note: string;
  componentRef: ComponentRef<any>;
  gateImage: string = "";
  inStore;
  is_ShowOldCardSubmitbtn = false;
  is_stripeGateway;
  stripeCardNumber;
  customer_id;
  is_stripe_property_exist;
  paymentType;
  stripeOldCardList = [];
  oldStripeCardToken;
  customer_email;
  alertForFreeAccount = {};
  additional_gateway = [];
  additional = false;

  @Output() openShipping = new EventEmitter();
  @Output("cart") cart: EventEmitter<any> = new EventEmitter();
  @Input("fromOrderDetails") fromOrderDetails: boolean;
  @Input("fromAdminStore") fromAdminStore: boolean;
  @Input("orderId") orderId: number;
  @Input("quote") quote: boolean;
  @Input() quoteResponse: any;
  @ViewChild("cardConect", { read: ViewContainerRef })
  cardConect: ViewContainerRef;
  @ViewChild("hasCusAlert") alertContainer: ElementRef;
  selectedGateway: any;
  is_note_required: boolean = true;
  is_instructions = false;
  amount_tendered: number;
  is_paid: any;

  constructor(
    @Inject(PLATFORM_ID) private platform: Object,
    private alert: AlertService,
    private router: Router,
    public cartS: CartService,
    private resolver: ComponentFactoryResolver,
    public orderS: OrderService
  ) {
    this.getAdditionalGateway();
  }

  ngOnChanges() {
    if (this.quote) {
      this.orderDone = true;
    }
    if (this.quoteResponse) {
      if (this.quoteResponse.payment.success) {
        this.order_id = this.quoteResponse.payment.order_id;
        // this.printRecipet();
        this.orderDone = true;
        this.cart.emit();
        this.token = null;
        this.reloadCanlender();
        setTimeout(() => {
          this.clearCart();
        });
      }
    }
  }

  ngOnInit() {
    this.getPaymentGateway();

    if (this.fromOrderDetails) {
      this.payment_amount = 0;
      sessionStorage.setItem(
        "fromOrderDetails",
        JSON.stringify(this.fromOrderDetails)
      );

      this.cartItems = [];
      this.orderS.getPaymentAmountSummary(this.orderId).subscribe(
        res => {
          const paid_amount = res.paid || 0;
          this.total_order_amount = res.total || 0;
          this.due_amount = res.due || 0;
          // this.payment_amount_for_order = this.due_amount;
        },
        err => console.log(err)
      );

      this.customer_id = JSON.parse(sessionStorage.getItem("customer_id"));
      this.customer_id = parseInt(this.customer_id);
      if (!isNaN(this.customer_id)) {
        this.getSripeDetails();
      }
    } else if (this.fromAdminStore) {
      sessionStorage.setItem("fromOrderDetails", JSON.stringify(false));
      this.cartItems = this.cartS.getSessionData("cartList");
      this.getBillInfo();

      this.customer_email = sessionStorage.getItem("customer_email");

      if (
        this.customer_email != null &&
        this.customer_email != "" &&
        this.customer_email != undefined
      ) {
        this.getSripeDetails();
      }
    }

    this.cartS.boltOn.subscribe(val => {
      if (val) {
        this.boltSub.unsubscribe();
        this.loaderApi = false;
        this.responseText = null;
      }
    });

    this.changeMode("Cash");

    //check plan type
    const user = localStorage.getItem("currentUser")
      ? JSON.parse(localStorage.getItem("currentUser"))
      : {};

    if (user != null && user.subscription.hasOwnProperty("account_type")) {
      this.alertForFreeAccount = {
        message:
          user.subscription.account_type == "FREE"
            ? "Orders are for testing only. Order will not be fulfilled."
            : ""
      };
    }

    setTimeout(t => {
      this.alertForFreeAccount = null;
    }, 3000);
  }

  private getAdditionalGateway() {
    const gateway = JSON.parse(localStorage.getItem("gateway-items"));
    if (gateway) this.additional_gateway = gateway.additional;
  }

  backToShipping() {
    this.openShipping.emit(true);
  }

  ngOnDestroy() {
    for (let s of this.sub) {
      s.unsubscribe();
    }
  }

  getPaymentGateway() {
    this.cartS.getGateways().subscribe(res => {
      this.allGateways = res;
      if (this.fromOrderDetails) {
        const is_online = this.allGateways.some(item => item.type === "online");
        if (is_online) {
          const onlineGateway = {
            is_online: true,
            name: "Credit card",
            status: 1,
            type: "online"
          };
          this.showGateways = [onlineGateway];
        }
        const offline_gateways = this.allGateways
          .filter(gateway => gateway.type === "offline")
          .map(gateway => {
            return {
              is_online: false,
              name: gateway.name,
              status: gateway.status,
              type: "offline",
              config: gateway.config ? gateway.config : null,
              additional: true,
              id: gateway.id
            };
          });
        if (offline_gateways.length > 0) {
          offline_gateways.forEach(gateway => {
            this.showGateways.push(gateway);
          });
        }
        console.log(this.showGateways);
        // all payment gateways will be active from after order payment.
        const cardSwipeObj = this.allGateways.find(
          item => item.name === "BoltCardConnect"
        );
        if (cardSwipeObj) {
          this.showGateways = [cardSwipeObj, ...this.showGateways];
        }
      } else {
        const is_online = this.allGateways.some(item => item.type === "online");
        this.showGateways = this.allGateways.filter(
          item => item.type === "offline"
        );
        if (is_online) {
          const onlineGateway = {
            is_online: true,
            name: "Credit card",
            status: 1,
            type: "online"
          };
          this.showGateways = [onlineGateway, ...this.showGateways];
        }
        const cardSwipeObj = this.allGateways.find(
          item => item.name === "BoltCardConnect"
        );
        if (cardSwipeObj) {
          this.showGateways = [cardSwipeObj, ...this.showGateways];
        }
      }

      console.log(this.showGateways);
    });
  }

  checkPayAmount() {
    let index = this.payment_amount.indexOf(".");
    this.maxLength = index < 0 ? 15 : index + 3;
    const totalAmount =
      typeof this.cartItems.total === "string"
        ? parseFloat(this.cartItems.total)
        : this.cartItems.total;
    const pay = this.amount_tendered;
    if (pay < totalAmount) {
      this.payment_error = true;
    } else {
      this.payment_error = false;
    }

    this.changeAmount = pay - totalAmount;
  }

  checkAfterOrderPayAmount() {
    // let index = this.payment_amount_for_order.indexOf(".");
    // this.maxLength = index < 0 ? 15 : index + 3;
    // const totalAmount = this.due_amount;
    // const pay =
    //   typeof this.payment_amount_for_order === "string"
    //     ? parseFloat(this.payment_amount_for_order)
    //     : this.payment_amount_for_order;
    // this.changeAmount = pay - totalAmount;
    // if (this.changeAmount < 0) {
    //   this.changeAmount = 0;
    // }
  }

  getBillInfo() {
    this.sub[0] = this.cartS.payment.subscribe(val => {
      if (val) {
        this.paymentInfo = this.cartS.getSessionData("billInfo");
        console.log(this.paymentInfo);
        //  this.paymentInfo["type"] = parseInt(this.paymentInfo["type"]);
        this.paymentType = parseInt(this.paymentInfo["type"]);

        this.orderDone = false;
        // this.getTotalAmount();
      }
    });
  }

  removeShippingAddress() {
    for (let key in this.paymentInfo) {
      if (key.includes("shipping")) {
        delete this.paymentInfo[key];
      }
    }
  }
  getNewAddress() {
    this.paymentInfo = this.cartS.getSessionData("billInfo");
  }

  getDate(d) {
    return this.cartS.formateListDate(d);
  }

  getType(d, t) {
    return d && t ? formateRentType(d, t) : "";
  }

  changeMode(mode) {
    this.selectedGateway = mode;
    if (this.selectedGateway.type === 'offline') {
      this.is_note_required = this.selectedGateway.config ? (this.selectedGateway.config.hasOwnProperty('add_note') ? this.selectedGateway.config.add_note : true) : true;
      this.is_instructions = this.selectedGateway.config ? (this.selectedGateway.config.instructions ? true : false) : false;
      this.is_paid = this.selectedGateway.config ? (this.selectedGateway.config.hasOwnProperty('is_paid') ? this.selectedGateway.config.is_paid : false) : false;
    }
    if (mode == "Cash") {
      this.additional = false;
      sessionStorage.setItem("activeGateway", null);
    } else if (mode.id !== "" && mode.id != undefined) {
      // localStorage.setItem("gatewayId", mode.id);
      sessionStorage.setItem("activeGateway", JSON.stringify(mode));
    }

    this.loaderApi = false;
    if (mode.name === "BoltCardConnect" && mode.type === "online") {
      this.mode = 2;
      this.additional = false;
      //  this.paymentInfo["type"] = 1;
      this.paymentType = 1;
      this.is_online = true;
      if (GET_USER().terminal_id) {
        if (!this.fromOrderDetails) {
          this.applyBackdrop();
          $(".bacdrop-payment").removeClass("dis-none");
          $(".bacdrop-payment").addClass("dis-block");
          this.loaderApi = true;
          this.swipeApiCall();
        }
      } else {
        this.alert.info(
          this.alertContainer,
          "Terminal is not selected. Please select terminal",
          true,
          3000
        );
        this.token = null;
      }
    } else if (mode.name === "Credit card") {
      this.additional = false;
      this.mode = 1;
      //  this.paymentInfo["type"] = 1;
      this.paymentType = 1;
      this.is_online = true;
      setTimeout(() => {
        this.showOnlineGateway();
      }, 100);
    } else if (mode.name === "Pay Later") {
      // this.paymentInfo["type"] = 2;
      this.additional = true;
      this.paymentType = 2;
      this.is_online = false;
      this.mode = 3;
      this.payment_amount = this.cartItems.total;

      mode.config.add_note === false
        ? (this.is_NoteAllow = false)
        : (this.is_NoteAllow = true);
    } else if (mode === "Cash") {
      // this.paymentInfo["type"] = 2;
      this.additional = false;
      this.paymentType = 2;
      this.is_online = false;
      this.mode = 4;
      this.payment_amount = this.cartItems.total;

      if (!this.fromOrderDetails) {
        let index = this.payment_amount.indexOf(".");
        this.maxLength = index < 0 ? 15 : index + 3;
      } else {
        this.maxLength = 15;
      }
    } else {
      this.additional = true;
      //  this.paymentInfo["type"] = 2;
      this.paymentType = 2;
      this.is_online = false;
      this.mode = 3;
      this.payment_amount = this.cartItems.total;

      mode.config.add_note === false
        ? (this.is_NoteAllow = false)
        : (this.is_NoteAllow = true);
    }

    if (this.fromOrderDetails) {
      this.changeAmount = 0;

      this.orderS.getPaymentAmountSummary(this.orderId).subscribe(
        res => {
          const paid_amount = res.paid || 0;
          this.total_order_amount = res.total || 0;
          this.due_amount = res.due || 0;
          // this.payment_amount_for_order = this.due_amount;
        },
        err => console.log(err)
      );
    }
  }

  applyBackdrop() {
    // if (isPlatformBrowser(this.platform)) {
    // const w = $(window).width();
    // const h = $(window).height();
    // $(".bacdrop-payment").css("width", w);
    // $(".bacdrop-payment").css("height", h);
    // }
  }

  swipeApiCall() {
    this.loader = true;
    const data = {};

    if (this.fromOrderDetails) {
      let customer_id = JSON.parse(sessionStorage.getItem("customer_id"));
      var activeGateWay = JSON.parse(sessionStorage.getItem("activeGateway"));
      let activeGateWayName = activeGateWay === null ? "" : activeGateWay.name;
      let activeGateWayId = activeGateWay === null ? "" : activeGateWay.id;

      data["location_id"] = GET_USER().location_id;
      data["amount"] = this.payment_amount_for_order;
      data["terminal_id"] = GET_USER().terminal_id;
      data["order_id"] = this.order_id;
      data["gateway_id"] = activeGateWayId;
      data["payment_gateway_name"] = activeGateWayName;
      data["customer_id"] = customer_id;
    } else {
      data["location_id"] = GET_USER().location_id;
      data["amount"] =
        typeof this.cartItems.total == "string"
          ? parseFloat(this.cartItems.total)
          : this.cartItems.total;
      data["terminal_id"] = GET_USER().terminal_id;
      data["token"] = this.cartS.getSessionData("cartToken");
    }

    this.boltSub = this.cartS.getCreditCardSwipe(data).subscribe(
      res => {
        this.loader = false;
        this.loaderApi = false;
        if (res.data.success) {
          this.responseText = res.data.payment.response_text;
          this.terminalHSN = res.data.payment.terminal;
          this.alert.success(
            this.alertContainer,
            res.data.response_text,
            true,
            3000
          );
        } else {
          this.responseText = null;
          this.alert.error(
            this.alertContainer,
            res.data.response_text,
            true,
            3000
          );
        }
        const log = this.cartS.getSessionData("log")
          ? this.cartS.getSessionData("log")
          : [];
        log.push(res.data.log_text);
        this.cartS.setSessionData("log", log);
        $(".bacdrop-payment").removeClass("dis-block");
        $(".bacdrop-payment").addClass("dis-none");
      },
      err => {
        this.loader = false;
        this.loaderApi = false;
        console.log(err);
        $(".bacdrop-payment").removeClass("dis-block");
        $(".bacdrop-payment").addClass("dis-none");
        this.loaderApi = false;
        this.responseText = null;
        this.alert.error(
          this.alertContainer,
          "Your transaction failed. Please try again",
          true,
          3000
        );
      }
    );
  }

  submit(e) {
    this.paymentInfo = Object.assign(this.paymentInfo, e);
    this.submitPayment();
  }

  submitSwipe(f) {
    if (this.fromOrderDetails) {
      this.swipeApiCall();
    } else {
      this.loader = true;
      this.paymentInfo["swipe"] = true;
      this.paymentInfo["response_text"] = this.responseText;
      this.paymentInfo["terminal"] = this.terminalHSN;
      this.paymentInfo["log_text"] = this.cartS.getSessionData("log")
        ? this.cartS.getSessionData("log")
        : [];
      this.paymentInfo["payment_amount"] = this.cartItems.total;
      this.submitPayment();
    }
  }

  offlinePayment(additional?: boolean) {
    this.loader = true;
    let terminal = GET_USER().terminal_id;
    this.paymentInfo["terminal_id"] = terminal === null ? "" : GET_USER().terminal_id;
    if (additional) {
      this.submitPayment(true);
    } else {
      this.submitPayment();
    }
  }

  submitPayment(additional?: boolean) {
    this.paymentInfo["type"] = this.paymentType;
    if (this.fromOrderDetails) {
      let customer_id = JSON.parse(sessionStorage.getItem("customer_id"));

      this.paymentInfo["currency"] = "USD";
      this.paymentInfo["amount"] = this.payment_amount_for_order;
      this.paymentInfo["is_admin"] = true;
      this.paymentInfo["customer_id"] = customer_id;
      if (additional) {
        this.paymentInfo = {};
        this.paymentInfo["note"] = this.note;
        this.paymentInfo["payment_gateway_name"] = this.selectedGateway.name;
        this.paymentInfo["gateway_id"] = this.selectedGateway.id;
        this.paymentInfo["type"] = 3;
        if (this.is_paid) {
          this.paymentInfo["currency"] = "USD";
          this.paymentInfo["amount"] = this.payment_amount_for_order;
          this.paymentInfo["amount_tendered"] = this.amount_tendered;
        }
      }
      this.cartS
        .addPaymentFromOrderDetails(this.orderId, this.paymentInfo)
        .then(res => {
          this.paymentInfo = {};
          if (res.status == "OK") {
            this.loader = false;
            if (this.componentRef) {
              this.componentRef.instance.loader = false;
            }
            this.alert.success(
              this.alertContainer,
              res.result.message,
              true,
              3000
            );
            this.getSripeDetails();
            this.orderS.loadSidebarPayment(true);
          } else if (res.status == "NOK") {
            if (this.componentRef) {
              this.componentRef.instance.loader = false;
            }
            this.loader = false;
            this.alert.error(
              this.alertContainer,
              res.result.message,
              true,
              3000
            );
          }
        })
        .catch(err => {
          console.log(err);
          const errorLog = err.error;
          this.paymentInfo = {};
          this.loader = false;
          if (this.componentRef) {
            this.componentRef.instance.loader = false;
          }
          if (errorLog.status === "NOK") {
            if (
              errorLog.result.hasOwnProperty("message") &&
              errorLog.result.message !== ""
            ) {
              this.alert.error(
                this.alertContainer,
                errorLog.result.message,
                true,
                3000
              );
            } else {
              this.alert.error(
                this.alertContainer,
                "Payment is failed. Please try again.",
                true,
                3000
              );
            }
          } else {
            this.alert.error(
              this.alertContainer,
              "Payment is failed. Please try again.",
              true,
              3000
            );
          }
        });
    } else {
      const delivery = this.cartS.getSessionData("deliveryCharge");

      const shipping_method = localStorage.getItem("admin_shipping_method")
        ? JSON.parse(localStorage.getItem("admin_shipping_method"))
        : "";

      if (delivery) {
        this.paymentInfo["delivery"] = delivery;
        if (delivery.hasOwnProperty("type") && delivery.type === "instore") {
          this.removeShippingAddress();
        }
      }

      const customFields = this.cartS.getSessionData("custom_values_inStore");
      if (customFields) {
        this.paymentInfo["custom_values"] = customFields;
      }

      var activeGateWay = JSON.parse(sessionStorage.getItem("activeGateway"));
      let activeGateWayName = activeGateWay === null ? "" : activeGateWay.name;
      let activeGateWayId = activeGateWay === null ? "" : activeGateWay.id;

      this.paymentInfo["gateway_id"] = activeGateWayId;
      this.paymentInfo["payment_gateway_name"] = activeGateWayName;

      this.paymentInfo["currency"] = "USD";
      this.paymentInfo["is_admin"] = true;
      this.paymentInfo["location"] = GET_USER().location_id;
      this.paymentInfo["token"] = this.cartS.getSessionData("cartToken");
      this.paymentInfo["pickup"] = sessionStorage.getItem("inStore")
        ? JSON.parse(sessionStorage.getItem("inStore")).pickup
        : null;
      this.paymentInfo["return_to"] = sessionStorage.getItem("return_to");

      this.paymentInfo["item_log"] = JSON.parse(
        sessionStorage.getItem("item_log")
      )
        ? JSON.parse(sessionStorage.getItem("item_log"))
        : [];

      this.paymentInfo["order_source"] = "Admin";
      this.paymentInfo["shipping_method"] = shipping_method;
      if (additional) {
        this.paymentInfo["note"] = this.note;
        if (this.is_paid) {
          this.paymentInfo["amount"] = this.payment_amount;
          this.paymentInfo["amount_tendered"] = this.amount_tendered;
        }
      }

      console.log(this.paymentInfo);
      this.cartS
        .addPayment(this.paymentInfo)
        .then(res => {
          this.loader = false;
          if (this.componentRef) {
            this.componentRef.instance.loader = false;
          }
          if (res.result.data) {
            if (res.result.data.payment.success) {
              this.order_id = res.result.data.payment.order_id;
              // this.printRecipet();
              this.orderDone = true;
              this.cart.emit();
              this.token = null;
              this.reloadCanlender();
              setTimeout(() => {
                this.clearCart();
              });
            } else {
              $("custom-alert").css("display", "block");
              this.alert.error(
                this.alertContainer,
                res.result.data.payment.message + "!!!",
                true,
                3000
              );
            }
          } else {
            this.alert.error(this.alertContainer, res.result.error, true, 3000);
          }
        })
        .catch(err => {
          console.log(err);
          const errorLog = err.error;

          this.loader = false;
          if (this.componentRef) {
            this.componentRef.instance.loader = false;
          }
          if (errorLog.status === "NOK") {
            if (
              errorLog.result.hasOwnProperty("message") &&
              errorLog.result.message !== ""
            ) {
              this.alert.error(
                this.alertContainer,
                errorLog.result.message,
                true,
                3000
              );
            } else {
              this.alert.error(
                this.alertContainer,
                "Payment is failed. Please try again.",
                true,
                3000
              );
            }
          } else {
            this.alert.error(
              this.alertContainer,
              "Payment is failed. Please try again.",
              true,
              3000
            );
          }
        });
    }
  }

  private reloadCanlender() {
    if (this.router.url.includes("/calender")) {
      this.cartS.reloadCalander.emit(true);
    }
  }

  clearCart() {
    const cartToken = this.cartS.getSessionData("cartToken");
    if (cartToken) {
      this.cartS.deleteAllCart(cartToken).subscribe(
        res => console.log(res),
        err => console.log(err)
      );
      this.sessionEmpty.forEach(name => {
        this.cartS.removeSessionData(name);
      });
      this.cartS.cartNoChange(null);
      this.cartS.cancelBoltTerminal(null);
    }
  }
  private printRecipet() {
    const data = {
      order_id: this.order_id
    };
    if (!this.is_online) {
      data["amount_tendered"] = parseFloat(this.payment_amount);
      data["change_amount"] = this.changeAmount;
    }
    this.cartS.printReceipt(data).subscribe(
      res => {
        console.log(res);
      },
      err => console.log(err)
    );
  }

  // getTotalAmount() {
  //   this.cartItems = this.cartS.getSessionData("cartList");
  // }

  showOnlineGateway() {
    const creditCardComponents = this.allGateways.filter(item => {
      if (item.name !== "BoltCardConnect" && item.type !== "offline") {
        return true;
      }
    });
    console.log(creditCardComponents);
    if (creditCardComponents.length > 0) {
      // let activeGateway = creditCardComponents.find(x => x.is_online);
      let activeGateway = creditCardComponents;
      sessionStorage.setItem("activeGateway", JSON.stringify(activeGateway[0]));

      if (activeGateway[0].name !== "Stripe") {
        this.is_stripeGateway = false;
        this.loadComponent(activeGateway[0].name);
      } else {
        if (this.fromOrderDetails || this.fromAdminStore) {
          if (this.customer_id != undefined || this.customer_id != "") {
            if (this.is_stripe_property_exist === false) {
              this.is_stripeGateway = false;
              this.loadComponent(activeGateway[0].name);
            } else {
              this.is_stripeGateway = true;
            }
          } else {
            this.is_stripeGateway = false;
            this.loadComponent(activeGateway[0].name);
          }
        } else {
          this.is_stripeGateway = false;
          this.loadComponent(activeGateway[0].name);
        }
      }
    }
  }

  private loadComponent(name) {
    // console.log(name);
    switch (true) {
      case name.toLowerCase() === "cardconnect":
        this.createComponent(CardConnectCheckoutComponent);
        this.gateImage = "./assets/img/home/<USER>";
        break;
      case name.toLowerCase() === "stripe":
        this.createComponent(StripeCheckoutV3Component, name);
        this.gateImage = "./assets/img/home/<USER>";
        break;
      case name.toLowerCase() === "paypal":
        this.createComponent(PaypalCheckoutComponent);
        this.gateImage = "./assets/img/home/<USER>";
        break;
      case name.toLowerCase() === "authorize.net":
        this.createComponent(AuthorizeDotNetCheckoutComponent);
        this.gateImage = "./assets/img/home/<USER>";
        break;
      case name.toLowerCase() === "gomerchant":
        this.createComponent(GoMerchantCheckoutComponent);
        this.gateImage = "./assets/img/home/<USER>";
        break;
    }
  }

  private createComponent(com: Type<any>, name?: string) {
    if (this.cardConect != undefined) {
      const factory = this.resolver.resolveComponentFactory(com);
      this.cardConect.clear();
      this.componentRef = this.cardConect.createComponent(factory);
      this.componentRef.instance.loaderColor = "m-loader--skin-dark";
      this.componentRef.instance.btnColor = "btn-brand";
      this.componentRef.instance.inStore = true;
      this.componentRef.instance.onSelectPayment.subscribe(e => this.submit(e));
      if (name) {
        const data = this.allGateways.find(
          f => f.name.toLowerCase() === name.toLowerCase()
        );
        this.componentRef.instance.inStore = false;
        this.componentRef.instance.key = data.config["publishable_key"];
        this.componentRef.instance.grid = ['col-xl-7', 'col-lg-8', 'col-md-10'];
        if (this.fromAdminStore) {
          this.componentRef.instance.source = 'admin_cart_checkout';
        } else {
          this.componentRef.instance.source = 'admin_after_order';
        }
      }
    }
  }

  onChangeCardType(cardType, oldCardToken?) {
    if (cardType === "newCard") {
      this.is_ShowOldCardSubmitbtn = false;
      this.loadComponent("Stripe");
    } else {
      this.oldStripeCardToken = oldCardToken;
      this.is_ShowOldCardSubmitbtn = true;
      this.cardConect.clear();
    }
  }

  onClickOldCardNumberSubmit(capture) {
    var activeGateWay = JSON.parse(sessionStorage.getItem("activeGateway"));

    if (this.fromAdminStore) {
      this.paymentInfo["type"] = this.paymentType;
      const delivery = this.cartS.getSessionData("deliveryCharge");
      if (delivery) {
        this.paymentInfo["delivery"] = delivery;
        if (delivery.hasOwnProperty("type") && delivery.type === "instore") {
          this.removeShippingAddress();
        }
      }

      const customFields = this.cartS.getSessionData("custom_values_inStore");
      if (customFields) {
        this.paymentInfo["custom_values"] = customFields;
      }

      const shipping_method = localStorage.getItem("admin_shipping_method")
        ? JSON.parse(localStorage.getItem("admin_shipping_method"))
        : "";

      var activeGateWay = JSON.parse(sessionStorage.getItem("activeGateway"));
      let activeGateWayName = activeGateWay === null ? "" : activeGateWay.name;
      let activeGateWayId = activeGateWay === null ? "" : activeGateWay.id;

      this.paymentInfo["capture"] = capture;
      this.paymentInfo["gateway_id"] = activeGateWayId;
      this.paymentInfo["payment_gateway_name"] = activeGateWayName;
      this.paymentInfo["currency"] = "USD";
      this.paymentInfo["account"] = this.oldStripeCardToken;
      this.paymentInfo["customer_id"] = this.customer_id;
      this.paymentInfo["is_admin"] = true;
      this.paymentInfo["location"] = GET_USER().location_id;
      this.paymentInfo["paytype"] = "stripe_token";
      this.paymentInfo["token"] = this.cartS.getSessionData("cartToken");
      this.paymentInfo["pickup"] = sessionStorage.getItem("inStore")
        ? JSON.parse(sessionStorage.getItem("inStore")).pickup
        : null;
      this.paymentInfo["return_to"] = sessionStorage.getItem("return_to");

      this.paymentInfo["item_log"] = JSON.parse(
        sessionStorage.getItem("item_log")
      )
        ? JSON.parse(sessionStorage.getItem("item_log"))
        : [];

      this.paymentInfo["shipping_method"] = shipping_method;

      console.log(this.paymentInfo);
      this.cartS
        .addPayment(this.paymentInfo)
        .then(res => {
          this.loader = false;
          if (this.componentRef) {
            this.componentRef.instance.loader = false;
          }
          if (res.result.data) {
            if (res.result.data.payment.success) {
              this.order_id = res.result.data.payment.order_id;
              // this.printRecipet();
              this.orderDone = true;
              this.cart.emit();
              this.token = null;
              this.reloadCanlender();
              setTimeout(() => {
                this.clearCart();
              });
            } else {
              $("custom-alert").css("display", "block");
              this.alert.error(
                this.alertContainer,
                res.result.data.payment.message + "!!!",
                true,
                3000
              );
            }
          } else {
            this.alert.error(this.alertContainer, res.result.error, true, 3000);
          }
        })
        .catch(err => {
          console.log(err);
          const errorLog = err.error;

          this.loader = false;
          if (this.componentRef) {
            this.componentRef.instance.loader = false;
          }
          if (errorLog.status === "NOK") {
            if (
              errorLog.result.hasOwnProperty("message") &&
              errorLog.result.message !== ""
            ) {
              this.alert.error(
                this.alertContainer,
                errorLog.result.message,
                true,
                3000
              );
            } else {
              this.alert.error(
                this.alertContainer,
                "Payment is failed. Please try again.",
                true,
                3000
              );
            }
          } else {
            this.alert.error(
              this.alertContainer,
              "Payment is failed. Please try again.",
              true,
              3000
            );
          }
        });
    } else if (this.fromOrderDetails) {
      let data = {
        account: this.oldStripeCardToken,
        currency: "USD",
        amount: this.payment_amount_for_order,
        is_admin: true,
        paytype: "stripe_token",
        type: this.paymentType,
        gateway_id: activeGateWay == null ? "" : activeGateWay.id,
        payment_gateway_name: activeGateWay.name,
        customer_id: this.customer_id
      };

      this.cartS
        .addPaymentFromOrderDetails(this.orderId, data)
        .then(res => {
          if (res.status == "OK") {
            this.loader = false;
            if (this.componentRef) {
              this.componentRef.instance.loader = false;
            }
            this.alert.success(
              this.alertContainer,
              res.result.message,
              true,
              3000
            );

            this.orderS.loadSidebarPayment(true);
          } else {
            this.alert.error(
              this.alertContainer,
              res.result.message,
              true,
              3000
            );
          }
        })
        .catch(err => {
          console.log(err);
          const errorLog = err.error;

          this.loader = false;
          if (this.componentRef) {
            this.componentRef.instance.loader = false;
          }

          if (errorLog.status === "NOK") {
            if (
              errorLog.result.hasOwnProperty("message") &&
              errorLog.result.message !== ""
            ) {
              this.alert.error(
                this.alertContainer,
                errorLog.result.message,
                true,
                3000
              );
            } else {
              this.alert.error(
                this.alertContainer,
                "Payment is failed. Please try again.",
                true,
                3000
              );
            }
          } else {
            this.alert.error(
              this.alertContainer,
              "Payment is failed. Please try again.",
              true,
              3000
            );
          }
        });
    }
  }

  getSripeDetails() {
    if (this.fromOrderDetails) {
      this.is_stripe_property_exist = false;
      Helpers.setLoading(true);
      setTimeout(() => {
        this.orderS.getStripInfo(this.customer_id).subscribe(res => {
          if (res.data) {
            const data = res.data;
            if (data.hasOwnProperty("stripe")) {
              if (data.stripe.hasOwnProperty("sources")) {
                this.is_stripeGateway = true;
                this.is_stripe_property_exist = true;
                this.stripeOldCardList = data.stripe.sources.data;
              } else {
                this.is_stripe_property_exist = false;
              }
            }
            Helpers.setLoading(false);
          }
        }),
          err => {
            Helpers.setLoading(false);
          };
      }, 1000);
    } else if (this.fromAdminStore) {
      this.is_stripe_property_exist = false;
      Helpers.setLoading(true);
      setTimeout(() => {
        this.orderS.getStripInfoByEmail(this.customer_email).subscribe(res => {
          if (res.data) {
            const data = res.data;
            if (data.hasOwnProperty("stripe")) {
              if (data.stripe.hasOwnProperty("sources")) {
                this.is_stripeGateway = true;
                this.is_stripe_property_exist = true;
                this.stripeOldCardList = data.stripe.sources.data;

                this.customer_id = data.id;
              } else {
                this.is_stripe_property_exist = false;
              }
            }
            Helpers.setLoading(false);
          }
        }),
          err => {
            Helpers.setLoading(false);
          };
      }, 1000);
    }
  }

  stripeOldNumber(oldCard) {
    return (
      oldCard.last4 +
      "(Exp - " +
      oldCard.exp_month +
      "/" +
      oldCard.exp_year +
      ")"
    );
  }
}
