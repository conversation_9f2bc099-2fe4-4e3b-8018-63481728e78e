import { Component, OnInit, EventEmitter, Output, ViewChild, ElementRef } from "@angular/core";
import { GET_USER, algoliaPlaceSearchConfig } from "../../../../../globals/_classes/functions";
import { CheckOut } from "../../../../cart-service/cart.models";
import { allCountry } from "../../../../../globals/_classes/country";
import { CartService } from "../../../../cart-service/cart.service";
import { CustomCheckout } from "../../../settings/models/settings.models";
import { SettingService } from "../../../settings/setting-service/setting.service";
//import * as places from "places.js";
import * as $ from "jquery";
import { HttpService } from "../../../../../modules/http-with-injector/http.service";
import { HttpEventType } from "@angular/common/http";
import { Helpers } from "../../../../../helpers";
import { AlertService } from "../../../../../modules/alert/alert.service";
import { NgbModalOptions, NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { TermsConditionComponent } from "../../../../../modules/terms-condition/terms-condition.component";

@Component({
  selector: "app-billing-info",
  templateUrl: "./billing-info.component.html",
  styleUrls: ["./billing-info.component.css"]
})
export class BillingInfoComponent implements OnInit {
  termsCondition = false;
  billingForm: CheckOut;
  errorFiled: boolean;
  customCheckoutList: CustomCheckout[] = [];
  customCheckoutResult = [];
  instore: boolean;

  rawCountries = allCountry;
  countries = allCountry;
  countryCode = 230;
  selectedCountry: string;
  placesAutocomplete: any;

  stateCode;
  allState;

  @Output("openShipping") openShipping: EventEmitter<any> = new EventEmitter();
  @ViewChild("hasCusAlert") alertContainer: ElementRef;

  constructor(
     private cartS: CartService,
     private settingS: SettingService,
     private http:HttpService,
     private modalService: NgbModal,
     private alertS: AlertService
     ) {
    this.countries = this.rawCountries.map(item => {
      return {
        name: item.name,
        code: item.code.toUpperCase(),
        id: item.id
      };
    });
    this.countries.sort((a, b) => a.name.localeCompare(b.name));
  }

  ngOnInit() {
    this.addInfoToBill();

    this.settingS.getCustomCheckouts().subscribe(res => {
      this.customCheckoutList = res.data;
      console.log(this.customCheckoutList);
    });

    const locations = JSON.parse(localStorage.getItem("locations"));
    const currentUser = GET_USER();
    const currentUserLocation = currentUser.location_id;
    let country = "US";
    locations.forEach(loc => {
      if (currentUserLocation === loc.id) {
        country = loc.country;
      }
    });


    this.selectedCountry = country;

    // this.placesAutocomplete = places({
    //   appId: "plIF9PULAHKJ",
    //   apiKey: "33382f2e6281756a1ceb6302fbc6bcbe",
    //   container: document.querySelector("#address-input"),
    //   type: "city",
    //   countries: [country],
    //   templates: {
    //     value: function(suggestion) {
    //       var str = [];
    //       str.push(suggestion.name);
    //       str.push(suggestion.administrative);
    //       str.push(suggestion.postcode);
    //       var filtered = str.filter(function(el) {
    //         return el != null;
    //       });
    //       var addressStr2 = filtered.join(", ");

    //       return addressStr2;
    //     },
    //     suggestion: function(suggestion) {
    //       var str = [];
    //       str.push(suggestion.name);
    //       str.push(suggestion.administrative);
    //       str.push(suggestion.postcode);
    //       var filtered = str.filter(function(el) {
    //         if (el) {
    //           return el;
    //         }
    //       });

    //       var addressStr1 = filtered.join(", ");
    //       return addressStr1;
    //     }
    //   }
    // }).configure({
    //   countries: [this.selectedCountry]
    // });

    
    // this.placesAutocomplete.on("change", function resultSelected(e) {
    //   if (e.suggestion.type == "city") {
    //     $("#Shcity").val(e.suggestion.name || "");
    //   } else {
    //     $("#Shcity").val(e.suggestion.city || "");
    //   }
    //   $("#Shzipcode").val(e.suggestion.postcode || "");
    //   $("#ShState").val(e.suggestion.administrative || "");
    // });

    // this.placesAutocomplete.configure({ countries: [country] });



    // this.placesAutocomplete = algoliaPlaceSearchConfig('city-input',country,this.selectedCountry)

    // this.placesAutocomplete.on("change", function resultSelected(e) {
    //   $("#Shzipcode").val(e.suggestion.postcode || "");
    //   $("#ShState").val(e.suggestion.administrative || "");
    // });

    // this.placesAutocomplete.configure({ countries: [country] });
  }

  onChange(countryCode) {
    this.selectedCountry = countryCode;
    this.billingForm.country = this.selectedCountry;
    // this.placesAutocomplete.configure({ countries: [this.selectedCountry] });

    $("#city-input").val('')
    $("#ShState").val('')
    $("#Shzipcode").val('')

    this.billingForm.city = $("#city-input")
    .val()
    .toString();

    this.billingForm.state = $("#ShState")
    .val()
    .toString();
    
    this.billingForm.zipcode = $("#Shzipcode")
    .val()
    .toString();

  }

  // setShippingCountryAndState() {
  //   if (this.billingForm.shipping_country) {
  //     this.countryCode = this.countries.find(
  //       f => f.code.toUpperCase() == this.billingForm.shipping_country
  //     ).id;
  //   }
  //   this.getState();
  // }


  openTermsConditionPopup(is_form_valid) {
    let ngbModalOptions: NgbModalOptions = {
      backdrop:'static',
      size: "lg",
      centered: true,
      windowClass: "terms-condition-modal"
    };

    const modalRef = this.modalService.open(
      TermsConditionComponent,
      ngbModalOptions
    );

    modalRef.componentInstance.IsTermsConditionChecked = this.termsCondition;
    modalRef.componentInstance.IsFormValid =is_form_valid;

    modalRef.result.then(res=>{
      console.log(res);
      if(res.continue)
      {
       this.termsCondition=true;
       this.submitBilling();
      }
    })

  }

  
  
  addInfoToBill() {
    this.cartS.checkout.subscribe(val => {
      if (val) {
        let bill = this.cartS.getSessionData("billInfo");
        if (bill) {
          bill.country = bill.country == "" ? "US" : bill.country.toUpperCase();
          this.billingForm = bill;
        } else {
          this.billingForm = new CheckOut();
          this.billingForm.country="US";
          this.billingForm.type = 1;
        }

        this.billingForm.token = this.cartS.getSessionData("cartToken");
        this.billingForm.driving_license_required = this.checkDrivingLicense();
        this.errorFiled = false;
      }
    });
  }


  onChangeCity(){
    this.billingForm.city = $("#city-input")
    .val()
    .toString();

    this.billingForm.state = $("#ShState")
    .val()
    .toString();
    
  this.billingForm.zipcode = $("#Shzipcode")
    .val()
    .toString();

  }

  submitBilling() {
    // this.billingForm.address_line2 = $("#address-input")
    //   .val()
    //   .toString();
     this.billingForm.city = $("#city-input")
      .val()
      .toString();
    this.billingForm.address_line1 = $("#Shaddress1")
      .val()
      .toString();
    // this.billingForm.city = $("#Shcity")
    //   .val()
    //   .toString();
    this.billingForm.state = $("#ShState")
      .val()
      .toString();
    this.billingForm.zipcode = $("#Shzipcode")
      .val()
      .toString();

    sessionStorage.setItem(
      "custom_values_inStore",
      JSON.stringify(this.customCheckoutResult)
    );

    if (
      this.billingForm.driving_license_required &&
      !this.billingForm.driving_license
    ) {
      this.errorFiled = true;
    } else {
      this.billingForm.salesman = GET_USER().user_id;

      this.billingForm.country = this.billingForm.country ? this.billingForm.country.toUpperCase() : '';
      this.cartS.setSessionData("billInfo", this.billingForm);

      sessionStorage.setItem('customer_email',this.billingForm.email)
      this.openShipping.emit(true);
    }
  }

  onChangeData(value, customField) {
    var customCheckout = this.customCheckoutResult.filter(x => x.id == customField.id);

    if (value !== "null" && value != "") {
      var data = {
        id: customField.id,
        field_name: customField.field_name,
        field_label: customField.field_label,
        type: customField.field_type,
        field_values: value
      };

      if (customCheckout.length === 0) {
        this.customCheckoutResult.push(data);
        // console.log(this.customCheckoutResult)
      } else {
        let index = this.customCheckoutResult.indexOf(customCheckout);
        this.customCheckoutResult.splice(index, 1);

        this.customCheckoutResult.push(data);
        // console.log(this.customCheckoutResult)
      }
    } else {
      let index = this.customCheckoutResult.indexOf(customCheckout);
      this.customCheckoutResult.splice(index, 1);
    }
  }

  checkDrivingLicense() {
    const cart = this.cartS.getSessionData("cartList");
    for (let c of cart) {
      if (c.driving_license_required) {
        return true;
      }
    }
    return false;
  }


  changeCountry(e) {
    const id = e.id ? e.id : 230;
    this.countryCode = id;
    this.getState();
    const country = this.countries.find(f => f.id === id);
    this.billingForm.shipping_country = country.code.toUpperCase();
  }

  changeState(e) {
    if (e.id) {
      this.stateCode = e.id;
      const state = this.allState.find(f => f.id === e.id);
      this.billingForm.shipping_state = state.code.toUpperCase();
    } else {
      this.billingForm.shipping_state = "";
    }
  }

  private getState() {
    this.cartS.getCountryState(this.countryCode).subscribe(res => {
      this.allState = res;
      if (this.allState && this.billingForm.shipping_state) {
        const data = this.allState.find(
          f => f.code.toUpperCase() == this.billingForm.shipping_state
        );
        this.stateCode = data ? data.id : null;
        if (!data) {
          this.billingForm.shipping_state = "";
        }
      }
    });
  }

  onSubmitCustomer(customer) {
    if (customer) {
      
      customer.country = customer.country == "" ? "US" : customer.country;
      this.billingForm = customer;
      this.selectedCountry = customer.country;
      this.placesAutocomplete.configure({ countries: [this.selectedCountry] });

    }
  }

  onBackToCart(){
    this.openShipping.emit(false)
  }

  onUpload(inputFile:File,customField){
    Helpers.setLoading(true);
    const formData = new FormData();
    formData.append('file',inputFile);
    formData.append('type',customField.field_name);
     
    this.http.post('media/upload', formData)
    .subscribe(res => {
       if(res.status=="OK") {
        Helpers.setLoading(false);
        this.onChangeData(res.result.data.filename,customField);
        console.log(res.result.data);
      }
      else{
        this.alertS.error(this.alertContainer, res.result.message, true, 3000);
      }
         
    }) 
  }
}
