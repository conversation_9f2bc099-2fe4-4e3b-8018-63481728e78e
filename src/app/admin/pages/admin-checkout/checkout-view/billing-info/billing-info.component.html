<div class="custom-alert" #hasCusAlert></div>

<div class="row">
  <div class="col-md-11">
    <h2 class="ac-title">
      Customer Information
    </h2>
  </div>
</div>
<app-customer-search
  (onSubmitCustomer)="onSubmitCustomer($event)"
></app-customer-search>
<div class="animated fadeIn content">
  <div class="m-portlet__body form-panel" style="padding-bottom: 15px;">
    <form class="m-form m-form--fit m-form--label-align-right" #form="ngForm">
      <div class="row">
        <div class="col-sm-6">
          <label for="fname" class="colorPurpel">
            First Name
          </label>
          <div class="form-group">
            <!--              <input-->
            <!--              type="search"  -->
            <!--              class="form-control m-input" -->
            <!--              id="address-input1" -->
            <!--              placeholder="Where are we going?"-->
            <!--              name="first_name"-->
            <!--              #fName="ngModel"-->
            <!--              [(ngModel)]="billingForm.first_name"-->
            <!--            />-->

            <input
              class="form-control m-input"
              id="fname"
              type="text"
              autocomplete="billing given-name"
              placeholder="First Name"
              name="first_name"
              #fName="ngModel"
              [(ngModel)]="billingForm.first_name"
            />
          </div>
        </div>
        <div class="col-sm-6">
          <label for="lname" class="colorPurpel">
            Last Name
          </label>
          <div class="form-group">
            <input
              class="form-control m-input"
              id="lname"
              type="text"
              autocomplete="billing family-name"
              placeholder="Last Name"
              name="last_name"
              #laName="ngModel"
              [(ngModel)]="billingForm.last_name"
            />
          </div>
        </div>
        <div class="col-sm-6">
          <label for="mobile" class="colorPurpel">
            Mobile number
          </label>
          <div class="form-group">
            <input
              numberOnly
              class="form-control m-input"
              id="mobile"
              type="text"
              autocomplete="billing mobile"
              placeholder="Mobile number"
              name="mobile"
              #mobile="ngModel"
              [(ngModel)]="billingForm.mobile"
           
            />
            <!-- <span *ngIf="mobile.invalid && mobile.touched">
              <small *ngIf="mobile.errors.required" class="error"
                >Mobile number is Required</small
              >
            </span> -->
          </div>
        </div>
        <div class="col-sm-6">
          <label for="email" class="colorPurpel">
            Email
          </label>
          <div class="form-group">
            <input
              class="form-control m-input"
              id="email"
              type="email"
              autocomplete="billing email"
              placeholder="Email"
              name="email"
              #email="ngModel"
              [(ngModel)]="billingForm.email"
              pattern="[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,5}$"
            
            />
            <!-- <span *ngIf="email.invalid && email.touched">
              <small *ngIf="email.errors.pattern" class="error"
                >Please enter correct email</small
              >
            </span> -->
          </div>
        </div>

        <div class="col-sm-6">
          <label for="companyName" class="colorPurpel">
            Company Name
          </label>
          <div class="form-group">
            <input
              class="form-control m-input"
              id="companyName"
              type="text"
              placeholder="Company Name"
              name="company"
              #companyName="ngModel"
              [(ngModel)]="billingForm.company"
            />
          </div>
        </div>
      </div>

      <h4 style="padding: 10px 0px;">
        Address
      </h4>
      <div class="row">
          <div class="col-sm-12">
              <label for="address-input-delivery" class="colorPurpel">
                Country
              </label>
              <div class="form-group">
                <select
                  class="form-control m-input dropdown-cls"
                  (change)="onChange($event.target.value)"
                  name="Country"
                  #Country="ngModel"
                  [(ngModel)]="billingForm.country"
                >
                  <option
                    [value]="country.code"
                    *ngFor="let country of countries"
                    >{{ country.name }}</option
                  >
                </select>
              </div>
            </div>

        <div class="col-sm-12">
          <label for="Shaddress1" class="colorPurpel">
            Address Line1
          </label>
          <div class="form-group">
            <input
              class="form-control m-input"
              id="Shaddress1"
              type="text"
              autocomplete="billing address-line1"
              placeholder="Address Line1"
              name="Shaddress1"
              #Shaddress1="ngModel"
              [(ngModel)]="billingForm.address_line1"
              
            />
            <!-- <span *ngIf="Shaddress1.invalid && Shaddress1.touched">
              <small *ngIf="Shaddress1.errors.required" class="error"
                >Address line1 is Required</small
              >
            </span> -->
          </div>
        </div>

        <div class="col-sm-12">
          <label for="Shaddress2" class="colorPurpel">
            Address Line2
          </label>
          <div class="form-group">
            <input
              class="form-control m-input"
              id="Shaddress2"
              type="text"
              autocomplete="billing address-line2"
              placeholder="Address Line2"
              name="Shaddress2"
              #Shaddress2="ngModel"
              [(ngModel)]="billingForm.address_line2"
            />
          </div>
        </div>

      </div>

      <!-- previous algolia search -->
      <!-- <div class="row">
        <div class="col-sm-6">
          <label for="address-input-delivery" class="colorPurpel">
            Country*
          </label>
          <div class="form-group">
            <select
              class="form-control m-input dropdown-cls"
              (change)="onChange($event.target.value)"
              name="Country"
              #Country="ngModel"
              [(ngModel)]="billingForm.country"
            >
              <option
                [value]="country.code"
                *ngFor="let country of countries"
                >{{ country.name }}</option
              >
            </select>
          </div>
        </div>

         
         <div class="col-sm-6">
          <label for="address-input" class="colorPurpel">
            City/State/Zip code*
          </label>
          <div class="form-group">
            <input
              type="search"
              class="form-control m-input"
              id="address-input"
              autocomplete="off"
              name="ShcombineAddress"
              #ShcombineAddress="ngModel"
              [(ngModel)]="billingForm.address_line2"
              placeholder="City/State/Zip code"
            />
          </div>
        </div>

      </div> -->

        <div class="row">
         
          <div class="col-sm-6">
            <label for="address-input" class="colorPurpel">
              Suburb or city
            </label>
            <div class="form-group">
              <input
                type="search"
                class="form-control m-input"
                id="city-input"
                autocomplete="none"
                name="city"
                #ShcombineCity="ngModel"
                [(ngModel)]="billingForm.city"
                placeholder="City"
                (change)="onChangeCity()"
                
              />
              <!-- <span *ngIf="ShcombineCity.invalid && ShcombineCity.touched">
                <small *ngIf="ShcombineCity.errors.required" class="error"
                  >Suburb or city is Required</small
                >
              </span> -->
            </div>
          </div>

          <div class="col-sm-6">
            <label for="Shzipcode" class="colorPurpel">
              postcode/zipcode code
            </label>
            <div class="form-group">
              <input
                class="form-control m-input"
                id="Shzipcode"
                type="text"
                autocomplete="billing postal-code"
                placeholder="Zip Code"
                name="Shzipcode"
                #Shzipcode="ngModel"
                [(ngModel)]="billingForm.zipcode"
                
              />
              <!-- <span *ngIf="Shzipcode.invalid && Shzipcode.touched">
                <small *ngIf="Shzipcode.errors.required" class="error"
                  >postcode/zipcode is Required</small
                >
              </span> -->
            </div>
          </div>

         

          <div class="col-sm-6">
            <label for="Shstate" class="colorPurpel">
              State or region
            </label>
            <input
              class="form-control m-input"
              id="ShState"
              type="text"
              autocomplete="billing address-level1"
              placeholder="State"
              name="ShState"
              #ShState="ngModel"
              [(ngModel)]="billingForm.state"
              
            />
            <!-- <span *ngIf="ShState.invalid && ShState.touched">
              <small *ngIf="ShState.errors.required" class="error"
                >State or region is Required</small
              >
            </span> -->
          </div>

        </div>


      <h4 style="padding: 10px 0px;">
        Additional Information
      </h4>
      <div class="row">
        <div class="col-sm-6">
          <label for="alt_phone" class="colorPurpel">
            Special Instructions/Comments
          </label>
          <div class="form-group">
            <input
              class="form-control m-input"
              id="alt_phone"
              type="text"
              autocomplete="nope"
              name="special_instructions"
              [(ngModel)]="billingForm.special_instructions"
            />
          </div>
        </div>
        <div class="col-sm-6">
          <label for="althone" class="colorPurpel">
            Special Requests
          </label>
          <div class="form-group">
            <input
              class="form-control m-input"
              id="althone"
              type="text"
              autocomplete="nope"
              name="special_requests"
              [(ngModel)]="billingForm.special_requests"
            />
          </div>
        </div>
        <div class="col-sm-6" *ngIf="billingForm.driving_license_required">
          <label for="event" class="colorPurpel">
            Driver's License*
          </label>
          <div class="form-group">
            <input
              class="form-control m-input"
              id="event"
              type="text"
              autocomplete="nope"
              placeholder="Driving License"
              name="driving_license"
              [(ngModel)]="billingForm.driving_license"
            />
            <small
              *ngIf="!billingForm.driving_license && errorFiled"
              class="error"
              >Please Enter your driver's license.</small
            >
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-md-6" *ngFor="let customCheckout of customCheckoutList">
          <div class="form-group">
            <label for="">{{ customCheckout.field_label }}</label>

            <input
              *ngIf="customCheckout.field_type == 0"
              type="text"
              class="form-control"
              name="name1"
              autocomplete="none"
              (change)="
                onChangeData(
                  $event.target.value,
                  customCheckout
                )
              "
            />

            <select
              name="howto_hear"
              class="form-control"
              *ngIf="customCheckout.field_type == 1"
              (change)="
                onChangeData(
                  $event.target.value,
                  customCheckout
                )
              "
            >
              <option value="null">--Select--</option>
            <ng-container *ngIf="customCheckout.field_values">
              <option
                *ngFor="let opt of customCheckout.field_values.split(';')"
                [value]="opt"
                name="name2"
                >{{ opt }}</option
              >
            </ng-container>
            </select>

            <input 
            *ngIf="customCheckout.field_type == 2" 
            type="file" 
            class="form-control"
            name="file"
            (change)="onUpload($event.target.files[0],customCheckout)" />
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-12">
          <div class="form-check" style="padding-left: 0px;">
            <label class="m-checkbox">
              <input
                type="checkbox"
                name="remember"
                [(ngModel)]="termsCondition"
                required
              />
              &nbsp;&nbsp;I have read and agree with the
              <span></span>
            </label>
            &nbsp;&nbsp;
            <a (click)='openTermsConditionPopup(form.form.valid)' style="cursor: pointer; color: #716aca;"
            >terms & conditions</a>
          </div>
        </div>
      </div>

      <div class="row" style="padding-top: 10px;">
        <div class="col-md-10">
          <button class="btn btn-brand" (click)="onBackToCart()">
            Back to cart
          </button>
        </div>
        <div class="col-md-2">
          <button
            type="submit"
            [disabled]="!form.form.valid"
            class="btn btn-brand"
            (click)="submitBilling()"
          >
            Continue
          </button>
        </div>
      </div>
    </form>
  </div>
</div>
