import { Component, OnInit, Output, EventEmitter, Input } from "@angular/core";

@Component({
  selector: "app-checkout-view",
  templateUrl: "./checkout-view.component.html",
  styleUrls: ["./checkout-view.component.css"]
})
export class CheckoutViewComponent implements OnInit {
  billingInfo = false;
  shippingInfo = false;
  payment = false;
  fromAdminStore=true;

  @Output() goToCart = new EventEmitter();
  @Input() quote: boolean;
  quoteResponse: any;

  constructor() {}

  ngOnInit() {
    this.loadExternalScript();
  }

  openShipping(e) {
    if(e)
    {
      this.billingInfo = true;
    }
    else{
      this.goToCart.emit(true);
    }
  }

  private loadExternalScript() {
    return new Promise((resolve, reject) => {
      const scriptElement = document.createElement("script");
      scriptElement.src = "https://js.stripe.com/v3/";
      scriptElement.onload = resolve;
      document.body.appendChild(scriptElement);
    });
  }

  goToNext(e) {
    if (e.hasOwnProperty('payment') && e.payment) {
      this.billingInfo = true;
      this.shippingInfo = true;
    } else if(e.hasOwnProperty('payment') && !e.payment){
      this.billingInfo = false;
      this.shippingInfo = false;
    } else if (e.hasOwnProperty('quote') && e.quote) {
      this.billingInfo = true;
      this.shippingInfo = true;
    }
  }

  backToShipping() {
    this.billingInfo = true;
    this.shippingInfo = false;
    this.payment = false;
  }

  getQuoteResponse(e) {
    if (e) {
      this.quoteResponse = e;
    }
  }
}
