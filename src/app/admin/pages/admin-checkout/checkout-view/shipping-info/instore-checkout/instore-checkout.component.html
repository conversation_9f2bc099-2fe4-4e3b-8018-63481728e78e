<div class="row mt-5">
  <div class="col-md-12">
    <div class="checkout-header">
      <h5 class="mb-0">
        <i class="fa fa-map-marker main"></i><i class="fa fa-angle-double-right ml-3"></i>
        Pick-up at
      </h5>
    </div>
  </div>
</div>
<div class="row mt-3">
  <div class="col-md-12 pl-5 pr-5">
    <div class="service-part child" *ngFor="let s of allLocation">
      <div class="form-group">
        <div class="m-radio-inline">
          <label class="m-radio" (change)="setDeliveryCharge(s, 1)">
            <input
            [(ngModel)]="instoreRadioBtnId"
              type="radio"
              name="instore"
              [value]="s.id"
              />
            {{ s.name }} <span></span>
            <samp *ngIf="s.location">({{ s.location }})</samp> <samp></samp
          ></label>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <app-returned-location ></app-returned-location>
</div>
<div *ngIf="loader" class="table-load m-loader m-loader--brand"></div>
<div class="row mt-3">
  <div class="col-md-12">
    <a
      (click)="back()"
      class="btn btn-dark caps mr-5 "
      style="margin-left:0px!important; float:left; color: #fff"
    >
      <i class="fa fa-backward"></i>
      &nbsp;Back</a
    >
    <button
      (click)="submit()"
      class="btn theme-btn btn-dark caps"
      style="margin-left:0px!important; float: right"
    >
      continue &nbsp;<i class="fa fa-forward"></i>
    </button>
  </div>
</div>
