import { Component, OnInit, EventEmitter, Output } from "@angular/core";
import { CartService } from "../../../../../cart-service/cart.service";
import { map, catchError } from "rxjs/operators";
import { of } from "rxjs";

@Component({
  selector: "app-instore-checkout",
  templateUrl: "./instore-checkout.component.html",
  styleUrls: ["./instore-checkout.component.css"]
})
export class InstoreCheckoutComponent implements OnInit {
  allLocation = [];
  @Output() onsubmit = new EventEmitter();
  @Output() onSubmitback = new EventEmitter();
  loader;
  token;
  instoreRadioBtnId;

  constructor(private cartS: CartService) {
    this.token = this.cartS.getSessionData("cartToken");
    this.getLocation();
  }

  ngOnInit() {}
  getLocation() {
    this.cartS
      .getterminals()
      .pipe(
        map(res => res.result.data),
        catchError(e => of([]))
      )
      .subscribe(res => {
        this.allLocation = res.filter(f => f.status);
        if (this.allLocation.length) {
          this.setDeliveryCharge();
        }
      });
  }
  setDeliveryCharge(s?, m?) {
    if(s && m){
      this.loader = true;
      const sendData = {
        shipping_method: m,
        token: this.token,
        shipping_cost: s && s.charge ? s.charge : 0,
        tax: s && s.tax ? s.tax : 0
      };
      // console.log(s)
      const data = {
        instore: true,
        pickup: s.id
      };
      sessionStorage.setItem("inStore", JSON.stringify(data));
      this.cartS
        .addDeliveryCharge(sendData)
        .then(res => {
          this.loader = false;
          if (res.status == "OK") {
            if (s) {
              s["type"] = "instore";
              this.cartS.setSessionData("deliveryCharge", s);
            }
            // this.cartS.setSessionData("cartList", res.result.data);
          }
        })
        .catch(err => {
          this.loader = false;
        });
    }
    else{
      if (sessionStorage.getItem("inStore")) {
        let instore =JSON.parse(sessionStorage.getItem("inStore"));
        const instoreId=Number(instore.pickup)
        if(!isNaN(instoreId))
        {
          this.instoreRadioBtnId = instoreId;
        }
       
      }
    } 
    
  }

  submit() {
    this.onsubmit.emit(true);
  }
  back() {
    this.onSubmitback.emit(true);
  }
}
