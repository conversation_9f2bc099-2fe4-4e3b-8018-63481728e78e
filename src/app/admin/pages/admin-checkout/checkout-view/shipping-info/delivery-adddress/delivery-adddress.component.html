<div class="order_details">
  <div class="row mt-5">
    <div class="col-md-12">
      <div class="checkout-header">
        <h5 class="mb-0">
          <i class="fa fa-truck main"></i
          ><i class="fa fa-angle-double-right ml-3"></i>
          {{name}} Address
        </h5>
      </div>
    </div>
  </div>
  <form
    class="row mt-4 pl-3 pr-3 order_details"
    [formGroup]="form"
  >

  <div class="col-sm-12">
      <label for="address-input-delivery" class="colorPurpel">
        Country*
      </label>
      <div class="form-group">
        <select class="form-control m-input dropdown-cls" (change)="onChange($event.target.value)"  formControlName="shipping_country">
          <option [value]="country.code" *ngFor="let country of countries">{{country.name}}</option>
        </select>
      </div>
    </div>
    
    <div class="col-md-12">
      <div class="form-group ">
        <label for="">Address Line1*</label>
        <input
          type="text"
          id="Streetaddress"
          formControlName="shipping_address1"
          required
          class=" col-md-12 mb-2 form-control"
          (change)="onChangeData()"
          autocomplete="shipping address-line1"
        />
        <span *ngIf="form.get('shipping_address1').invalid">
          <small
            *ngIf="
              form.get('shipping_address1').hasError('required') &&
              form.get('shipping_address1').touched
            "
            class="error"
            >Address Line1 is Required</small
          >
        </span>
      </div>
    </div>


    <div class="col-md-12">
      <div class="form-group ">
        <label for="">Address Line2</label>
        <input
          type="text"
          id="Streetaddress2"
          formControlName="shipping_address2"
          class=" col-md-12 mb-2 form-control"
          (change)="onChangeData()"
          autocomplete="shipping address-line2"
        />
        
      </div>
    </div>

   
    
      <div class="col-md-6">
        <div class="form-group">
          <label for="">Suburb or city*</label>
          <input
            id="Shcity_Delivery"
            type="text"
            formControlName="shipping_city"
            class="form-control"
            autocomplete="nope"
            (change)="onChangeCity()"
          />
          <span *ngIf="form.get('shipping_city').invalid">
              <small
                *ngIf="
                  form.get('shipping_city').hasError('required') &&
                  form.get('shipping_city').touched
                "
                class="error"
                >Suburb or city is Required</small
              >
            </span>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-group">
          <label for="">State or region*</label>
          <input
            id="ShState_Delivery"
            type="text"
            formControlName="shipping_state"
            class="form-control"
            autocomplete="shipping address-level1"
          />
          <span *ngIf="form.get('shipping_state').invalid">
              <small
                *ngIf="
                  form.get('shipping_state').hasError('required') &&
                  form.get('shipping_state').touched
                "
                class="error"
                >State or region is Required</small
              >
            </span>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-group ">
          <label for="">postcode/zipcode*</label>
          <input
            id="Shzipcode_Delivery"
            type="text"
            formControlName="shipping_zipcode"
            class="form-control"
            autocomplete="shipping postal-code"
          />
          <span *ngIf="form.get('shipping_zipcode').invalid">
              <small
                *ngIf="
                  form.get('shipping_zipcode').hasError('required') &&
                  form.get('shipping_zipcode').touched
                "
                class="error"
                >postcode/zipcode is Required</small
              >
            </span>
        </div>
      </div>
  
  </form>
</div>
