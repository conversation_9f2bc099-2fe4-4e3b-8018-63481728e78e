import { Component, OnInit, Output, EventEmitter, Input, OnDestroy } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { CartService } from "../../../../../cart-service/cart.service";
import { allCountry } from "../../../../../../globals/_classes/country";
//import * as places from "places.js";
import * as $ from "jquery";
import { GET_USER, algoliaPlaceSearchConfig } from "../../../../../../globals/_classes/functions";
import { Subscription } from "rxjs";

@Component({
  selector: "app-delivery-adddress",
  templateUrl: "./delivery-adddress.component.html",
  styleUrls: ["./delivery-adddress.component.css"]
})
export class DeliveryAdddressComponent implements OnInit, OnDestroy {
  contents: any;
  @Input() showBtn: boolean;
  @Input('name') name:string;
  form: FormGroup;
  rawCountries = allCountry;
  countries = allCountry;
  countryCode = 230;
  selectedCountry: string;
  stateCode;
  allState;
  placesAutocomplete: any;
  subs: Subscription[] = [];
  @Output() onChangeAddress = new EventEmitter();

  constructor(private fb: FormBuilder, private cartService: CartService) {
    this.countries = this.rawCountries.map(item => {
      return {
        name: item.name,
        code: item.code.toUpperCase(),
        id: item.id
      };
    });
    this.selectedCountry = this.countries["229"].code;
    this.countries.sort((a, b) => a.name.localeCompare(b.name));
  }

  ngOnInit() {
    this.createForm();
    this.setShippingInfo();
    this.onChanges();

    const locations = JSON.parse(localStorage.getItem("locations"));
    const currentUser = GET_USER();
    const currentUserLocation = currentUser.location_id;
    let country = "US";
    locations.forEach(loc => {
      if (currentUserLocation === loc.id) {
        country = loc.country;
      }
    });
   
    this.placesAutocomplete = algoliaPlaceSearchConfig('Shcity_Delivery',country,this.selectedCountry)

    this.placesAutocomplete.on("change", function resultSelected(e) {
      $("#Shzipcode_Delivery").val(e.suggestion.postcode || "");
      $("#ShState_Delivery").val(e.suggestion.administrative || "");
    });

    this.placesAutocomplete.configure({ countries: [country] });
    
  }

  ngOnDestroy() {
    this.subs.map(sub => sub.unsubscribe());
  }

  onChanges() {
    this.subs.push(this.form.valueChanges.subscribe(value => {
      if (value) {
        this.onChangeAddress.emit(value);
      }
    }));
  }

  createForm() {
    this.form = this.fb.group({
      shipping_first_name: [""],
      shipping_last_name: [""],
      shipping_mobile: [""],
      shipping_email: [""],
      shipping_address1: ["", Validators.required],
      shipping_combinedaddress: [""],
      shipping_address2: [""],
      shipping_city: ["",Validators.required],
      shipping_state: ["",Validators.required],
      shipping_country: ["",Validators.required],
      shipping_zipcode: ["",Validators.required]
    });
  }

  setShippingInfo() {
    let shippingInfo = this.form.value;
    let bill = this.cartService.getSessionData("billInfo");
    if (bill) {
      shippingInfo = bill;

      if(!bill.hasOwnProperty('shipping_address1'))
      {
        shippingInfo["shipping_address1"] = bill.address_line1;
        shippingInfo["shipping_address2"] = bill.address_line2;
        shippingInfo["shipping_country"] = bill.country == "" ? "US" : bill.country.toUpperCase();
        shippingInfo["shipping_state"] = bill.state;
        shippingInfo["shipping_city"] = bill.city;
        shippingInfo["shipping_zipcode"] = bill.zipcode;
      }
     

      this.selectedCountry = bill.country;
    }
    
    this.form.patchValue(shippingInfo);
  }


  onChangeCity(){
    this.onChangeData();
  }


  onChange(countryCode) {
    this.selectedCountry = countryCode;
    this.form.get("shipping_country").setValue(this.selectedCountry);
    this.placesAutocomplete.configure({ countries: [this.selectedCountry] });

    $("#Shcity_Delivery").val('')
    $("#Shzipcode_Delivery").val('');
    $("#ShState_Delivery").val('');

    this.onChangeData();
  }

  onChangeData() {
    this.form.get("shipping_address1").setValue(
      $("#Streetaddress")
        .val()
        .toString()
    );

    this.form.get("shipping_address2").setValue(
      $("#Streetaddress2")
        .val()
        .toString()
    );

    this.form.get("shipping_state").setValue(
      $("#ShState_Delivery")
        .val()
        .toString()
    );
    this.form.get("shipping_city").setValue(
      $("#Shcity_Delivery")
        .val()
        .toString()
    );
    this.form.get("shipping_zipcode").setValue(
      $("#Shzipcode_Delivery")
        .val()
        .toString()
    );


  }


  showError() {
    console.log(this.form.getRawValue());
    if (this.form.invalid) {
      Object.keys(this.form.controls).forEach(field => {
        const control = this.form.get(field);
        control.markAsTouched({ onlySelf: true });
      });
      return;
    }
  }
}
