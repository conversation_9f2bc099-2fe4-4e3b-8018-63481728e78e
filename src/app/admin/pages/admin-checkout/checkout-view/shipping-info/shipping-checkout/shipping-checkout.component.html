<app-delivery-adddress 
  #deliveryAddress 
  [name]="delivery_name" 
  (onChangeAddress)="getChangedAddress($event)">
</app-delivery-adddress>

<div *ngIf="loader" class="table-load m-loader m-loader--brand"></div>

<div class="row m-0 pt-4">
  <div class="col-md-12">
    <div class="custom-alert" #hasAlert></div>
    <h5 *ngIf="shipping.length">Select Shipping Method</h5>
    <ng-container *ngFor="let ship of shipping">
      <div class="service-part shipping-radio-btn" *ngFor="let s of ship">
        <div class="form-group w-100 mb-0">
          <div class="m-radio-inline">
            <label class="m-radio w-100" (change)="setDeliveryCharge(s, s.method)">
              <input formcontrolname="header" type="radio" value="default" class="ng-valid ng-dirty ng-touched"
                name="main" [value]="s.id"><span></span>
              <div *ngIf="s.method === 4 else standard">
                <div class="flex2" style="flex:2">
                  <h6>{{ s.service_name  | titlecase }}</h6>
                  <h6 style="font-size:13px;font-weight:normal;color:#ccc;">
                    <p>
                      {{
                      getDate(s.delivery_date)
                        ? "Estimated Delivery Date: " + (getDate(s.delivery_date) | date)
                        : ""
                    }}
                      &nbsp;
                      Delivery days: {{s.delivery_days}}
                    </p>
                  </h6>
                </div>
                <div class="text-right flex1" style="flex:1" style="margin-top: -33px">
                  <h6>{{ s.charge | currency }}</h6>
                </div>
              </div>
              <ng-template class="flex2" #standard>
                <div class="flex2" style="flex:2">
                  <h6>{{  s.carrier_code   | titlecase}}</h6>
                </div>
                <div class="text-right flex1" style="flex:1" style="margin-top: -33px">
                  <h6>{{ s.charge | currency }}</h6>
                </div>
              </ng-template>

            </label>
          </div>
        </div>
      </div>
    </ng-container>

  </div>
</div>
<div class="row m-0" *ngIf="error.status">
  <div class="custom-alert alert-message">
    <span class="error"> {{ error.message }}</span>
  </div>
</div>

<!-- <div class="row">
  <app-returned-location ></app-returned-location>
</div> -->

<div class="row mt-3">
  <div class="col-md-4 mb-3">
    <a (click)="back()" class="btn btn-dark caps mr-5 " style="margin-left:0px!important; float:left; color: #fff">
      <i class="fa fa-backward"></i>&nbsp;Back</a>
  </div>

  <div class="col-md-8" *ngIf="freeShipping; else normalShipping">
    <button (click)="submit()" class="btn theme-btn btn-dark caps" style="margin-left:0px!important; float: right">
      Continue &nbsp;<i class="fa fa-forward"></i>
    </button>
  </div>
  <ng-template #normalShipping>
    <div class="col-md-8">
      <button (click)="getshippingMethod()" class="btn theme-btn btn-dark caps mr-5"
        style="margin-left:0px!important; float: left">
        Get Shipping Method &nbsp;<i class="fa fa-forward"></i>
      </button>
      <button (click)="submit()" *ngIf="shippingMethod" class="btn theme-btn btn-dark caps"
        style="margin-left:0px!important; float: right">
        Continue &nbsp;<i class="fa fa-forward"></i>
      </button>
    </div>
  </ng-template>

</div>