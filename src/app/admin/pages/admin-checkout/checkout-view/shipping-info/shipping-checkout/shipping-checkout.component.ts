import {
  Component,
  OnInit,
  Output,
  EventEmitter,
  ViewChild,
  ElementRef,
  Input
} from "@angular/core";
import { DeliveryAdddressComponent } from "../delivery-adddress/delivery-adddress.component";
import { AlertService } from "../../../../../../modules/alert/alert.service";
import { CartService } from "../../../../../cart-service/cart.service";
import { GET_USER } from "../../../../../../globals/_classes/functions";

@Component({
  selector: "app-shipping-checkout",
  templateUrl: "./shipping-checkout.component.html",
  styleUrls: ["./shipping-checkout.component.css"]
})
export class ShippingCheckoutComponent implements OnInit {
  @Output() onsubmit = new EventEmitter();
  @Output() onSubmitback = new EventEmitter();
  @Input('delivery_name') delivery_name:string;
  @Input('freeShipping') freeShipping:boolean=false;
  shipping = [];
  shippingMethod=false;
  shippingMethodValue;
  error = { status: false, message: "" };
  loader;
  token;
  @ViewChild("hasAlert") alertContainer: ElementRef;
  @ViewChild("deliveryAddress") addressComponet: DeliveryAdddressComponent;
  constructor(private alert: AlertService, private cartService: CartService) {}

  getDate(d) {
    if (d) {
      return new Date(d);
    }
    return "";
  }

  getshippingMethod() {
    this.error.status = false;
    if (this.addressComponet.form.invalid) {
      this.addressComponet.showError();
      return;
    }
    let billingInfo = this.cartService.getSessionData("billInfo");
    if (billingInfo) {
      Object.assign(billingInfo, this.addressComponet.form.getRawValue());
      this.cartService.setSessionData("billInfo", billingInfo);
    }
    this.loader = true;
    const loc = GET_USER().location_id;
    this.token = this.cartService.getSessionData("cartToken");

    const address = this.addressComponet.form.getRawValue();
    this.cartService
      .getShippingOptions({ address: address, pickup: loc, token: this.token })
      .subscribe(res => {
        this.loader = false;
        if (res.status === "OK") {
          this.shippingMethod=true;
          this.shipping=[];
          let items = [];
          const data = res.result;
          for (let s in data) {
            items= data[s];
            if (String(s) === "standard") {
              let standard=[];
              standard[0]= items;
              standard[0]["method"] = 6;
              this.shipping.push(standard);
            } else if (String(s) === "flat") {
              let flat_items = [];
              flat_items[0] = items;
              flat_items[0]["method"] = 7;
              this.shipping.unshift(flat_items);
            } else {
              items = items.map(item => {
                item["method"] = 4;
                return item;
              });
              this.shipping.push(items);
            }
            // items = items.map(item => {
            //   if (String(s) === "standard") {
            //     item["method"] = 6;
            //     return item;
            //   } else if (String(s) === "flat"){
            //     const flat_items = [];
            //     flat_items[0] = item;
            //     flat_items[0]["method"] = 7;
            //     return flat_items;
            //   }else  {
            //     item["method"] = 4;
            //     return item;
            //   }
            // });
            // this.shipping.push(items);
          }
        } else {
          this.error.status = true;
          this.error.message = res.result.error;
          this.alert.error(this.alertContainer, res.result.error, true, 5000);

        }
      });
  }
  ngOnInit() {}
  setDeliveryCharge(s, m) {
    const sendData = {
      shipping_method: m,
      token: this.token,
      shipping_cost: s && s.charge ? s.charge : 0,
      tax: s && s.tax ? s.tax : 0,
      tax_id: s && s.tax_id ? s.tax_id : null
    };

    this.shippingMethodValue = m;
    sessionStorage.removeItem("inStore");
    this.cartService
      .addDeliveryCharge(sendData)
      .then(res => {
        this.loader = false;
        if (res.status == "OK" && res.result.data) {
          if (s) {
            this.cartService.setSessionData("deliveryCharge", s);
          }
          this.cartService.setSessionData("cartList", res.result.data);
        } else {
        }
      })
      .catch(err => {
        this.loader = false;
      });
  }
  submit() {
    this.error.status = false;
    if (this.addressComponet.form.invalid) {
      this.addressComponet.showError();
      return;
    }
    
    let billingInfo = this.cartService.getSessionData("billInfo");
    if (billingInfo) {
      Object.assign(billingInfo, this.addressComponet.form.getRawValue());
      billingInfo.shipping_first_name = billingInfo.first_name;
      billingInfo.shipping_last_name = billingInfo.last_name;
      billingInfo.shipping_mobile = billingInfo.mobile;
      billingInfo.shipping_email = billingInfo.email;
      this.cartService.setSessionData("billInfo", billingInfo);
    }

    localStorage.setItem("admin_shipping_method", JSON.stringify(this.shippingMethodValue));
    
    this.onsubmit.emit(true);
  }

  back() {
    this.onSubmitback.emit(true);
  }

  getChangedAddress(e) {
    if (e) {
      this.shipping = [];
    }
  }
}
