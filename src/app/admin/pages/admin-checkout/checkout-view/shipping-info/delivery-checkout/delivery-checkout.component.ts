import {
  Component,
  OnInit,
  ViewChild,
  ElementRef,
  Output,
  EventEmitter,
  Input
} from "@angular/core";
import { DeliveryAdddressComponent } from "../delivery-adddress/delivery-adddress.component";
import { AlertService } from "../../../../../../modules/alert/alert.service";
import { ActivatedRoute } from "@angular/router";
import { CartService } from "../../../../../cart-service/cart.service";
import { GET_USER } from "../../../../../../globals/_classes/functions";

@Component({
  selector: "app-delivery-checkout",
  templateUrl: "./delivery-checkout.component.html",
  styleUrls: ["./delivery-checkout.component.css"]
})
export class DeliveryCheckoutComponent implements OnInit {
  @ViewChild("deliveryAddress") addressComponet: DeliveryAdddressComponent;
  deliveryCost;
  error = { status: false, message: "" };
  token;
  loader;
  zone = [];
  @Input() delivery_settings;
  @Input('delivery_name') delivery_name:string;
  inValidDistance;
  selectMethod: boolean;
  @Output() onsubmit = new EventEmitter();
  @Output() onSubmitback = new EventEmitter();
  @ViewChild("hasAlert") alertContainer: ElementRef;
  constructor(private alert: AlertService, private cartService: CartService) {
    this.token = this.cartService.getSessionData("cartToken");
  }

  getCost() {
    if (this.addressComponet.form.invalid) {
      this.addressComponet.showError();
      return;
    }
    this.inValidDistance = false;
    this.selectMethod = false;
    this.deliveryCost = null;
    this.error.status = false;

    let billingInfo = this.cartService.getSessionData("billInfo");
    if (billingInfo) {
      Object.assign(billingInfo, this.addressComponet.form.getRawValue());
      this.cartService.setSessionData("billInfo", billingInfo);
    }

    const loc = GET_USER().location_id;
    const address = this.addressComponet.form.getRawValue();
    this.loader = true;
    this.cartService
      .getDeliveryCharge({ address: address, pickup: loc })
      .subscribe(res => {
        this.loader = false;
        if (res.status === "OK") {
          if (!this.delivery_settings.charge_by_zone) {
            this.inValidDistance = res.result.location[0].max_distance;
            if (!res.result.location[0].max_distance) {
              this.deliveryCost = res.result.location[0].charge;
              this.setDeliveryCharge(res.result.location[0], 3);
            }
          } else {
            this.zone = res.result.location;
          }
        } else {
          this.error.status = true;
          this.error.message = res.result.error;
        }
      });
  }

  setDeliveryCharge(s, m) {
    this.loader = true;
    const sendData = {
      shipping_method: m,
      token: this.token,
      shipping_cost: s && s.charge ? s.charge : 0,
      tax: s && s.tax ? s.tax : 0,
      tax_id: s && s.tax_id ? s.tax_id : null
    };

    sessionStorage.removeItem("inStore");
    this.cartService
      .addDeliveryCharge(sendData)
      .then(res => {
        this.loader = false;
        if (res.status == "OK" && res.result.data) {
          this.selectMethod = true;
          if (s) {
            this.cartService.setSessionData("deliveryCharge", s);
          }
          this.cartService.setSessionData("cartList", res.result.data);
        } else {
        }
      })
      .catch(err => {
        this.loader = false;
      });
  }

  ngOnInit() {

  }


  submit() {
    if (this.addressComponet.form.invalid) {
      this.addressComponet.showError();
      return;
    }
    
    let billingInfo = this.cartService.getSessionData("billInfo");
    if (billingInfo) {
      Object.assign(billingInfo, this.addressComponet.form.getRawValue());
      this.cartService.setSessionData("billInfo", billingInfo);
    }
    
    this.onsubmit.emit(true);
  }

  getChangedAddress(e) {
    if (e) {
      this.zone = [];
      this.deliveryCost = '';
      this.selectMethod = false;
    }
  }
  
  back() {
    this.onSubmitback.emit(true);
  }
}
