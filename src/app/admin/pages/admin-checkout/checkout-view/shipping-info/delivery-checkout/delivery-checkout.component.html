<app-delivery-adddress
  #deliveryAddress
  [showBtn]="selectMethod"
  [name]="delivery_name"
  (onChangeAddress)="getChangedAddress($event)"
></app-delivery-adddress>

<div *ngIf="loader" class="table-load m-loader m-loader--brand"></div>
<div class="row mt-0">
  <div class="col-md-12 mt-4">
    <div>
      <ng-container *ngIf="zone.length > 0">
        <div class="pl-3 pr-4" *ngFor="let s of zone">
          <div class="w-100" style="width: 50px;">
            <div class="form-group">
              <div class="m-radio-inline">
                <label class="m-radio w-100" (change)="setDeliveryCharge(s, 2)">
                  <input type="radio" name="main" /> <span></span><b>{{ s.name }}</b>
                  <samp class="float-right">{{ s.charge | currency }}</samp>
                </label>
              </div>
            </div>
          </div>
        </div>
      </ng-container>
    </div>
  </div>
</div>

<div class="row m-0" *ngIf="error.status">
  <div class="custom-alert alert-message">
    <span class="error"> {{ error.message }}</span>
  </div>
</div>
<div class="row">
  <!-- <app-returned-location ></app-returned-location> -->
  <br />
  
  <div class="row" *ngIf="deliveryCost">
      <div class="col-md-12" style="color: #000; font-size: 19px;font-weight: 500;">
        Delivery Cost {{ deliveryCost | currency }}
      </div>
    </div>
    <div class="row m-0" *ngIf="inValidDistance">
        <div class="alert-message">
          <span
            >Your address is outside of our delivery area. Please contact us to make
            other arrangements.</span
          >
        </div>
      </div>
</div>
<div class="row mt-3">
  <div class="col-sm-4 mb-3">
    <a (click)="back()"
      class="btn btn-dark caps mr-5 "
      style="margin-left:0px!important; float:left; color: #fff"
    >
      <i class="fa fa-backward"></i>&nbsp;Back</a
    >
  </div>

  <div class="col-sm-8 mb-3">
    <button
      (click)="submit()"
      class="btn theme-btn btn-dark caps"
      style="margin-left:0px!important; float: right"
    >
      Continue &nbsp;<i class="fa fa-forward"></i></button
    >&nbsp;&nbsp;
    <button
    *ngIf="zone.length == 0"
      (click)="getCost()"
      class="btn theme-btn btn-dark caps mr-5"
      style="margin-left:0px!important; float: left"
    >
      Get Delivery Cost &nbsp;<i class="fa fa-forward"></i>
    </button>
  </div>
</div>
