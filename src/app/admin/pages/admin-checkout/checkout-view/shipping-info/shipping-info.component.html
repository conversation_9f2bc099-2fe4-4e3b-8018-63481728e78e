<!-- <div class="container"> -->
<div *ngIf="loader" class="table-load m-loader m-loader--brand"></div>
<div class="row">
    <div class="col-md-11">
        <h2 class="ac-title">
            Fulfillment
        </h2>
    </div>
  <div class="col-lg-12 col-md-12 mt-4" *ngIf="deliverySettings">
    <div class="row">
      <div class="col-md-12">
        <ul
          class="nav nav-tabs shipping-tab justify-content-center mt-0"
          id="myTab"
          role="tablist"
        >
          <li class="nav-item" *ngIf="isPicupActive==true" (click)="selectType('in-store')">
            <a
            class="nav-link "
            [class.active]="isPicupActive==true"
              id="home-tab"
              data-toggle="tab"
              href="#instore"
              role="tab"
              aria-controls="home"
              aria-selected="true"
            >
              <img class="icon" src="../assets/img/home/<USER>" alt="" />
              <h5>Pickup</h5>
            </a>
          </li>
          <li
            class="nav-link "
            (click)="selectType('delivery')"
            class="nav-item"
            *ngIf="deliverySettings.is_requiered"
          >
            <a
              class="nav-link"
              [class.active]="isDeliveryActive==true"
              id="profile-tab"
              data-toggle="tab"
              href="#delivery"
              role="tab"
              aria-controls="profile"
              aria-selected="false"
            >
              <img class="icon" src="../assets/img/home/<USER>" alt="" />
              <h5>Delivery</h5>
            </a>
          </li>
          <li
              class="nav-link "
            (click)="selectType('shipping')"
            class="nav-item"
            *ngIf="deliverySettings.shipping"
          >
            <a
              class="nav-link"
              [class.active]="isShippingActive==true"
              id="contact-tab"
              data-toggle="tab"
              href="#shipping"
              role="tab"
              aria-controls="contact"
              aria-selected="false"
            >
              <img class="icon" src="../assets/img/home/<USER>" alt="" />
              <h5>Shipping</h5>
            </a>
          </li>
        </ul>
        <div class="tab-content" id="myTabContent">
          <div
            class="tab-pane fade show"
            [class.active]="isPicupActive==true"
            id="instore"
            role="tabpanel"
            aria-labelledby="home-tab"
            *ngIf="type === 'in-store'"
          >
            <app-instore-checkout (onSubmitback)="goToBilling()" (onsubmit)="goToPayment()"></app-instore-checkout>
          </div>
          <div
            class="tab-pane fade show"
            [class.active]="isDeliveryActive==true"
            id="delivery"
            role="tabpanel"
            *ngIf="type === 'delivery'"
            aria-labelledby="profile-tab"
          >
            <app-delivery-checkout (onSubmitback)="goToBilling()" [delivery_settings]="deliverySettings" [delivery_name]="type" (onsubmit)="goToPayment()" *ngIf="show"></app-delivery-checkout>
          </div>
          <div 
          class="tab-pane fade show"
          [class.active]="isShippingActive==true"
          id="shipping"
          role="tabpanel"
          *ngIf="type === 'shipping'"
          aria-labelledby="shipping-tab"
          >
            <app-shipping-checkout [delivery_name]="type" [freeShipping]="free_shipping" (onSubmitback)="goToBilling()" (onsubmit)="goToPayment()" *ngIf="show"></app-shipping-checkout>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>


<div class="form-group text-center" style="padding: 20px;" *ngIf="allShipping">
    <button (click)="submit()" class="btn btn-dark">Continue
    </button>
  </div>