import { CommonModule } from "@angular/common";
import { ShippingInfoComponent } from "./shipping-info.component";
import { NgModule } from "@angular/core";
import { InstoreCheckoutComponent } from "./instore-checkout/instore-checkout.component";
import { DeliveryCheckoutComponent } from "./delivery-checkout/delivery-checkout.component";
import { ShippingCheckoutComponent } from "./shipping-checkout/shipping-checkout.component";
import { DeliveryAdddressComponent } from "./delivery-adddress/delivery-adddress.component";
import { RouterModule } from "@angular/router";
import { Select2NormalModule } from "../../../../../modules/select2-normal/select2-normal.module";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { ReturnedLocationComponent } from './returned-location/returned-location.component';

@NgModule({
  imports: [
    CommonModule,
    RouterModule,
    Select2NormalModule,
    FormsModule,
    ReactiveFormsModule
  ],
  declarations: [
    ShippingInfoComponent,
    InstoreCheckoutComponent,
    DeliveryCheckoutComponent,
    ShippingCheckoutComponent,
    DeliveryAdddressComponent,
    ReturnedLocationComponent
  ],
  exports: [
    ShippingInfoComponent,
    DeliveryAdddressComponent,
    InstoreCheckoutComponent,
    DeliveryCheckoutComponent,
    ShippingCheckoutComponent,
    Select2NormalModule
  ]
})
export class ShippingSettingCheckoutModule {}
