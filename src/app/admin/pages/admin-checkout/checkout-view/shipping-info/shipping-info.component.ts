import { Component, OnInit, Output, EventEmitter, Input, ElementRef, ViewChild } from "@angular/core";
import { map, catchError } from "rxjs/operators";
import { of, Subscription } from "rxjs";
import { CartService } from "../../../../cart-service/cart.service";
import { forPickUp, GET_USER } from "../../../../../globals/_classes/functions";
import { AlertService } from "./../../../../../modules/alert/alert.service";

@Component({
  selector: "app-shipping-info",
  templateUrl: "./shipping-info.component.html",
  styleUrls: ["./shipping-info.component.css"]
})
export class ShippingInfoComponent implements OnInit {
  allShipping;
  zone = [];
  shipping = [];
  isRequaird: boolean;
  byZone: boolean;
  gotoNext: boolean;
  instoreSelect;
  isPicupActive:boolean=false;
  isDeliveryActive:boolean=false;
  isShippingActive:boolean=false;
  zoneSelect;
  allLocation = [];
  loader: boolean;
  type: string;
  show: boolean;
  @Output() openPayment = new EventEmitter();
  @Output() quoteResponse = new EventEmitter();
  deliverySettings: any;
  free_shipping: boolean;
  @Input() quote: boolean;
  paymentInfo = new Object();
  sub: Subscription[] = [];
  @ViewChild("hasCusAlert") alertContainer: ElementRef;


  constructor(
    private cartS: CartService,
    private alert: AlertService) {
   // this.getDeliverySettings();
  }

  ngOnChanges() {
    if (this.quote) {
      this.getBillInfo();
    }
  }
  getDeliverySettings() {
    this.cartS.getShipping().then(res => {
      this.deliverySettings = res.delivery_settings;
    });
  }
  ngOnInit() {
    this.getFreeShippingStatus();

    this.cartS.getShipping().then(res => {
      this.deliverySettings = res.delivery_settings;

      if(!this.deliverySettings.hasOwnProperty('disable_instore_pickup') ||(this.deliverySettings.hasOwnProperty('disable_instore_pickup') && !this.deliverySettings.disable_instore_pickup)){
        this.selectType('in-store');
        this.isPicupActive=true;

        if(this.deliverySettings.hasOwnProperty('shipping') && this.deliverySettings.shipping){
          this.isShippingActive=false;
        }
        else{
          this.isDeliveryActive=false;
        }
      }
     else{
      if(this.deliverySettings.hasOwnProperty('shipping') && this.deliverySettings.shipping &&
      this.deliverySettings.hasOwnProperty('is_requiered') && this.deliverySettings.is_requiered){
        this.selectType('shipping');
        this.isShippingActive=true;

        this.isDeliveryActive=false;
      }
      else if(this.deliverySettings.hasOwnProperty('shipping') && this.deliverySettings.shipping){
       this.selectType('shipping');
        this.isShippingActive=true;
      }
      else{
        this.selectType('delivery');
        this.isDeliveryActive=true;
      }
     }
     
    });

  }


  getFreeShippingStatus() {
    let cart_token=this.cartS.getSessionData("cartToken");
    this.cartS.checkFreeShipping(cart_token).then(
      res => res.result.data ? this.free_shipping = true : this.free_shipping = false
    );
  }

  selectType(type) {
    this.type = type;
    this.show = false;
    setTimeout(() => {
      this.show = true;
    }, 100);
  }

  getDate(d) {
    if (d) {
      return new Date(d);
    }
    return "";
  }

  private inStoreSelection(s, m) {
    if (m === 1) {
      this.zoneSelect = null;
      this.instoreSelect = "in";
      forPickUp(true, s.id, true);
    } else {
      this.instoreSelect = null;
      const pick = JSON.parse(sessionStorage.getItem("inStore"));
      if (pick.instore || pick.pickup) {
        forPickUp(false, null, true);
      }
    }
  }

  private zoneSelection(m) {
    if (m === 2 && this.zone.length > 0) {
      this.instoreSelect = null;
      this.zoneSelect = "zone";
    } else {
      this.zoneSelect = null;
    }
  }

  sendSelectData(s, m) {
    this.loader = true;
    const token = this.cartS.getSessionData("cartToken");
    this.inStoreSelection(s, m);
    this.zoneSelection(m);
    const sendData = {
      shipping_method: m,
      token,
      shipping_cost: s && s.charge ? s.charge : 0,
      tax: s && s.tax ? s.tax : 0
    };
    this.cartS
      .addDeliveryCharge(sendData)
      .then(res => {
        this.loader = false;
        if (res.status == "OK" && res.result.data) {
          if (s) {
            this.cartS.setSessionData("deliveryCharge", s);
          }
          this.cartS.setSessionData("cartList", res.result.data);
          this.gotoNext = true;
        } else {
          this.gotoNext = false;
        }
      })
      .catch(err => {
        this.loader = false;
        console.error(err);
        this.gotoNext = false;
      });
  }

  goToPayment() {
    if (this.quote) {
      this.makeQuotePayment();
      return;
    }
    this.openPayment.emit({
      payment: true
    });
    this.cartS.goToPayment(true);
  }

  goToBilling() {
    this.openPayment.emit({
      payment: false
    });
  }

  private makeQuotePayment() {
    this.loader = true;
    const delivery = this.cartS.getSessionData("deliveryCharge");
    const shipping_method = localStorage.getItem("admin_shipping_method")
      ? JSON.parse(localStorage.getItem("admin_shipping_method"))
      : "";
    if (delivery) {
      this.paymentInfo["delivery"] = delivery;
      if (delivery.hasOwnProperty("type") && delivery.type === "instore") {
        this.removeShippingAddress();
      }
    }
    const customFields = this.cartS.getSessionData("custom_values_inStore");
    if (customFields) {
      this.paymentInfo["custom_values"] = customFields;
    }

    var activeGateWay = JSON.parse(sessionStorage.getItem("activeGateway"));
    let activeGateWayName = activeGateWay === null ? "" : activeGateWay.name;
    let activeGateWayId = activeGateWay === null ? "" : activeGateWay.id;

    this.paymentInfo["gateway_id"] = activeGateWayId;
    this.paymentInfo["payment_gateway_name"] = activeGateWayName;
    this.paymentInfo["quote"] = true;
    this.paymentInfo['type'] = 2; // for quote order type = 2
    this.paymentInfo["currency"] = "USD";
    this.paymentInfo["is_admin"] = true;
    this.paymentInfo["location"] = GET_USER().location_id;
    this.paymentInfo["token"] = this.cartS.getSessionData("cartToken");
    this.paymentInfo["pickup"] = sessionStorage.getItem("inStore")
      ? JSON.parse(sessionStorage.getItem("inStore")).pickup
      : null;
    this.paymentInfo["return_to"] = sessionStorage.getItem("return_to");

    this.paymentInfo["item_log"] = JSON.parse(
      sessionStorage.getItem("item_log")
    )
      ? JSON.parse(sessionStorage.getItem("item_log"))
      : [];

    this.paymentInfo["order_source"] = "Admin";
    this.paymentInfo["shipping_method"] = shipping_method;

    console.log(this.paymentInfo);
    this.cartS
      .addPayment(this.paymentInfo)
      .then(res => {
        this.loader = false;
        if (res.result.data) {
          if (res.result.data.payment.success) {
            this.quoteResponse.emit(res.result.data);
            this.openPayment.emit({
              quote: true
            });
          } else {
            $("custom-alert").css("display", "block");
            this.alert.error(
              this.alertContainer,
              res.result.data.payment.message + "!!!",
              true,
              3000
            );
          }
        } else {
          this.alert.error(this.alertContainer, res.result.error, true, 3000);
        }
      })
      .catch(err => {
        console.log(err);
        this.loader = false;
      });
  }

  private getBillInfo() {
    this.paymentInfo = sessionStorage.getItem('billInfo') ? JSON.parse(sessionStorage.getItem('billInfo')) : null;
  }

  private removeShippingAddress() {
    for (let key in this.paymentInfo) {
      if (key.includes("shipping")) {
        delete this.paymentInfo[key];
      }
    }
  }
}
