import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { AdminCheckoutComponent } from "./admin-checkout.component";
import { CartCouponModule } from "../../../modules/cart-coupon/cart-coupon.module";
import { CartDeliveryPartModule } from "../../../modules/cart-delivery-part/cart-delivery-part.module";
import { CartItemModule } from "../../../modules/cart-item/cart-item.module";
import { CartQtyModule } from "../../../modules/cart-qty/cart-qty.module";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { NumberOnlyDirectiveModule } from "../../../modules/directive/directive.module";
import { RentalDatetimepickerModule } from "../../../modules/rental-datetime-picker/rental-datetime-picker.module";
import { CartItemShowComponent } from "./cart-view/cart-item-show/cart-item-show.component";
import { CarttableComponent } from "./cart-view/carttable/carttable.component";
import { CartComponent } from "./cart-view/cart/cart.component";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { CheckoutViewComponent } from "./checkout-view/checkout-view.component";
import { BillingInfoComponent } from "./checkout-view/billing-info/billing-info.component";
import { ShippingSettingCheckoutModule } from "./checkout-view/shipping-info/shipping-info.module";
import { PaymentSettingCheckoutModule } from "./checkout-view/paymnet-settings/paymnet-settings.module";
import { CustomerSearchModule } from "../../../modules/customer-search/customer-search.module";
import { PackageCartModule } from "./package-cart/package-cart.module";
import { DateTimeRangeModule } from './../../../modules/date-time-range/date-time-range.module';
import { TermsConditionComponent } from "../../../modules/terms-condition/terms-condition.component";
import { TermsConditionModule } from "../../../modules/terms-condition/terms-condition.module";
import { AdditionalChargeComponent } from "./cart-view/additional-charge/additional-charge.component";
import { DialogBoxComponent } from "../../../modules/dialog-box/dialog-box.component";
import { DialogBoxModule } from "../../../modules/dialog-box/dialog-box.module";
import { DateFormatModule } from "../../../modules/date-format/date-format-pipe";

@NgModule({
  imports: [
    CommonModule,
    CartCouponModule,
    CartDeliveryPartModule,
    CartItemModule,
    CartQtyModule,
    FormsModule,
    ReactiveFormsModule,
    NumberOnlyDirectiveModule,
    RentalDatetimepickerModule,
    NgbModule,
    ShippingSettingCheckoutModule,
    PaymentSettingCheckoutModule,
    CustomerSearchModule,
    PackageCartModule,
    DateTimeRangeModule,
    TermsConditionModule,
    DialogBoxModule,
    DateFormatModule
  ],
  declarations: [
    AdminCheckoutComponent,
    CartItemShowComponent,
    CarttableComponent,
    CartComponent,
    CheckoutViewComponent,
    BillingInfoComponent,
    AdditionalChargeComponent
  ],
  exports: [AdminCheckoutComponent],
  entryComponents:[TermsConditionComponent,DialogBoxComponent,AdditionalChargeComponent]
})
export class AdminCheckoutModule {}
