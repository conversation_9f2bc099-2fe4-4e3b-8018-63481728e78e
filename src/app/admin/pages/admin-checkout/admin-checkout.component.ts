import { Component, OnInit } from "@angular/core";
import { CartService } from "../../cart-service/cart.service";
import { SidebarService } from "../sidebar-service/sidebar.service";
import { GET_USER } from "../../../globals/_classes/functions";

declare let $: any;
@Component({
  selector: "admin-checkout",
  templateUrl: "./admin-checkout.component.html",
  styleUrls: ["./admin-checkout.component.css"]
})
export class AdminCheckoutComponent implements OnInit {
  checkOut: boolean;
  payment: boolean;
  shipping: boolean;
  sessionEmpty = [
    "cartList",
    "cartToken",
    "billInfo",
    "orderId",
    "inStore",
    "log",
    "item_log",
    "coupon",
    "deliveryCharge",
    "return_to"
  ];
  show: boolean;
  loader: boolean;
  allShipping;
  quote: boolean = false;

  constructor(public cartS: CartService, public sidebarS: SidebarService) {}

  ngOnInit() {
    this.sidebarS.openFrom.subscribe(val => {
      if (!val) {
        this.backToCart();
      }
    });
  }

  checkOutOpen(e) {
    if (e) {
      this.quote = e;
    } else {
      this.quote = false;
    }
    this.checkOut = true;
    this.shipping = false;
    this.payment = false;
    this.show = false;
    this.sidebarS.adjustWidtInCart();
    $(".native-routing-container-cart").scrollTop(0, 0);
  }

  openShipping(address) {
    const instore = sessionStorage.getItem("inStore")
      ? JSON.parse(sessionStorage.getItem("inStore")).instore
      : false;
    if (instore) {
      this.checkOut = true;
      this.shipping = true;
      this.payment = true;
      this.cartS.goToPayment(true);
    } else {
      this.callForShiiping(address);
    }
    this.sidebarS.adjustWidtInCart();
    $(".native-routing-container-cart").scrollTop(0, 0);
  }

  private callForShiiping(address) {
    this.loader = true;
    const obj = {};
    for (let a in address) {
      if (a.includes("shipping_")) {
        obj[a] = address[a];
      }
    }
    this.shipping = true;
    this.payment = false;
    this.allShipping = null;
    const loc = GET_USER().location_id;
    const token = localStorage.getItem("token");
    this.cartS
      .getAllShipping({ address: obj, pickup: loc, token })
      .then(res => {
        this.loader = false;
        if (res.status === "OK" && res.result) {
          this.allShipping = res.result;
        } else {
          this.shipping = false;
        }
      })
      .catch(err => {
        this.loader = false;
        this.shipping = false;
      });
  }

  openBilling(e) {
    if (e) {
      this.payment = true;
      this.cartS.cancelBoltTerminal(null);
    } else {
      this.checkOut = false;
      this.payment = false;
    }
    this.sidebarS.adjustWidtInCart();
    $(".native-routing-container-cart").scrollTop(0, 0);
  }

  backToCart() {
    this.checkOut = false;
    this.payment = false;
    this.show = false;
    this.sidebarS.adjustWidtInCart();
    $(".native-routing-container-cart").scrollTop(0, 0);
  }

  backToContact() {
    this.shipping = false;
    this.payment = false;
    this.show = false;
    this.sidebarS.adjustWidtInCart();
    $(".native-routing-container-cart").scrollTop(0, 0);
  }

  ClearFullCart(y?: boolean) {
    if (y) {
      this.backToCart();
      const cartToken = this.cartS.getSessionData("cartToken");
      if (cartToken) {
        this.cartS
          .deleteAllCart(cartToken)
          .subscribe(res => console.log(res), err => console.log(err));
      }
    } else {
      this.show = true;
      this.sidebarS.adjustWidtInCart();
    }
    this.sessionEmpty.forEach(name => {
      this.cartS.removeSessionData(name);
    });
    this.cartS.cartNoChange(null);
    this.cartS.cancelBoltTerminal(null);
    $(".native-routing-container-cart").scrollTop(0, 0);
  }


  onGoToCart(e){
    if(e){
     this.backToCart();
    }
  }
}
