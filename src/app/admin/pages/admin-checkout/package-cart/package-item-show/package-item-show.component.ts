import {
  Component,
  OnInit,
  Input,
  OnChanges,
  Output,
  EventEmitter,
  ElementRef,
  ViewChild
} from "@angular/core";
import {
  GET_USER,
  changeNullToEmpty,
  checkSameDatetime,
  preventInputAlphabets,
  isCartRentalDateExist,
  getCartDate
} from "../../../../../globals/_classes/functions";
import { CartService } from "../../../../cart-service/cart.service";
import { HttpService } from "../../../../../modules/http-with-injector/http.service";
import { product_image } from "../../../../../globals/endPoint/config";
import { AlertService } from "../../../../../modules/alert/alert.service";
interface CartProduct {
  variants_products_id: number;
  quantity: number;
  product_id: number;
  available: number;
  product_index?: number;
}
declare let $: any;
class CartObject {
  package_id: number;
  token: string;
  price_id?: number;
  location: number;
  rental_type: string;
  products: CartProduct[];
  variants_products_id: number;
  rental_duration?: number;
  term?: number;
  rent_start?: string;
  rent_end?: string;
  quantity: number;
  price?: number;
  is_admin?: boolean;
}
@Component({
  selector: "app-package-item-show",
  templateUrl: "./package-item-show.component.html",
  styleUrls: ["./package-item-show.component.css"]
})
export class PackageItemShowComponent implements OnInit, OnChanges {
  imageUrl = product_image + GET_USER().store_id;
  @Input() product;
  @Input() edit;
  @Input() editItem;
  packageProducts = [];
  selectedVarientQuantity;
  package;
  cartObject: CartObject = new CartObject();
  productPrice;
  rentPriceId = 0;
  rent_startDate;
  rent_endDate;
  showCartError = {
    status: false,
    message: ""
  };
  available_addTo_cart = true;
  is_btndisable;
  isDisableDatePicker = false;

  addonslabel: string;
  addonsProductList = [];

  @Output() resetCart = new EventEmitter();
  @Output() addPackageCart = new EventEmitter();
  @Output() showError = new EventEmitter();
  @ViewChild("hasCusAlert") alertContainer: ElementRef;
  is_recurring_product: boolean;
  recurringPriceId: number;
  selectedPriceObj: any;

  constructor(
    private cartS: CartService,
    private alertS: AlertService,
    private http: HttpService
  ) {
    this.is_btndisable = false;
  }

  ngOnChanges() {
    this.package = this.product;
    this.is_recurring_product = this.product.hasOwnProperty('recurring_prices') && this.product.recurring_prices.length > 0
      ? true
      : false;
    if (this.package.products.length) {
      this.packageProducts = this.package.products;
      this.productPrice = this.cartS.formatePrice(this.package.price);
      this.createCartObject();

      this.isDisableDatePicker = isCartRentalDateExist('adminOrder');

      if (this.isDisableDatePicker) {
        const cartDateObj = getCartDate('adminOrder');
        this.rent_startDate = cartDateObj.startDate;
        this.rent_endDate = cartDateObj.endDate;
      }
      const price = {
        obj: this.is_recurring_product 
          ? this.product.recurring_prices[0]
          : this.productPrice.rent[0],
        recurring: this.is_recurring_product  ? true : false,
        index: 0
      }
      this.formateRent(price.obj, price.index, price.recurring);
    }

    this.getAddonsProductList();

  }


  getAddonsProductList() {
    this.cartS.getAddonProductListById(this.package.id).then(res => {
      if (res.status == "OK") {
        this.addonsProductList = res.result.data;
        this.addonslabel = res.result.label;
        this.addonsProductList.map(prod => {
          let index = 0;
          if (prod.image) {
            prod['img'] = `${this.imageUrl}/${prod.id}/${prod.image}`;
          } else {
            prod['img'] = './assets/img/home/<USER>';
          }
          prod.variants.map(v => {
            if (index == 0) {
              v["min_qty"] = prod.min_quantity;
            }
            else {
              v["min_qty"] = 0;
            }
            index++;
          })
          return prod;
        })
        console.log(this.addonsProductList)
      }
      else {
        this.addonsProductList = [];
      }
    }).catch(err => {
      console.log(err)
    })
  }

  private async getRentDateForDisabledEndDate(
    price_id,
    locan_id,
    start_date
  ): Promise<any> {
    await this.http
      .get(
        `product/get_dates_price_duration?start_date=${start_date}&price_id=${price_id}&location=${locan_id}`
      )
      .toPromise()
      .then(res => {
        if (res.status == "OK") {
          let data = res.result.data;
          this.rent_startDate = data.start_date;
          this.rent_endDate = data.end_date;
          this.cartObject.rent_start = data.start_date;
          this.cartObject.rent_end = data.end_date;
        }
      });
  }


  onVariantQtyKeyup(quantity, variant_id, product_id) {
    if (!isNaN(parseInt(quantity))) {

      this.addonsProductList.map(p => {
        if (p.id == product_id) {
          p.variants.map(v => {
            if (v.id == variant_id) {
              v['min_qty'] = parseInt(quantity);
            }

          })
        }
      })

      console.log(this.addonsProductList)
    }

  }

  createCartObject() {
    const defaultCartParoduct = this.packageProducts.map((res, index) => {
      return {
        variants_products_id: res.variants[0].id,
        quantity: res.variants[0].quantity,
        product_id: res.id,
        available: res.variants[0].available,
        product_index: index
      };
    });

    const obj = {
      package_id: this.package.id,
      token: this.cartS.getSessionData("cartToken"),
      location: GET_USER().location_id,
      rental_type: "rent",
      products: defaultCartParoduct,
      variants_products_id: this.package.variants_products_id,
      quantity: this.cartObject.quantity ? this.cartObject.quantity : 1
    };
    this.cartObject = obj;
    this.checKAvailablity();
  }

  checKAvailablity() {
    this.available_addTo_cart = this.cartObject.products.every(
      item => item.available > item.quantity * this.cartObject.quantity
    );
    if (!this.available_addTo_cart) {
      this.showCartError = {
        status: true,
        message: "Unavailable Product"
      };
    } else {
      this.showCartError = {
        status: false,
        message: ""
      };
    }
  }
  removingUnAssignedValue(item) {
    // console.log(item)
    let products_varients = [];
    products_varients = item;
    products_varients = products_varients.filter(
      v => v.variant_chain !== "Unassigned: Unassigned"
    );
    return products_varients;
  }
  decreaseItemQuant() {
    if (this.cartObject.quantity > 1) {
      this.cartObject.quantity--;
      this.checKAvailablity();
    }
  }

  increaseItemQuant() {
    if (this.available_addTo_cart) {
      this.cartObject.quantity++;
      this.checKAvailablity();
    }
  }

  async formateRent(data, i, isRecurring?: boolean) {
    event.preventDefault();
    if (this.package.recurring_prices && this.package.recurring_prices.length > 0) {
      this.selectedPriceObj = isRecurring ? data : undefined;
    }
    if (this.selectedPriceObj) {
      this.recurringPriceId = data["id"];;
      this.rentPriceId = null;
    } else {
      this.rentPriceId = data["id"];;
      this.recurringPriceId = null;
    }
    if (this.isDisableDatePicker) {
      const cartDateObj = getCartDate('adminOrder');
      this.rent_startDate = cartDateObj.startDate;
      this.rent_endDate = cartDateObj.endDate;

      this.cartObject.price = this.product.rental_price;
    } else {
      this.cartObject.price = data["price"];
      this.cartObject.price_id = data["id"];
      const loc = localStorage.getItem("currentUser")
        ? JSON.parse(localStorage.getItem("currentUser")).location_id
        : null;
      await this.getRentDateForDisabledEndDate(
        this.cartObject.price_id,
        loc,
        this.rent_startDate ? this.rent_startDate : data.rent_start
      );
    }
    // this.rentPriceId = i;
    this.cartObject.price_id = data["id"];
    this.cartObject.rental_duration = data["rent_duration"];
    this.cartObject.term = data["duration"];
    this.formateCartRentalDate();
  }
  ngOnInit() {
    // console.log(this.product);
  }
  private formateCartRentalDate() {
    this.cartObject.rent_start = this.rent_startDate;
    this.cartObject.rent_end = this.rent_endDate;
  }
  private rentalDateChange(e) {
    this.rent_startDate = e;
    this.formateCartRentalDate();
  }
  private rentalEndDateChange(e) {
    this.rent_endDate = e;
    this.formateCartRentalDate();
  }
  reset() {
    this.cartObject = new CartObject();
    this.resetCart.emit(true);
  }


  modifyAddonsProductVariantQuantity(quantity) {
    this.addonsProductList = this.addonsProductList.map(p => {
      let index = 0;
      p.variants.map(v => {
        if (index == 0) {
          v['min_qty'] = quantity * p.min_quantity;
        }
        else {
          v['min_qty'] = 0;
        }

        index++;

        return v;
      })

      return p;
    })

    console.log(this.addonsProductList)
  }


  isAddonsProductCombinationOk() {

    let requiredAddonsQty = 0;
    let result = true;
    let sumOfVariantQty = 0;

    if (this.addonsProductList.length > 0) {
      this.addonsProductList.map(p => {
        requiredAddonsQty = requiredAddonsQty + (this.cartObject.quantity * p.min_quantity)

        p.variants.map(v => {
          sumOfVariantQty = sumOfVariantQty + v.min_qty
        })
      })

      if (sumOfVariantQty > requiredAddonsQty) {
        result = false;
      }

    }

    return result;
  }


  addItem() {

    if (!this.isAddonsProductCombinationOk()) {
      this.alertS.error(
        this.alertContainer,
        "Select product add-on quantities",
        true,
        5000
      );

      return
    }


    this.showCartError = {
      status: false,
      message: ""
    };

    this.cartObject.is_admin = true;
    const cart = changeNullToEmpty(this.cartObject);
    if (this.isExactStartDate) {
      cart['rent_start'] = this.product.rent_start;
      cart['rent_end'] = this.product.rent_end;
    }

    if (isCartRentalDateExist('adminOrder')) {
      cart['source'] = 'admin'
    }

    if (this.addonsProductList.length > 0) {
      let required_addons = [];
      let variant = [];

      this.addonsProductList.map(p => {
        variant = p.variants.filter(v => v.min_qty > 0);
        if (variant.length != 0) {
          for (let i = 0; i < variant.length; i++) {
            required_addons.push({
              product_id: p.id,
              variants_products_id: variant[i].id,
              quantity_id: variant[i].quantity_id,
              quantity: variant[i].min_qty
            })
          }
        }
      });
      cart['required_addons'] = required_addons;
    }

    if (this.is_recurring_product) {
      if (this.selectedPriceObj && this.selectedPriceObj.recurring) {
        cart["recurring"] = true;
      }
    }

    this.http
      .post("package/add-to-cart", cart)
      .toPromise()
      .then(res => {
        if (res.status === "OK") {
          if (res.result.error) {
            this.showCartError = {
              status: true,
              message: res.result.error
            };
            this.showError.emit({
              status: "NOK",
              result: {
                error: "Something Wrong Please try again !!!"
              }
            });
          } else {
            this.addPackageCart.emit(res);
          }
        } else {
          this.showError.emit({
            status: "NOK",
            result: {
              error: "Something Wrong Please try again !!!"
            }
          });
        }
      })
      .catch(err => {
        this.showError.emit({
          status: "NOK",
          result: {
            error: "Something Wrong Please try again !!!"
          }
        });
      });
  }

  showVarientQuantiy(varient_id, id, index) {
    let selectedVarient;
    this.packageProducts.forEach(item => {
      if (item.id === id) {
        selectedVarient = item.variants.find(v => v.id === Number(varient_id));
      }
    });

    //  Show available & quantity
    // $(`#${id}`).val = selectedVarient.quantity;
    // document.getElementById(`ap_${id}`).innerHTML = `Available : ${
    //   selectedVarient.available
    // }`;

    this.cartObject.products.forEach(item => {
      if (item.product_index === index) {
        // item.quantity = selectedVarient.quantity;
        item.variants_products_id = selectedVarient.id;
        // item.available = selectedVarient.available;
      }
    });
    this.checKAvailablity();
    // console.log(this.cartObject);
  }
  async checkPrice(e) {
    if (this.showPricingOption && !this.showEndDate) {
      const loc = localStorage.getItem("currentUser")
        ? JSON.parse(localStorage.getItem("currentUser")).location_id
        : null;
      await this.getRentDateForDisabledEndDate(this.cartObject.price_id, loc, this.rent_startDate);
    }
    const cart = changeNullToEmpty(this.cartObject);
    let result = checkSameDatetime(
      this.showStartTime,
      this.rent_startDate,
      this.rent_endDate
    );
    result = false;
    if (result) {
      this.is_btndisable = true;
      this.alertS.error(
        this.alertContainer,
        "Please select correct end date",
        true,
        5000
      );
    } else {
      this.http
        .post("get-package-price", cart)
        .toPromise()
        .then(res => {
          if (res.status === "OK") {
            if (res.result.error) {
              return;
            }
            // this.packageProducts = res.result.products;
            // this.createCartObject();
            this.cartObject.price = res.result.data;
            this.is_btndisable = false;
          }
        });
    }

  }
  // new date time range picker
  get showStartTime() {
    const contents = localStorage.getItem("contents")
      ? JSON.parse(localStorage.getItem("contents"))
      : null;
    if (
      contents.site_specific.confg &&
      contents.site_specific.confg.hasOwnProperty("show_start_time")
    ) {
      return contents.site_specific.confg.show_start_time;
    }
    return true;
  }

  get showEndDate() {
    const contents = localStorage.getItem("contents")
      ? JSON.parse(localStorage.getItem("contents"))
      : null;

    if (
      contents.site_specific.confg &&
      contents.site_specific.confg.hasOwnProperty("show_end_date")
    ) {
      return contents.site_specific.confg.show_end_date;
    }
    return true;
  }


  selectstartDateTime(e) {
    this.rent_startDate = e;
    this.cartObject.rent_start = e;
  }

  selectendDateTime(e) {
    this.rent_endDate = e;
    this.cartObject.rent_end = e;
  }


  validateQuantity(e) {
    preventInputAlphabets(e);
  }

  onQuantityMouseLeave(value) {
    if (value === "") {
      this.cartObject.quantity = 1;
    }
  }

  get isExactStartDate(): boolean {
    const contents = localStorage.getItem("contents")
      ? JSON.parse(localStorage.getItem("contents"))
      : null;
    if (
      contents.site_specific.confg &&
      contents.site_specific.confg.datetime &&
      contents.site_specific.confg.datetime.hasOwnProperty(
        "exact_start_date"
      ) && contents.site_specific.confg.datetime.exact_start_date
    ) {
      return true;
    }
    return false;
  }

  get showPricingOption() {
    const contents = localStorage.getItem("contents")
      ? JSON.parse(localStorage.getItem("contents"))
      : null;

    if (
      contents.site_specific.confg &&
      contents.site_specific.confg.hasOwnProperty("rental_price_option")
    ) {
      return contents.site_specific.confg.rental_price_option;
    }
    return true;
  }

    // new date time range picker ngx-daterangepicker-material from template
  getDateFromManualChange(e) {
    if (e.startDate) {
      this.selectstartDateTime(e.startDate);
    } else if (e.endDate) {
      this.selectendDateTime(e.endDate);
    }
  }
}
