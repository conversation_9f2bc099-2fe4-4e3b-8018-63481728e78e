<div class="custom-alert" #hasCusAlert></div>

<div
  class="m-portlet__body form-panel"
  style="padding-bottom: 15px;"
  *ngIf="package"
>
  <div class="row">
    <div class="col-xl-3 col-lg-4 col-md-4">
      <div class="img-container mt-3">
        <img
          *ngIf="package.images.length == 0; else alter"
          class="img-fluid img-avatar img-thumbnail img-orgin"
          src="./assets/img/home/<USER>"
          alt="Product Image"
        />
        <ng-template #alter>
          <img
            class="img-fluid img-avatar img-thumbnail img-orgin"
            src="{{
              imageUrl +
                '/' +
                package.id +
                '/' +
                (package.images[0] ? package.images[0].image_large : '')
            }}"
            alt="Product Image"
            onError="this.src='./assets/img/home/<USER>';"
          />
        </ng-template>
      </div>
    </div>
    <div class="col-xl-5 col-lg-8 col-md-8 pl-0">
      <h3 class="pt-4">{{ package.name }}</h3>
      <div class="price mt-3 mb-3">
        <h4>
          Price: {{ cartObject.quantity * cartObject.price * 1 | currency }}
        </h4>
      </div>

      <ng-container *ngIf="!is_recurring_product">
        <div class="m-form__group form-group row" style="margin:0px;" *ngIf="!isExactStartDate">
          <div class="m-radio-list col-sm-6">
            <label
            class="w-100" [ngClass]="[!isDisableDatePicker ? 'm-radio' : '']"
              *ngFor="let rent of productPrice.rent; let i = index"
              (click)="formateRent(rent, i)"
            >
              <input
              *ngIf="!isDisableDatePicker;else arrow"
                type="radio"
                [value]="rent.id"
                name="rent"
                [(ngModel)]="rentPriceId"
              />
  
              <ng-template #arrow>
                  <label><i class="fa fa-arrow-right"></i>&nbsp;</label>
              </ng-template>
  
              {{ rent.price | currency }} {{ rent.duration ? "/" : "" }}
              {{ rent?.duration }} {{ rent.rent_type }}
              <span></span>
            </label>
          </div>
        </div>
      </ng-container>

      <ng-container *ngIf="is_recurring_product">
        <div class="m-form__group form-group row" style="margin:0px;" *ngIf="!isExactStartDate">
          <div class="m-radio-list col-sm-6">
            <h5 style="margin-bottom: 1rem;">Recurring pricing</h5>
            <label
            class="w-100" [ngClass]="[!isDisableDatePicker ? 'm-radio' : '']"
              *ngFor="let rent of package.recurring_prices; let i = index"
              (click)="formateRent(rent, i, true)"
            >
              <input
                *ngIf="!isDisableDatePicker;else arrow"
                type="radio"
                [value]="rent.id"
                name="rent"
                [(ngModel)]="recurringPriceId"/>
              <ng-template #arrow>
                  <label><i class="fa fa-arrow-right"></i>&nbsp;</label>
              </ng-template>

              <ng-container *ngIf="rent?.term; else noTerm">
                {{rent?.term}} {{rent.term > 1 ? rent.label + 's' : rent.label}} (billed {{rent.label}}ly at {{ rent?.price | currency }})
              </ng-container>
              <ng-template #noTerm>
                {{ rent.price | currency }} {{ rent.duration ? "/" : "" }}
                {{ rent?.duration }} {{ rent.label }}
              </ng-template>
              <span></span>
            </label>
          </div>
        </div>
      </ng-container>

      <div style="position: relative;" *ngIf="!isExactStartDate; else dueDate">
        <label>Rental date range:</label>
        <app-date-time-range
        [ngClass]="[showEndDate === false ? 'single' : 'both']"
          (startDateTime)="selectstartDateTime($event)"
          (endDateTime)="selectendDateTime($event)"
          (reload)="checkPrice($event)"
          (dateModel)="getDateFromManualChange($event)"
          [placeholderEnd]="rent_endDate"
          [placeholderStart]="rent_startDate"
          [displayTime]="showStartTime"
          [onlyStartDate]="!showEndDate"
          [diabledPicker]="isDisableDatePicker"
        ></app-date-time-range>
      </div>
      <ng-template #dueDate>
        <h4>Due Date {{product.rent_end | date: "MM/dd/yyyy"}}</h4>
      </ng-template>

      <div class="form-group mt-2">
        <label class="colorPurpel">Quantity</label>
        <div class="quantity admin-quantity clearfix">
          <span class="btn btn-sm btn-dark no-m" (click)="decreaseItemQuant()"
            >-</span
          >
          <!-- <span class="cart-qunt btn btn-sm no-m">{{
            cartObject.quantity
          }}</span> -->
          <input type="text" autocomplete="off" class="input-qty" name="qty" 
          [(ngModel)]="cartObject.quantity"
          (keydown)="validateQuantity($event)" 
          (mouseleave)="onQuantityMouseLeave($event.target.value)" />
          <span class="btn btn-sm btn-dark  no-m" (click)="increaseItemQuant()"
            >+</span
          >
        </div>
        <!-- <p *ngIf="showCartError.status" style="color:red ;margin-top: 12px; margin-bottom: 0">{{showCartError.message}} </p> -->
      </div>

      <div class="row viewpage-addons" *ngIf="addonsProductList.length>0">
        <h2 class="product-name mb-3 mt-4 pl-3">{{addonslabel}}</h2>
        <div class="col-md-12 mt-2" *ngFor="let addon of addonsProductList">
          <h5>
            <img [src]="addon.img">{{addon?.name}}
          </h5>
          <div class="row mt-2 mb-2" *ngFor="let variant of addon.variants">
            <div class="col-xl-6 col-lg-7 col-md-10 col-sm-10 col-10">
              <label *ngIf="variant?.name !='Unassigned: Unassigned'" class="mb-0 mt-2">{{variant?.name}}</label>
            </div>
            <div class="col-xl-4 col-lg-4 col-md-2 col-sm-10 col-2">
              <input 
              class="form-control float-right"
              [ngClass]="[
              variant?.name =='Unassigned: Unassigned'
                ? 'form-control no-variant'
                : 'form-control'
              ]"
              type="text" autocomplete="off"  
              name="qty" [value]="variant?.min_qty"
              (keyup)="onVariantQtyKeyup($event.target.value,variant.id,addon?.id)"  />
            </div>
          </div>
          <hr>
        </div>
      </div>

      
      <div class="quant row">
        <div class="col-md-12 mt-3">
          <!-- <button
            class="btn btn-brand"
            [disabled]="!available_addTo_cart"
            (click)="addItem()"
          >
            Add Item
          </button> -->
          <button
          [disabled]="is_btndisable"
            class="btn btn-brand"
            (click)="addItem()"
          >
            {{edit ? 'Update Item' : 'Add Item'}}
          </button>
          <!-- <button *ngIf="edit" class="btn btn-brand" (click)="updateCart()">
                    Update Item
                  </button> -->
          <button
            class="btn btn-danger"
            (click)="reset()"
            style="margin-left: 10px;"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>

    <div class="col-xl-4 col-lg-12 col-md-12">
      <div class=" packageContainer " *ngIf="packageProducts.length">
        <div class="products mt-3">
          <h6>Products Include</h6>                
          <div *ngIf="package?.package_content" class="p-3" [innerHTML]="package?.package_content | safeHtml"></div>

          <div class="single_product " *ngFor="let product of packageProducts; let c = index">
            <div class="package-pt">
              <h5>{{ product.name }} ({{ product.quantity }})</h5>
            </div>

            <div class="product-varient">
              <div class="product-varient-inner">
                <select
                  *ngIf="removingUnAssignedValue(product.variants).length"
                  (change)="showVarientQuantiy($event.target.value, product.id, c)"
                  class="form-control"
                  name=""
                  id=""
                >
                  <option
                    [selected]="i === 0"
                    [value]="varient.id"
                    *ngFor="
                      let varient of removingUnAssignedValue(product.variants);
                      let i = index
                    "
                    >{{ varient.variant_chain }}</option
                  >
                </select>

                <!-- <div class="qtyBtn">
                                <input [id]="product.id" type="text" class="form-control" readonly [value]="product.variants[0].quantity">
                                <span><small [id]="'ap_'+product.id" >Available : {{  product.variants[0].available}}</small></span>
                            </div> -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
