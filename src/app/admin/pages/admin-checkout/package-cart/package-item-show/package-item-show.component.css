.m-portlet__body {
    display: flex;
    width: 100%;
}

.img-container {
    /* width: 300px; */
    /* flex: 1; */
}
.packageContainer {
    /* flex: 2; */
}
.price h4 {
    font-size: 35px;
    font-weight: 400;
}
.products {
    border: 1px solid #f2f3f8;
    /* padding-bottom: 15px; */
}
.products h6 {
    padding: 10px 15px;
    background-color: #f2f3f8;
    border-bottom: 1px solid rgba(0,0,0,.125);
    text-align: center;
    font-weight: 500;
    text-transform: uppercase;
    font-size: 18px;
}
.single_product {
    display: flex;
    flex-direction: column;
    padding: 0 15px;
    padding-bottom: 10px;
}
.product-varient,
.package-pt {
    width: 100%;
}
.package-pt h5 {
    padding: 5px 0;
    text-transform: uppercase;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 0;
}
.product-varient-inner {
    display: flex;
}
.product-varient-inner select {
    margin-right: 15px;
    width: 300px;
}
.qtyBtn {
    text-align: center;
}
.qtyBtn .form-control {
    background-color: #f2f3f8;
    border: 1px solid #f2f3f8;
}
.product-varient-inner input {
    text-align: center;
    width: 150px;
}

.custom-alert{
    position: absolute;
    top: 48px;
    right: 50px;
    z-index: 99000;
}


.viewpage-addons .form-control {
    width: 40px;
    height: calc(2rem + 2px);
    padding: 0;
    text-align: center;
}
.no-variant {
    float: right;
    margin-top: -46px;
}
.viewpage-addons h5 img {
    width: 60px;
    border: 1px solid #eee;
    margin-right: 10px;
    height: 50px;
    object-fit: cover;
}
.viewpage-addons label {
    padding-left: 72px;
}

@media (max-width: 991px) {
    .viewpage-addons h5 {
        font-size: 14px;
    } 
}
@media (max-width: 575px) {
    .viewpage-addons h5 img {
        width: 45px;
        height: 40px;
    } 
    .viewpage-addons label {
        padding-left: 56px;
    }
}