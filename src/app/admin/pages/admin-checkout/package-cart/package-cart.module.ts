import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { PackageItemShowComponent } from "./package-item-show/package-item-show.component";
import { FormsModule } from "@angular/forms";
import { RentalDatetimepickerModule } from "../../../../modules/rental-datetime-picker/rental-datetime-picker.module";
import { CurrencyFormatModule } from "../../../../modules/currency-format/currency-format.pipe";
import { DateTimeRangeModule } from './../../../../modules/date-time-range/date-time-range.module';
import { SafeHtmlModule } from "./../../../../modules/safe-html/safe-html.pipe";

@NgModule({
  imports: [CommonModule, FormsModule, RentalDatetimepickerModule, CurrencyFormatModule, DateTimeRangeModule, SafeHtmlModule],
  declarations: [PackageItemShowComponent],
  exports: [PackageItemShowComponent]
})
export class PackageCartModule {}
