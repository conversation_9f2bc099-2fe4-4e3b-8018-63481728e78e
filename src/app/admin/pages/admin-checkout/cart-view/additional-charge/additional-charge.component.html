<div class="custom-alert" #hasCusAlert></div>

<div class="m-grid__item m-grid__item--fluid m-wrapper mb-0">
  <div class="m-content">
    <div class="row">
      <div class="col-xl-12 col-lg-12 col-md-12">
        <div class="m-portlet m-portlet--mobile mb-0">
          <div class="m-portlet__head">
            <div class="m-portlet__head-caption">
              <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text"> Additional Charge </h3>
                <a class="additinal-modal-close" (click)="onClickCloseModal()"><i class="la la-close"></i></a>
              </div>
            </div>
          </div>

          <div class="m-portlet__body">
            <div class="row">

              <div class="col-12">
                <div *ngIf="is_ShowChargeForm">
                  <form class="m-form m-form--fit m-form--label-align-right" #form="ngForm">
                    <div class="row">
                      <div class="col-sm-8">
                        <label for="note" class="colorPurpel">
                          Note
                        </label>
                        <div class="form-group m-form__group">
                          <input class="form-control m-input" id="note" type="text" autocomplete="off"
                            placeholder="Note" name="note" #note="ngModel" [(ngModel)]="charge.note" />
                          <span *ngIf="note.invalid && note.touched">
                            <small *ngIf="note.errors.required" class="error">Note is required</small>
                          </span>
                        </div>
                      </div>
                      <div class="col-sm-4">
                        <label for="amount" class="colorPurpel">
                          Amount
                        </label>
                        <div class="form-group m-form__group">
                          <input class="form-control m-input" id="amount" type="number" autocomplete="off"
                            placeholder="Amount" name="amount" #amount="ngModel" [(ngModel)]="charge.amount" />
                          <span *ngIf="amount.invalid && amount.touched">
                            <small *ngIf="amount.errors.required" class="error">Amount is required</small>
                          </span>
                        </div>
                      </div>
                    </div>
                    <div class="pb-4">
                      <div *ngIf="loader; else addCharge" class="m-loader m-loader--brand"
                        style="width: 30px; display: inline-block;"></div>
                      <ng-template #addCharge>
                        <button *ngIf="is_edit_charge; else add" type="submit" [disabled]="!form.form.valid"
                          (click)="updateCharge()" class="btn btn-brand btn-sm mr-2">
                          Update
                        </button>
                        <ng-template #add>
                          <button type="submit" [disabled]="!form.form.valid" (click)="submitCharge(form)" 
                          class="btn btn-brand btn-sm mr-2">
                            Submit
                          </button>
                        </ng-template>
                        <button type="button" class="btn btn-danger btn-sm" (click)="resetCharge()">
                          Cancel
                        </button>
                      </ng-template>
                    </div>
                  </form>
                  <!--end::Form-->
                </div>

                <div class="float-right" *ngIf="!is_ShowChargeForm">
                  <button (click)="addCharge()" title="Add"
                    class="btn m-btn add-charge-btn">
                    Add charge<i class="fa fa-plus ml-2"></i>
                  </button>
                </div>

                <div class="table-responsive">
                  <table class="table cart mb-0">
                    <thead>
                      <tr>
                        <!--              <th>Date</th>-->
                        <th>Note</th>
                        <th>Amount</th>
                        <th>Action</th>
                      </tr>
                    </thead>
                    <tbody *ngIf="chargeList && chargeList.length > 0; else NoItem">
                      <ng-container *ngFor="let charge of chargeList; let i = index">
                        <tr>
                          <!--                <td>-->
                          <!--                  {{ getDate(charge.created) | date: "MM/dd/yyyy, hh:mm a" }}-->
                          <!--                </td>-->
                          <td>
                            {{ charge.note }}
                          </td>
                          <td>
                            {{ charge.amount | currency }}
                          </td>
                          <td>
                            <button (click)="editCharge(charge.id)" title="Edit"
                              class="btn m-btn m-btn--hover-info m-btn--icon m-btn--icon-only m-btn--pill">
                              <i class="fa fa-edit"></i>
                            </button>
                            <button (click)="removeCharge(charge.id, i)"
                              class="btn m-btn m-btn--hover-danger m-btn--icon m-btn--icon-only m-btn--pill">
                              <i class="fa fa-trash"></i>
                            </button>
                          </td>
                        </tr>
                      </ng-container>
                    </tbody>
                    <ng-template #NoItem>
                      <tbody>
                        <tr>
                          <td colspan="7">
                            <h5 class="text-center">No Charges Added</h5>
                          </td>
                        </tr>
                      </tbody>
                    </ng-template>
                  </table>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>

    </div>
  </div>
</div>