import { Component, OnInit, ViewChild, ElementRef, Input } from '@angular/core';
import { NgbModal, NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { ActivatedRoute, Router } from '@angular/router';
import { Customer, Order } from '../../../reservations/models/order-models';
import { AlertService } from '../../../../../modules/alert/alert.service';
import { OrderService } from '../../../reservations/order.service/order.service';
import { DialogBoxComponent } from '../../../../../modules/dialog-box/dialog-box.component';
import { Helpers } from '../../../../../helpers';
import { isJson } from '../../../../../globals/_classes/functions';


class Charge {
  note: string;
  amount: number;
  order_id: number;
}


@Component({
  selector: 'app-additional-charge',
  templateUrl: './additional-charge.component.html',
  styleUrls: ['./additional-charge.component.css']
})
export class AdditionalChargeComponent implements OnInit {


  charge: Charge;
  chargeList = [];
  is_ShowChargeForm = false;
  is_edit_charge = false;
  charge_id = 0;
  loader: boolean = false;
  order_id: number;

  customer: Customer;
  order: Order = new Order();
  cartData;

  @ViewChild("hasCusAlert") alertContainer: ElementRef;


  constructor(
    private router: Router,
    private alertS: AlertService,
    private route: ActivatedRoute,
    private modalService: NgbModal,
    private orderS: OrderService,
    private activeModal: NgbActiveModal,
  ) {
    this.charge = new Charge();
  }

  ngOnInit() {
    const data = sessionStorage.getItem('cartList');
    this.cartData = data && isJson(data) ? JSON.parse(data) : null;

    this.order_id = parseInt(this.route.snapshot.params["order_id"]);
    this.getChargeList();
  }



  //***************charge section***************** */
  submitCharge(form) {
    this.loader = true;
    console.log(form);
    this.charge['cart_id'] = this.cartData.cart_id;
    this.orderS
      .addOrUpdateCharge(this.charge)
      .then(res => {
        console.log(res);

        this.alertS.success(
          this.alertContainer,
          "Charge has been added",
          true,
          5000
        );

        this.charge = new Charge();
        this.getChargeList();
        this.loader = false;
      })
      .catch(err => {
        console.log(err);
        this.error(err, "Something wrong! Charge has been not added");
        this.loader = false;
      });
  }

  addCharge() {
    this.charge = new Charge();
    this.is_ShowChargeForm = true;
  }

  editCharge(id) {
    this.is_ShowChargeForm = true;
    this.is_edit_charge = true;
    this.orderS
      .getCharge(this.cartData.cart_id, id)
      .then(res => {
        this.loader = false;
        let data = res.result.data;

        this.charge.note = data.note;
        this.charge.amount = data.amount;
        this.charge_id = parseInt(id);
        console.log(res);
      })
      .catch(err => {
        console.log(err);
        this.loader = false;
      });
  }

  updateCharge() {
    this.loader = true;

    this.charge['cart_id'] = this.cartData.cart_id;
    this.charge["id"] = this.charge_id;
    this.orderS
      .addOrUpdateCharge(this.charge)
      .then(res => {
        console.log(res);

        this.alertS.success(
          this.alertContainer,
          "Charge has been updated",
          true,
          5000
        );

        this.getChargeList();
        this.loader = false;
        this.is_ShowChargeForm = false;
      })
      .catch(err => {
        console.log(err);
        this.error(err, "Something wrong! Charge has been not updated");
        this.loader = false;
      });
  }

  resetCharge() {
    this.charge = new Charge();
    this.is_ShowChargeForm = false;
    // console.log(this.customer);
  }

  getChargeList() {
    this.loader = true;
    this.orderS
      .getAdditionalChargeList(this.cartData.cart_id)
      .then(res => {
        this.loader = false;
        this.chargeList = res.result.data;
        console.log(this.chargeList);
      })
      .catch(err => {
        console.log(err);
        this.loader = false;
      });
  }

  removeCharge(id, i) {
    const modalRef = this.modalService.open(DialogBoxComponent, {
      centered: true,
      size: "sm"
    });
    modalRef.componentInstance.massage = "Are you sure you want to delete?";
    modalRef.result.then(
      result => {
        if (result) {
          Helpers.setLoading(true);
          this.archiveChargeItem(id, i);
        }
      },
      res => {
        console.log(res);
      }
    );
  }

  archiveChargeItem(id, i) {
    this.orderS
      .deleteCharge(id)
      .then(res => {
        Helpers.setLoading(false);
        if(res.status=="OK"){
          this.alertS.success(
            this.alertContainer,
            "Charge has been deleted",
            true,
            5000
          );

          this.getChargeList();
        }
        else{
          this.alertS.error(
            this.alertContainer,
            "Something wrong! Charge has not been deleted",
            true,
            5000
          );
        }
        
      })
      .catch(err =>
        this.error(err, "Something wrong! Charge has not been deleted")
      );
  }

  error(err, message) {
    this.loader = false;
    Helpers.setLoading(false);
    this.alertS.error(this.alertContainer, message, true, 5000);
  }

  onClickCloseModal(){
    this.activeModal.close(true)
  }


}
