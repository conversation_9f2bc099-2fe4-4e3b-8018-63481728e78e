.not_found{
    text-align: center;
    font-size: 17px;
    font-weight: 400;
    margin-top: 26px;
    background: #efefef;
    padding: 13px;
}

.custom-alert{
    position: fixed;
    top: 90px;
    right: 30px;
    z-index: 5000;
  }

.search-btn {
    position: absolute;
    height: 50px;
    right: 20px;
    top: 5px;
    background-color: transparent;
    color: #333;
    border-radius: 0;
    border: none;
    padding: 0 20px;
}
.search-btn:hover {
    color: #111 !important;
    background-color: transparent !important;
}
.btn.btn-brand.active, .btn.btn-brand.focus, .btn.btn-brand:focus, .btn.btn-brand:hover:not(:disabled) {
    color: #111 !important;
    background-color: transparent !important;
}

.form-group{
padding-left: 0 !important;
padding-right: 0 !important;
padding-top: 0 !important;
padding-bottom: 15px !important;
}

.additinal-modal-close {
    float: right;
    padding: 17px 7px;
    cursor: pointer;
}
.m-portlet .m-portlet__head {
    height: 4.1rem;
}
.m-portlet .m-portlet__body {
    padding: 15px 20px;
}
.m-portlet .m-portlet__head {
    padding: 0 20px;;
}
table tr th,
table tr td {
    text-align: center;
}
.add-charge-btn {
    background-color: transparent;
    color: #555;
    font-weight: 500;
}
.add-charge-btn:hover {
    background-color: transparent;
    color: #555;
}
.table thead tr th {
    background-color: #f2f3f8;
}
.btn-brand:hover {
    background-color: #111 !important;
    color: #fff !important;
}
.btn.btn-brand.active, 
.btn.btn-brand.focus, 
.btn.btn-brand:focus, 
.btn.btn-brand:hover:not(:disabled) {
    background-color: #111 !important;
    color: #fff !important;
}