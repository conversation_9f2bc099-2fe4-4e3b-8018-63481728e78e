import {
  Component,
  OnInit,
  ViewChild,
  ElementRef,
  EventEmitter,
  Output,
  AfterViewInit,
  Input,
  OnChanges
} from "@angular/core";
import { Helpers } from "../../../../../helpers";
import { product_image } from "../../../../../globals/endPoint/config";
import { Subscription } from "rxjs";
import { AlertService } from "../../../../../modules/alert/alert.service";
import { CartService } from "../../../../cart-service/cart.service";
import { CartItem, Coupon } from "../../../../cart-service/cart.models";
import {
  GET_USER,
  convertTime12to24,
  formatProductSearch,
  changeNullToEmpty,
  formateRentType,
  checkSameDatetime,
  preventInputAlphabets,
  setCartRentalDate,
  removeCartDate,
  getCartDate,
  isCartRentalDateExist
} from "../../../../../globals/_classes/functions";
import { Observable } from "rxjs/internal/Observable";
import {
  debounceTime,
  distinctUntilChanged,
  tap,
  switchMap,
  map,
  retry,
  catchError
} from "rxjs/operators";
import { SidebarService } from "../../../sidebar-service/sidebar.service";
import { HttpService } from "../../../../../modules/http-with-injector/http.service";
import { AdditionalChargeComponent } from "../additional-charge/additional-charge.component";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";

declare let $: any;

interface Price {
  base: {};
  rent: any[];
}

class RentStart {
  date: string;
  time: string;
}
class RentEnd {
  date: string;
  time: string;
}

class Variant {
  variants: Attr[] = [];
  variants_products_id: number;
}

class Attr {
  attr_set_id: number;
  attr_set: string;
  attr: string;
}

@Component({
  selector: "app-cart",
  templateUrl: "./cart.component.html",
  styleUrls: ["./cart.component.css"]
})
export class CartComponent implements OnInit, AfterViewInit {
  imageUrl = product_image + GET_USER().store_id;
  loader_sub: boolean;
  loader: boolean;
  loaderAdd: boolean;
  sub: Subscription[] = [];
  product = null;
  productPrice: Price;
  mode: number = 1;
  item: CartItem;
  rentalStart: RentStart = new RentStart();
  rentalEnd: RentEnd = new RentEnd();
  rentPriceId = 0;
  productList;
  variants: Variant = new Variant();
  locationList = [];
  shipping;
  session = ["cartList"];
  edit: boolean;
  rent: boolean;
  index: number;
  couponCode: Coupon = new Coupon();
  fromInventory: boolean = true;
  searchData: any[] = [];
  calenderDate: string;
  rent_startDate;
  rent_endDate;

  cart_startDate;
  cart_endDate;

  min_date: string = "";
  isDisableDatePicker = false;
  isRentalDateEditMode: boolean = false;
  isShowCartRentalDate = false;

  enable_exact_time = false;
  extact_durations = [];
  extact_times = [];
  selected_exact_time = "null";
  selected_exact_duration;
  contents;
  addonsProductList = [];
  addonslabel: string;

  type: number; // [1 = product , 2 = package ]

  alertForFreeAccount = {};
  is_recurring_product: boolean;
  selectedPriceObj: any;
  recurringPriceId: number;

  @Output("clear") clear: EventEmitter<any> = new EventEmitter();
  @Output("checkOut") checkOut: EventEmitter<any> = new EventEmitter();
  @ViewChild("hasCusAlert") alertContainer: ElementRef;
  searchProductId;
  is_btndisable;
  dateChangedManually: boolean;
  selectCancle = false;
  constructor(
    private http: HttpService,
    private alertS: AlertService,
    private cartS: CartService,
    private sidebarS: SidebarService,
    private modalService: NgbModal
  ) {}

  ngOnInit() {
    this.is_btndisable = false;

    this.sub[0] = this.cartS.location.subscribe(val => {
      if (val) {
        this.locationList = val.filter(m => {
          return m.status == 1;
        });
      }
    });

    this.sub[1] = this.cartS.cartNo.subscribe(val => {
      if (val && val.cart_items.length > 0) {
        // this.productList = val;

        const data = this.cartS.getSessionData("cartList");
        this.productList = data ? data : null;
      } else {
        const data = this.cartS.getSessionData("cartList");
        this.productList = data ? data : null;
      }
      const cop = this.cartS.getSessionData("coupon");
      this.couponCode = cop ? cop : new Coupon();
    });

    this.getShipping();

    this.sub[2] = this.cartS.ProductId.subscribe(val => {
      if (val && val.id !== 0 && this.searchProductId !== val.id) {
        this.searchProductId = val.id;

        this.sidebarS.openSub.next(false);
        this.edit = false;
        this.loader_sub = false;
        this.product = null;
        this.calenderDate = val.date ? val.date : null;
        if ("variant" in val && val.variant) {
          $(".admin-cart input").val(val.name.trim());
          this.RentAddCart(val.variant);
        } else {
          //  console.log(val);
          $(".admin-cart").click();
          $(".admin-cart input").val("");
          this.fromInventory = true;
          $(".admin-cart input").focus();
          $(".admin-cart input").val(val.name.trim());
          $(".admin-cart").click();
          // setTimeout(() => {
          //   this.fromInventory = false;
          // }, 500);
        }
      }
    });
    this.changeCartNo();
    this.sidebarS.sidebarOpen.subscribe(val => {
      // console.log('call'+val)
      if (!val) {
        this.reset();
      } else {
        $(".native-routing-container-cart").scrollTop(0, 0);
      }

      //check plan type
      const user = localStorage.getItem("currentUser")
        ? JSON.parse(localStorage.getItem("currentUser"))
        : {};

      if (user != null && user.subscription.hasOwnProperty("account_type")) {
        this.alertForFreeAccount = {
          message:
            user.subscription.account_type == "FREE"
              ? "Orders are for testing only. Order will not be fulfilled."
              : ""
        };
      }
    });
    this.cartS.cartDiscount.subscribe(res => {
      if (res.reload) {
        this.productList = res.cart;
        this.updateCartList();
      }
    });
    this.min_date = this.addMonths(new Date(), -6).toString();
    // console.log(this.productList);
    if (this.productList) {
      if (
        this.productList.rent_start != undefined &&
        this.productList.rent_start != ""
      ) {
        this.isShowCartRentalDate = true;
        this.rent_startDate = this.productList.rent_start;
        this.rent_endDate = this.productList.rent_end;

        this.cart_startDate = this.productList.rent_start;
        this.cart_endDate = this.productList.rent_end;
        setCartRentalDate("adminOrder", this.cart_startDate, this.cart_endDate);
      } else {
        this.isShowCartRentalDate = false;
        removeCartDate("adminOrder");
      }
    }
  }

  private addMonths(date: Date, months: number): Date {
    var d = date.getDate();
    date.setMonth(date.getMonth() + months);
    if (date.getDate() != d) {
      date.setDate(0);
    }
    return date;
  }

  ngAfterViewInit() {
    const contentsTimer = setInterval(
      () => {
        this.contents = localStorage.getItem("contents")
        ? JSON.parse(localStorage.getItem("contents"))
        : null;
  
      if (
        this.contents &&
        this.contents.site_specific.confg &&
        this.contents.site_specific.confg.datetime &&
        this.contents.site_specific.confg.datetime.hasOwnProperty(
          "exact_start_time"
        ) &&
        this.contents.site_specific.confg.datetime.exact_start_time
      ) {
        this.enable_exact_time = true;
      } else {
        this.enable_exact_time = false;
      }
      if (this.contents) {
        clearInterval(contentsTimer);
      }
      }, 100
    )
    this.dateTimePicker(new Date());
  }

  getAddonsProductList(product_id) {
    this.cartS
      .getAddonProductListById(product_id)
      .then(res => {
        if (res.status == "OK") {
          this.addonsProductList = res.result.data;
          this.addonslabel = res.result.label;
          this.addonsProductList.map(prod => {
            let index = 0;
            if (prod.image) {
              prod["img"] = `${product_image}${GET_USER().store_id}/${
                prod.id
              }/${prod.image}`;
            } else {
              prod["img"] =
                "./assets/img/home/<USER>";
            }
            prod.variants.map(v => {
              if (index == 0) {
                v["min_qty"] = prod.min_quantity;
              } else {
                v["min_qty"] = 0;
              }
              index++;
            });
            return prod;
          });
          // console.log(this.addonsProductList);
        } else {
          this.addonsProductList = [];
        }
      })
      .catch(err => {
        console.log(err);
      });
  }

  onVariantQtyKeyup(quantity, variant_id, product_id) {
    if (!isNaN(parseInt(quantity))) {
      this.addonsProductList.map(p => {
        if (p.id == product_id) {
          p.variants.map(v => {
            if (v.id == variant_id) {
              v["min_qty"] = parseInt(quantity);
            }
          });
        }
      });

      console.log(this.addonsProductList);
    }
  }

  onClickEdit() {
    this.isRentalDateEditMode = true;
  }
  onCancel(e) {
    this.selectCancle = e;
    this.isRentalDateEditMode = false;
  }

  checkAvailable(e) {
    if(this.selectCancle) return this.selectCancle = false;
    Helpers.setLoading(true);
    setTimeout(t => {
      this.http
        .post("products/availability", {
          type: "cart",
          token: this.cartToken,
          start_date: this.cart_startDate,
          end_date: this.cart_endDate,
          source: "admin"
        })
        .toPromise()
        .then(res => {
          Helpers.setLoading(false);
          if (res.status === "NOK") {
            this.isRentalDateEditMode = false;
          } else {
            this.isRentalDateEditMode = false;
            this.productList = res.result.data;

            sessionStorage.setItem(
              "cartList",
              JSON.stringify(this.productList)
            );

            this.rent_startDate = this.productList.rent_start;
            this.rent_endDate = this.productList.rent_end;

            this.cart_startDate = this.productList.rent_start;
            this.cart_endDate = this.productList.rent_end;

            setCartRentalDate(
              "adminOrder",
              this.cart_startDate,
              this.cart_endDate
            );
          }
        })
        .catch(err => {
          Helpers.setLoading(false);
          this.isRentalDateEditMode = true;
        });
    }, 1000);
  }

  get cartToken() {
    const cartToken = this.cartS.getSessionData("cartToken");
    return cartToken ? cartToken : null;
  }

  async getShipping() {
    this.shipping = await this.cartS.getShipping();
  }

  ClearFullCart() {
    this.isShowCartRentalDate = false;
    this.rent_startDate = "";
    this.rent_endDate = "";
    removeCartDate("adminOrder");
    this.clear.emit(true);
  }

  getType(d, t) {
    return d && t ? formateRentType(d, t) : "";
  }

  getDate(d) {
    return this.cartS.formateListDate(d);
  }

  get showEndDate() {
    const contents = localStorage.getItem("contents")
      ? JSON.parse(localStorage.getItem("contents"))
      : null;

    if (this.enable_exact_time && !this.isDisableDatePicker) {
      return false;
    } else if (
      contents.site_specific.confg &&
      contents.site_specific.confg.hasOwnProperty("show_end_date")
    ) {
      return contents.site_specific.confg.show_end_date;
    }
    return true;
  }

  get showPricingOption() {
    const contents = localStorage.getItem("contents")
      ? JSON.parse(localStorage.getItem("contents"))
      : null;

    if (
      contents.site_specific.confg &&
      contents.site_specific.confg.hasOwnProperty("rental_price_option")
    ) {
      return contents.site_specific.confg.rental_price_option;
    }
    return true;
  }

  get showCartEndDate() {
    const contents = localStorage.getItem("contents")
      ? JSON.parse(localStorage.getItem("contents"))
      : null;

    if (
      contents.site_specific.confg &&
      contents.site_specific.confg.hasOwnProperty("show_end_date")
    ) {
      return contents.site_specific.confg.show_end_date;
    }
    return true;
  }

  get isExactStartDate(): boolean {
    const contents = localStorage.getItem("contents")
      ? JSON.parse(localStorage.getItem("contents"))
      : null;
    if (
      contents.site_specific.confg &&
      contents.site_specific.confg.datetime &&
      contents.site_specific.confg.datetime.hasOwnProperty(
        "exact_start_date"
      ) &&
      contents.site_specific.confg.datetime.exact_start_date
    ) {
      return true;
    }
    return false;
  }

  /* ************** Autocomplete *********** */

  closeSearch() {
    this.loader_sub = false;
    this.fromInventory = false;
    $(".admin-cart input").val("");
  }

  onFocus(e: Event): void {
    //   console.log(this.fromInventory, this.loader_sub);
    if (this.fromInventory || this.loader_sub) {
      e.stopPropagation();
      setTimeout(() => {
        //  this.fromInventory = false;
        const inputEvent: Event = new Event("input");
        e.target.dispatchEvent(inputEvent);
      }, 500);
    }
  }

  search = (text$: Observable<string>) => {
    return text$.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      tap(() => {
        $(".admin-cart").click();
        this.searchData = [];
        this.loader_sub = true;
      }),
      switchMap(term => {
        this.loader_sub = true;
        return this.getProductList(term);
      }),
      tap(() => {
        this.loader_sub = false;
        setTimeout(() => {
          this.loader_sub = false;
          this.cartS.formateSearchList(this.searchData);
          $(".package-search").click(e => {
            e.preventDefault();
            let attr = $(e.target).data("attr");
            this.getProduct(attr, null, null, 2);
          });
          $(".buy-search").click(e => {
            // Buy Search
            e.preventDefault();
            let attr = $(e.target).data("attr");
            if (typeof attr == "string") {
              attr = parseInt(attr);
            }
            const data = this.cartS.findAttrProId(this.searchData, attr);
            this.BuyAddCart(data);
          });
          $(".rent-search").click(e => {
            e.preventDefault();
            let attr = $(e.target).data("attr");
            if (typeof attr == "string") {
              attr = parseInt(attr);
            }
            const data = this.cartS.findAttrProId(this.searchData, attr);
            this.RentAddCart(data);
          });
        }, 100);
      })
    );
  };

  private getProductList(params): any {
    if (!params && params === "") {
      this.loader_sub = false;
      return [];
    }
    const search = "search=" + params.trim();
    return this.cartS.searchProduct(search).pipe(
      map(res => {
        this.loader_sub = false;
        this.searchData = formatProductSearch(res.result.data);
        return this.searchData;
      }),
      retry(3),
      catchError(() => {
        this.loader_sub = false;
        return [];
      })
    );
  }

  formatter = x => x.name;

  BuyAddCart(b) {
    this.variants = new Variant();
    this.edit = false;
    this.rent = false;
    this.formatvariant(b);
    this.getProduct(b.variants_products_id, true);
  }

  private async getRentDateForDisabledEndDate(
    price_id,
    locan_id,
    start_date
  ): Promise<any> {
    await this.http
      .get(
        `product/get_dates_price_duration?start_date=${start_date}&price_id=${price_id}&location=${locan_id}`
      )
      .toPromise()
      .then(res => {
        if (res.status == "OK") {
          let data = res.result.data;
          this.rent_startDate = data.start_date;
          this.rent_endDate = data.end_date;
        }
      });
  }

  async checkPrice(e) {
    if (this.showPricingOption && !this.showEndDate) {
      const loc = localStorage.getItem("currentUser")
        ? JSON.parse(localStorage.getItem("currentUser")).location_id
        : null;
      await this.getRentDateForDisabledEndDate(this.item.price_id, loc, this.rent_startDate);
    }

    if (this.enable_exact_time) {
      if (this.isShowStartTimeSelection) {
        if (this.selected_exact_time && this.selected_exact_duration) {
          this.changTime(this.selected_exact_time);
        }
      } else {
        if (this.selected_exact_duration) {
          this.changTime("start_time_off");
        }
      }
    } else {
      let result = checkSameDatetime(
        this.showStartTime,
        this.rent_startDate,
        this.rent_endDate
      );
      result = false;
      if (result) {
        this.is_btndisable = true;
        this.alertS.error(
          this.alertContainer,
          "Please select correct end date",
          true,
          5000
        );
      } else {
        this.item.location = GET_USER().location_id;
        this.formatSendData(
          this.rent_startDate,
          this.product.deposit_amount,
          this.product.deposite_tax,
          this.rent_endDate
        );
        this.item.rental_type =
          this.item.rental_type === "buy" ? "buy" : "rent";
        const cart = changeNullToEmpty(this.item);
        this.http
          .post("get-price-value", cart)
          .toPromise()
          .then(res => {
            if (res.status === "OK") {
              if (res.result.error) {
                return;
              }
              this.item.price = res.result.data;
              this.is_btndisable = false;
            }
          });
      }
    }
  }

  RentAddCart(r) {
    this.variants = new Variant();
    this.rent = true;
    this.edit = false;

    this.isDisableDatePicker = isCartRentalDateExist("adminOrder");

    if (this.isDisableDatePicker) {
      const cartDateObj = getCartDate("adminOrder");
      this.rent_startDate = cartDateObj.startDate;
      this.rent_endDate = cartDateObj.endDate;
    }

    this.formatvariant(r);
    this.getProduct(r.variants_products_id, false);
  }

  formatvariant(data) {
    this.variants.variants_products_id = data.variants_products_id;
    this.variants.variants = [];
    for (let i = 0; i < data.variant_chain_name.length; i++) {
      let obj: Attr = new Attr();
      obj.attr_set_id = data.variant_set_id[i];
      obj.attr_set = data.variant_set_name[data.variant_set_id[i]];
      obj.attr = data.variant_chain_name[i];
      this.variants.variants.push(obj);
    }
  }

  /* Autocomplete */

  private dateTimePicker(date) {
    this.cartS.datePicker(date);
    // this.rentalDateChange();
    //  this.rentalEndDateChange();
    //  this.rentalTimeChange();
    //  this.rentalEndTimeChange();
  }

  initProduct() {
    this.item = new CartItem();
    this.item.quantity = 1;
    this.item.token = this.cartS.getSessionData("cartToken")
      ? this.cartS.getSessionData("cartToken")
      : null;
  }

  getProduct(i, buy: boolean, auto?: boolean, type = 1) {
    this.loader_sub = true;
    this.cartS.getProduct(i, type, this.cartToken).subscribe(
      res => {
        // console.log(i, buy, auto, type, res);
        this.loader_sub = false;
        this.product = res.data;

        this.extact_durations = this.product.extact_durations.durations;
        this.extact_times = this.product.extact_durations.times;
        this.selected_exact_time = "null";

        this.is_recurring_product = this.product.hasOwnProperty('recurring_prices') && this.product.recurring_prices.length > 0
          ? true
          : false;
        if (this.is_recurring_product) {
          this.product.recurring_prices.map(p => {
            p['recurring'] = true;
          })
        }

        this.type = type;
        if (type === 1) {
          if (!this.edit) {
            this.initProduct();
          }
          this.formateProductCart(buy, auto);
        }

        this.getAddonsProductList(this.product.id);
      },
      err => {
        this.loader_sub = false;
        this.error(err, "Something wrong! Please try again");
      }
    );
  }

  formateProductCart(buy, auto) {
    this.productPrice = this.cartS.formatePrice(this.product.prices);
    this.item.sales_tax = this.product.sales_tax;
    this.item.product_id = this.product.id;
    /* New data */
    this.item.variants_products_id = this.variants.variants_products_id;

    const blen = Object.keys(this.productPrice.base).length;
    const rlen = this.productPrice.rent.length;
    if (blen > 0 || rlen > 0) {
      if (auto) {
        this.autoAdd(blen, rlen);
      } else {
        this.checkBuy(buy);
      }
    } else {
      this.product = null;
      this.alertS.info(
        this.alertContainer,
        "Sorry!!! Product has no price. Please add price.",
        true,
        5000
      );
    }
  }

  autoAdd(b, r) {
    switch (true) {
      case b > 0 && r < 1:
        this.rent = false;
        this.checkBuy(true);
        break;
      case b > 0 && r > 0:
        this.rent = false;
        this.mode = 1;
        this.formateBuy();
        break;
      default:
        this.rent = true;
        this.checkBuy(false);
        break;
    }
  }

  checkBuy(buy) {
    if (buy) {
      this.productPrice.rent = [];
      this.formateBuy();
      this.addItem();
    } else {
      this.mode = 2;
      if (this.productPrice.rent.length > 0) {
        const ind = this.productPrice.rent.findIndex(
          f =>
            f.price === this.item.price && f.rent_type === this.item.rental_type
        );
        const price = {
          obj: this.is_recurring_product 
            ? this.product.recurring_prices[0]
            : this.productPrice.rent[ind > -1 ? ind : 0],
          recurring: this.is_recurring_product  ? true : false,
          index: ind > -1 ? ind : 0
        }
        this.formateRent(price.obj, price.index, price.recurring);
      }
    }
  }

  formateBuy() {
    this.item.price = this.productPrice.base["price"];
    this.item.price_id = this.productPrice.base["id"];
    this.item.rental_duration = this.productPrice.base["rent_duration"];
    this.item.rental_type = this.productPrice.base["rent_type"];
    this.item.term = this.productPrice.base["duration"];
  }

  async formateRent(data, i, isRecurring?: boolean) {
    event.preventDefault();
    if (this.product.recurring_prices && this.product.recurring_prices.length > 0) {
      this.selectedPriceObj = isRecurring ? data : undefined;
    }
    if (this.selectedPriceObj) {
      this.recurringPriceId = data["id"];;
      this.rentPriceId = null;
    } else {
      this.rentPriceId = data["id"];;
      this.recurringPriceId = null;
    }
    this.item.price_id = this.rentPriceId ? this.rentPriceId : this.recurringPriceId;
    if (this.isDisableDatePicker) {
      const cartDateObj = getCartDate("adminOrder");
      this.rent_startDate = cartDateObj.startDate;
      this.rent_endDate = cartDateObj.endDate;
      this.item.price = this.product.rental_price;
    } else {
      this.item.price = data["price"];
      const loc = localStorage.getItem("currentUser")
        ? JSON.parse(localStorage.getItem("currentUser")).location_id
        : null;
      await this.getRentDateForDisabledEndDate(
        this.item.price_id,
        loc,
        this.rent_startDate ? this.rent_startDate : data.rent_start
      );
    }
    if (this.product.driving_license) {
      this.item.driving_license_required = true;
    }
    // this.rentPriceId = i;
    this.item.rental_duration = this.item.rental_duration
      ? this.item.rental_duration
      : data["rent_duration"];
    this.item.rental_type = this.is_recurring_product ? data["label"] : data["rent_type"];
    this.item.term = data["duration"];
    if (this.edit) {
      this.formatingDateTime(new Date(this.item.rent_start));
    } else {
      if (this.calenderDate) {
        this.formatingDateTime(new Date(this.calenderDate));
      } else {
        this.formatingDateTime(new Date());
      }
    }
  }

  private formatingDateTime(date) {
    const rentTime = this.cartS.getCurrentDateTime(date);
    this.rentalStart.date = rentTime["date"];
    this.rentalStart.time = rentTime["time"];
    setTimeout(() => this.dateTimePicker(date), 100);
  }

  reset() {
    this.type = null;
    this.edit = null;
    this.item = new CartItem();
    this.product = null;
    this.loader_sub = false;
    $(".admin-cart input").val("");
    this.cartS.getProductId(null);
    this.calenderDate = null;
  }

  decreaseRent() {
    if (this.item.rental_duration > 1) {
      this.item.rental_duration--;
    }
  }

  increaseRent() {
    if (this.item.rental_duration < 5) {
      this.item.rental_duration++;
    }
  }

  error(err, message) {
    this.loader = false;
    this.loader_sub = false;
    this.loaderAdd = false;
    Helpers.setLoading(false);
    //   console.log(err);
    this.alertS.error(this.alertContainer, message, true, 5000);
  }

  trackList(index, pro) {
    return pro ? pro.id : null;
  }

  successAddToCart(res) {
    this.alertS.success(
      this.alertContainer,
      "Product has been added to cart",
      true,
      5000
    );
    this.productList = res.result.data;

    this.cartS.setSessionData("cartToken", res.result.data.token);
    this.updateCartList();
    this.reset();

    if (
      this.productList.rent_start != undefined &&
      this.productList.rent_start != ""
    ) {
      this.isShowCartRentalDate = true;
      this.rent_startDate = this.productList.rent_start;
      this.rent_endDate = this.productList.rent_end;

      this.cart_startDate = this.productList.rent_start;
      this.cart_endDate = this.productList.rent_end;

      setCartRentalDate("adminOrder", this.cart_startDate, this.cart_endDate);
    } else {
      this.isShowCartRentalDate = false;
      removeCartDate("adminOrder");
    }
  }
  showError(res) {
    this.alertS.error(this.alertContainer, res.result.error, true, 5000);
  }

  isAddonsProductCombinationOk() {
    let requiredAddonsQty = 0;
    let result = true;
    let sumOfVariantQty = 0;

    if (this.addonsProductList.length > 0) {
      this.addonsProductList.map(p => {
        requiredAddonsQty =
          requiredAddonsQty + this.item.quantity * p.min_quantity;

        p.variants.map(v => {
          sumOfVariantQty = sumOfVariantQty + v.min_qty;
        });
      });

      if (sumOfVariantQty > requiredAddonsQty) {
        result = false;
      }
    }

    return result;
  }

  addItem() {
    if (!this.isAddonsProductCombinationOk()) {
      this.alertS.error(
        this.alertContainer,
        "Select product add-on quantities",
        true,
        5000
      );

      return;
    }

    this.loaderAdd = true;
    this.item.location = GET_USER().location_id;
    if (!this.item.rental_type) {
      this.formatSendData();
    } else {
      this.formatSendData(
        this.rent_startDate,
        this.product.deposit_amount,
        this.product.deposite_tax,
        this.rent_endDate
      );
    }
    // this.item.rental_type = this.item.rental_duration ? "rent" : "buy";
    this.item.rental_type = this.rent ? "rent" : "buy";
    this.item.is_admin = true;
    const sendItem = changeNullToEmpty(this.item);
    if (this.isExactStartDate) {
      sendItem["rent_start"] = this.product.rent_start;
      sendItem["rent_end"] = this.product.rent_end;
    }

    if (isCartRentalDateExist("adminOrder")) {
      sendItem["source"] = "admin";
    }

    if (this.addonsProductList.length > 0) {
      let required_addons = [];
      let variant = [];

      this.addonsProductList.map(p => {
        variant = p.variants.filter(v => v.min_qty > 0);
        if (variant.length != 0) {
          for (let i = 0; i < variant.length; i++) {
            required_addons.push({
              product_id: p.id,
              variants_products_id: variant[i].id,
              quantity_id: variant[i].quantity_id,
              quantity: variant[i].min_qty
            });
          }
        }
      });
      sendItem["required_addons"] = required_addons;
    }

    if (this.is_recurring_product) {
      if (this.selectedPriceObj && this.selectedPriceObj.recurring) {
        sendItem["recurring"] = true;
      }
    }

    this.cartS
      .addCart(sendItem)
      .then(res => {
        this.loaderAdd = false;
        if (res.status == "OK") {
          if (res.result.error) {
            this.showError(res);
          } else {
            this.successAddToCart(res);

            if (
              this.productList.rent_start != undefined &&
              this.productList.rent_start != ""
            ) {
              this.isShowCartRentalDate = true;
              this.rent_startDate = this.productList.rent_start;
              this.rent_endDate = this.productList.rent_end;

              setCartRentalDate(
                "adminOrder",
                this.rent_startDate,
                this.rent_endDate
              );
            } else {
              this.isShowCartRentalDate = false;
              removeCartDate("adminOrder");
            }
          }
        }
      })
      .catch(err =>
        this.error(err, "Something wrong! Product has not been added to cart")
      );
  }

  private formatSendData(date?, dAmount?, dTax?, endDate?) {
    this.item.rent_start = date ? date : null;
    this.item.rent_end = endDate ? endDate : null;
    this.item.deposit_amount = dAmount ? dAmount : 0;
    this.item.deposite_tax = dTax ? dTax : false;
  }

  changeCartNo() {
    this.cartS.cartNoChange(this.productList);
  }

  updateCartList(data?) {
    // console.log(data);
    this.productList = data ? data : this.productList;
    this.cartS.setSessionData("cartList", this.productList);
    this.changeCartNo();
  }

  decreaseItemQuant() {
    if (this.item.quantity > 1) {
      this.item.quantity--;
    }
  }

  increaseItemQuant() {
    this.item.quantity++;
  }

  editCart(e) {
    // console.log(e);
    const { cart, i } = e;
    this.reset();
    this.edit = true;
    this.item = cart;
    this.rent = true;
    this.index = i;

    this.isDisableDatePicker = isCartRentalDateExist("adminOrder");

    if (this.isDisableDatePicker) {
      const cartDateObj = getCartDate("adminOrder");
      this.rent_startDate = cartDateObj.startDate;
      this.rent_endDate = cartDateObj.endDate;
    }

    if (cart.product_type === 2) {
      this.getProduct(cart.product.uuid, false, null, cart.product_type);
    } else {
      this.getProduct(
        cart.variants_products_id,
        false,
        null,
        cart.product_type
      );
    }
  }

  updateCart() {
    this.loaderAdd = true;
    this.formatSendData(
      this.rentalStart,
      this.product.deposit_amount,
      this.product.deposite_tax
    );
    this.item.location = GET_USER().location_id;
    if (!this.item.rental_type) {
      this.formatSendData();
    } else {
      this.formatSendData(
        this.rent_startDate,
        this.product.deposit_amount,
        this.product.deposite_tax,
        this.rent_endDate
      );
    }

    const sendItem = changeNullToEmpty(this.item);

    if (isCartRentalDateExist("adminOrder")) {
      sendItem["source"] = "admin";
    }

    this.cartS
      .addCart(sendItem)
      .then(res => {
        this.loaderAdd = false;
        if (res.status == "OK") {
          if (res.result.error) {
            this.alertS.error(
              this.alertContainer,
              res.result.error,
              true,
              5000
            );
          } else {
            this.alertS.success(
              this.alertContainer,
              "Product has been updated",
              true,
              5000
            );
            this.productList = res.result.data;

            if (
              this.productList.rent_start != undefined &&
              this.productList.rent_start != ""
            ) {
              this.isShowCartRentalDate = true;
              this.rent_startDate = this.productList.rent_start;
              this.rent_endDate = this.productList.rent_end;

              setCartRentalDate(
                "adminOrder",
                this.rent_startDate,
                this.rent_endDate
              );
            } else {
              this.isShowCartRentalDate = false;
              removeCartDate("adminOrder");
            }

            this.cartS.setSessionData("cartToken", res.result.data.token);
            this.updateCartList();
            this.reset();
          }
        }
      })
      .catch(err =>
        this.error(err, "Something wrong!!! Product has been not updated")
      );
  }

  applyCoupon(e) {
    this.product = null;
    if (e) {
      this.updateCartList(e);
    }
  }

  ClearCart() {
    this.session.forEach(name => {
      this.cartS.removeSessionData(name);
    });
  }

  checkOutProceed(quote?: boolean) {
    if (quote) {
      this.checkOut.emit(quote);
    } else {
      this.checkOut.emit(false);
    }
    this.cartS.goToCheckOut(true);
    this.reset();
  }

  deliveryUpdate(e) {
    if (e.status) {
      this.updateCartList(e.data);
    }
  }

  updateCartQunt(e) {
    if (e.data) {
      this.product = null;
      this.updateCartList(e.data);
      this.successMsg(e.message);
    } else {
      this.errorMsg(e.message);
    }
  }

  private successMsg(message) {
    $("custom-alert").css("display", "block");
    this.alertS.success(this.alertContainer, message, true, 3000);
  }

  private errorMsg(message) {
    $("custom-alert").css("display", "block");
    this.alertS.error(this.alertContainer, message, true, 3000);
  }

  archiveItem(e) {
    if (e.data) {
      this.successMsg(e.message);
      this.updateCartList(e.data);
      if (
        this.productList.cart_items &&
        this.productList.cart_items.length < 1
      ) {
        this.ClearCart();
        this.isShowCartRentalDate = false;
        removeCartDate("adminOrder");
      }
      this.product = null;
    } else {
      this.errorMsg(e.message);
    }
  }

  // new date time range picker ngx-daterangepicker-material from template
  getDateFromManualChange(e) {
    if (e.startDate) {
      this.selectstartDateTime(e.startDate);
    } else if (e.endDate) {
      this.selectendDateTime(e.endDate);
    }
  }

  selectstartDateTime(e) {
    this.rent_startDate = e;
  }

  selectendDateTime(e) {
    if (!this.enable_exact_time || this.isDisableDatePicker) {
      this.rent_endDate = e;
    } else {
      this.rent_endDate = "";
    }
  }

  selectCartstartDateTime(e) {
    this.cart_startDate = e;
  }
  selectCartendDateTime(e) {
    this.cart_endDate = e;
  }

  get showCartStartTime() {
    const contents = localStorage.getItem("contents")
      ? JSON.parse(localStorage.getItem("contents"))
      : null;
    if (
      contents.site_specific.confg &&
      contents.site_specific.confg.hasOwnProperty("show_start_time")
    ) {
      return contents.site_specific.confg.show_start_time;
    }
    return true;
  }

  get showStartTime() {
    const contents = localStorage.getItem("contents")
      ? JSON.parse(localStorage.getItem("contents"))
      : null;

    if (this.enable_exact_time && !this.isDisableDatePicker) {
      return false;
    } else if (
      contents.site_specific.confg &&
      contents.site_specific.confg.hasOwnProperty("show_start_time")
    ) {
      return contents.site_specific.confg.show_start_time;
    }
    return true;
  }

  get isShowStartTimeSelection(): boolean {
    let show_start_time: boolean;
    if (
      this.contents.site_specific.confg &&
      this.contents.site_specific.confg.hasOwnProperty("show_start_time")
    ) {
      show_start_time = this.contents.site_specific.confg.show_start_time;
    }
    if (this.enable_exact_time && show_start_time) {
      return true;
    }
    return false;
  }

  validateQuantity(e) {
    preventInputAlphabets(e);
  }

  onQuantityMouseLeave(value) {
    if (value === "") {
      this.item.quantity = 1;
    }
  }

  onDurationChange(duration_value) {
    this.selected_exact_time = null;
    this.is_btndisable = true;

    if (duration_value != "null") {
      const selectedDurationObject = this.extact_durations.find(
        d => d.value === duration_value.split(",")[0]
      );
      this.extact_times = selectedDurationObject.times;

      let duration = duration_value.split(",");
      this.selected_exact_duration = { value: duration[0], type: duration[1] };

      if (this.selected_exact_time != null) {
        this.changTime(this.selected_exact_time);
      }
      if (!this.isShowStartTimeSelection) {
        this.changTime("start_time_off");
      }
    }
  }

  changTime(value) {
    if (value != "null") {
      const location_id = GET_USER().location_id;
      let sendDate = {
        product_id: this.item.product_id,
        variants_products_id: this.item.variants_products_id,
        start_date: this.rent_startDate.split(" ")[0],
        start_time:
          value !== "start_time_off" ? this.selected_exact_time : null,
        duration: parseInt(this.selected_exact_duration.value),
        type: this.selected_exact_duration.type,
        location_id: location_id
      };

      this.http
        .post("product/get_dates_from_duration", sendDate)
        .subscribe(res => {
          if (res.status == "OK") {
            let data = res.result.data;

            this.rent_startDate = data.start_date;
            this.rent_endDate = data.end_date;
            this.item.price = data.price;
            this.is_btndisable = false;

            if (data.hasOwnProperty("available")) {
              //Todo::
              // this.availableQ=data.available
            }
          }
        });
    } else {
    }
  }

  onClickAdditionalCharge() {
    this.loadCart();
    const modalStatus = this.modalService.open(AdditionalChargeComponent, {
      centered: true,
      size: "lg",
      backdrop: "static",
      windowClass: "additional-modal"
    });

    modalStatus.result.then(
      result => {
        if (result) {
          this.loadCart();
        }
      },
      res => {
        // console.log(res);
      }
    );
  }

  loadCart() {
    if (this.cartToken) {
      Helpers.setLoading(true);
      this.cartS.getCartList(this.cartToken).subscribe(res => {
        this.productList = res.data;
        this.updateCartList();
        Helpers.setLoading(false);
      });
    }
  }
}
