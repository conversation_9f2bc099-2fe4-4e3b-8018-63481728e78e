.form-panel {
    padding: 0px 0px 10px 3px;
}

.m-form__group {
    padding: 0px 0px 10px 0px!important;
}

.label-head {
    width: 20%;
    margin-right: 5px;
}

.admin-cart {
    width: 60%;
}

.table tr td,
.table tr th {
    vertical-align: middle;
    border-bottom: 1px solid #ebedf2!important;
    min-width: 60px;
}

.table tr th {
    font-weight: 600;
}

.table tr td a {
    text-decoration: none;
}

.form-group {
    margin-bottom: 0px;
}

.pad-l-zero {
    padding-left: 0px;
}

.custom-alert {
    position: absolute;
    top: 48px;
    right: 50px;
    z-index: 99000;
}

.deposit {
    width: 50%;
    float: left;
    font-weight: bold;
    padding-left: 10%;
    text-transform: capitalize
}

.total {
    width: 50%;
    float: left;
    text-align: right;
    padding-right: 10%;
    font-weight: bold;
}

.header-part {
    position: absolute;
    top: 1px;
    background: white;
    padding-top: 12px;
    padding-right: 20px;
    border-bottom: 1px solid #ebedf2;
    z-index: 9;
    padding-bottom: 5px;
    /* overflow: hidden; */
    /* width:90.5%; */
}

.search-view-cart {
    height: 55px;
    border: 5px solid #eee;
    font-size: 16px;
}

.block-cursor {
    pointer-events: none;
}

.edit-icon {
    cursor: pointer;
    margin-left: 10px;
}


/* .content {
    margin-top: 60px;
} */

.clear-cart-btn {
    text-align: right;
}

.img-resize {
    max-width: 100px!important;
    height: 100px!important;
    object-fit: contain;
}

.img-orgin {
    max-width: 100%!important;
    height: auto!important;
}

.free-account-alert {
    padding: 10px;
    background-color: yellow;
}

.viewpage-addons .form-control {
    width: 40px;
    height: calc(2rem + 2px);
    padding: 0;
    text-align: center;
}

.no-variant {
    float: right;
    margin-top: -46px;
}

.viewpage-addons h5 img {
    width: 60px;
    border: 1px solid #eee;
    margin-right: 10px;
    height: 50px;
    object-fit: cover;
}

.viewpage-addons label {
    padding-left: 72px;
}

@media (max-width: 991px) {
    .viewpage-addons h5 {
        font-size: 14px;
    }
}

@media (max-width: 575px) {
    .viewpage-addons h5 img {
        width: 45px;
        height: 40px;
    }
    .viewpage-addons label {
        padding-left: 56px;
    }
}

@media screen and (max-width: 992px) {
    /* .content {
        margin-top: 90px;
    } */
    .label-head {
        width: 30%;
    }
    .admin-cart {
        width: 60%;
    }
    .clear-cart-btn {
        text-align: left;
    }
    .header-part {
        padding-bottom: 15px;
    }
}

@media only screen and (max-width: 575px) {
    .quant .price {
        padding-top: 0px;
    }
    .pro-image-show {
        padding-bottom: 15px;
        text-align: center;
    }
    .pro-image-show img {
        width: 200px;
    }
}

app-package-item-show {
    width: 100%;
}

app-date-time-range {
    display: inline-block;
}

.cancel-btn {
    display: inline-block;
    margin-left: 15px;
    height: 33px;
    border-radius: 3px;
    font-size: 12px;
    padding: 0 15px;
    background-color: #333;
    color: #fff;
    border: none;
}

.product-details-cart .form-group .form-control {
    width: 200px;
}

.aditional-charge-btn {
    margin-top: -50px;
    position: relative;
    z-index: 1;
    font-size: 12px;
}

.quote-checkout-btn-area {
    margin-top: -290px;
    position: relative;
}