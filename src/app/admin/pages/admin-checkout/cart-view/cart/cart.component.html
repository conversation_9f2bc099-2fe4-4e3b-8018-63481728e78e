<div class="custom-alert" #hasCusAlert></div>

<div class="animated fadeIn">
    <div class="m-portlet__body form-panel" style="padding-bottom: 15px;">
        <div class="header-part">
            <h3 class="label-head colorPurpel text-uppercase" style="padding-bottom: 15px; display: inline-block;">
                View Cart
            </h3>
            <div class="form-group admin-cart" style="display: inline-block;">
                <ng-template #rt let-r="result" let-t="term">
                    <div>{{ r.name }}</div>
                    <div class="colorPurpel" *ngIf="r.chain">
                        <small style="font-style: italic">{{ r.chain }}</small>
                    </div>
                    <div>
                        <button *ngIf="r.buy" class="btn btn-sm btn-xsm btn-outline-dark" style="margin-right: 10px;">
              Buy
            </button>
                        <button *ngIf="r.rent" class="btn btn-sm btn-xsm btn-outline-danger">
              Rent
            </button>
                    </div>
                </ng-template>
                <input id="typeahead-http" #autoCom name="search" type="text" class="form-control search-view-cart" [inputFormatter]="formatter" [ngbTypeahead]="search" placeholder="Search Item by id, name, barcode and supplier product id" [resultTemplate]="rt" (focus)="onFocus($event)"
                    style="display: inline; width: 90%;" />
                <div *ngIf="autoCom.value" (click)="autoCom.value = ''; closeSearch()" class="search-close" style="margin: auto; padding: 5px;cursor: pointer;display: inline;">
                    <i class="fa fa-close"></i>
                </div>
                <div *ngIf="loader_sub" class="m-loader m-loader--brand search-loader" style="width: 30px; display: inline-block;top:-6px;"></div>
            </div>
            <div class="clear-cart-btn" style="display: inline-block;width: 19%;">
                <button class="btn btn-sm btn-danger" (click)="ClearFullCart()">
          Clear Cart
        </button>
            </div>
        </div>

        <!-- View Product to add -->
        <div class="content"></div>
        <div class="product-details-cart row" style="padding-top: 25px;" *ngIf="product && type===1 &&  (rent || productPrice.rent.length > 0) ">
            <div class="col-sm-3 pro-image-show">
                <img *ngIf="product.images.length == 0; else alter" class="img-fluid img-avatar img-thumbnail img-orgin" src="./assets/img/home/<USER>" alt="Product Image" />
                <ng-template #alter>
                    <img class="img-fluid img-avatar img-thumbnail img-orgin" src="{{
              imageUrl +
                '/' +
                product.id +
                '/' +
                (product.images[0] ? product.images[0].image_large : '')
            }}" alt="Product Image" onError="this.src='./assets/img/home/<USER>';" />
                </ng-template>
            </div>
            <div class="col-sm-9">
                <h4>{{ product.name }}</h4>
                <div class="m-form__group form-group" *ngIf="product.prices && product.prices.length > 0">
                    <div class="m-radio-inline">
                        <label class="m-radio" (click)="formateBuy()" *ngIf="!rent">
              <input type="radio" name="main" [value]="1" [(ngModel)]="mode" />
              Buy
              <span></span>
            </label>
                        <label *ngIf="productPrice.rent.length > 0 && !rent" class="m-radio" (click)="formateRent(productPrice.rent[0], 0)">
              <input type="radio" name="main" [value]="2" [(ngModel)]="mode" />
              Rent
              <span></span>
            </label>
                    </div>
                </div>

                <div class="row" style="padding-bottom: 20px;">
                    <div class="col-sm-6" *ngFor="let a of variants.variants">
                        <label *ngIf="a.attr_set_id != 1">
              <span class="colorPurpel">{{ a.attr_set }}:</span>
              {{ " " + a.attr }}</label>
                    </div>
                </div>

                <div class="rent" *ngIf="mode == 2">

                    <ng-container *ngIf="!is_recurring_product">
                        <div class="m-form__group form-group row" style="margin:0px;"
                            *ngIf="(!isExactStartDate  && (!enable_exact_time || isDisableDatePicker))">
                            <div class="m-radio-list col-sm-6">
                                <div *ngFor="let rent of productPrice.rent; let i = index">
                                    <label class="w-100" [ngClass]="[!isDisableDatePicker ? 'm-radio' : '']" (click)="formateRent(rent, i)">
                                        <input *ngIf="!isDisableDatePicker;else arrow" type="radio" [value]="rent.id" name="rent"
                                            [(ngModel)]="rentPriceId" />
                        
                                        <ng-template #arrow>
                                            <label><i class="fa fa-arrow-right"></i>&nbsp;</label>
                                        </ng-template>
                        
                                        {{ rent.price | currency }} {{ rent.duration ? "/" : "" }} {{ rent?.duration }} {{ rent.rent_type }}
                                        <span></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </ng-container>

                    <ng-container *ngIf="is_recurring_product">
                        <div class="m-form__group form-group row" style="margin:0px;"
                            *ngIf="(!isExactStartDate  && (!enable_exact_time || isDisableDatePicker))">
                            <div class="m-radio-list col-sm-6">
                                <h5>Recurring price</h5>
                                <div *ngFor="let rent of product?.recurring_prices; let i = index">
                                    <label class="w-100" [ngClass]="[!isDisableDatePicker ? 'm-radio' : '']" (click)="formateRent(rent, i, true)">
                                        <input
                                            *ngIf="!isDisableDatePicker;else arrow"
                                            type="radio" 
                                            [value]="rent.id" 
                                            name="rent"
                                            [(ngModel)]="recurringPriceId" />
                                        <ng-template #arrow>
                                            <label><i class="fa fa-arrow-right"></i>&nbsp;</label>
                                        </ng-template>

                                        <ng-container *ngIf="rent?.term; else noTerm">
                                            {{rent?.term}} {{rent.term > 1 ? rent.label + 's' : rent.label}} (billed {{rent.label}}ly at {{ rent?.price | currency }})
                                        </ng-container>
                                        <ng-template #noTerm>
                                            {{ rent.price | currency }} {{ rent.duration ? "/" : "" }} 
                                                {{ rent?.duration }} {{ rent.label }}
                                        </ng-template>
                                        <span></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </ng-container>

                    <div style="position: relative;" *ngIf="!isExactStartDate; else dueDate">
                        <label *ngIf="enable_exact_time && !this.isDisableDatePicker">Select Start Date:</label>
                        <label *ngIf="!enable_exact_time">Rental date range:</label>
                        <br />
                        <app-date-time-range [ngClass]="[showEndDate === false ? 'single' : 'both']" (startDateTime)="selectstartDateTime($event)" (endDateTime)="selectendDateTime($event)" (reload)="checkPrice($event)" (cancel)="onCancel($event)" (dateModel)="getDateFromManualChange($event)" [placeholderEnd]="rent_endDate"
                            [placeholderStart]="rent_startDate" [displayTime]="showStartTime" [onlyStartDate]="!showEndDate" [mindate]="min_date" [diabledPicker]="isDisableDatePicker">
                        </app-date-time-range>
                    </div>
                    <ng-template #dueDate>
                        <h4>Due Date {{product.rent_end | date: "MM/dd/yyyy"}}</h4>
                    </ng-template>


                    <ng-container *ngIf="mode == 2 && enable_exact_time && !this.isDisableDatePicker">
                        <div class="row m-0">
                            <div class="form-group col-sm-12 pad-l-zero mt-3 pl-0">
                                <label for="">Select Duration</label>
                                <select class="form-control m-input" id="duration" name="duration" (change)="onDurationChange($event.target.value)">
                  <option value="null">-Select-</option>
                  <option value="{{duration.value +','+duration.type}}" *ngFor="let duration of extact_durations">
                    {{duration.label}}</option>
                </select>

                            </div>

                            <div *ngIf="extact_times.length>0 && isShowStartTimeSelection" class="form-group col-sm-12 pad-l-zero mt-3 pl-0">
                                <label for="">Select Start Time</label>
                                <select class="form-control m-input" id="Exact_time" name="selected_exact_time" [(ngModel)]="selected_exact_time" (change)="changTime($event.target.value)">
                  <option value="null">-Select-</option>
                  <option [value]="e_time" *ngFor="let e_time of extact_times">{{e_time}}</option>
                </select>
                            </div>
                        </div>
                    </ng-container>

                </div>

                <div class="quant row">
                    <div class="col-sm-5 mt-3">
                        <label class="colorPurpel">Quantity</label>
                        <div class="quantity admin-quantity clearfix">
                            <span class="btn btn-sm btn-dark no-m" (click)="decreaseItemQuant()">-</span>
                            <!-- <span class="cart-qunt btn btn-sm no-m">{{ item.quantity }}</span> -->
                            <input type="text" autocomplete="off" class="input-qty" name="qty" [(ngModel)]="item.quantity" (keydown)="validateQuantity($event)" (mouseleave)="onQuantityMouseLeave($event.target.value)" />
                            <span class="btn btn-sm btn-dark  no-m" (click)="increaseItemQuant()">+</span>
                        </div>
                        <!-- <span>Available : {{product.quantity}}</span
          > -->
                    </div>
                    <!-- <div class="col-sm-6 pad-l-zero">
            <label for="" class="colorPurpel">Rental Duration</label>
            <div class="quantity clearfix">
              <span class="btn btn-sm btn-dark no-m" (click)="decreaseRent()">-</span>
              <span class="cart-qunt btn btn-sm no-m">{{item.rental_duration}}</span>
              <span class="btn btn-sm btn-dark  no-m" (click)="increaseRent()">+</span>
            </div>
          </div> -->
                    <div class="col-sm-6 price mt-4 pt-4 text-left">
                        <h4>
                            Price: {{ item.quantity * item.price * (item.rental_duration ? item.rental_duration : 1) | currency }}
                        </h4>
                    </div>

                    <div class="row viewpage-addons w-100" *ngIf="addonsProductList.length>0">
                        <h2 class="product-name mb-3 mt-4 pl-3">{{addonslabel}}</h2>
                        <div class="col-md-12 mt-2" *ngFor="let addon of addonsProductList">
                            <h5>
                                <img [src]="addon.img">{{addon?.name}}
                            </h5>
                            <div class="row mt-2 mb-2" *ngFor="let variant of addon.variants">
                                <div class="col-xl-6 col-lg-7 col-md-10 col-sm-10 col-10">
                                    <label *ngIf="variant?.name !='Unassigned: Unassigned'" class="mb-0 mt-2">{{variant?.name}}</label>
                                </div>
                                <div class="col-xl-4 col-lg-4 col-md-2 col-sm-10 col-2">
                                    <input class="form-control float-right" [ngClass]="[
                  variant?.name =='Unassigned: Unassigned'
                    ? 'form-control no-variant'
                    : 'form-control'
                  ]" type="text" autocomplete="off" name="qty" [value]="variant?.min_qty" (keyup)="onVariantQtyKeyup($event.target.value,variant.id,addon?.id)" />
                                </div>
                            </div>
                            <hr>
                        </div>
                    </div>

                    <div class="col-12">
                        <div class="pt-3">
                            <div *ngIf="loaderAdd; else addButton" class="m-loader m-loader--brand" style="width: 30px; display: inline-block;"></div>
                            <ng-template #addButton>
                                <button *ngIf="!edit" [disabled]="is_btndisable || rent_endDate==''" class="btn btn-brand" (click)="addItem()">
                    Add Item
                  </button>
                                <button *ngIf="edit" [disabled]="is_btndisable" class="btn btn-brand" (click)="updateCart()">
                    Update Item
                  </button>
                                <button class="btn btn-danger" (click)="reset()" style="margin-left: 10px;">
                    Cancel
                  </button>
                            </ng-template>
                        </div>
                    </div>



                </div>
            </div>

        </div>

        <div class="package-product-show product-details-cart row" *ngIf="type===2 && product">
            <app-package-item-show [edit]="edit" [editItem]="item" (showError)="showError($event)" (addPackageCart)="successAddToCart($event)" [product]="product" (resetCart)="reset()"></app-package-item-show>
        </div>
    </div>

    <!-- View List -->

    <div class="row pb-3 pt-3">
        <div class="col-md-3">
            <h4 class="colorPurpel">
                View Items
            </h4>
        </div>
        <div *ngIf="isShowCartRentalDate && product==null" class="col-md-8">
            <div *ngIf="isRentalDateEditMode;else showText">
                <app-date-time-range [ngClass]="[showCartEndDate === false ? 'single' : 'both']" (startDateTime)="selectCartstartDateTime($event)" (endDateTime)="selectCartendDateTime($event)" (reload)="checkAvailable($event)" (cancel)="onCancel($event)" [placeholderEnd]="cart_endDate" [placeholderStart]="cart_startDate"
                    [displayTime]="showCartStartTime" [onlyStartDate]="!showCartEndDate">
                </app-date-time-range>
                <button class="button theme-btn cancel-btn" (click)="isRentalDateEditMode = false">Cancel</button>
            </div>

            <ng-template #showText>
                <label class="mt-1">
                    Rental Dates {{cart_startDate | customDate: 'rental'}} - {{cart_endDate | customDate: 'rental'}}
            
                    <i class="fa fa-edit edit-icon" (click)="onClickEdit()"></i>
                </label>
            </ng-template>
        </div>
    </div>
    <div class="m-portlet__body form-panel">
        <button *ngIf="productList" class="btn btn-brand float-right aditional-charge-btn" (click)="onClickAdditionalCharge()">Additional Charge</button>
        <app-carttable [productList]="productList" (qtyUpdate)="updateCartQunt($event)" (productUpdate)="archiveItem($event)" (editProduct)="editCart($event)"></app-carttable>

        <div *ngIf="cartToken" class="mt-3">
            <app-cart-item-show [cartList]="productList" (couponApply)="applyCoupon($event)"></app-cart-item-show>
            <div class="quote-checkout-btn-area">
                <button class="btn btn-primary mr-3" [disabled]="productList?.cart_items.length < 1 || shippingSelect == 0" (click)="checkOutProceed(true)">
          Make Quote <i class="fa fa-angle-right"></i>
        </button>
                <button class="btn btn-brand" [disabled]="productList?.cart_items.length < 1 || shippingSelect == 0" (click)="checkOutProceed()">
          Proceed to Checkout <i class="fa fa-angle-right"></i>
        </button>
            </div>
        </div>
    </div>
</div>