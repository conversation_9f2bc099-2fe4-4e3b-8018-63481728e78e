import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { CartService } from '../../../../cart-service/cart.service';
import { formateRentType, GET_USER } from '../../../../../globals/_classes/functions';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ProductDiscountComponent } from '../../../cart-view/product-discount/product-discount.component';
import { DialogBoxComponent } from '../../../../../modules/dialog-box/dialog-box.component';
import { product_image } from '../../../../../globals/endPoint/config';

@Component({
  selector: 'app-carttable',
  templateUrl: './carttable.component.html',
  styleUrls: ['../cart/cart.component.css']
})
export class CarttableComponent implements OnInit {


  @Input('productList') productList;
  @Output('qtyUpdate') qtyUpdate = new EventEmitter;
  @Output('productUpdate') productUpdate = new EventEmitter;
  @Output('editProduct') editProduct = new EventEmitter;

  imageUrl = product_image + GET_USER().store_id;

  constructor(
    private cartS: CartService,
    private modalService: NgbModal,
  ) { }

  ngOnInit() {
  }


  get showStartTime() {
    const contents = localStorage.getItem("contents")
      ? JSON.parse(localStorage.getItem("contents"))
      : null;
    if (
      contents.site_specific.confg &&
      contents.site_specific.confg.hasOwnProperty("show_start_time")
    ) {
      return contents.site_specific.confg.show_start_time;
    }
    return true;
  }

  getDate(d) {
    return this.cartS.formateListDate(d);
  }

  getType(d, t) {
    return d && t ? formateRentType(d, t) : "";
  }

  updateCartQunt(data) {
    this.qtyUpdate.emit(data);
  }

  addDiscount(price, basePrice, cart) {
    console.log(basePrice, price, cart)
    const modalRef = this.modalService.open(ProductDiscountComponent, {
      centered: true,
      windowClass: "discount"
    });
    modalRef.componentInstance.basePrice = basePrice;
    modalRef.componentInstance.price = price;
    modalRef.componentInstance.cart = cart;
    modalRef.componentInstance.token = this.productList.token;
    modalRef.result.then(
      result => {
        if (result) {
        }
      },
      res => {
        console.log(res);
      }
    );
  }

  removeCart(cart) {
    const modalRef = this.modalService.open(DialogBoxComponent, {
      centered: true,
      size: "sm"
    });
    modalRef.componentInstance.massage = "Are you sure you want to delete?";
    modalRef.result.then(
      result => {
        if (result) {
          this.archiveItem(cart);
        }
      },
      res => {
        console.log(res);
      }
    );
  }

  archiveItem(cart) {
    this.cartS
      .deleteCart(cart, this.productList.token)
      .then(res => {
        if (res.status == "OK" && res.result.data) {
          this.deletLog(cart);
          this.productUpdate.emit({data: res.result.data, message: 'Cart product has been deleted'});
        } else {
          this.productUpdate.emit({data: null, message: 'Cart product has been not deleted'});
        }
      })
      .catch(err =>
        this.productUpdate.emit({data: null, message: 'Something wrong!!! Cart product has been not deleted'})
      );
  }

  deletLog(cart) {
    if (sessionStorage.getItem("item_log")) {
      let item_log = [];
      item_log = JSON.parse(sessionStorage.getItem("item_log"));
      if (item_log.length) {
        item_log = item_log.filter(item => {
          if (item.id !== cart.id) {
            return true;
          }
        });
        sessionStorage.setItem("item_log", JSON.stringify(item_log));
      }
    }
  }

  editCart(cart, i) {
    this.editProduct.emit({cart, i});
  }

}
