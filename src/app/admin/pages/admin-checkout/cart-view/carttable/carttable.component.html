<div class="table-responsive">
  <table class="table table-hover">
    <thead>
      <tr>
        <th>Image</th>
        <th>Description</th>
        <th>Price</th>
        <!-- <th>Sales Tax</th> -->
        <th style="min-width:132px;">Quantity</th>
        <th>Subtotal</th>
        <!-- <th>Total</th> -->
        <th></th>
      </tr>
    </thead>

    <tbody
      *ngIf="productList && productList?.cart_items?.length > 0; else NoItem"
    >
      <ng-container
        *ngFor="
          let cart of productList?.cart_items;
          let i = index;
          trackBy: trackList
        "
      >
        <tr>
          <td>
            <img
              *ngIf="
                cart.product.images && cart.product.images?.length > 0;
                else alterImage
              "
              class="img-fluid img-avatar img-thumbnail img-resize"
              src="{{
                imageUrl +
                  '/' +
                  cart.product.id +
                  '/' +
                  (cart.product.images[0]
                    ? cart.product.images[0].image_small
                    : '')
              }}"
              onError="this.src='./assets/img/home/<USER>';"
              alt="Product Image"
            />
            <ng-template #alterImage>
              <img
                class="img-fluid img-avatar img-thumbnail"
                src="./assets/img/home/<USER>"
                alt="Product Image"
                width="100"
              />
            </ng-template>
          </td>
          <td>
            <h5>{{ cart.product.name }}</h5>

            <div
              *ngIf="
                cart.product.variant_chain_name &&
                cart.product.variant_chain_name != 'Unassigned: Unassigned'
              "
            >
              <small>({{ cart.product.variant_chain_name }})</small>
            </div>
            <!-- <div>
              <div *ngIf="cart.rent_start">
                <small
                  >From
                  {{cart?.rent_start}}</small
                >
              </div>
              <div *ngIf="cart.rent_end">
                <small
                  >To
                  {{ cart?.rent_end }}</small
                >
              </div>
            </div> -->
            <div *ngIf="cart.deposit_amount > 0">
              Deposit Amount: {{ cart.deposit_amount | currency }}
              {{ cart.deposite_tax == "true" ? "(Including Tax)" : "" }}
            </div>
            <ng-container
              *ngIf="cart.product_type === 2 && cart.products.length"
            >
              <h6 class="pt-3 pb-1">Package Includes</h6>
              <ng-container  *ngFor="let packageProduct of cart.products"> 
                <div
                class="mb-2"
                *ngIf="packageProduct?.product_type !==3"
              >
                <div style="font-size:13px;">
                  {{ packageProduct.name }}({{
                    packageProduct.quantity
                  }})
                </div>
                <small
                  *ngIf="
                    packageProduct.variant_chain !==
                    'Unassigned: Unassigned'
                  "
                >
                  <i>{{
                    packageProduct.variant_chain
                  }}</i></small
                >
              </div>
           </ng-container>
            </ng-container>
          </td>
          <td>
            {{ cart.price | currency }}
            <!-- {{cart.rental_type && cart.rental_type != 'buy'? '/' + cart.term :''}} {{getType(cart.rental_type, cart.term)}} -->
          </td>
          <td>
            <app-cart-qty
              [index]="i"
              [cartItem]="cart"
              [cart]="productList"
              (afterApply)="updateCartQunt($event)"
            ></app-cart-qty>
          </td>
          <td>
            {{ cart.sub_total | currency }}
          </td>
          <td>
            <button
              (click)="
                addDiscount(cart.sub_total, cart.substantive_price, cart)
              "
              title="Edit Discount"
              class="btn m-btn m-btn--hover-brand m-btn--icon m-btn--icon-only m-btn--pill"
            >
              <i class="fa fa-percent"></i>
            </button>
            <!-- <button
              *ngIf="cart.rental_type && cart.rental_type != 'buy' && cart?.product_type == 1"
              (click)="editCart(cart, i)"
              title="Edit Cart Item"
              class="btn m-btn m-btn--hover-info m-btn--icon m-btn--icon-only m-btn--pill"
            >
              <i class="fa fa-edit"></i>
            </button> -->
            <button
              (click)="removeCart(cart)"
              title="Remove Item"
              class="btn m-btn m-btn--hover-danger m-btn--icon m-btn--icon-only m-btn--pill"
            >
              <i class="fa fa-trash"></i>
            </button>
          </td>
        </tr>

        <ng-container *ngFor="let addon_item of cart?.products">
          <tr *ngIf="addon_item.product_type==3">
            <td class="cart-img">
              <img *ngIf="
              addon_item.image != '';
                  else placeholder
                " class="img-fluid img-avatar img-thumbnail img-resize" src="{{ img_url }}{{ item?.store_id }}/{{
                  addon_item?.id
                }}/{{
                  addon_item?.image
                }}" onError="this.src='./assets/img/home/<USER>';" />
              <ng-template #placeholder>
                <img class="img-fluid img-avatar img-thumbnail"
                  src="./assets/img/home/<USER>" />
              </ng-template>
            </td>
            <td>
              <div class="product p-0">
                {{ addon_item.name }}

                <div class="p_variants">
                  <span *ngIf="
                     addon_item.variant_chain !=
                        'Unassigned: Unassigned'
                      ">
                    <small>{{ addon_item.variant_chain }} </small>
                  </span>
                </div>
              </div>
            </td>
            <td></td>
            <td>
              <p>{{ addon_item?.quantity }}</p>
            </td>
            <td></td>
            <td></td>
            
          </tr>
        </ng-container>

      </ng-container>
    </tbody>
    <ng-template #NoItem>
      <tbody>
        <tr>
          <td colspan="7">
            <h5 class="text-center">No Item Found</h5>
          </td>
        </tr>
      </tbody>
    </ng-template>
  </table>
</div>
