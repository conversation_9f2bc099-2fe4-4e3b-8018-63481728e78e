import { DragDropModule } from './../../../../modules/drag-drop/drag-drop.module';
import { PlanUpgradeAlertModule } from './../../../../modules/plan-upgrade-alert/plan-upgrade-alert.module';
import { NumberOnlyDirectiveModule } from './../../../../modules/directive/directive.module';
import { VariantsModule } from './../../../../modules/variants/variants.module';
import { Select2AddOptionModule } from './../../../../modules/select2-add-option/select2.module';
import { LayoutModule } from './../../../layouts/layout.module';
import { ReactiveFormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AddAbbreviatedProductComponent } from './add-abbreviated-product.component';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';

const routes: Routes = [
  {
    path: '',
    component: AddAbbreviatedProductComponent
  }
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    ReactiveFormsModule,
    LayoutModule,
    Select2AddOptionModule,
    VariantsModule,
    NumberOnlyDirectiveModule,
    PlanUpgradeAlertModule,
    NgbModule,
    DragDropModule
  ],
  declarations: [AddAbbreviatedProductComponent]
})
export class AddAbbreviatedProductModule { }
