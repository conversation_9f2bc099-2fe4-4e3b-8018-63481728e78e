.m-portlet .m-portlet__head {
    height: 3.5rem;
}
.drop {
    width: 100%;
    padding: 10px;
    min-height: 150px;
    border: 2px dashed #DADFE3;
    overflow: hidden;
    text-align: center;
    background: white;
    transition: all 0.5s ease-out;
    margin: 0px;
    position: relative;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
}
.drop .cont {
    padding-top: 10px;
    width: 100%;
    color: #8E99A5;
    transition: all 0.5s ease-out;
    margin: auto;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
}
.drop .cont i {
    font-size: 150px;
    color: #efefef;
    position: absolute;
    text-align: center;
    top: 50%;
    left: 30%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
}
.drop .cont .tit[_ngcontent-c8], .drop .cont .desc {
    position: relative;
    top: 30%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
}

.drop .cont .tit {
    font-size: 150%;
}
.drop .cont .tit[_ngcontent-c8], .drop .cont .desc {
    position: relative;
    top: 30%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
}

.drop .cont .desc {
    color: #A4AEBB;
}

.drop input {
    width: 100%;
    height: 100%;
    cursor: pointer;
    background: red;
    opacity: 0;
    margin: auto;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
}
.drop .cont .tit {
    font-size: 150%;
    position: relative;
}
.form-control, .form-control {
    border-color: #ebedf2;
    color: #575962;
}
.btn-submit {
    background-color: #5fa55f;
    color: #fff;
}
.btn-submit:hover {
    background-color: #408440;
}
.advanced-option-title .m-portlet__head{
    border-bottom: none;
}
.advanced-option-body {
    border-top: 1px solid #ebedf2;
}
.custom-alert {
    position: fixed;
    top: 90px;
    right: 30px;
    z-index: 5000;
}
.m-portlet .m-portlet__head .m-portlet__head-text {
    /* color: #575962; */
    font-size: 16px !important;
}
.btn-savenmore {
    background: #36a3f7;
    color: #fff;
}
.main-title {
    font-weight: 500;
}
@media (min-width:1200px) {
    .image-upload-body {
        height: 292px;
    }
}
@media (min-width:1200px) and (max-width: 1510px) {
    .abbreviated-product-body .col-xl-9 {
        flex: 0 0 90%;
        max-width: 90%;
    }
    .advanced-area-inner .form-group {
        /* flex: 0 0 50%;
        max-width: 50%; */
    }
}