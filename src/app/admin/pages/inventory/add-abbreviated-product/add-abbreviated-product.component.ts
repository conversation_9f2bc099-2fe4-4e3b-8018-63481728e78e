import { HttpErrorResponse } from '@angular/common/http';
import { EndPoint } from "./../../../../globals/endPoint/config";
import { Supplier } from "./../product-models/inventory.models";
import { ActivatedRoute, Router } from "@angular/router";
import { Helpers } from "./../../../../helpers";
import { InventoryService } from "./../inventory-serveice/inventory.service";
import { AlertService } from "./../../../../modules/alert/alert.service";
import {
  Component,
  OnInit,
  AfterViewInit,
  ElementRef,
  ViewChild
} from "@angular/core";
import { FormGroup, FormBuilder, Validators, FormArray } from "@angular/forms";

declare let $: any;

@Component({
  selector: "app-add-abbreviated-product",
  templateUrl: "./add-abbreviated-product.component.html",
  styleUrls: ["./add-abbreviated-product.component.css"]
})
export class AddAbbreviatedProductComponent implements OnInit, AfterViewInit {
  locations: any;
  form: FormGroup;
  photoValidation: string;
  photoFileName: string;
  submitted: boolean = false;
  advancedOptions = false;
  pricingOption = "standard";
  varinats: any[] = [];
  currency;
  variantIds: number[];
  tr_status = [
    { name: "Track quantity only", value: 0 },
    { name: "Track specific assets", value: 1 }
  ];
  showVar: boolean;
  suplliers: Supplier[] = [];
  tagsList: any[] = [];
  formInitialized = false;
  @ViewChild("hasCusAlert") alertContainer: ElementRef;
  locationFormArray: FormArray;
  upload_url: string = EndPoint;
  image_additional = {
    type: "image"
  };
  accept_type = 'image/x-png, image/gif, image/jpeg, image/bmp';
  is_hide_quantity: boolean;

  constructor(
    private fb: FormBuilder,
    private alertS: AlertService,
    private inventoryS: InventoryService,
    private activeRoute: ActivatedRoute,
    private router: Router
  ) {
    this.getLocations();
    this.initForm();
    this.getVariants();
    this.currency = JSON.parse(localStorage.getItem("currency"));
    this.form.addControl('standard', this.initPricingForm());
    this.form.patchValue({
      standard: {
        price_type: 3
      }
    });
  }

  ngOnInit(): void {
    this.suplliers = this.activeRoute.snapshot.data["supplier"].data;
    if (this.suplliers) {
      this.suplliers = this.suplliers;
    }
  }

  ngAfterViewInit(): void {
    this._description();
  }

  private initPricingForm(): FormGroup {
    return this.fb.group({
      price_type: [2],
      additional_duration: [""],
      additional_range: [""],
      additional_price: [""],
      duration_type: ["hourly"],
      duration: [""],
      price: [""]
    });
  }

  private initForm(): void {
    this.form = this.fb.group({
      name: ["", Validators.required],
      description: [""],
      image: [""],
      basic: this.initPricingForm(),
      quantity: this.fb.array([]),
      base_price: [''],
      tags: [""],
      keyword: [""],
      supplier_id: [""],
      supply_id: [""],
      client_specific_id: [""],
      sales_tax: [""],
      deposit_amount: [""],
      driving_license: [false],
      featured: [false],
      free_shipping: [false],
      is_default_weight: [true],
      weight_amount: [""],
      weight_unit: ["pound"],
      is_tracked: [0]
    });
    for (let x = 0; x < this.locations.length; x++) {
      this.locationFormArray = this.form.get("quantity") as FormArray;
      this.locationFormArray.push(
        this.quantityForm(this.locations[x].id, this.locations[x].name)
      );
    };
    this.formInitialized = true;
  }

  private quantityForm(location_id, name): FormGroup {
    return this.fb.group({
      quantity: [""],
      location_id: [location_id ? location_id : null],
      label: [name ? name : ""]
    });
  }

  get formDataLocation() {
    return this.form.get("quantity") as FormArray;
  }

  private getLocations() {
    this.locations = localStorage.getItem("locations")
      ? JSON.parse(localStorage.getItem("locations"))
      : [];
  }

  submit(flag?: string): void {
    this.submitted = true;
    if (this.form.invalid) {
      //window.scrollTo(0, 0);
      return;
    }
    Helpers.setLoading(true);
    let data = {};
    data = this.form.getRawValue();
    if (this.pricingOption === "basic") {
      data["price"] = data["basic"];
    } else if (this.pricingOption === "standard") {
      data["price"] = data["standard"];
    } else {
      data["price"] = data["advance"];
    }
    if (this.variantIds && this.variantIds.length > 0) {
      data["variant_set"] = this.variantIds;
    }
    delete data["basic"];
    delete data["standard"];
    delete data["advance"];
    this.inventoryS.addAbbrevietedProduct(data).then(res => {
      console.log(res);
      Helpers.setLoading(false);
      this.submitted = false;
      if (res.status === 'OK') {
        const timeout = 3000;
        this.alertS.success(this.alertContainer, res.result.message, true, timeout);
        if (flag === 'saveAndAdd') {
          this.form.reset();
          this.is_hide_quantity = false;
          this.form.get('is_tracked').setValue(0);
          this.form.controls['quantity'] = this.fb.array(this.locations.map((loc) => {
            const group = this.quantityForm(loc.id, loc.name);
            return group;
          }));
          //window.scroll(0,0);
        } else {
          setTimeout(() => {
            this.router.navigate(['/admin/inventory']);
          }, timeout);
        }
      }
    }).catch((err: HttpErrorResponse) => {
      Helpers.setLoading(false);
      this.submitted = false;
      this.alertS.error(this.alertContainer, err.error.result ? err.error.result.message : err.message, true, 3000);
    });
  }

  private getVariants() {
    this.inventoryS.getAtrributeSet().subscribe(
      res => {
        this.varinats = res.data.filter(f => f.id !== 1);
      },
      err => console.log(err)
    );
  }

  private _description = () => {
    $(".summernote-description").summernote(this.inventoryS.summarNoteMinimum());
    $(".summernote-description").on("summernote.blur", () => {
      this.form
        .get("description")
        .setValue($(".summernote-description").summernote("code"));
    });
  };

  private getTags() {
    this.inventoryS.getTags().subscribe(res => {
      this.tagsList = res.data;
    });
  }

  toggleAdvancedOptions(): void {
    this.advancedOptions = !this.advancedOptions;
    $("#advanceSearch").toggleClass("la-angle-down la-angle-up");
    if (this.advancedOptions) {
      this.getTags();
    }
  }

  isValidNumber(e: any) {
    return e.charCode === 0 || ((e.charCode >= 48 && e.charCode <= 57) || (e.charCode == 46))
  }

  isValidDuration(event: any) {
    const pattern = /[0-9]/;
    const inputChar = String.fromCharCode(event.charCode);
    if (!pattern.test(inputChar)) {
      event.preventDefault();
    }
  }

  onSelect(value): void {
    if (value === "basic") {
      this.pricingOption = "basic";
      this.form.get("basic.price_type").setValue(2);
    } else if (value === "standard") {
      this.pricingOption = "standard";
      this.form.addControl('standard', this.initPricingForm());
      this.form.patchValue({
        standard: {
          price_type: 3
        }
      });
    } else {
      this.pricingOption = "advance";
      this.form.addControl('advance', this.initPricingForm());
      this.form.get("advance.price_type").setValue(4);
    }
  }

  changedTagList(e: any): void {
    if (e.messag) {
      this.alertS.error(this.alertContainer, e.message, true, 3000);
    } else {
      this.form.get("tags").setValue(this.inventoryS.formatToNumber(e.id));
    }
  }

  changed(e: any): void {
    if (e.messag) {
      this.alertS.error(this.alertContainer, e.message, true, 3000);
    } else {
      this.form.get("supplier_id").setValue(parseInt(e.id));
    }
  }

  uploadImages(e) {
    if (e.status.status === "OK") {
      this.form.get("image").setValue(e.status.result.data.filename);
    }
  }

  imageRemoved(e): void {
    if (e) {
      this.form.get("image").setValue(null);
    }
  }

  changeTrackStatus(e) {
    if (e===1) {
      this.is_hide_quantity = true;
    } else {
      this.is_hide_quantity = false;
    }
  }

  // ********************* Variant **********************

  varinatValues(e) {
    this.variantIds = e.filter(f => f.set_id).map(m => m.set_id);
  }

  showVariants() {
    this.showVar = true;
    this.variantIds = [];
  }

  cancelVariants() {
    this.showVar = false;
    this.variantIds = [];
  }
}
