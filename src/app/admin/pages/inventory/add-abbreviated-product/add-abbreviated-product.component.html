<div class="custom-alert" #hasCusAlert></div>

<div class="m-subheader">
  <div class="d-flex align-items-center">
    <div class="mr-auto">
      <h3 class="m-subheader__title m-subheader__title--separator">
        Add Product
      </h3>
      <ul class="m-subheader__breadcrumbs m-nav m-nav--inline">
        <li class="m-nav__item m-nav__item--home">
          <a routerLink="/admin" class="m-nav__link m-nav__link--icon">
            <i class="m-nav__link-icon la la-home"></i>
          </a>
        </li>
        <li class="m-nav__separator">
          <i class="fa fa-angle-right"></i>
        </li>
        <li class="m-nav__item">
          <a routerLink="/admin/inventory" class="m-nav__link">
            <span class="m-nav__link-text">
              Product List
            </span>
          </a>
        </li>
        <li class="m-nav__separator">
          <i class="fa fa-angle-right"></i>
        </li>
        <li class="m-nav__item">
          <a class="m-nav__link">
            <span class="m-nav__link-text">
              Add Product
            </span>
          </a>
        </li>
      </ul>
    </div>
  </div>
</div>

<form class="m-content row abbreviated-product-body" [formGroup]="form" (submit)="submit()" *ngIf="formInitialized">

  <div class="col-sm-12 col-md-12 col-lg-12 col-xl-9">
    <div class="m-portlet">
      <!-- <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
          <div class="m-portlet__head-title m-product-title">
            <h3 class="m-portlet__head-text" style="width:100%;"> Add Product </h3>
          </div>
        </div>
      </div> -->
      <div class="m-portlet-body pt-4 pb-4">
        <div class="m-form m-form--fit m-form--label-align-right">
          <div class="row m-0 pl-3 pr-3">
            <div class="col-md-6">
              <div class="form-group pb-0">
                <label for="title"> Product Name* </label>
                <input autocomplete="off" formControlName="name" class="form-control" placeholder="Product Name" type="text">
                <small style="color: red;" *ngIf="submitted && form.get('name').invalid">Product name is required.</small>
              </div>
              <ng-container *ngIf="!is_hide_quantity">
                <div class="form-group pb-0" formArrayName="quantity"
                *ngFor="let item of formDataLocation.controls; let i = index;">
                  <div [formGroupName]="i">
                    <label for="title"> Qty {{item.value.label}}</label>
                    <input class="form-control" placeholder="Quantity" type="text" formControlName="quantity">
                  </div>
                </div>
              </ng-container>
            </div>
            <div class="col-md-6">
              <label for="title"> Image </label>
              <div class="form-group pb-0 mb-0">
                <div style="position: relative;">
                  <drag-drop
                    [multi]='false'
                    [url]="upload_url + 'media/upload'"
                    [info]="image_additional"
                    [fileName]="'image'" 
                    [accept]="accept_type"
                    [directUpload]="true"
                    [class]="'col-md-12'"
                    [imagePreviewClass]="'col-md-6'"
                    (upload)="uploadImages($event)"
                    (removeImage)="imageRemoved($event)">
                  </drag-drop>
                </div>
              </div>
            </div>
            <div class="col-md-12">
              <div class="form-group pb-0">
                <label for="title"> Description </label>
                <div class="summernote-description addproduct-summernote-description"></div>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>

  <!-- <div class="col-sm-12 col-md-12 col-lg-12 col-xl-5">
    <div class="m-portlet">
      <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
          <div class="m-portlet__head-title m-product-title">
            <h3 class="m-portlet__head-text"> Image </h3>
          </div>
        </div>
      </div>
      <div class="m-portlet-body image-upload-body pt-2">
        <div class="m-form m-form--fit m-form--label-align-right">
          
        </div>
      </div>
    </div>
  </div> -->


  <div class="col-sm-12 col-md-12 col-lg-12 col-xl-9">
    <div class="m-portlet">
      <!-- <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
          <div class="m-portlet__head-title m-product-title">
            <h3 class="m-portlet__head-text"> Price </h3>
          </div>
        </div>
      </div> -->
      <div class="m-portlet-body pt-4 pb-4">
        <div class="m-form m-form--fit m-form--label-align-right">
          <div class="row m-0 pl-3 pr-3">
            <div class="col-lg-6">
              <div class="form-group pb-0 mb-3">
                <label class="main-title" for="title"> Sell Price </label>
                <input autocomplete="off" formControlName="base_price" class="form-control" placeholder="Sell Price" type="text" (keypress)="isValidNumber($event)">
              </div>
            </div>
          </div>
          <div class="row m-0 pl-3 pr-3">
            <div class="col-lg-6">
              <div class="form-group pb-0 ">
                <label class="main-title" for="title"> Rental Price </label>
                <select class="form-control" (change)="onSelect($event.target.value)">
                  <option value="basic">Basic</option>
                  <option value="standard" selected>Standard</option>
                  <option value="advance">Advance</option>
                </select>
              </div>
            </div>
          </div>
          <div class="row m-0 pl-3 pr-3" *ngIf="pricingOption === 'basic'" formGroupName="basic">
            <div class="col-lg-6">
              <div class="form-group pb-0">
                <label for="title"> Fixed price </label>
                <div class="input-group m-input-group">
                  <div class="input-group-prepend"><span class="input-group-text"> $ </span></div>
                  <input autocomplete="off" class="form-control m-input" formControlName="price" numberonly="" placeholder="Amount" type="text" (keypress)="isValidNumber($event)">
                  <div class="input-group-append"><small class="input-group-text"> USD </small></div>
                </div>
              </div>
            </div>
          </div>

          <div class="row m-0 pl-3 pr-3" *ngIf="pricingOption === 'standard'" formGroupName="standard">
            <!-- standard Price -->
            <div class="col-md-12">
              <div class="row">
                  <div class="col-md-3">
                    <label>Time</label>
                  </div>
                  <div class="col-md-4">
                      <label>Duration</label>
                  </div>
                  <div class="col-md-5">
                      <label>Amount</label>
                  </div>
              </div>
              <div class="row">
                  <div class="form-group col-md-3 pt-0">
                      <div class="input-group m-input-group">
                        <select class="form-control m-input dropdown-cls" formControlName="duration_type">
                          <option value="hourly">Hourly</option>
                          <option value="daily">Daily</option>
                          <option value="weekly">Weekly</option>
                          <option value="monthly">Monthly</option>
                        </select>
                      </div>
                  </div>
                  <div class="form-group col-md-4 pt-0">
                      <div class="input-group m-input-group">
                          <input autocomplete="off" (keypress)="isValidDuration($event)" class="form-control m-input" formControlName="duration" numberonly="" placeholder="Duration" type="text">
                      </div>
                  </div>
                  <div class="form-group col-md-5 pt-0">
                      <div class="input-group m-input-group">
                          <div class="input-group-prepend"><span class="input-group-text"> $ </span></div>
                          <input autocomplete="off" (keypress)="isValidNumber($event)" class="form-control m-input" formControlName="price" numberonly="" placeholder="Amount" type="text">
                          <div class="input-group-append"><small class="input-group-text"> USD </small></div>
                        </div>
                  </div>
              </div>
            </div>
          </div>

          <div class="row pl-3 pr-3 advanced-area" *ngIf="pricingOption === 'advance'" formGroupName="advance">
                <!-- Advanced Price -->
                <div class="col-md-12">
                  <div class="row m-0 advanced-area-inner">
                    <div class="form-group col-lg-2 pt-0">
                      <label>Time </label>
                        <div class="input-group m-input-group">
                          <select class="form-control m-input dropdown-cls" formControlName="duration_type">
                            <option value="hourly">Hourly</option>
                            <option value="daily">Daily</option>
                            <option value="weekly">Weekly</option>
                            <option value="monthly">Monthly</option>
                          </select>
                        </div>
                    </div>
                    <div class="form-group col-lg-4 pt-0">
                      <label>Duration / Range ? </label>
                        <div class="input-group m-input-group">
                            <input autocomplete="off" (keypress)="isValidDuration($event)" class="form-control m-input"  placeholder="Duration" type="text" formControlName="duration">
                            <input autocomplete="off" (keypress)="isValidDuration($event)" class="form-control m-input" placeholder="range" type="text" formControlName="additional_range">
                        </div>
                    </div>
                    <div class="form-group col-lg-2 pt-0">
                      <label>Amount</label>
                        <div class="input-group m-input-group">
                            <input autocomplete="off" (keypress)="isValidNumber($event)" class="form-control m-input" placeholder="Amount" type="text" formControlName="price">
                        </div>
                    </div>
                    <div class="form-group col-lg-4 pt-0">
                      <label>Additional Price / Duration ?</label>
                        <div class="input-group m-input-group ">
                            <input autocomplete="off" (keypress)="isValidNumber($event)" class="form-control m-input" placeholder="Amount" type="text" formControlName="additional_price">
                            <input autocomplete="off" (keypress)="isValidDuration($event)" class="form-control m-input ml-2" placeholder="Duration" type="text" formControlName="additional_duration">
                        </div>
                    </div>
                  </div>
                </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="col-sm-12 col-md-12 col-lg-12 col-xl-9">
    <div class="m-portlet">
      <!-- <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
          <div class="m-portlet__head-title m-product-title">
            <h3 class="m-portlet__head-text"> Quantity </h3>
          </div>
        </div>
      </div> -->
      <div class="m-portlet-body ">
        <div class="m-form m-form--fit m-form--label-align-right">
          <div class="row">
            

            <!-- Advanced options -->
            <div class="col-lg-12 advanced-option-area">
              <div class="advanced-option-title">
                <div class="m-portlet__head" style="cursor: pointer;" (click)="toggleAdvancedOptions()">
                  <div class="m-portlet__head-caption">
                      <div class="m-portlet__head-title">
                          <h3 class="m-portlet__head-text text-left" style="width:92%;">Advanced options </h3>
                          <h3 class="text-right" style="margin: auto;padding: 14px 5px;"><i class="la la-angle-down" id="advanceSearch" style="font-size: 1.8rem;"></i></h3>
                      </div>
                  </div>
                </div>
              </div>

              <div class="advanced-option-body pt-4" *ngIf="advancedOptions">
                
                <div class="form-group m-form__group pb-0">
                  <label for="keyword">
                    Tags
                  </label>
                  <select2-add-option [multi]="true" [data]="tagsList" [prop]="'name'" [url]="'tags'" [domId]="'tags'"
                    [placeholder]="'Tag'" (changeValue)="changedTagList($event)"></select2-add-option>
                </div>
                <div class="form-group m-form__group">
                  <label for="primary">
                    Keyword
                  </label>
                  <input type="text" autocomplete="off" id="primary" class="form-control m-input" placeholder="Keyword"
                    name="keyword" formControlName="keyword">
                </div>
                <div class="row m-0 pl-3 pr-3">
                  <div class="col-md-4">
                    <div class="form-group m-form__group pl-0 pr-0">
                      <label for="Merchant">
                        Select Vendor
                      </label>
                      <select2-add-option [data]="suplliers" [prop]="'name'" [url]="'suppliers'" [domId]="'suppliers'"
                        [placeholder]="'Vendor'" (changeValue)="changed($event)"></select2-add-option>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group m-form__group pl-0 pr-0">
                      <label for="merchantID">
                          Vendor Product ID
                      </label>
                      <input type="text" autocomplete="off" id="merchantID" class="form-control m-input"
                        placeholder="Vendor Product ID" name="supply_id" formControlName="supply_id">
                      <span>
                        <!-- <small class="error" >Merchant Product ID required</small> -->
                      </span>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group m-form__group pl-0 pr-0">
                      <label for="clientSpecifID">
                        Your SKU
                      </label>
                      <input type="text" autocomplete="off"  class="form-control m-input"
                            placeholder="Your SKU" name="client_specific_id" formControlName="client_specific_id">
          
                    </div>
                  </div>
                </div>
          
                <div class="row m-0 pl-3 pr-3">
                  <div class="col-lg-6">
                    <div class="form-group m-form__group pt-3 pl-0 pr-0">
                      <label for="sale_tax">
                        Sales Tax
                      </label>
                      <div class="input-group m-input-group">
                        <input id="sale_tax" numberOnly class="form-control m-input" type="text" name="sale_tax"
                          placeholder="0.00" autocomplete="off" formControlName="sales_tax">
                        <div class="input-group-append">
                          <span class="input-group-text" id="table-cost-addon">
                            %
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-6">
                    <div class="form-group m-form__group pt-3 pl-0 pr-0">
                      <label for="deposit">
                        Deposit Amount
                      </label>
                      <div class="input-group m-input-group">
                        <div class="input-group-prepend">
                          <span class="input-group-text" id="table-cost-addon">
                            {{ currency.symbol? currency.symbol : '$'}}
                          </span>
                        </div>
                        <input numberOnly class="form-control m-input" name="deposit"
                          type="text" placeholder="0.00" autocomplete="off" formControlName="deposit_amount">
                        <div class="input-group-append">
                          <small class="input-group-text" id="table-cost-addon">
                            {{ currency.code? currency.code : 'USD'}}
                          </small>
                        </div>
                      </div>
                    </div>
          
                  </div>
                </div>
          
          
          
          
          
                <div class="form-group m-form__group row" style="padding-top: 20px;">
                  <div class="col-md-6">
                    <label class="m-checkbox">
                      <input type="checkbox" name="image" formControlName="driving_license">
                      Driver's licence required?
                      <span></span>
                    </label>
                  </div>
                  <div class="col-md-6">
                    <label class="m-checkbox">
                      <input type="checkbox" name="featured" formControlName="featured" >
                      Make as featured
                      <span></span>
                    </label>
                  </div>
                  <div class="col-md-6">
                    <label class="m-checkbox">
                      <input type="checkbox" name="shipping" formControlName="free_shipping" >
                      Free shipping
                      <span></span>
                    </label>
                  </div>
                </div>
                <hr />
                <div class="form-group m-form__group row pb-0">
                  <div class="col-md-6">
                    <label class="m-checkbox" style="margin-top: 20px;">
                      <input type="checkbox" name="image" formControlName="is_default_weight">
                      Enable default weight
                      <span></span>
                    </label>
                  </div>
                  <div class="col-md-6">
                    <label for="unit">
                      Weight
                    </label>
                    <div class="input-group m-input-group">
                      <input type="text" numberOnly autocomplete="off" id="unit" class="form-control m-input" placeholder="0.00"
                        name="weight_amount" formControlName="weight_amount">
                      <div class="input-group-append">
                        <select class="form-control cursor-pointer" name="weight_unit" formControlName="weight_unit">
                          <option value="pound">lb</option>
                          <option value="kilogram">kg</option>
                          <option value="ounce">oz</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>
          
                <div class="form-group m-form__group" style="padding-top: 20px;">
                  <div class="row">
                    <div class="col-6"><label>Variants</label></div>
                    <div class="col-6 text-right">
                      <button type="button" *ngIf="!showVar" class="btn btn-sm btn-dark" (click)="showVariants()">Add</button>
                      <button type="button" *ngIf="showVar" class="btn btn-sm btn-danger" style="margin-left: 5px;"
                        (click)="cancelVariants()">Cancel</button>
                    </div>
                  </div>
                  <p>Add variants if the product comes in different versions, like different sizes or colors</p>
                  <div *ngIf="showVar">
                    <app-variants [variants]="varinats" (values)="varinatValues($event)"></app-variants>
                  </div>
                </div>
          
                <div class="row m-0 pl-3">
          
                  <div class="col-md-12">
                    <h5>Product Tracking</h5>
                    <p class="ml-0">You can track details of a specific item in your inventory by using assets
                      <ng-template #popContentRange>Tip:To track bulk products by quantity, select 'Track quantity', or to track specific items/serialized products, create a unique asset ID for each product.</ng-template>
                    &nbsp;&nbsp;&nbsp;&nbsp;<b><span triggers="mouseenter:mouseleave" [ngbPopover]="popContentRange">?</span></b>
                    </p>
          
                    <div class="form-group">
                      <ng-container *ngFor="let item of tr_status">
                        <label class="m-radio" >
                          <input type="radio" name="is_tracked" (change)="changeTrackStatus(item.value)" [value]="item.value" formControlName="is_tracked"/>
                          {{item.name}}<span></span>
                        </label>
                        <br>
                      </ng-container>
                    </div>
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="col-sm-12 col-md-12 col-lg-12 col-xl-9">
    <div class="form-group text-right pb-0">
        <button class="btn btn-submit" type="submit">Save</button>
        <button class="btn btn-savenmore ml-3" (click)="submit('saveAndAdd')" type="button">Save and add new</button>
    </div>
  </div>

</form>