
<div class="custom-alert" #hasCusAlert></div>

<div class="m-subheader">
  <div class="d-flex align-items-center">
    <div class="mr-auto">
      <h3 class="m-subheader__title m-subheader__title--separator">
        {{ isProduct ? 'Add Product' : 'Add Package'}}
      </h3>
      <ul class="m-subheader__breadcrumbs m-nav m-nav--inline">
        <li class="m-nav__item m-nav__item--home">
          <a routerLink="/admin" class="m-nav__link m-nav__link--icon">
            <i class="m-nav__link-icon la la-home"></i>
          </a>
        </li>
        <li class="m-nav__separator">
          <i class="fa fa-angle-right"></i>
        </li>
        <li class="m-nav__item">
          <a routerLink="/admin/inventory" class="m-nav__link">
            <span class="m-nav__link-text">
              Product List
            </span>
          </a>
        </li>
        <li class="m-nav__separator">
          <i class="fa fa-angle-right"></i>
        </li>
        <li class="m-nav__item">
          <a class="m-nav__link">
            <span class="m-nav__link-text">
              {{ isProduct ? 'Add Product' : 'Add Package'}}
            </span>
          </a>
        </li>
      </ul>
    </div>
  </div>
</div>
<!-- END: Subheader -->
<div class="m-content row">
  <div class="m-portlet col-sm-12 col-md-12 col-lg-12 col-xl-8">
    <div class="m-portlet-body">
      <!--begin::Portlet-->
      <form class="m-form m-form--fit m-form--label-align-right" #form="ngForm" (ngSubmit)="addProduct(form)">
        <div class="form-group m-form__group">
          <label for="title">
            Title*
          </label>
          <input type="text" autocomplete="off" class="form-control m-input" placeholder="Title" id="title" name="name"
            #name="ngModel" [(ngModel)]="product.name" required>
          <span *ngIf="name.errors && name.touched">
            <small *ngIf="name.errors.required" class="error">Title Required</small>
          </span>
        </div>
        <div class="form-group m-form__group">
          <label>
            Description
          </label>
          <div class="summernote-description"></div>
        </div>
        <div class="form-group m-form__group">
          <label for="keyword">
            Tags
          </label>
          <select2-add-option [multi]="true" [data]="tagsList" [prop]="'name'" [url]="'tags'" [domId]="'tags'"
            [placeholder]="'Tag'" (changeValue)="changedTagList($event)"></select2-add-option>
        </div>
        <div class="form-group m-form__group">
          <label for="primary">
            Keyword
          </label>
          <input type="text" autocomplete="off" id="primary" class="form-control m-input" placeholder="Keyword"
            name="keyword" [(ngModel)]="product.keyword">
        </div>
        <div class="row">
          <div class="col-md-4">
            <div class="form-group m-form__group">
              <label for="Merchant">
                Select Vendor
              </label>
              <select2-add-option [data]="suplliers" [prop]="'name'" [url]="'suppliers'" [domId]="'suppliers'"
                [placeholder]="'Vendor'" (changeValue)="changed($event)"></select2-add-option>
            </div>
          </div>
          <div class="col-md-4">
            <div class="form-group m-form__group">
              <label for="merchantID">
                  Vendor Product ID
              </label>
              <input type="text" autocomplete="off" id="merchantID" class="form-control m-input"
                placeholder="Vendor Product ID" name="supply_id" #supId="ngModel" [(ngModel)]="product.supply_id">
              <span *ngIf="supId.errors && supId.touched">
                <small class="error" *ngIf="supId.errors.required">Merchant Product ID required</small>
              </span>
            </div>
          </div>
          <div class="col-md-4">
            <div class="form-group m-form__group">
              <label for="clientSpecifID">
                Your SKU
              </label>
              <input type="text" autocomplete="off"  class="form-control m-input"
                     placeholder="Your SKU" name="client_specific_id"  [(ngModel)]="product.client_specific_id">

            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-lg-6">
            <div class="form-group m-form__group">
              <label for="sale_tax">
                Sales Tax
              </label>
              <div class="input-group m-input-group">
                <input id="sale_tax" numberOnly class="form-control m-input" type="text" name="sale_tax"
                  [(ngModel)]="product.sales_tax" placeholder="0.00" autocomplete="off">
                <div class="input-group-append">
                  <span class="input-group-text" id="table-cost-addon">
                    %
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-6">
            <div class="form-group m-form__group">
              <label for="deposit">
                Deposit Amount
              </label>
              <div class="input-group m-input-group">
                <div class="input-group-prepend">
                  <span class="input-group-text" id="table-cost-addon">
                    {{ currency.symbol? currency.symbol : '$'}}
                  </span>
                </div>
                <input numberOnly class="form-control m-input" name="deposit" [(ngModel)]="product.deposit_amount"
                  type="text" placeholder="0.00" autocomplete="off">
                <div class="input-group-append">
                  <small class="input-group-text" id="table-cost-addon">
                    {{ currency.code? currency.code : 'USD'}}
                  </small>
                </div>
              </div>
            </div>

          </div>
        </div>

        <div class="row" style="margin-top: 15px;display: none;">
          <div class="col-md-6">
            <div class="form-group m-form__group">
              <label for="lwd">
                Loss/Damage Waiver Amount
              </label>
              <div class="input-group m-input-group">
                <div class="input-group-prepend">
                  <span class="input-group-text" id="table-cost-addon">
                    {{ currency.symbol? currency.symbol : '$'}}
                  </span>
                </div>
                <input id="lwd" numberOnly class="form-control m-input" type="text" name="ldw_tax"
                  [(ngModel)]="product.ldw_tax" placeholder="0.00" autocomplete="off">
                <div class="input-group-append">
                  <span class="input-group-text" id="table-cost-addon">
                    {{ currency.code? currency.code : 'USD'}}
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div class=" col-md-6"></div>
         </div>





        <div class="form-group m-form__group row" style="padding-top: 20px;">
          <div class="col-md-6" *ngIf="isProduct">
            <label class="m-checkbox">
              <input type="checkbox" name="image" [(ngModel)]="product.driving_license">
              Driver's licence required?
              <span></span>
            </label>
          </div>
          <div class="col-md-6">
            <label class="m-checkbox">
              <input type="checkbox" name="featured" [(ngModel)]="product.featured">
              Make as featured
              <span></span>
            </label>
          </div>
          <div class="col-md-6">
            <label class="m-checkbox">
              <input type="checkbox" name="shipping" [(ngModel)]="product.free_shipping">
              Free shipping
              <span></span>
            </label>
          </div>
        </div>
        <hr />
        <div class="form-group m-form__group row">
          <div class="col-md-6" *ngIf="isProduct">
            <label class="m-checkbox" style="margin-top: 20px;">
              <input type="checkbox" name="image" [(ngModel)]="product.is_default_weight">
              Enable default weight
              <span></span>
            </label>
          </div>
          <div class="col-md-6">
            <label for="unit">
              Weight
            </label>
            <div class="input-group m-input-group">
              <input type="text" numberOnly autocomplete="off" id="unit" class="form-control m-input" placeholder="0.00"
                name="weight_amount" [(ngModel)]="product.weight_amount">
              <div class="input-group-append">
                <select class="form-control cursor-pointer" name="weight_unit" [(ngModel)]="product.weight_unit">
                  <option value="pound">lb</option>
                  <option value="kilogram">kg</option>
                  <option value="ounce">oz</option>
                </select>
              </div>
            </div>
          </div>
        </div>
        <div class="form-group m-form__group" style="padding-top: 20px;" *ngIf="isProduct">
          <div class="row">
            <div class="col-6"><label>Variants</label></div>
            <div class="col-6 text-right">
              <button type="button" *ngIf="!showVar" class="btn btn-sm btn-dark" (click)="showVariants()">Add</button>
              <button type="button" *ngIf="showVar" class="btn btn-sm btn-danger" style="margin-left: 5px;"
                (click)="cancelVariants()">Cancel</button>
            </div>
          </div>
          <p>Add variants if the product comes in different versions, like different sizes or colors</p>
          <div *ngIf="showVar">
            <app-variants [variants]="varinats" (values)="varinatValues($event)"></app-variants>
          </div>
        </div>
        <div class="row" *ngIf="isProduct">
          <!-- <div class=" col-md-7">
            <h5>Assets</h5>
            <p>You can track details of a specific item in your inventory by using assets</p>
            <div class="form-group">
              <select class="form-control m-input" [(ngModel)]="product.is_tracked" name="is_tracked">
                <option [value]="item.value" *ngFor="let item of tr_status">{{item.name}}</option>
              </select>
            </div>
          </div> -->

          <div class="col-md-12">
            <h5>Product Tracking</h5>
            <p class="ml-4">You can track details of a specific item in your inventory by using assets
              <ng-template #popContentRange>Tip:To track bulk products by quantity, select 'Track quantity', or to track specific items/serialized products, create a unique asset ID for each product.</ng-template>
             &nbsp;&nbsp;&nbsp;&nbsp;<b><span triggers="mouseenter:mouseleave" [ngbPopover]="popContentRange">?</span></b>
            </p>
  
            <div class="form-group">
              <ng-container *ngFor="let item of tr_status">
                <label class="m-radio" >
                  <input type="radio" [(ngModel)]="product.is_tracked" name="is_tracked" [value]="item.value"
                    (click)="onChange($event.target.value)" />
                  {{item.name}}<span></span>
                </label>
                <br>
              </ng-container>
            </div>
          </div>
        </div>

        <div *ngIf="customFieldList.length>0" class="row">
          <div class=" col-md-12">
            <h5>Custom Fields</h5>
            
            <div class="row">
              <div class="col-md-6" *ngFor="let customField of customFieldList">
                <div class="form-group">
                  <label for="">{{ customField.label }}</label>
      
                  <input
                    *ngIf="customField.type == 'textBox'"
                    type="text"
                    class="form-control"
                    name="name1"
                    autocomplete="none"
                    (change)="
                    onChangeCustomFieldData(
                        $event.target.value,
                        customField
                      )
                    "
                  />
      
                  <!-- <select
                    name="howto_hear"
                    class="form-control"
                    *ngIf="customField.field_type == 1"
                    (change)="
                      onChangeCustomFieldData(
                        $event.target.value,
                        customField
                      )
                    "
                  >
                    <option value="null">--Select--</option>
                    <option
                      *ngFor="let opt of customField.field_values.split(';')"
                      [value]="opt"
                      name="name2"
                      >{{ opt }}</option
                    >
                  </select>
      
                  <input 
                  *ngIf="customField.type == 2" 
                  type="file" 
                  class="form-control"
                  name="file"
                  (change)="onUpload($event.target.files[0],customField)" /> -->

                </div>
              </div>
            </div>

          </div>
        </div>



        <div class="form-group m-form__group">
          <div *ngIf="loader; else button" class="m-loader m-loader--brand" style="width: 30px; display: inline-block;">
          </div>
          <ng-template #button>
            <button type="submit" class="btn btn-brand" [disabled]="!form.form.valid">
              <i class="fa fa-save"></i> <span style="padding-left:10px;">Save</span>
            </button>
          </ng-template>
        </div>


      
      </form>
      <!--end::Form-->
    </div>
  </div>


</div>
