import {
  Component,
  AfterViewInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef
} from "@angular/core";
import { Router, ActivatedRoute } from "@angular/router";
import { Supplier, Descriptuion } from "../product-models/inventory.models";
import { InventoryService } from "../inventory-serveice/inventory.service";
import { AlertService } from "../../../../modules/alert/alert.service";

declare let $: any;

@Component({
  selector: "admin-add-product",
  templateUrl: "./add-product.component.html",
  styleUrls: ["./add-product.component.css"]
})
export class AddProductComponent implements AfterViewInit {
  suplliers: Supplier[] = [];
  product: Descriptuion = new Descriptuion();
  loader: boolean = false;
  tagsList: any[] = [];
  customFieldList: any[] = [];
  customFieldResult=[];
  varinats: any[] = [];
  showVar: boolean;
  variantIds: number[];
  subs_plan: any;
  currency;
  isProduct: boolean;
  tr_status = [
    { name: "Track quantity only", value: 0 },
    { name: "Track specific assets", value: 1 }
  ];
  @ViewChild("hasCusAlert") alertContainer: ElementRef;

  constructor(
    private router: Router,
    private activeRoute: ActivatedRoute,
    private alertS: AlertService,
    private inventoryS: InventoryService
  ) {
    this.getVariants();
    this.currency = JSON.parse(localStorage.getItem("currency"));
  }

  ngOnInit() {
    const type = this.activeRoute.snapshot.data["type"];
    type === "product" ? (this.isProduct = true) : (this.isProduct = false);
    this.suplliers = this.activeRoute.snapshot.data["supplier"].data;
    if (this.suplliers) {
      this.suplliers = this.suplliers;
    }
    this.getTags();
    this.product.is_tracked = this.tr_status[0].value;
    this.isProduct === true ? (this.product.type = 1) : (this.product.type = 2);
    this.getCustomFieldList();
  }

  ngAfterViewInit() {
    this._description();
  }

  private getVariants() {
    this.inventoryS.getAtrributeSet().subscribe(
      res => {
        this.varinats = res.data.filter(f => f.id !== 1);
      },
      err => console.log(err)
    );
  }

  private _description = () => {
    $(".summernote-description").summernote(this.inventoryS.summarNote());

    $(".summernote-description").on("summernote.blur", () => {
      this.product.description = $(".summernote-description").summernote(
        "code"
      );
    });
  };

  changed(e: any): void {
    if (e.messag) {
      this.alertS.error(this.alertContainer, e.message, true, 3000);
    } else {
      this.product.supplier_id = parseInt(e.id);
    }
  }

  getCustomFieldList() {
    this.inventoryS.getCustomFieldList().subscribe(res => {
      this.customFieldList = res.data;
    });
  }

  getTags() {
    this.inventoryS.getTags().subscribe(res => {
      this.tagsList = res.data;
    });
  }


  onChangeCustomFieldData(value, customField) {
    var customFields = this.customFieldResult.filter(x => x.id == customField.id);

    if (value !== "null" && value != "") {
      var data = {
        id: customField.id,
        name: customField.name,
        label: customField.label,
        type: customField.type,
        field_values: value
      };

      if (customFields.length === 0) {
        this.customFieldResult.push(data);
        // console.log(this.customCheckoutResult)
      } else {
        let index = this.customFieldResult.indexOf(customFields);
        this.customFieldResult.splice(index, 1);

        this.customFieldResult.push(data);
        // console.log(this.customCheckoutResult)
      }
    } else {
      let index = this.customFieldResult.indexOf(customFields);
      this.customFieldResult.splice(index, 1);
    }
  }


  changedTagList(e: any) {
    if (e.messag) {
      this.alertS.error(this.alertContainer, e.message, true, 3000);
    } else {
      this.product.tags = this.inventoryS.formatToNumber(e.id);
    }
  }

  addProduct(f) {
    this.loader = true;
    this.product.status = 2;
    if (this.variantIds && this.variantIds.length > 0) {
      this.product["variant_set"] = this.variantIds;
    }
    // this.product.free_shipping ? this.product.free_shipping = 1 : this.product.free_shipping = 0;

    if(this.customFieldResult.length>0)
    {
      this.product["custom"] = this.customFieldResult;
    }

    this.inventoryS
      .addProduct(this.product)
      .then(res => {
        this.loader = false;
        const showSubscriptionAlert = sessionStorage.getItem(
          "showSubscriptionAlert"
        );
        if (
          showSubscriptionAlert != undefined &&
          showSubscriptionAlert != null &&
          showSubscriptionAlert == "yes"
        ) {
          const subscription_data = JSON.parse(
            sessionStorage.getItem("subscription")
          );
          const user_data = JSON.parse(localStorage.getItem("currentUser"));
          user_data.subscription = subscription_data;
          localStorage.setItem("currentUser", JSON.stringify(user_data));
          $("#SubsAlart").show();
        }
        this.router.navigate(["admin/inventory"]);
      })
      .catch(err => {
        console.log(err.error.result.error);
        this.loader = false;
        this.alertS.error(
          this.alertContainer,
          "Someting wrong! Product has been not added",
          true,
          5000
        );
      });
  }

  // ********************* Variant **********************

  varinatValues(e) {
    this.variantIds = e.filter(f => f.set_id).map(m => m.set_id);
  }

  showVariants() {
    this.showVar = true;
    this.variantIds = [];
  }

  cancelVariants() {
    this.showVar = false;
    this.variantIds = [];
  }
}
