import {RouterModule, Routes} from '@angular/router';
import {NgModule} from '@angular/core';
import { AdminService } from '../../admin.service';
import { InventoryComponent } from './inventory.component';
import { PagesComponent } from '../pages.component';
import { InventoryResolveService } from './inventory-serveice/inventory-resolve.service';
import { ProductListService } from './product-list/product-service/product-list.service';


const routes: Routes = [
    {
        path: '',
        component: PagesComponent,
        children:[
            {path: '',
            component: InventoryComponent,
            canActivate: [AdminService],
            children: [
                {
                    path: '',
                    loadChildren: () => import('./product-list/product-list.module').then(m => m.ProductListModule),
                    resolve: {'supplier': InventoryResolveService},
                    data : { title:'Inventory List'}
                },
                {
                    path: 'dashboard',
                    loadChildren: () => import('./inventory-dashboard/inventory-dashboard.module').then(m => m.InventoryDashboardModule),
                    data : { title:'Inventory dashboard'}
                },
                {
                    path: 'add',
                    loadChildren: () => import('./add-product/add-product.module').then(m => m.AddProductModule),
                    resolve: {'supplier': InventoryResolveService},
                    data : { type : 'product',title:'Add new product'}
                },
                {
                    path: 'package/add',
                    loadChildren: () => import('./add-product/add-product.module').then(m => m.AddProductModule),
                    resolve: {'supplier': InventoryResolveService},
                    data : { type : 'package',title:'Add new package'}
                },
                {
                    path: 'product-reservation',
                    loadChildren: () => import('./product-reservation/product-reservation.module').then(m => m.ProductReservationModule),
                    data : { title:'reservation'}
                },
                {
                    path: 'import-inventory',
                    loadChildren: () => import('./import-file/import-file.module').then(m => m.ImportFileModule),
                    data : { title:'Import inventory'}
                },
                {
                    path: ':product_id/details',
                    loadChildren: () => import('./product-details/product-details.module').then(m => m.ProductDetailsModule),
                    data : { title:'Product details'}
                },
                {
                    path: 'package/:product_id/details',
                    loadChildren: () => import('./product-details/product-details.module').then(m => m.ProductDetailsModule),
                    data : { title:'Package details'}
                },
                {
                    path: 'product-quantity-list',
                    loadChildren: () => import('./product-qty-list/product-list.module').then(m => m.ProductListModule),
                    resolve: {'supplier': InventoryResolveService},
                    data : { title:'Update product quantity'}
                },
                {
                    path: 'product-asset',
                    resolve: {'supplier': InventoryResolveService},
                    loadChildren: () => import('./product-asset/product-asset.module').then(m => m.ProductAssetModule),
                    data : { title:'Product asset'}
                },
                {
                    path: 'advanced-product-search',
                    resolve: {'supplier': InventoryResolveService},
                    loadChildren: () => import('./advanced-product-search/advanced-product-search.module').then(m => m.AdvancedProductSearchModule)
                },
                {
                    path: "custom-fields",
                    loadChildren:
                        () => import('./custom-fields/custom-fields.module').then(m => m.CustomFieldsModule),
                    resolve: { list: InventoryResolveService },
                    data : { title:'CustomFields'}
                },
                {
                    path: "exact-date-time",
                    loadChildren:
                        () => import('./exact-date-time/exact-date-time.module').then(m => m.ExactDateTimeModule),
                    data : { title:'Exact Datetime'}
                },
                {
                    path: "create",
                    loadChildren: () => import('./add-abbreviated-product/add-abbreviated-product.module').then(m => m.AddAbbreviatedProductModule),
                    resolve: {'supplier': InventoryResolveService},
                    data : { title:'Add new product'}
                },
                {
                    path: "books",
                    loadChildren: () => import('./books-inventory/books-inventory.module').then(m => m.BooksInventoryModule)
                }
            ]}
        ]
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})

export class InventoryRoutingModule {
}

