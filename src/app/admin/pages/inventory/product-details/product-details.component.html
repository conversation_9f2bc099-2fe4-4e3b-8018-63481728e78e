<!-- BEGIN: Subheader -->
<div class="m-subheader">
    <div class="d-flex align-items-center">
      <div class="mr-auto">
        <h3 class="m-subheader__title m-subheader__title--separator">
          Inventory
        </h3>
        <ul class="m-subheader__breadcrumbs m-nav m-nav--inline">
          <li class="m-nav__item m-nav__item--home">
            <a routerLink="/admin/dashboard" class="m-nav__link m-nav__link--icon">
              <i class="m-nav__link-icon la la-home"></i>
            </a>
          </li>
          <li class="m-nav__separator">
            <i class="fa fa-angle-right"></i>
          </li>
          <li class="m-nav__item">
            <a routerLink="/admin/inventory" class="m-nav__link">
              <span class="m-nav__link-text">
                Product List
              </span>
            </a>
          </li>
          <li class="m-nav__separator" style="padding-left: 10px">
            <i class="fa fa-angle-right"></i>
          </li>
          <li class="m-nav__item">
            <a class="m-nav__link">
              <span class="m-nav__link-text">
                Product Details
              </span>
            </a>
          </li>
        </ul>
      </div>
    </div>
</div>
  <!-- END: Subheader -->

<div class="m-content animated fadeIn">
	<div class="row">
		<div class="col-xl-3 col-sm-4">
			<div class="m-portlet m-portlet--full-height">
				<div class="m-portlet__body">
					<div class="m-card-profile">
						<div class="m-card-profile__details">
							<img *ngIf="pro_image; else alter" src="{{img_url + store_id + '/' + product_id + '/' + pro_image}}" onError="this.src='./assets/img/home/<USER>';" alt="No Image" style="width:100%"/>
							<ng-template #alter>
								<img src="./assets/img/home/<USER>" alt="" style="width:100%"/>
							</ng-template>
						</div>
          </div>
          
					<ul class="m-nav m-nav--hover-bg m-portlet-fit--sides">
						<li class="m-nav__separator m-nav__separator--fit"></li>
						<li class="m-nav__section m--hide">
							<span class="m-nav__section-text">
								Section
							</span>
						</li>
						<li class="m-nav__item">
							<a [routerLink]="'./edit/description'" class="m-nav__link">
								<i class="m-nav__link-icon flaticon-profile-1"></i>
								<span class="m-nav__link-title">
									<span class="m-nav__link-wrap">
										<span class="m-nav__link-text">
											Description
										</span>
									</span>
								</span>
							</a>
						</li>
						<li class="m-nav__item">
							<a class="m-nav__link" [routerLink]="'./edit/pricing'">
								<i class="m-nav__link-icon flaticon-share"></i>
								<span class="m-nav__link-text">
									Pricing
								</span>
							</a>
						</li>
						<li class="m-nav__item">
							<a class="m-nav__link" [routerLink]="'./edit/categories'">
								<i class="m-nav__link-icon flaticon-chat-1"></i>
								<span class="m-nav__link-text">
									Category
								</span>
							</a>
						</li>
						<li class="m-nav__item">
							<a class="m-nav__link" [routerLink]="'./edit/images'">
								<i class="m-nav__link-icon flaticon-calendar"></i>
								<span class="m-nav__link-text">
									Images
                </span>
                <span class="m-nav__link-badge">
									<span class="m-badge m-badge--success">
										{{summary.total_image}}
									</span>
								</span>
							</a>
						</li>
						<li class="m-nav__item" *ngIf="!isPackage">
							<a class="m-nav__link" [routerLink]="'./edit/variant'">
								<i class="m-nav__link-icon flaticon-graphic-2"></i>
								<span class="m-nav__link-text">
									Variants
								</span>
							</a>
						</li>
						<li class="m-nav__item">
							<a class="m-nav__link" [routerLink]="'./edit/related-product'">
								<i class="m-nav__link-icon flaticon-time-3"></i>
								<span class="m-nav__link-text">
									Related Product
								</span>
							</a>
						</li>
					</ul>
					<div class="m-portlet__body-separator"></div>
					<div class="m-widget1 m-widget1--paddingless">
						<div class="m-widget1__item">
							<div class="row m-row--no-padding align-items-center">
								<div class="col">
									<h3 class="m-widget1__title">
										Stocks
									</h3>
								</div>
								<div class="col m--align-right">
									<span class="m-widget1__number m--font-brand">
										{{summary.total_stock}}
									</span>
								</div>
							</div>
						</div>
						<div class="m-widget1__item">
							<div class="row m-row--no-padding align-items-center">
								<div class="col">
									<h3 class="m-widget1__title">
										Total Ordered
									</h3>
								</div>
								<div class="col m--align-right">
									<span class="m-widget1__number m--font-danger">
										{{summary.total_order}}
									</span>
								</div>
							</div>
						</div>
						<div class="m-widget1__item">
							<div class="row m-row--no-padding align-items-center">
								<div class="col">
									<h3 class="m-widget1__title">
										Total in Cart
									</h3>
								</div>
								<div class="col m--align-right">
									<span class="m-widget1__number m--font-success">
										{{summary.total_cart}}
									</span>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="col-xl-9 col-sm-8 details-section">
			<product-details-description [proId]="product_id"></product-details-description>
			<app-package-product-list *ngIf="isPackage" [proId]="product_id"></app-package-product-list>
      <product-details-pricing [proId]="product_id"></product-details-pricing>
   
      <div class="row">
        <div class="col-md-6">
          <product-details-related-product [proId]="product_id"></product-details-related-product>
        </div>
        <div class="col-md-6" *ngIf="!isPackage">
          <product-details-attribute [proId]="product_id"></product-details-attribute>
        </div>
      </div>

      <product-details-category [proId]="product_id"></product-details-category>



		</div>
	</div>
</div>



<!-- sidebar -->

<div class="native-routing animated">
	<button class="close-sidebar btn btn-sm btn-brand">
		<i class="fa fa-chevron-right"></i>
	</button>
	<span class="close-sidebar-upper">
    <i class="la la-close"></i>
  </span>
    <div class="native-routing-container">
        <router-outlet></router-outlet>
    </div>
</div>
<!-- <div class="backdrop animated"></div> -->
