<div class="m-portlet m-portlet--mobile related-product">
  <div class="m-portlet__head">
    <div class="m-portlet__head-caption">
      <div class="m-portlet__head-title">
        <h3 class="m-portlet__head-text">
          <span class="colorPurpel">Variants</span> <small>( <div class="colorPlate"></div> Default Variant )</small>
        </h3>
        <div class="edit-btn">
          <a id="m_product_sidebar_toggle" [routerLink]="'./edit/variant'"
          class="btn btn-secondary m-btn m-btn--icon m-btn--icon-only m-btn--pill m-btn--air">
            <i class="la la-edit"></i>
          </a>
        </div>
      </div>
    </div>
  </div>

    <div class="m-portlet__body pricing" style="position: relative;">
      <h5 *ngIf="!unassign">{{attributeChain}}</h5>
      <div *ngIf="loader" class="table-load m-loader m-loader--brand" style="z-index: 999;"></div>
      <div class="table-responsive">
        <table class="table attr-table">
            <thead>
                <tr>
                    <ng-template ngFor let-a let-i='index' [ngForOf]="filteredAttributes">
                        <th *ngIf="!unassign">
                            {{a?.name}}
                        </th>
                    </ng-template>
                    <th>
                        Barcode
                    </th>
                    <th style="min-width: 125px;">
                        <div class="row">
                            <div class="col-8">Location</div>
                            <div class="col-4"><b>QTY</b></div>
                        </div>
                    </th>
                </tr>
            </thead>
            <tbody *ngIf="attributeList?.length>0; else noData">
                <tr *ngFor="let list of attributeList; let id='index';" [ngClass]="{'bgColor': list?.default}">
                    <ng-template ngFor let-a let-i='index' [ngForOf]="filteredAttributes">
                        <td *ngIf="!unassign">
                            {{getArrtibuteSet(a?.id, list?.variant_set)}}
                        </td>
                    </ng-template>
                    <td>{{list?.barcode}}</td>
                    <td>
                        <div class="row" *ngFor="let loc of list?.location">
                            <div class="col-8">{{loc?.name}}:</div>
                            <div class="col-4"><b>{{ loc?.quantity}}</b></div>
                        </div>
                    </td>
                </tr>
            </tbody>
            <ng-template #noData>
                <tbody>
                <tr *ngIf="!loader">
                    <td [attr.colspan]="filteredAttributes.length + 2">
                    <h5 class="text-center">
                        No Variant Found
                    </h5>
                    </td>
                </tr>
                </tbody>
            </ng-template>
        </table>
      </div>
    </div>

    
</div>
