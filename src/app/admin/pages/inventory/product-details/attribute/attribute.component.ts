import { Component, OnInit, Input, OnDestroy } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { InventoryService } from '../../inventory-serveice/inventory.service';
import { FORMATE_ATTR_VALUE } from '../../../../../globals/_classes/functions';
import { Subscription } from 'rxjs';
import { AttributeSet } from '../../product-sidebar/product_models';
import { HttpInspectorService } from '../../../../../modules/http-with-injector/http-inspector.service';

@Component({
  selector: 'product-details-attribute',
  templateUrl: './attribute.component.html',
  styleUrls: ['../product-details.component.css']
})
export class AttributeComponent implements OnInit, OnDestroy {

  attributeList = [];
  subcription: Subscription;
  attributeChain: string;
  attribute_id_list:any[] = [];
  attributeSet: AttributeSet[] = [];
  filteredAttributes: AttributeSet[] = [];
  loader: boolean;

  @Input('proId') pro_id: number;

  constructor(
    private inventoryS: InventoryService
  ) {}

  ngOnInit() {
    this.getAttributeSet();
    this.subcription = this.inventoryS.reloadDetails.subscribe(
      val => {
        if (val.reload) {
          this.pro_id = val.id;
          switch (val.from) {
            case 'ATTRIBUTE':
              this.attributeList = val.data.data;
              this.attributeChain = val.data.chain;
              this.filteredAttributes = val.data.filter;
              break;
            case 'ALL':
              this.getAttributeSet();
              break;
          }
        }
      }
    );
  }

  ngOnDestroy() {
    this.subcription.unsubscribe();
  }

  get unassign() {
    return this.attribute_id_list.includes(1);
  }

  formatAttributeSet() {
    this.filteredAttributes = [];
    for(let a of this.attribute_id_list) {
      this.filteredAttributes.push(
        this.attributeSet.find((f) => {
          return f.id == a;
        })
      );
    }
    this.attributeChain = this.filteredAttributes.map((a) => {
      return a.name;
    }).join(' -> ');
  }

  getChain() {
    this.inventoryS.getAttributeChain(this.pro_id).subscribe(
      res => {
        this.attribute_id_list = res.data;
        if(this.attribute_id_list.length > 0) {
          this.formatAttributeSet();
          this.getAttributeList();
        }
      }
    );
  }

  getArrtibuteSet(id, list) {
    return list.find((f) => id==f.variant_set_id).name;
  }

  getAttributeSet(){
    this.loader = true;
    this.inventoryS.getAtrributeSet()
    .subscribe(
      res => {
        this.attributeSet = res.data;
        this.getChain();
      },
      err => {
        console.log(err);
        this.attributeSet = [];
      }
    );
  }

  getAttributeList() {
    this.inventoryS.getAttributeList(this.pro_id).subscribe(
      res => {
        this.loader = false;
        if(res.status == 'OK') {
          this.attributeList = res.result.data;
        } else {
          this.attributeList = [];
        }
      },
      err => {
        this.loader = false;
        console.log(err);
        this.attributeList = [];
      }
    );
  }


}
