import { Component, OnInit, Input, OnDestroy } from '@angular/core';
import { ProductPrice } from '../../product-sidebar/product_models';
import { InventoryService } from '../../inventory-serveice/inventory.service';
import { Subscription } from 'rxjs';
import { singleOrNot } from '../../../../../globals/_classes/functions';


@Component({
  selector: 'product-details-pricing',
  templateUrl: './pricing.component.html',
  styleUrls: ['../product-details.component.css']
})
export class PricingComponent implements OnInit, OnDestroy {

  priceList= [];
  prices = {
    base:null,
    hourly:[],
    daily:[],
    weekly:[],
    monthly:[],
    fixed:null
  }
  subcription: Subscription;
  attributeList: any[] = [];
  selectedId = {
    variants_products_id: 0,
    product_id: 0
  };
  listLoader: boolean;

  @Input('proId') pro_id: number;

  constructor(
    private inventoryS: InventoryService
  ) {}

  ngOnInit() {
    this.getAttributeList();
    this.subcription = this.inventoryS.reloadDetails.subscribe(
      val => {
        if (val.reload) {
          this.pro_id = val.id;
          switch (val.from) {
            case 'PRICE':
              this.selectedId = val.data.selectedId;
             // this.priceList = val.data.price;
             console.log(val.data.price)
              const prices =  val.data.price
              this.prices.base = prices.base;
              this.prices.daily = prices.daily?prices.daily:[];
              this.prices.hourly = prices.hourly?prices.hourly:[];
              this.prices.weekly = prices.weekly?prices.weekly:[];
              this.prices.monthly = prices.monthly?prices.monthly:[];
              break;
            case 'ALL':
              this.getAttributeList();
              break;
          }
        }
      }
    );
  }

  ngOnDestroy() {
    this.subcription.unsubscribe();
  }

  single(v) {
    return singleOrNot(v)
  }

  get unassign() {
    return this.attributeList.map( m => m['set_id']).includes(1);
  }

  getPriceList() {
    this.inventoryS.getPriceList(this.pro_id, this.selectedId.variants_products_id).subscribe(
      res => {
        this.listLoader = false;
        const prices = res.data[0];
       
        if(res.data && prices) {
        //  this.priceList = res.data;
         this.prices['base'] = prices.base;
         this.prices.daily = prices.daily?prices.daily:[];
         this.prices.hourly = prices.hourly?prices.hourly:[];
         this.prices.weekly = prices.weekly?prices.weekly:[];
         this.prices.monthly = prices.monthly?prices.monthly:[];
         this.prices.fixed = prices.fixed?prices.fixed:null;
         console.log( this.prices)
        }
       // else  {this.priceList = [];}
      },
      err=> {this.listLoader = false;console.log(err);}
    );
  }

  getAttributeList() {
    this.listLoader = true;
    this.inventoryS.getAttributeList(this.pro_id).subscribe(
      res => {
        if(res.status == 'OK' && res.result.data.length > 0) {
          this.formateAttribute(res.result.data);
        } else {
          this.attributeList = [];
        }
      },
      err => {
        console.log(err);
        this.attributeList = [];
      }
    );
  }

  formateAttribute(data) {
    this.attributeList = data.map((m) => {
      m['id'] = m.ids[m.ids.length - 1];
      m['chain'] = m.variant_set.map((a) => a.variant_set_name + '(' + a.name + ')').join(', ');
      m['set_id'] = m.variant_set.length > 0 ? m.variant_set[0].variant_set_id : null;
      return m;
    });
    if(this.attributeList.length > 0) {
      let attr =  this.attributeList.find( f => {
        return f.default;
      });
      this.selectedId.variants_products_id = attr ? attr.id : this.attributeList[0].id;
      this.selectedId.product_id = this.pro_id;
    }
    this.getPriceList();
  }

  attributeChange() {
    this.selectedId.product_id = this.pro_id;
    this.listLoader = true;
    this.getPriceList();
  }

}
