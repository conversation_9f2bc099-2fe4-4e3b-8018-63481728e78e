import { Component, AfterViewInit, HostListener, ViewChild, OnInit, OnDestroy } from '@angular/core';
import { offCanvas } from '../../../layouts/offCanvas';
import { Router, ActivatedRoute, NavigationEnd } from '@angular/router';
import { InventoryService } from '../inventory-serveice/inventory.service';
import { SidebarService } from '../../sidebar-service/sidebar.service';
import { product_image } from '../../../../globals/endPoint/config';
import { Subscription } from 'rxjs';
import { GET_STORE_ID } from '../../../../globals/_classes/functions';
import {Location} from '@angular/common';
import { map } from 'rxjs/operators';


declare let $: any;

@Component({
  selector: 'app-product-details',
  templateUrl: './product-details.component.html',
  styleUrls: ['./product-details.component.css']
})
export class ProductDetailsComponent implements OnInit, AfterViewInit, OnDestroy {
  sub :Subscription;
  isPackage = false;
  product_id: number;
  sidebarOpen: boolean;
  pro_image: string;
  summary = {
    total_image: 0,
    total_stock: 0,
    total_cart: 0,
    total_order: 0
  };
  img_url: string = product_image;
  subcription: Subscription[] = [];
  store_id = GET_STORE_ID();

  @HostListener('window:resize', ['$event'])
  onResize(event) {
    if(this.sidebarOpen) this.sidebarS.openSidebar();
  }

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private inventoryS: InventoryService,
    private sidebarS: SidebarService,
    private location: Location
  ) {
    const events = this.router.events;
    this.subcription[0] = events
      .pipe(
        map((e) => {
          const editRoute = this.router.createUrlTree(['edit'], { relativeTo: this.route });
          return this.router.isActive(editRoute, false);
        })
      ).subscribe(active => {
        if (active) this.sidebarS.openSidebar();
        else this.sidebarS.removeSidebar();
        this.sidebarOpen = active;
      });
    this.subcription[2] = this.route.paramMap.subscribe(
      val => {
        this.product_id = parseInt(val.get('product_id'));
      }
    );
  }


  ngOnInit() {
    this.checkRoute();
    this.getSummary();
    this.subcription[1] = this.inventoryS.reloadDetails.subscribe(
      val => {
        if (val.reload) {
          this.product_id = val.id;
          if (val.from == 'SUMMARY' || val.from == 'ATTRIBUTE' || val.from == 'ALL') {
            this.getSummary();
          }
        }
      }
    );
    this.location.subscribe(
      x => {
        const url = x.url.match(/\d+/g);
        const id = url ? url.map(Number) : null;
        if (id && this.product_id != id[0]) {
          this.inventoryS.reload({reload: true, id: id[0], from: 'ALL'});
        }
      }
    );
  }

  ngAfterViewInit() {
    //window.scrollTo(0, 0);
    this.closeEdit();
  }

  ngOnDestroy() {
    for(let sub of this.subcription) {
      sub.unsubscribe();
    }
  }

  private closeEdit() {
    $('.close-sidebar').click((e) => {
      e.preventDefault();
      this.close(); 
    });
    $('.close-sidebar-upper').click((e)=>{
      e.preventDefault();
      this.close();
    });
  }

  private close() {
    this.sidebarOpen = false;
    let loc = `admin/inventory/${this.product_id}/details`;
    this.sidebarS.removeSidebar();
    this.router.navigate([loc]);
  }


  getSummary() {
    this.inventoryS.getDetailsPageImage(this.product_id).subscribe(
      res => {
        this.pro_image = res.data ? res.data.image_large : null;
      }, err => console.log(err)
    );

    this.inventoryS.getDetailsPageSummary(this.product_id).subscribe(
      res => {
        this.summary = res.data ? this.chcekNull(res.data) : this.summary;
      }, err => console.log(err)
    );
  }
  

  private chcekNull(data) {
    for (let d in data) {
      if(!data[d]) {
        data[d] = 0;
      }
    }
    return data;
  }
  

  


  private checkRoute() {
    let url = this.router.url;
    this.sub = this.router.events.subscribe(e => {
      if (e instanceof NavigationEnd) {
        url = String(e.urlAfterRedirects);
      }
    });
    if (url.includes("/package/")) {
      this.isPackage = true;
    } else {
      this.isPackage = false;
    }
  }

}
