import { Component, OnInit, Input, OnDestroy } from '@angular/core';
import { InventoryService } from '../../inventory-serveice/inventory.service';
import { Descriptuion } from '../../product-models/inventory.models';
import { HttpInspectorService } from '../../../../../modules/http-with-injector/http-inspector.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'product-details-description',
  templateUrl: './description.component.html',
  styleUrls: ['../product-details.component.css']
})
export class DescriptionComponent implements OnInit, OnDestroy {
  tags: string;
  description:Descriptuion = new Descriptuion();
  
  subcription: Subscription;
  
  @Input('proId') pro_id: number;

  constructor(
    private inventoryS: InventoryService
  ) {}

  ngOnInit() {
    this.getProDescription();
    this.subcription = this.inventoryS.reloadDetails.subscribe(
      val => {
        if (val.reload) {
          this.pro_id = val.id;
          switch (val.from) {
            case 'DESCRIPTION':
              this.getDescription(val.data);
              break;
            case 'ALL':
              this.getProDescription();
              break;
          }
        }
      }
    );
  }

  ngOnDestroy() {
    this.subcription.unsubscribe();
  }

  getProDescription(){
    this.inventoryS.getProductDescription(this.pro_id).subscribe(
      res=>{
        this.getDescription(res.data);
      },
      err=> console.log(err)
    );
  }

  getDescription(data) {
    this.description = data;
    this.tags = this.description.tags.map(m => m.name).join(', ');
  }

  formatDescription(data) {
    $('#description-div').html(data);
  }

  getStatus(data){
    if(data) {
      switch (data) {
        case 1:
          return 'Active';
        case 2:
          return 'Inactive';
        case 3:
          return 'Out of Stock';
        case 4:
          return 'Faulty';
        case 5:
          return 'Delete';
      }
    } 
    return '';
  }

  checkStatus(s) {
    if(s) {
      switch (s) {
        case 1:
          return 'm-badge--success';
        case 2:
          return 'm-badge--brand';
        case 3:
          return 'm-badge--primary';
        case 4:
          return 'm-badge--warning';
        case 5:
          return 'm-badge--danger';
      }
    } 
    return '';
  }


}
