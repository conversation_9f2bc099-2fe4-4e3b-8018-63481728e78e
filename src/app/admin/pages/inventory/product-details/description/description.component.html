<div class="m-portlet m-portlet--mobile" style="position: relative;">
  <div class="m-portlet__head">
    <div class="m-portlet__head-caption">
      <div class="m-portlet__head-title">
        <h3 class="m-portlet__head-text colorPurpel">
          Details
        </h3>
        <div class="edit-btn">
          <a id="m_product_sidebar_toggle" [routerLink]="'./edit/description'" 
          class="btn btn-secondary m-btn m-btn--icon m-btn--icon-only m-btn--pill m-btn--air">
            <i class="la la-edit"></i>
          </a>
        </div>
      </div>
    </div>
  </div>


    <div class="m-portlet__body">
      <div class="row">
        <div class="col-md-6 col-div">
          <div class="description-fields">
            <p><span class="description-field-title">Title: </span> <span>{{description.name}}</span></p>
            
            <p><span class="description-field-title">Tags: </span> <span>{{tags}}</span></p>
            <p><span class="description-field-title">Keyword: </span> <span>{{description.keyword}}</span></p>
            
            <p><span class="description-field-title">Merchant/Supplier Name: </span> <span>{{description.supplier_name}}</span></p>
            
            <p><span class="description-field-title">Merchant Id: </span> <span>{{description.supply_id}}</span></p>
            
            <p><span class="description-field-title">Product Status: </span> <span class="m-badge m-badge--wide" [ngClass]="checkStatus(description.status)">{{getStatus(description.status)}}</span></p>
            <p><span class="description-field-title">Driving License: </span> <span>{{description.driving_license ? 'Required' : 'Not Required'}}</span></p>
          </div>
        </div>
        <div class="col-md-6 col-div">
          <div class="description-fields">
            <div>
              <p class="description-field-title">Description</p>
              <div id="description-div">{{formatDescription(description.description)}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
 
</div>