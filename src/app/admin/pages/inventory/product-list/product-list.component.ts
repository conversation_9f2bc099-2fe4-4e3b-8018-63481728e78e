import { ExportInventoryComponent } from './export-inventory/export-inventory.component';
import {
  Component,
  OnInit,
  ViewEncapsulation,
  AfterViewInit,
  HostListener,
  ViewChild,
  ElementRef,
  OnDestroy,
  Input,
  OnChanges
} from "@angular/core";
import { EndPoint, product_image } from "../../../../globals/endPoint/config";
import { ProductListData } from "../product-models/inventory.models";
import { Router, ActivatedRoute } from "@angular/router";
import { SidebarService } from "../../sidebar-service/sidebar.service";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { DialogBoxComponent } from "../../../../modules/dialog-box/dialog-box.component";
import { ProductListService } from "./product-service/product-list.service";
import { InventoryService } from "../inventory-serveice/inventory.service";
import { AlertService } from "../../../../modules/alert/alert.service";
import { Helpers } from "../../../../helpers";
import { CartService } from "../../../cart-service/cart.service";
import { Pagi } from "../../../../modules/pagination/pagi.model";
import {
  GET_STORE_ID,
  downloadFile,
  singleOrNot,
  getSubscriptionPlan,
  GET_USER
} from "../../../../globals/_classes/functions";
import { Subscription } from "rxjs";
import { PricingService } from "../../pricing/pricing.service";
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { DialogBoxSubscriptionComponent } from "../../../../modules/dialog-box-subscription/dialog-box-subscription.component";
import { HttpErrorResponse } from '@angular/common/http';
import { AdminCheckoutComponent } from '../../admin-checkout/admin-checkout.component';
import { map } from 'rxjs/operators';

declare let $: any;

@Component({
  selector: "admin-product-list",
  templateUrl: "./product-list.component.html",
  styleUrls: ["./product-list.component.css"],
  encapsulation: ViewEncapsulation.None
})
export class ProductListComponent extends AdminCheckoutComponent implements OnInit, AfterViewInit, OnDestroy {
  productList: ProductListData[] = [];
  sidebarOpen: boolean;
  filter: string;
  selectedId = null;
  search: string;
  load: boolean;
  loader: boolean;
  end = EndPoint + "products/export";
  pagi: Pagi = new Pagi();
  image = product_image + GET_STORE_ID();
  sub: Subscription[] = [];
  goTop: boolean;
  reloadFilter: boolean;
  pro_id: any;
  copyDone: boolean;
  subs_plan: any;
  abc = true;
  totalItems = 0;
  productStatue;
  availableProdutsList = [];

  @ViewChild("hasCusAlert") alertContainer: ElementRef;
  @ViewChild("searchPro", {static: true}) searchPro;

  constructor(
    public router: Router,
    public alertS: AlertService,
    public route: ActivatedRoute,
    public productS: ProductListService,
    public inventoryS: InventoryService,
    public sidebarS: SidebarService,
    public cartS: CartService,
    public priceS: PricingService,
    public modalService: NgbModal
  ) {
    super(cartS, sidebarS);
    this.checkRoute();
    this.subs_plan = getSubscriptionPlan();
  }

  @HostListener("window:resize", ["$event"])
  onResize(event) {
    if (this.sidebarOpen) {
      this.openSidbarWithSize();
    }
  }

  ngOnInit() {
    //window.scrollTo(0, 0);
    this.sub[0] = this.route.queryParamMap.subscribe(val => {
      const path = this.route.snapshot["_routerState"].url;
      if (!path.includes("edit")) {
        this.pagi.page = val.get("page")
          ? parseInt(val.get("page"))
          : this.pagi.page
          ? this.pagi.page
          : 1;
        this.pagi.limit = val.get("limit")
          ? parseInt(val.get("limit"))
          : this.pagi.limit
          ? this.pagi.limit
          : 20;
        this.filter = val.get("param")
          ? val.get("param")
          : this.filter
          ? this.filter
          : "";
        this.search = this.productS.formatSearch(this.filter);
        this.getProductList(this.pagi.page, this.pagi.limit, this.filter);
      } else {
        if (!this.pagi.page) {
          this.formatPage();
          this.search = this.productS.formatSearch(this.filter);
          this.getProductList(this.pagi.page, this.pagi.limit, this.filter);
        }
      }

      if(this.search!=null && this.search.includes('avd_search=true'))
      {
        this.search=null;
      }
      
    });
    this.sub[1] = this.cartS.reloadInventory.subscribe(val => {
      if (val) {
        this.getProductList(this.pagi.page, this.pagi.limit, this.filter);
      }
    });
  }

  ngAfterViewInit() {
    this.closeEdit();
  }

  ngOnDestroy() {
    for (let s of this.sub) {
      s.unsubscribe();
    }
  }

  copyUrl(copyText) {
    copyText.select();
    document.execCommand("copy");
    this.copyDone = true;
    setTimeout(() => {
      this.copyDone = false;
    }, 2000);
  }

  single(v) {
    return singleOrNot(v);
  }

  makeQuryparam(param) {
    this.router.navigate(["./"], {
      relativeTo: this.route,
      queryParams: param
    });
  }

  checkRoute() {
    const event = this.router.events;
    event
      .pipe(
        map(e => {
          const editRoute = this.router.createUrlTree(["edit"], {
            relativeTo: this.route
          });
          return this.router.isActive(editRoute, false);
        })
      )
      .subscribe(active => {
        if (active) {
          this.openSidbarWithSize();
        } else {
          this.sidebarS.removeSidebar();
        }
        this.sidebarOpen = active;
      });
  }

  private openSidbarWithSize() {
    if (this.route.snapshot["_routerState"].url.includes("calendar")) {
      // let w =
      //   $(".global-sidebar-wrapper").width() +
      //   ($(window).width() > 992 ? 25 : 0) +
      //   "px";
      // this.sidebarS.openSidebar(w);
    } else {
      // let w =
      //   $(".global-sidebar-wrapper").width() +
      //   ($(window).width() > 992 ? 25 : 0) +
      //   "px";
      // this.sidebarS.openSidebar();
    }
  }

  getProductList(p, l, q) {
    this.loader = true;
    q = this.pro_id ? q + "&product_id=" + this.pro_id : q;
    this.inventoryS.getAllProduct(p, l, q).subscribe(
      res => {
        if (this.goTop) {
          //window.scrollTo(0, 0);
        }
        this.loader = false;
        if (this.searchPro) {
          this.searchPro.loader = false;
        }
        if (res.status == "OK") {
          if (this.pro_id) {
            const index = this.productList.findIndex(f => f.id === this.pro_id);
            if (index > -1 && res.result.data[0]) {
              const editIndex = res.result.data.findIndex(
                f => f.id === this.pro_id
              );
              this.productList[index] = res.result.data[editIndex];
            }
            this.pro_id = null;
          } else {
            this.productList = res.result.data;
            this.totalItems = res.result.total_inventory;
            this.pagi.page = res.result.page_no
              ? JSON.parse(res.result.page_no)
              : 1;
            this.pagi.limit = res.result.limit
              ? JSON.parse(res.result.limit)
              : 20;
            this.pagi.total = res.result.total
              ? JSON.parse(res.result.total)
              : 0;
          }
        } else {
          this.error();
        }
        this.pro_id = null;
      },
      err => this.error()
    );
  }

  error() {
    this.alertS.error(
      this.alertContainer,
      "Something wrong!!! Please try again.",
      true,
      3000
    );
    this.productList = [];
    this.loader = false;
    this.searchPro.loader = false;
    this.pro_id = null;
  }

  tarckProduct(index, pro) {
    return pro ? pro.id : null;
  }

  checkAll(e) {
    if (e.target.checked) {
      this.checkUncheck(true);
      this.selectedId = this.productList
        .map(m => {
          return m.id;
        })
        .join(",");
    } else {
      this.checkUncheck(false);
      this.selectedId = null;
    }
  }

  checkUncheck(d) {
    this.productList = this.productList.map(m => {
      m["check"] = d;
      return m;
    });
  }

  checkOne(e, i) {
    this.productList[i]["check"] = e.target.checked;
    this.selectedId = this.productList
      .filter(f => {
        return f["check"];
      })
      .map(m => {
        return m.id;
      })
      .join(",");
  }

  reloadTable(e) {
    this.pagi.page = e.page;
    const obj = {
      page: this.pagi.page,
      limit: this.pagi.limit,
      param: this.filter
    };

    if (this.pagi.limit != e.limit) {
      this.pagi.page =
        Math.ceil(this.pagi.total / e.limit) <= this.pagi.page
          ? Math.ceil(this.pagi.total / e.limit)
          : this.pagi.page;
      this.pagi.limit = e.limit;
      obj.limit = e.limit;
      obj.page = this.pagi.page;
    } else {
      delete obj.limit;
    }

    this.goTop = true;
    if (this.filter) {
      this.makeQuryparam(obj);
    } else {
      delete obj.param;
      this.makeQuryparam(obj);
    }
  }

  loadSearchData(filter) {
    this.filter = filter;
    this.selectedId = null;
    this.pagi.page = 1;
    this.goTop = true;
    if (this.filter) {
      this.makeQuryparam({ page: this.pagi.page, param: this.filter });
    } else {
      this.makeQuryparam({ page: this.pagi.page });
    }
    this.search = null;
  }

  searchList(value) {
    this.searchPro.closeFilter();
    this.selectedId = null;
    this.goTop = true;
    if (value.search) {
      const search = "&search=" + value.search.trim();
      this.filter = search;
    }
    this.pagi.page = 1;
    this.makeQuryparam({ page: this.pagi.page, param: this.filter });
  }

  reset(f) {
    if (this.search) {
      this.filter = "";
      this.pagi.page = 1;
      this.goTop = true;
      this.makeQuryparam({ page: this.pagi.page });
    }
    f.form.reset();
  }

  openCartSidebar(pro) {
    this.cartS.ProductId.emit({ id: pro.id, name: pro.name });
    this.sidebarS.openCartSidebar();
    this.sidebarS.sidebarOpenChange(true);
  }

  gotoProduct(pro) {
    if (pro.image) {
      //window.open(`/product/${pro.uuid}/${pro.url}`);
    } else {
      this.router.navigate([`/admin/inventory/edit/${pro.id}/images`]);
    }
  }

  openSidebar(pro, route) {
    this.pro_id = pro.id;
    sessionStorage.setItem(
      "proInfo",
      JSON.stringify({
        page: this.pagi.page,
        limit: this.pagi.limit,
        filter: this.filter
      })
    );

      this.router.navigate([`edit/${pro.id}/${route}`], {
        relativeTo: this.route,
        queryParams:{type_id:pro.type}
      });
   
  }

  checkStatus(s) {
    return this.productS.checkStatus(s);
  }

  getStatus(s) {
    return this.productS.getStatus(s);
  }

  private formatPage() {
    const data = JSON.parse(sessionStorage.getItem("proInfo"));
    this.pagi.limit = this.pagi.limit
      ? this.pagi.limit
      : data
      ? data.limit
      : 20;
    this.filter = this.filter ? this.filter : data ? data.filter : "";
    this.pagi.page = this.pagi.page ? this.pagi.page : data ? data.page : 1;
  }

  private closeEdit() {
    $(".close-sidebar").click(e => {
      e.preventDefault();
      this.close();
    });
    $(".close-sidebar-upper").click(e => {
      e.preventDefault();
      this.close();
    });


    $(".close-sidebar-cart").click(e => {
      e.preventDefault();
      this.closeCartSidebar();
    });
    $(".close-sidebar-upper-cart").click(e => {
      e.preventDefault();
      const cartData = sessionStorage.getItem('cartToken');
      if (!cartData) {
        super.ClearFullCart(true);
      }
      this.closeCartSidebar();
    });
  }


  private closeCartSidebar() {
    this.sidebarS.removeCartSidebar();
  }

  private close() {
    this.sidebarOpen = false;
    this.sidebarS.removeSidebar();
    this.router.navigate(["admin/inventory"]);
    this.goTop = false;
    this.formatPage();
    this.reloadFilter = true;
    if (this.filter) {
      this.makeQuryparam({ page: this.pagi.page, param: this.filter });
    } else {
      this.makeQuryparam({ page: this.pagi.page });
    }
    sessionStorage.removeItem("proInfo");
  }

  deleteProductList(prod) {
    this.productStatue=prod.status;
    let message="Are you sure you want to archive?";
    if( this.productStatue==5){
      message="Are you sure you want to Delete?"
    }
    this.deleteDialog(message).then(
      result => {
        if (result) {
         
          this.archiveProduct(prod.id);


        }
      },
      res => {}
    );
  }

  permanentDeleteProductList(id) {
    this.deleteDialog("Are you sure you want to permanently delete?").then(
      result => {
        if (result) {
          this.archiveProduct(id);
        }
      },
      res => {
        console.log(res);
      }
    );
  }

  addProduct(prodType) {
   let redirectUrl='';

    if(prodType=='product')
    {
    redirectUrl="admin/inventory/create"
    }
    else if(prodType=='package'){
      redirectUrl="admin/inventory/package/add"
    }

    this.subs_plan = getSubscriptionPlan();
    if (this.subs_plan == false) {
      sessionStorage.setItem('showSubscriptionAlert',"no")
      this.router.navigateByUrl(redirectUrl);
    } else {
      this.priceS.getSubscriptionInfo().subscribe(res => {
        console.log(res);
        if (res.hasOwnProperty("newInventory") && res.newInventory) {
          sessionStorage.setItem('showSubscriptionAlert',"no")
          this.router.navigateByUrl(redirectUrl);
        } else {
          const modalRef = this.modalService.open(
            DialogBoxSubscriptionComponent,
            {
              centered: true,
              windowClass: "animated fadeIn"
            }
          );
          modalRef.componentInstance.massage =
            "Adding new inventory will put you in a higher tier, do you want to upgrade your plan now or enter trial mode?";
          modalRef.result.then(
            result => {
              if (result) {
                this.router.navigateByUrl("/admin/plans");
              } else {
                sessionStorage.setItem('showSubscriptionAlert',"yes")
                sessionStorage.setItem('subscription',JSON.stringify(res));
                this.router.navigateByUrl(redirectUrl);
              }
            },
            res => {
              console.log(res);
            }
          );
        }
      });
    }
  }

  checkingSubsPlan(message) {
    const modalRef = this.modalService.open(DialogBoxComponent, {
      centered: true,

      windowClass: "animated fadeIn"
    });
    modalRef.componentInstance.massage = message;
    modalRef.result.then(
      result => {
        if (result) {
          this.router.navigateByUrl("/admin/plans");
        }
      },
      res => {
        console.log(res);
      }
    );
  }
  deleteDialog(message) {
    const modalRef = this.modalService.open(DialogBoxComponent, {
      centered: true,
      size: "sm",
      windowClass: "animated fadeIn"
    });
    modalRef.componentInstance.massage = message;
    return modalRef.result;
  }

  openTest() {
    const modalRef = this.modalService.open(ExportInventoryComponent, {
      centered: true,
      size: "sm",
      windowClass: "animated fadeIn"
    });
    modalRef.componentInstance.listSelection = this.selectedId;
    modalRef.result.then(res => {
      if (res) {
        console.log(res);
        this.exportProduct(true, res.export_type, res.from, res.to, res.export_id);
      }
    })
  }

  archiveProduct(id) {
    Helpers.setLoading(true);
    this.inventoryS.deleteProduct(id).then(
      res => {
        Helpers.setLoading(false);
        if (res.status == "OK") {
          this.alertS.success(
            this.alertContainer,
            res.result.message,
            true,
            5000
          );

         if(this.productStatue==5)
         {
          let form="delete";
          this.priceS.getSubscriptionInfo(form).subscribe(res => {
            console.log(res);

            const user_data = JSON.parse(localStorage.getItem("currentUser"));
            user_data.subscription = res;
  
            localStorage.setItem("currentUser", JSON.stringify(user_data));

            if (res.hasOwnProperty("newInventory") && res.newInventory) {
              $("#SubsAlart").hide();
            } else {
              $("#SubsAlart").show();
            }
          });
         }
          

          this.goTop = false;
          this.getProductList(this.pagi.page, this.pagi.limit, this.filter);
        } else {
          this.alertS.error(this.alertContainer, res.result.error, true, 5000);
        }
      },
      err => {
        console.log(err);
        Helpers.setLoading(false);
        this.alertS.error(
          this.alertContainer,
          "Something worng!!! Please try again",
          true,
          5000
        );
      }
    );
  }

  export() {
    if (this.filter || this.selectedId || this.search) {
      // this.exportProduct(true);
      this.openTest();
    } else {
      // this.dilogBox();
      this.openTest();
    }
  }

  dilogBox() {
    this.deleteDialog(
      'Do you want to export "' + this.pagi.total + '" number of products?'
    ).then(
      result => {
        if (result) {
          this.exportProduct(false);
        }
      },
      res => {
        console.log(res);
      }
    );
  }

  exportProduct(has: boolean, export_type?: string, from?: number, to?: number, selected_id?: any) {
    let params = "?export_id=";
    if (has) {
      params += this.selectedId ? this.selectedId : "";
      if (selected_id) {
        params += selected_id;
      }
      if (this.filter) {
        params += this.filter;
      } else if (this.search) {
        params += this.search;
      }
    }
    Helpers.setLoading(true);
    if (export_type) {
      params += '&export_type=' + export_type;
      params  += from ? '&from=' + from : '&from=' + undefined;
      params += to ? '&to=' + to : '&to=' + undefined;
    }
    this.inventoryS.exportProduct(params).then(
      res => {
        Helpers.setLoading(false);
        console.log(res);
        if (res.type === 'application/json') {
          this.alert();
        } else {
          downloadFile(res,this.getDownLoadFileName());
        }
      },
      err => {
        Helpers.setLoading(false)
        console.log(err);
        this.alertS.error(
          this.alertContainer,
          "Something wrong!!! Products have been not exported",
          true,
          3000
        );
      }
    );
  }

  private alert() {
    this.alertS.error(this.alertContainer, 'Export inventory request is large. We will send it in your email.', true, 3000);
  }


  getDownLoadFileName(){
    let storeName=GET_USER().store_name;
    let today = new Date();
    let dd = String(today.getDate()).padStart(2, '0');
    let mm = String(today.getMonth() + 1).padStart(2, '0'); //January is 0!
    let yyyy = today.getFullYear().toString().substr(-2);
    
    return `${storeName}_${yyyy}${mm}${dd}.xls`;
   }

  getRentPrice(price) {
    const config = JSON.parse(localStorage.getItem("currency"));
    let rPrice;
    if (price.fixed) {
      rPrice = price.fixed.price;
    } else {
      for (let c in price) {
        for (let i = 0; i < price[c].length; i++) {
          rPrice = price[c][i].price;
          break;
        }
        break;
      }
    }
    if (config.pre == false && config.post == true) {
      return rPrice !=undefined ? `Starting at ${rPrice} ${config.code}` : "-";
    } else if (config.pre == true && config.post == false) {
      return rPrice !=undefined ? `Starting at ${config.symbol}${rPrice}` : "-";
    } else {
      return rPrice !=undefined
        ? `Starting at ${config.symbol}${rPrice} ${config.code}`
        : "-";
    }
  }
  bulkDelete() {
    if (this.selectedId) {
      const sendData = {
        product_id: this.selectedId.split(",")
      };
      this.deleteDialog("Do you want to delete these products?").then(
        result => {
          if (result) {
            Helpers.setLoading(true);
            this.inventoryS.bulkDeleteProduct(sendData).then(
              res => {
                Helpers.setLoading(false);
                if (res.status == "OK") {
                  this.getProductList(
                    this.pagi.page,
                    this.pagi.limit,
                    this.filter
                  );
                  this.selectedId = null;
                  this.alertS.success(
                    this.alertContainer,
                    "Products have been deleted",
                    true,
                    3000
                  );
                } else {
                  this.alertS.error(
                    this.alertContainer,
                    "Products have been not deleted",
                    true,
                    3000
                  );
                }
              },
              err => {
                console.log(err);
                Helpers.setLoading(false);
                this.alertS.error(
                  this.alertContainer,
                  "Something wrong!!! Products have been not deleted",
                  true,
                  3000
                );
              }
            );
          }
        },
        res => {
          console.log(res);
        }
      );
    }
  }

  getAvailableProducts(e): void {
    Helpers.setLoading(true);
    let availabilityData = e;
    availabilityData['product_ids'] = this.productList.map(product => product.id);
    this.inventoryS.GetAvailableProductList(availabilityData).then(res => {
      Helpers.setLoading(false);
      if (res.status === 'OK') {
        this.availableProdutsList = res.result.data;
        this.productList.map(x => {
          return this.availableProdutsList.map(y => {
            if (x.id === y.product_id && x.type === 1) {
              x.quantity = y.available + ' of ' + y.quantity;
            }
          })
        })
      }
    }).catch(err => {
      Helpers.setLoading(false);
      this.alertS.error(this.alertContainer, 'Something went wrong!', true, 5000);
    });
  }

  onClickExport(){
    this.router.navigateByUrl("/admin/inventory/advanced-product-search");
  }

  resetAvailability(e: boolean): void{
    if (e) {
      this.getProductList(this.pagi.page, this.pagi.limit, this.filter);
    }
  }

  cloneProduct(product) {
    let message="Are you sure you want to clone?";
    this.deleteDialog(message).then(
      result => {
        if (result) {
          this.confirmedCloneProduct(product.id);
        }
      },
      res => {}
    );
  }

  confirmedCloneProduct(id) {
    Helpers.setLoading(true);
    this.inventoryS.cloneProduct(id).then(res => {
      Helpers.setLoading(false);
      if (res.status == "OK") {
        this.alertS.success(
          this.alertContainer,
          res.result ? res.result.message : 'Successfully cloned.',
          true,
          5000
        );
        this.getProductList(this.pagi.page, this.pagi.limit, this.filter);
      } else {
        this.alertS.error(
          this.alertContainer,
          res.result ? res.result.message : 'Something went wrong!',
          true,
          5000
        );
      }
    }).catch((err: HttpErrorResponse) => {
      Helpers.setLoading(false);
      this.alertS.error(
        this.alertContainer,
        err.error.result ? err.error.result.message : err.message,
        true,
        5000
      );
    })
  }
}

// @Component({
//   selector: 'delete-dialog-box',
//   templateUrl: "./export-selection.component.html",
//   styleUrls: ["./product-list.component.css"],
// })

// export class ExportSelcetion extends ProductListComponent {

//   selection: any;
//   from: number;
//   to: number;
//   isFormTo: boolean;
//   @Input() listSelection: any;
//   validationErr: string;

//   constructor(
//     public router: Router,
//     public alertS: AlertService,
//     public route: ActivatedRoute,
//     public productS: ProductListService,
//     public inventoryS: InventoryService,
//     public sidebarS: SidebarService,
//     public cartS: CartService,
//     public priceS: PricingService,
//     public modalService: NgbModal,
//     public activeModal: NgbActiveModal
//   ) {
//     super(router, alertS, route, productS, inventoryS, sidebarS, cartS, priceS, modalService);
//   }

//   choose(e: string): void {
//     if (e === 'index') {
//       this.isFormTo = true;
//     } else {
//       this.isFormTo = false;
//       this.from = null;
//       this.to = null;
//     }
//   }

//   submit(): void {
//     if (this.selection) {
//       if (this.from || this.to) {
//         if (isNaN(this.from) || isNaN(this.to)) {
//           return;
//         }
//       };
//       if (this.selection === 'index') {
//         if (this.isValidIndex(this.from, this.to)) {
//           this.exportProduct(true, this.selection, this.from, this.to, this.listSelection);
//           this.activeModal.close();
//         } else {
//           this.validationErr = "You can only export 1000 or less records at the same time or you have an invalid input.";
//           setTimeout(() => {
//             this.validationErr = "";
//           }, 4000);
//           return;
//         }
//       } else {
        
//         // this.activeModal.dismiss();
//         this.exportProduct(true, this.selection, this.from, this.to, this.listSelection);
//       }
//     } else {
//       this.activeModal.close();
//       return;
//     }
//   }

//   private isValidIndex(from: number, to: number): boolean {
//     if ((to - from <= 1000) && (to - from >= 1)) {
//       return true;
//     } else {
//       return false;
//     }
//   }
// }
