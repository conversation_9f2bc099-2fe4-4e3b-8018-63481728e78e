.new-alert {
    position: fixed;
    top: 215px !important;
    right: 0 !important;
    z-index: 5000;
}

.btn-text {
    margin: 0 10px;
}

.decoration-none:hover {
    text-decoration: none!important;
}

.product-list-right-btn>.first-child-btn {
    margin-right: 30px;
}

.product-list .m-portlet .m-portlet__head {
    /* border-bottom: none; */
}

.product-list .search-panel select option[data-default] {
    color: #888;
}

.product-list .search-panel select option[value=""][disabled] {
    display: none;
}

.product-list .search-panel input {
    text-align: center;
}

.product-list .search-panel select {
    text-align-last: center;
    padding-right: 29px;
}

.product-list .search-panel .form1-search-btn {
    padding-left: 2.6rem!important;
}

.product-list .search-panel .form-group,
.product-list .search-panel .m-form__group {
    padding: 1rem;
}

.product-list .add-list-btn {
    display: table-cell;
    vertical-align: middle;
}

.stocklist-active {
    padding: 25px 0 !important;
    line-height: 38px;
}


/* @media only screen and (max-width: 1000px){
    .product-list .search-panel .form1-search-btn{
        padding-top: 15px;
        padding-bottom: 15px;
    }
}
@media only screen and (max-width: 768px){
    .product-list .search-panel .form1-search-btn{
        padding-top: 0px;
        padding-bottom: 0px;
    } 
} */

.copy-msg {
    position: absolute;
    top: 10px;
    left: 45%;
}

.search-panal-action button {
    margin-right: 10px;
}

.custom-alert {
    position: fixed;
    top: 90px;
    right: 30px;
    z-index: 5000;
}

.dis-none {
    display: none;
}

.dis-block {
    display: block!important;
}

.disabled-cart {
    opacity: 0.5;
    pointer-events: none;
}

.m-portlet__head-title {
    width: 100%;
}

.img-resize-tum {
    max-width: 90px!important;
    height: 90px!important;
    object-fit: contain;
}

.stocklist-body .m-portlet__head-text a {
    padding: 25px 18px;
    color: #575962;
}

.stocklist-active {
    border-bottom: 1px solid #575962;
    color: #575962;
}

.m-portlet .m-portlet__head .m-portlet__head-caption .m-portlet__head-title .m-portlet__head-text {
    width: 78%;
}

.list-search form {
    width: 462px;
    /* float: right; */
}

.list-search button i {
    font-size: 11px !important;
    margin-right: 5px;
}

.list-link {
    padding: 13px 15px;
}

.m-portlet__head-tools {
    display: none !important
}

.list-search {
    margin-top: 15px;
}

@media (min-width:1400px) and (max-width: 1700px) {
    .m-portlet .m-portlet__head .m-portlet__head-caption .m-portlet__head-title .m-portlet__head-text {
        width: 72%;
    }
}

@media (max-width: 1399px) {
    .m-portlet .m-portlet__head .m-portlet__head-caption .m-portlet__head-title .m-portlet__head-text {
        width: 69%;
    }
    .list-search .form-control {
        font-size: 13px;
    }
    .list-search form {
        width: 430px;
    }
    .list-search .col-xl-10 {
        /* flex: 0 0 74.33333%; */
    }
}

@media (max-width: 1280px) {
    .m-portlet .m-portlet__head .m-portlet__head-caption .m-portlet__head-title .m-portlet__head-text {
        width: 66%;
    }
    .list-search form {
        width: 100%;
    }
}

@media (min-width: 992px) and (max-width: 1199px) {
    .stocklist-body .m-portlet__head-text a {
        padding: 15px 10px;
        font-size: 16px;
    }
    .m-portlet .m-portlet__head {
        height: auto;
    }
    .m-portlet .m-portlet__head .m-portlet__head-caption .m-portlet__head-title .m-portlet__head-text {
        width: 75%;
    }
    .list-search {
        padding-left: 0;
    }
    .list-search form {
        float: unset;
        margin-bottom: 10px !important;
        margin-top: 15px !important;
    }
    .add-list-btn .btn {
        margin-bottom: 15px;
    }
    .add-list-btn .btn:last-child {
        margin-bottom: 0;
    }
}

@media (min-width: 768px) and (max-width: 991px) {
    .m-portlet .m-portlet__head .m-portlet__head-caption .m-portlet__head-title .m-portlet__head-text {
        width: 82%;
    }
    .stocklist-body .m-portlet__head-text a {
        padding: 25px 10px;
        color: #575962;
        font-size: 12px;
    }
    .m-portlet .m-portlet__head .m-portlet__head-caption .m-portlet__head-title .m-portlet__head-text {
        width: 69%;
    }
    .list-search form {
        width: 395px;
    }
    .list-search form input {
        font-size: 11px !important;
    }
    .stocklist-body .m-portlet__head-text a {
        padding: 25px 0px;
    }
    .list-search button {
        font-size: 10px !important;
    }
    .add-list-btn .btn {
        font-size: 10px;
    }
}

@media (min-width: 576px) and (max-width: 767px) {
    .stocklist-active {
        padding: 15px 10px;
        font-size: 16px;
    }
    .m-portlet .m-portlet__head {
        height: auto;
    }
    .stocklist-body {
        display: block !important;
        padding-bottom: 15px;
    }
    .m-portlet .m-portlet__head .m-portlet__head-caption .m-portlet__head-title .m-portlet__head-text {
        width: 82%;
        display: table-cell;
    }
    .list-search {
        padding-left: 0;
    }
    .list-search form {
        float: unset;
        margin-bottom: 10px !important;
        margin-top: 15px !important;
        width: 100%;
    }
    .stocklist-packagelist .add-list-btn .btn-brand {
        margin-top: -60px;
    }
    .product-list .add-list-btn {
        display: unset;
    }
    .add-list-btn .btn:last-child {
        margin-bottom: 0;
    }
    .stocklist-packagelist .add-list-btn .btn-brand {
        margin-top: 0;
    }
    .list-link {
        padding: 13px 15px;
    }
}

@media (max-width: 575px) {
    .stocklist-active {
        padding: 10px 10px 20px;
        width: unset;
    }
    .list-search form {
        width: 90%;
        float: unset;
    }
    .list-search form input {
        font-size: 10px;
    }
    .stocklist-packagelist .add-list-btn .btn-brand {
        margin-top: 20px;
    }
}

@media (max-width: 575px) {
    .mr-auto {
        width: 80%
    }
    .first-child-btn {
        margin-bottom: 10px;
    }
    .m-portlet .m-portlet__body {
        padding: 15px 15px;
    }
    .m-portlet .m-portlet__head .m-portlet__head-caption .m-portlet__head-title.m-product-title {
        display: flex;
        padding-bottom: 15px;
    }
    .product-list .add-list-btn {
        padding-top: 10px;
    }
}

@media (max-width: 1199px) {
    .m-portlet .m-portlet__head .m-portlet__head-caption .m-portlet__head-title.m-product-title {
        position: relative;
    }
    .m-portlet .m-portlet__head .m-portlet__head-caption .m-portlet__head-title .m-portlet__head-text {
        width: 100%;
    }
    .stocklist-body .m-portlet__head-text a {
        width: unset !important;
    }
    .list-search form {
        width: 100%;
    }
    .list-search {
        padding-left: 0;
    }
    .list-search form input {
        margin-right: 10px;
    }
    .stocklist-active {
        padding: 43px 0 36px !important;
        line-height: 65px;
    }
    .product-list .add-list-btn {
        display: none !important;
    }
    .m-portlet__head-tools {
        display: table-cell !important;
    }
    .m-portlet .m-portlet__head .m-portlet__head-tools .m-portlet__nav .m-portlet__nav-item .m-portlet__nav-link {
        margin-top: 10px;
        padding: 10px 38px !important;
        cursor: pointer;
        margin-right: -32px;
    }
    .m-nav .m-nav__item>.m-nav__link {
        cursor: pointer;
    }
    .m--font-brand {
        color: #333!important;
        margin-top: 0;
    }
    .m-dropdown.m-dropdown--arrow.m-dropdown--up .m-dropdown__arrow.m-dropdown__arrow--right,
    .m-dropdown.m-dropdown--arrow .m-dropdown__arrow.m-dropdown__arrow--right {
        right: 27px !important;
    }
}

@media (max-width: 992px) {
    .stocklist-active {
        padding: 25px 0 26px !important;
        line-height: 60px;
    }
    .m--font-brand {
        margin-top: 0;
    }
    .list-link {
        padding: 0 15px;
    }
}

@media (max-width: 767px) {
    .m-portlet .m-portlet__head .m-portlet__head-caption .m-portlet__head-title.m-product-title {
        position: relative;
    }
    .m-portlet .m-portlet__head .m-portlet__head-caption .m-portlet__head-title .m-portlet__head-text {
        width: 100%;
    }
    .stocklist-body .m-portlet__head-text a {
        width: unset !important;
    }
    .list-search form {
        width: 100%;
    }
    .list-search {
        padding-left: 0;
    }
    .list-search form input {
        margin-right: 10px;
    }
    .stocklist-active {
        padding: 10px 0 20px !important;
    }
    .product-list .add-list-btn {
        display: none !important;
    }
    .m-portlet__head-tools {
        display: table-cell !important;
        position: absolute;
        top: 20px;
        right: -27px;
        z-index: 9;
    }
    .search-btn-area {
        padding-right: 0;
        text-align: right;
    }
    .m-dropdown.m-dropdown--align-right.m-dropdown--align-push .m-dropdown__wrapper {
        margin-right: 0;
    }
    .m-portlet .m-portlet__head .m-portlet__head-tools .m-portlet__nav .m-portlet__nav-item .m-portlet__nav-link {
        margin-right: -6px;
    }
}

@media (max-width: 575px) {
    .stocklist-active {
        padding: 20px 0 20px !important;
        line-height: 25px;
    }
    .list-search {
        margin-top: 25px;
    }
    .search-btn-area {
        padding-left: 0;
    }
}

.m-dropdown {
    /* border-bottom: 1px solid #333; */
    margin: 0 5px;
    border-radius: 0;
    line-height: 17px;
}

.m-dropdown .m-dropdown__wrapper {
    width: 290px;
}

.check-availability-area label {
    display: inline-block;
    color: #041531;
    font-weight: 400;
}

.check-availability-area app-date-time-range {
    display: inline-block;
    position: relative;
    padding-bottom: 15px;
}

.check-availability-area button {
    display: inline-block;
}

.export-modal-body {
    padding: 20px;
}

.export-modal-body .custom-control-label::after {
    top: 0.18rem;
}

.export-modal-body p {
    font-weight: 500;
}

.search-reset {
    position: absolute;
    right: 30px;
    top: 7px;
}

.addproduct-btn {
    background-color: #59a559;
    border-color: #59a559;
    margin-top: 30px;
    color: #fff;
}

.addproduct-btn:active,
.addproduct-btn:focus,
.addproduct-btn:hover {
    background-color: #74bf74;
    border-color: #74bf74;
    margin-top: 30px;
}

boot-pagination {
    margin-top: 20px;
    padding-top: 20px;
    display: inline-block;
    width: 100%;
}

.btn-addproduct {
    color: #fff !important;
    background-color: #716aca;
    border-color: #716aca;
}

.btn-addproduct:active,
.btn-addproduct:focus,
.btn-addproduct:hover {
    color: #fff !important;
    background-color: #8078e4;
    border-color: #8078e4;
}

.btn-addpackage {
    color: #fff !important;
}

.package-icon {
    width: 22px;
}

.rentmy-tooltip {
    color: #333;
    border: none;
    border-radius: 10px;
    padding: 10px 15px;
    text-align: center;
    font-size: 15px;
    outline: none;
    cursor: pointer;
    margin-right: 1rem;
}

[tooltip] {
    position: relative;
}

[tooltip]::before,
[tooltip]::after {
    text-transform: none;
    font-size: 0.9em;
    line-height: 1;
    user-select: none;
    pointer-events: none;
    position: absolute;
    display: none;
    opacity: 0;
}

[tooltip]::before {
    content: "";
    border: 5px solid transparent;
    z-index: 1001;
}

[tooltip]::after {
    content: attr(tooltip);
    text-align: center;
    min-width: 3em;
    max-width: 21em;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0.5em 1em;
    border-radius: 5px;
    box-shadow: 0 1em 2em -0.5em rgba(0, 0, 0, 0.35);
    background: #020204;
    color: #fff;
    z-index: 1000;
}

[tooltip]:hover::before,
[tooltip]:hover::after {
    display: block;
}

[tooltip=""]::before,
[tooltip=""]::after {
    display: none !important;
}

[tooltip]:not([flow])::before,
[tooltip][flow^="up"]::before {
    bottom: 100%;
    border-bottom-width: 0;
    border-top-color: #111;
}

[tooltip]:not([flow])::after,
[tooltip][flow^="up"]::after {
    bottom: calc(100% + 5px);
}

[tooltip]:not([flow])::before,
[tooltip]:not([flow])::after,
[tooltip][flow^="up"]::before,
[tooltip][flow^="up"]::after {
    left: 50%;
    transform: translate(-50%, -0.5em);
}

[tooltip]:not([flow]):hover::before,
[tooltip]:not([flow]):hover::after,
[tooltip][flow^="up"]:hover::before,
[tooltip][flow^="up"]:hover::after,
[tooltip][flow^="up-left"]:hover::before,
[tooltip][flow^="up-left"]:hover::after,
[tooltip][flow^="up-right"]:hover::before,
[tooltip][flow^="up-right"]:hover::after,
[tooltip][flow^="down"]:hover::before,
[tooltip][flow^="down"]:hover::after,
[tooltip][flow^="down-left"]:hover::before,
[tooltip][flow^="down-left"]:hover::after,
[tooltip][flow^="down-right"]:hover::before,
[tooltip][flow^="down-right"]:hover::after {
    animation: tooltips-vert 300ms ease-out forwards;
}

[tooltip][flow^="left"]:hover::before,
[tooltip][flow^="left"]:hover::after,
[tooltip][flow^="right"]:hover::before,
[tooltip][flow^="right"]:hover::after {
    animation: tooltips-horz 300ms ease-out forwards;
}

@-moz-keyframes tooltips-vert {
    to {
        opacity: 0.9;
        transform: translate(-50%, 0);
    }
}

@-webkit-keyframes tooltips-vert {
    to {
        opacity: 0.9;
        transform: translate(-50%, 0);
    }
}

@-o-keyframes tooltips-vert {
    to {
        opacity: 0.9;
        transform: translate(-50%, 0);
    }
}

@keyframes tooltips-vert {
    to {
        opacity: 0.9;
        transform: translate(-50%, 0);
    }
}

@-moz-keyframes tooltips-horz {
    to {
        opacity: 0.9;
        transform: translate(0, -50%);
    }
}

@-webkit-keyframes tooltips-horz {
    to {
        opacity: 0.9;
        transform: translate(0, -50%);
    }
}

@-o-keyframes tooltips-horz {
    to {
        opacity: 0.9;
        transform: translate(0, -50%);
    }
}

@keyframes tooltips-horz {
    to {
        opacity: 0.9;
        transform: translate(0, -50%);
    }
}