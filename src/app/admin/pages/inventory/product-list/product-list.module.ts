import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { DateTimeRangeModule } from './../../../../modules/date-time-range/date-time-range.module';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Routes, RouterModule } from '@angular/router';
import { LayoutModule } from '../../../layouts/layout.module';
import { ProductListComponent } from './product-list.component';
import { DialogBoxModule } from '../../../../modules/dialog-box/dialog-box.module';
import { DialogBoxComponent } from '../../../../modules/dialog-box/dialog-box.component';
import { ProductServiceModule } from './product-service/product.service.module';
import { ProductSearchComponent } from './product-search/product-search.component';
import { FormsModule } from '@angular/forms';
import { PaginationModule } from '../../../../modules/pagination/pagination.module';
import { PlanUpgradeAlertModule } from '../../../../modules/plan-upgrade-alert/plan-upgrade-alert.module';
import { CurrencyFormatModule } from '../../../../modules/currency-format/currency-format.pipe';
import { DialogBoxSubscriptionComponent } from '../../../../modules/dialog-box-subscription/dialog-box-subscription.component';
import { DialogBoxSubscriptionModule } from '../../../../modules/dialog-box-subscription/dialog-box-subscription.module';
import { ProductAvailabilityComponent } from './product-availability/product-availability.component';
import { ExportInventoryComponent } from './export-inventory/export-inventory.component';

const routes: Routes = [
  {
    path: '',
    component: ProductListComponent,
    children: [
      {
          path: 'edit/:product_id',
          loadChildren: () => import('./../product-sidebar/product-sidebar.module').then(m => m.ProductSideBarModule)
      }
    ]
  }
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    LayoutModule,
    DialogBoxModule,
    DialogBoxSubscriptionModule,
    ProductServiceModule.forRoot(),
    FormsModule,
    PaginationModule,
    PlanUpgradeAlertModule,
    CurrencyFormatModule,
    DateTimeRangeModule,
    NgbModule
  ],
  exports: [
    RouterModule
  ],
  declarations: [
    ProductListComponent,
    ProductSearchComponent,
    ProductAvailabilityComponent,
    ExportInventoryComponent
  ],
  entryComponents: [
    DialogBoxComponent,
    DialogBoxSubscriptionComponent,
    ExportInventoryComponent
  ]
})
export class ProductListModule {
}
