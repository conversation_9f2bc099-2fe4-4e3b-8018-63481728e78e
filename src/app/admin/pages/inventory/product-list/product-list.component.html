<!-- <plan-upgrade-alert
[message]="'You must be a silver or Platinum tier client to add additional inventory.'"
*ngIf="subs_plan==='FREE' && totalItems>10"></plan-upgrade-alert>
<plan-upgrade-alert [message]="'You must be a Platinum tier client to add additional inventory. '" *ngIf="subs_plan==='SILVER' && totalItems>50" ></plan-upgrade-alert> -->

<div class="custom-alert" #hasCusAlert></div>

<!-- BEGIN: Subheader -->
<div class="m-subheader product-section-list">
    <div class="d-flex align-items-center">
        <div class="mr-auto">
            <h3 class="m-subheader__title m-subheader__title--separator">
                Inventory
            </h3>
            <ul class="m-subheader__breadcrumbs m-nav m-nav--inline">
                <li class="m-nav__item m-nav__item--home">
                    <a routerLink="/admin" class="m-nav__link m-nav__link--icon">
                        <i class="m-nav__link-icon la la-home"></i>
                    </a>
                </li>
                <li class="m-nav__separator">
                    <i class="fa fa-angle-right"></i>
                </li>
                <li class="m-nav__item">
                    <a class="m-nav__link">
                        <span class="m-nav__link-text">
              Product List
            </span>
                    </a>
                </li>
            </ul>
        </div>
        <div class="-table-right-btn">
            <div class="first-child-btn m-dropdown m-dropdown--inline m-dropdown--arrow m-dropdown--align-right m-dropdown--align-push" data-dropdown-toggle="hover" aria-expanded="true" style="margin-right: 10px;">
                <button class="m-portlet__nav-link m-dropdown__toggle decoration-none btn m-btn--pill m-btn--air btn-light btn-sm">
          <span class="btn-text">Actions</span>
          <span class="btn btn-brand btn-sm btn-sm  m-btn m-btn--outline-2x m-btn--air m-btn--icon m-btn--icon-only m-btn--pill">
            <i class="la la-plus m--hide"></i>
            <i class="la la-angle-down"></i>
          </span>
        </button>
                <div class="m-dropdown__wrapper">
                    <span class="m-dropdown__arrow m-dropdown__arrow--right m-dropdown__arrow--adjust"></span>
                    <div class="m-dropdown__inner">
                        <div class="m-dropdown__body">
                            <div class="m-dropdown__content">
                                <ul class="m-nav">
                                    <li class="m-nav__item">
                                        <a (click)="addProduct('product')" class="m-nav__link">
                                            <i class="m-nav__link-icon flaticon-share"></i>
                                            <span class="m-nav__link-text">
                        Add Product
                      </span>
                                        </a>
                                    </li>
                                    <li class="m-nav__item">
                                        <a [routerLink]="['/admin/newusertour']" class="m-nav__link">
                                            <i class="m-nav__link-icon flaticon-share"></i>
                                            <span class="m-nav__link-text">
                        Use Wizard
                      </span>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="m-dropdown m-dropdown--inline m-dropdown--arrow m-dropdown--align-right m-dropdown--align-push" data-dropdown-toggle="hover" aria-expanded="true">
                <button class="m-portlet__nav-link m-dropdown__toggle decoration-none btn m-btn--pill m-btn--air btn-light btn-sm">
          <span class="btn-text">Tools</span>
          <span class="btn btn-brand btn-sm  m-btn m-btn--outline-2x m-btn--air m-btn--icon m-btn--icon-only m-btn--pill">
            <i class="la la-plus m--hide"></i>
            <i class="la la-angle-down"></i>
          </span>
        </button>
                <div class="m-dropdown__wrapper">
                    <span class="m-dropdown__arrow m-dropdown__arrow--right m-dropdown__arrow--adjust"></span>
                    <div class="m-dropdown__inner">
                        <div class="m-dropdown__body">
                            <div class="m-dropdown__content">
                                <ul class="m-nav">
                                    <li class="m-nav__item">
                                        <a [routerLink]="['/admin/inventory/import-inventory']" class="m-nav__link">
                                            <i class="m-nav__link-icon la la-cloud-upload"></i>
                                            <span class="m-nav__link-text">
                        Import Inventory
                      </span>
                                        </a>
                                    </li>
                                    <li class="m-nav__item">
                                        <a class="m-nav__link" style="cursor: pointer;" (click)="export()">
                                            <i class="m-nav__link-icon la la-cloud-download"></i>
                                            <span class="m-nav__link-text">
                        Export Inventory
                      </span>
                                        </a>
                                    </li>
                                    <li class="m-nav__item">
                                        <a class="m-nav__link" style="cursor: pointer;" (click)="bulkDelete()">
                                            <i class="m-nav__link-icon la la-archive"></i>
                                            <span class="m-nav__link-text">
                        Bulk delete
                      </span>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END: Subheader -->
<div class="m-content product-list animated fadeIn">
    <!-- Filter product list -->
    <product-search #searchPro [checkFilter]="reloadFilter" [abc]="abc" (search)="loadSearchData($event)"></product-search>

    <div class="m-portlet m-portlet--mobile">
        <div class="m-portlet__head">
            <div class="m-portlet__head-caption">
                <div class="m-portlet__head-title m-product-title stocklist-body stocklist-packagelist">
                    <h3 class="m-portlet__head-text">
                        <div class="row">
                            <div class="col-xl-4 col-lg-4 col-md-3 col-sm-12 list-link">
                                <a class="" [routerLink]="['/admin/inventory']" [routerLinkActive]="'stocklist-active'">Product List</a>
                                <!-- Availability filter  -->
                                <app-product-availability (availabilityParams)="getAvailableProducts($event)" (isReset)="resetAvailability($event)" title="Check Availability">
                                </app-product-availability>

                            </div>
                            <div class="col-xl-8 col-lg-8 col-md-9 col-sm-12 col-12 list-search">
                                <form #serForm="ngForm" class="row m-0">
                                    <div class="form-group col-xl-10 col-lg-9 col-md-9 col-sm-10 col-10">
                                        <input type="text" class="form-control" placeholder="Search by id, name, barcode and supplier product id" #searchAll="ngModel" name="search" [(ngModel)]="search" style="display: inline;">
                                        <div class="search-reset" *ngIf="searchAll.dirty || search" (click)="reset(serForm)">
                                            <i class="fa fa-close"></i>
                                        </div>
                                    </div>
                                    <div class="form-group col-xl-2 col-lg-3 col-md-3 col-sm-2 col-2 search-btn-area pl-0">
                                        <button type="submit" class="btn btn-md btn-brand" (click)="searchList(serForm.value)"><i class="fa fa-search"></i></button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </h3>
                    <div class="add-list-btn text-right">
                        <a class="btn btn-primary btn-addproduct btn-md" (click)="addProduct('product')">
                            <i class="fa fa-plus"></i> Add Product
                        </a>
                        &nbsp;
                        <a class="btn btn-info btn-md btn-addpackage" (click)="addProduct('package')">
                            <i class="fa fa-plus"></i> Add Package
                        </a>
                    </div>
                    <div class="m-portlet__head-tools">
                        <ul class="m-portlet__nav">
                            <li class="m-portlet__nav-item m-dropdown m-dropdown--inline m-dropdown--arrow m-dropdown--align-right m-dropdown--align-push" data-dropdown-toggle="hover" aria-expanded="true">
                                <a href="#" class="m-portlet__nav-link m-portlet__nav-link--icon m-portlet__nav-link--icon-xl m-dropdown__toggle">
                                    <i class="la la-ellipsis-h m--font-brand"></i>
                                </a>
                                <div class="m-dropdown__wrapper">
                                    <span class="m-dropdown__arrow m-dropdown__arrow--right m-dropdown__arrow--adjust" style="left: auto; right: 22.5px;"></span>
                                    <div class="m-dropdown__inner">
                                        <div class="m-dropdown__body">
                                            <div class="m-dropdown__content">
                                                <ul class="m-nav">
                                                    <li class="m-nav__item">
                                                        <a class="m-nav__link" (click)="addProduct('product')">
                                                            <i class="m-nav__link-icon flaticon-plus"></i>
                                                            <span class="m-nav__link-text">
                                Add Product
                              </span>
                                                        </a>
                                                    </li>
                                                    <li class="m-nav__item">
                                                        <a class="m-nav__link" (click)="addProduct('package')">
                                                            <i class="m-nav__link-icon flaticon-plus"></i>
                                                            <span class="m-nav__link-text">
                                Add Package
                              </span>
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="m-portlet__body">
            <!--begin: Datatable -->
            <div class="m-section mb-0">
                <div class="m-section__content price-table mb-0" style="position: relative;">
                    <span *ngIf="copyDone" class="success-mgs copy-msg"><b>Url is copied</b></span>
                    <!-- pagination Start-->
                    <boot-pagination [totalSize]="pagi.total" [page]="pagi.page" [listSize]="pagi.limit" [pagelimit]="pagi.limit" (pageChange)="reloadTable($event)"></boot-pagination>
                    <!-- pagination End-->
                    <div *ngIf="loader" class="table-load m-loader m-loader--brand"></div>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th class="text-center">
                                        <label class="m-checkbox m-checkbox--check-bold m-checkbox--state-brand">
                      <input type="checkbox" (click)="checkAll($event)">
                      <span></span>
                    </label>
                                    </th>
                                    <th>
                                    </th>
                                    <th>
                                        Image
                                    </th>
                                    <th>ID</th>
                                    <th>
                                        Name
                                    </th>
                                    <th>
                                        Quantity
                                    </th>
                                    <th>
                                        Buy Price
                                    </th>
                                    <th>
                                        Rent Price
                                    </th>
                                    <!-- <th>
                    Daily Price
                  </th>
                  <th>
                    Weekly Price
                  </th> -->
                                    <th>
                                        Status
                                    </th>
                                    <th>
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody *ngIf="productList.length<1; else table">
                                <tr *ngIf="!loader">
                                    <td class="text-center" colspan="11">
                                        <!-- <h4 class="text-center">No Product Found</h4> -->
                                        <div *ngIf="filter">
                                            <p>No product found!.</p>
                                            <button class="btn btn-md addproduct-btn" routerLink="/admin/inventory/create">Add Product</button>
                                        </div>

                                        <button *ngIf="!filter" class="btn btn-md addproduct-btn" routerLink="/admin/inventory/create">Add your first product</button>
                                    </td>
                                </tr>
                            </tbody>
                            <ng-template #table>
                                <tbody>
                                    <tr *ngFor="let pro of productList; let i='index'; trackBy: tarckProduct; let o='odd'; let e='even'" [ngClass]="{'odd-tr':o, 'even-tr':e}">
                                        <td class="text-center">
                                            <label class="m-checkbox m-checkbox--check-bold m-checkbox--state-primary">
                        <input type="checkbox" [attr.name]="'pro_'+pro.id" [(ngModel)]="pro.check" (click)="checkOne($event, i)">
                        <span></span>
                      </label>
                                        </td>
                                        <td>
                                            <button class="m-portlet__nav-link btn m-btn m-btn--hover-primary m-btn--icon m-btn--icon-only m-btn--pill" [ngClass]="{'disabled-cart' : pro.status==2}" title="Add Cart" (click)="openCartSidebar(pro)">
                        <i class="la la-cart-plus"></i>
                      </button>
                                        </td>
                                        <td (click)="gotoProduct(pro)">
                                            <img *ngIf="pro.image" class="img-fluid img-avatar img-thumbnail img-resize-tum" src="{{image + '/' + pro.id + '/' + pro.image}}" onError="this.src='./assets/img/home/<USER>';" style="cursor:pointer;">
                                            <img *ngIf="!pro.image" class="img-fluid img-avatar img-thumbnail img-resize-tum" src="./assets/img/home/<USER>" style="cursor:pointer;">
                                        </td>
                                        <th>{{pro.client_specific_id? pro.client_specific_id : pro.id}}</th>
                                        <th style="cursor:pointer;" (click)="openSidebar(pro, 'description')">
                                            <b class="mr-3">{{pro.name}}</b>
                                            <input type="text" class="hide-copy-input" [value]="'/product/' + pro.uuid + '/' + pro.url" readonly #copyText>
                                        </th>
                                        <td style="position:relative">
                                            <span *ngIf="pro?.type==1" class="m-badge m-badge--wide m-badge--primary" (click)="openSidebar(pro, 'variant')" style="cursor:pointer;">
                                                {{pro.quantity? pro.quantity : 0}}
                                            </span>
                                            <span *ngIf="pro?.type==2" tooltip="Package" flow="up" class="rentmy-tooltip">
                                              <img src="./assets/img/admin/box.png" class="package-icon" />
                                            </span>
                                        </td>
                                        <td>
                                            <!-- {{pro.price[0] | json}} -->
                                            {{pro.price.length && pro.price[0].base ? (pro.price[0].base.price | currency) : '-'}}
                                        </td>
                                        <td>
                                            <!-- {{pro.price.hourly_price ? '$' + (pro.price.hourly_price | number: '1.2-2') + ' / ' +
                      (pro.price.hourly_duration ? pro.price.hourly_duration : 1) + (single(pro.price.hourly_duration)
                      ? ' Hour' :
                      ' Hours') : '-'}} -->
                                            {{ pro.price && pro.price[0] ?getRentPrice(pro.price[0]) :'-'}}
                                        </td>
                                        <!-- <td>
                      {{pro.price.daily_price ? '$' + (pro.price.daily_price | number: '1.2-2') + ' / ' +
                      (pro.price.daily_duration ? pro.price.daily_duration : 1) + (single(pro.price.daily_duration) ? '
                      Day' : '
                      Days') : '-'}}
                    </td> -->
                                        <!-- <td>
                      {{pro.price.weekly_price ? '$' + (pro.price.weekly_price | number: '1.2-2') + ' / ' +
                      (pro.price.weekly_duration ? pro.price.weekly_duration : 1) + (single(pro.price.weekly_duration)
                      ? ' Week' :
                      ' Weeks') : '-'}}
                    </td> -->
                                        <td>
                                            <span class="m-badge m-badge--wide" [ngClass]="checkStatus(pro.status)">{{getStatus(pro.status)}}</span>
                                        </td>
                                        <td>
                                            <a (click)="cloneProduct(pro)" title="Clone" class="m-portlet__nav-link btn m-btn m-btn--hover-brand m-btn--icon m-btn--icon-only m-btn--pill">
                                                <i class="fa fa-copy"></i>
                                            </a>
                                            <a *ngIf="pro?.type==1" routerLink="/admin/inventory/{{pro.id}}/details" title="Product Details" class="m-portlet__nav-link btn m-btn m-btn--hover-primary m-btn--icon m-btn--icon-only m-btn--pill">
                                                <i class="fa fa-eye"></i>
                                            </a>
                                            <a *ngIf="pro?.type==2" routerLink="/admin/inventory/package/{{pro.id}}/details" title="Product Details" class="m-portlet__nav-link btn m-btn m-btn--hover-primary m-btn--icon m-btn--icon-only m-btn--pill">
                                                <i class="fa fa-eye"></i>
                                            </a>
                                            <a (click)="openSidebar(pro, 'description')" title="Edit Product" class="m-portlet__nav-link btn m-btn m-btn--hover-accent m-btn--icon m-btn--icon-only m-btn--pill">
                                                <i class="fa fa-edit"></i>
                                            </a>
                                            <a *ngIf="pro?.type==1" (click)="openSidebar(pro, 'calendar')" title="Calender" class="m-portlet__nav-link btn m-btn m-btn--hover-dark m-btn--icon m-btn--icon-only m-btn--pill">
                                                <i class="fa fa-calendar"></i>
                                            </a>
                                            <a title="Archive Product" class="m-portlet__nav-link btn m-btn m-btn--hover-danger m-btn--icon m-btn--icon-only m-btn--pill" (click)="deleteProductList(pro)">
                                                <i class="fa fa-trash"></i>
                                            </a>
                                        </td>
                                    </tr>
                                </tbody>
                            </ng-template>
                        </table>
                    </div>
                    <!-- pagination Start-->
                    <boot-pagination [totalSize]="pagi.total" [page]="pagi.page" [listSize]="pagi.limit" [pagelimit]="pagi.limit" (pageChange)="reloadTable($event)"></boot-pagination>
                    <!-- pagination End-->
                </div>
            </div>
            <!--end: Datatable -->
        </div>
    </div>
    <!--end::Portlet-->
</div>

<!-- sidebar -->

<div class="native-routing product-section-sidebar">
    <button class="close-sidebar btn btn-sm btn-brand">
    <i class="fa fa-chevron-right"></i>
  </button>
    <span class="close-sidebar-upper">
    <i class="la la-close"></i>
  </span>
    <div class="native-routing-container">
        <router-outlet></router-outlet>
    </div>
</div>
<!-- <div class="backdrop animated"></div> -->

<!-- delete Dialog -->