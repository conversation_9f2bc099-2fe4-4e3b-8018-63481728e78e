<!--begin::Portlet-->
<div class="m-portlet m-portlet--mobile">
  <div class="m-portlet__head" (click)="openAdvanceSearch()" style="cursor: pointer;">
    <div class="m-portlet__head-caption">
      <div class="m-portlet__head-title">
        <h3 class="m-portlet__head-text text-left" style="width:92%;">
          <i class="la la-search"></i>
          Advanced Search
        </h3>
        <h3 class="text-right" style="margin: auto;padding: 20px 5px;">
          <i class="la la-angle-up" id="advanceSearch" style="font-size: 1.8rem;"></i>
        </h3>
      </div>
    </div>
  </div>
  <div class="m-portlet__body search-panel dis-none" id="advanceSearch">
    <!--begin::Form-->
    <form class="m-form m-form--fit m-form--label-align-right" #form="ngForm" (ngSubmit)="searchProduct(form.value)">
      <div class="row">
        <div class="col-md-4 col-sm-6">
          <div class="form-group m-form__group">
            <input type="text" class="form-control m-input" name="name" [(ngModel)]="search.name"
              placeholder="Product Name" aria-describedby="Product Name" autocomplete="off">
          </div>
        </div>
        <div class="col-md-4 col-sm-6">
          <div class="form-group m-form__group">
            <select class="form-control m-input"  name="category_id" [(ngModel)]="search.category_id">
              <option value=''>-Category-</option>
              <option *ngFor="let cat of allcategories;" [value]="cat.id">{{cat.name}}</option>
            </select>
          </div>
        </div>
        <div class="col-md-4 col-sm-6">
          <div class="form-group m-form__group">
            <select class="form-control m-input" name="status" [(ngModel)]="search.status">
              <option value=''>-Status-</option>
              <option value="1">Active</option>
              <option value="2">Inactive</option>	
              <option value="3">Out of Stock</option>	
              <option value="4">Faulty</option>	
              <option value="5">Deleted</option>	
            </select>
          </div>
        </div>
        <div class="col-md-4 col-sm-6">
          <div class="form-group m-form__group">
            <select class="form-control m-input"  name="supplier_name" [(ngModel)]="search.supplier_name">
              <option value=''>-Supplier Name-</option>
              <option *ngFor="let sup of suplliers" [value]="sup.id">{{sup.text}}</option>
            </select>
          </div>
        </div>
        <div class="col-md-4 col-sm-6">
          <div class="form-group m-form__group">
            <input class="form-control m-input" name="supplier_id" [(ngModel)]="search.supplier_id"
            type="text" placeholder="Supplier Product ID" autocomplete="off">
          </div>
        </div>
        <div class="col-md-4 col-sm-6">
          <div class="form-group m-form__group">
            <input class="form-control m-input" name="barcode" [(ngModel)]="search.barcode"
            type="text" placeholder="Barcode" id="barcode" autocomplete="off">
          </div>
        </div>
        <!-- <div class="col-md-4 col-sm-6">
          <div class="form-group m-form__group">
            <input class="form-control m-input" name="secondary_barcode" ngModel
            type="text" placeholder="Secondary Barcode" id="sec-barcode" autocomplete="off">
          </div>
        </div> -->
        <div class="col-md-4 col-sm-6">
          <div class="form-group m-form__group">
            <select class="form-control m-input" name="type_id" [(ngModel)]="search.type_id">
              <option value=''>-Select Type-</option>
              <option value="1">Product</option>
              <option value="2">Package</option>
            </select>
          </div>
        </div>
        <div class="col-md-4 col-sm-6">
          <div class="form-group m-form__group">
            <select class="form-control m-input" name="rental_type" [(ngModel)]="search.rental_type">
              <option value=''>-Rental Type-</option>
              <option value="all">All</option>
              <option value="buy">Buy</option>
              <option value="rent">Rent</option>
            </select>
          </div>
        </div>
        <!-- <div class="col-md-4 col-sm-6">
          <div class="form-group m-form__group">
            <select class="form-control m-input"  name="location" ngModel>
              <option value=''>-Location-</option>
              <option *ngFor="let sup of location" [value]="sup?.id">{{sup?.location}}</option>
            </select>
          </div>
        </div> -->
        <div class="col-md-4 col-sm-6">
          <div class="form-group m-form__group">
              <label class="m-checkbox">
              <input type="checkbox" name="image" [(ngModel)]="search.image">
                Product without image
                <span></span>
              </label>
          </div>
        </div>
      </div>
      <div class="form-group m-form__group col-12 search-panal-action">
          <div *ngIf="loader; else button" class="m-loader m-loader--brand" 
          style="width: 30px; display: inline-block;"></div>
        <ng-template #button>
          <input type="hidden" value="1" name="avd_search" [(ngModel)]= "search.avd_search" />
          <button type="submit" class="btn m-btn--pill m-btn--air btn-brand btn-sm">
            <i class="fa fa-calendar-check-o"></i>
              Search
          </button>

          <button type="reset" id="resetFilter" class="btn m-btn--pill m-btn--air btn-danger btn-sm" (click)="resetSearch()">
            <i class="fa fa-history"></i>
              Cancel
          </button>
        </ng-template>
        
      </div>
    </form>
    <!--end::Form-->
  </div>
</div>
<!--end::Portlet-->