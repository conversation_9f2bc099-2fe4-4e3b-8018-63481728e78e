import { Component, OnInit, Output, EventEmitter, AfterViewInit } from '@angular/core';
import * as moment from "moment";
import * as $ from 'jquery';

@Component({
  selector: 'app-product-availability',
  templateUrl: './product-availability.component.html',
  styleUrls: ['./product-availability.component.css']
})
export class ProductAvailabilityComponent implements OnInit, AfterViewInit {

  startDate: string;
  endDate: string;
  @Output() availabilityParams = new EventEmitter();
  @Output() isReset = new EventEmitter<boolean>();

  constructor( ) { }

  ngOnInit() {
    this.startDate = moment(new Date()).format('YYYY-MM-DD HH:mm');
    this.endDate = moment(new Date()).format('YYYY-MM-DD HH:mm');
  }

  ngAfterViewInit(): void {
    $(".select-daterange-toggle, .selectrange-submit-btn").on("click", function () {
      $(".select-daterange-dropdown").slideToggle();
    });
    $(document).on('click', function () {
      $('.select-daterange-dropdown').hide(100);
    });
    $('.select-daterange-dropdown, .select-daterange-toggle, .selectrange-submit-btn').on('click', function(e){
        e.stopPropagation();
    });
  }

  dateChangeEvent(e: any): void {
    if (e.startDate && e.endDate) {
      this.startDate = e.startDate;
      this.endDate = e.endDate;
    }
  }

  submit(): void {
     const params = {
      start_date: this.startDate,
      end_date: this.endDate,
      location_id: localStorage.getItem('currentUser') ? JSON.parse(localStorage.getItem('currentUser')).location_id : null
    }
    this.availabilityParams.emit(params);
  }

  reset(): void {
    this.startDate = moment(new Date().toDateString()).format('YYYY-MM-DD HH:mm');
    this.endDate = moment(new Date().toDateString()).format('YYYY-MM-DD HH:mm');
    this.isReset.emit(true);
  }

}
