import { Component, OnInit } from '@angular/core';
import { InventoryService } from "./../../../inventory-serveice/inventory.service";
import { HttpErrorResponse } from '@angular/common/http';
import { ActivatedRoute } from "@angular/router";

@Component({
  selector: 'assets-tracking',
  templateUrl: './assets-tracking.component.html',
  styleUrls: ['./assets-tracking.component.css']
})
export class AssetsTrackingComponent implements OnInit {

  history = [];

  constructor(
    private inventoryS: InventoryService,
    private route: ActivatedRoute
  ) { }

  ngOnInit() {
    const id = this.route.snapshot.params.id;
    this.inventoryS.getAssetHistory(id).subscribe(
      (res: any) => {
        this.history = res.result.data;
      }, (err: HttpErrorResponse) => {
        console.log(err);
      }
    );
  }

}
