import { Component, OnInit, ElementRef, ViewChild } from "@angular/core";
import { InventoryService } from "../../../inventory-serveice/inventory.service";
import { Pagi } from "./../../../../../../modules/pagination/pagi.model";
import { Comment } from "./../../asset-models/comment.models";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { ActivatedRoute } from "@angular/router";
import { HttpErrorResponse } from "@angular/common/http";
import { AlertService } from "./../../../../../../modules/alert/alert.service";

@Component({
  selector: "assets-comments",
  templateUrl: "./assets-comments.component.html",
  styleUrls: ["./assets-comments.component.css"]
})
export class AssetsCommentsComponent implements OnInit {

  loader: boolean = false;
  comments: Comment = new Comment();
  commentList = [];
  routeId: number;
  @ViewChild("hasCusAlert") alertContainer: ElementRef;
  pagi: Pagi = { page: 1, limit: 10, total: 0 };
  constructor(
    private inventoryS: InventoryService,
    private modalService: NgbModal,
    private route: ActivatedRoute,
    private alertS: AlertService) {
  }

  ngOnInit() {
    this.routeId = this.route.snapshot.params.id;
    this.getCommentList();
  }

  getCommentList () {
    this.inventoryS.getAssetComment(this.routeId).subscribe(res => {
      console.log(res);
      this.commentList = res.data;
      console.log(this.commentList);
      this.pagi.total = res.total;
      this.loader = false;
    });
  }

  addComment() {
    this.comments.asset_id = this.routeId;
    this.loader = true;
    console.log(this.comments);
    this.inventoryS.postAssetComment(this.comments).subscribe(
      (res: any) => {
        this.loader = false;
        console.log(res);
        this.getCommentList();
        if (res.result.error) {
          this.alertS.error(this.alertContainer, res.result.error, true, 5000);
        } else {
          this.alertS.success(
            this.alertContainer,
            res.result.message,
            true,
            5000
          );
        }
      }, (err: HttpErrorResponse) => {
        this.loader = false;
          this.alertS.error(
            this.alertContainer,
            "Something wrong Please try again !!!",
            true,
            5000
          );
      }
    );
  }

  reloadTable(e) {
    let query = "";
    this.pagi.page = e.page;
    const obj = {
      page_no: this.pagi.page,
      limit: this.pagi.limit
    };

    if (this.pagi.limit != e.limit) {
      this.pagi.page =
        Math.ceil(this.pagi.total / e.limit) <= this.pagi.page
          ? Math.ceil(this.pagi.total / e.limit)
          : this.pagi.page;
      this.pagi.limit = e.limit;
      obj.limit = e.limit;
      obj.page_no = this.pagi.page;
    }
    for (let c in obj) {
      query += `${c}=${obj[c]}&`;
    }
    // this.getComments(query);
    this.inventoryS.getAssetComment(this.routeId, query).subscribe(res => {
      console.log(res);
      this.commentList = res.data && res.data.length ? res.data : [];
      console.log(this.commentList);
      this.pagi.total = res.total;
      this.loader = false;
    });
  }
}
