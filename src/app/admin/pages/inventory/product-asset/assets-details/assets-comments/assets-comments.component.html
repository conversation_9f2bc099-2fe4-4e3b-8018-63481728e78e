<div class="custom-alert" #hasCusAlert></div>
<div *ngIf="loader" class="table-load m-loader m-loader--brand"></div>

<div class="m-content product-sidebar-description mt-0 mb-30">
  <div class="col-md-8">
    <form class="m-form--fit m-form--label-align-right ng-untouched ng-pristine ng-valid" #form="ngForm" (ngSubmit)="addComment()">
      <div class="row">
        <div class="col-md-8">
          <div class="form-group">
            <input
              type="text"
              autocomplete="off"
              class="form-control m-input"
              placeholder="Comment Here...."
              style="display: inline-block;"
              name="comments"
              [(ngModel)] ="comments.comments">
          </div>
        </div>
        <div class="col-md-4">
          <div class="form-group">
            <button class="btn btn-brand" type="submit" style="display: inline-block;"> Submit</button>
          </div>
        </div>
      </div>
    </form>
  </div>

  <div class="m-timeline-3" style="margin-top:20px;">
    <div class="m-timeline-3__items" *ngIf="commentList?.length> 0; else noComment">
      <div class="m-timeline-3__item m-timeline-3__item--info"  *ngFor="let item of commentList">
        <span class="m-timeline-3__item-time" style="font-size: 14px;">{{item.created | date}}</span>
        <div class="m-timeline-3__item-desc">
          <span class="m-timeline-3__item-text">
            {{item.comments}}
          </span><br>
          <span class="m-timeline-3__item-user-name">
            <a href="javascript:;" class="m-link m-link--metal m-timeline-3__item-link">
              By {{item.user.first_name + " " + item.user.last_name}}
            </a>
          </span>
        </div>
      </div>

      <boot-pagination [totalSize]="pagi.total" [page]="pagi.page" [listSize]="pagi.limit" [pagelimit]="pagi.limit"
      (pageChange)="reloadTable($event)"></boot-pagination>
    </div>
    <ng-template #noComment>
      <p style="font-size:20px;">No comment added</p>
    </ng-template>
  </div>
</div>
