import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { Routes, RouterModule } from "@angular/router";
import { AssetsDetailsComponent } from "./assets-details.component";
import { AssetsCommentsComponent } from './assets-comments/assets-comments.component';
import { AssetsTrackingComponent } from './assets-tracking/assets-tracking.component';
import { AssetsSalesComponent } from './assets-sales/assets-sales.component';
import { AssetsInvestmentComponent } from './assets-investment/assets-investment.component';
import { DetailsComponent } from './details/details.component';
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { PaginationModule } from "./../../../../../modules/pagination/pagination.module";
import { FormsModule } from '@angular/forms';
import { DateFormatModule } from "./../../../../../modules/date-format/date-format-pipe";

const route: Routes = [
  {
    path: "",
    component: AssetsDetailsComponent
  }
];

@NgModule({
  imports: [CommonModule, NgbModule, PaginationModule, FormsModule, RouterModule.forChild(route),DateFormatModule],
  exports: [RouterModule],
  declarations: [AssetsDetailsComponent, AssetsCommentsComponent, AssetsTrackingComponent, AssetsSalesComponent, AssetsInvestmentComponent, DetailsComponent]
})
export class ProductAssetDetailsModule {}
