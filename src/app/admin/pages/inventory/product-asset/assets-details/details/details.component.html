
<div class="row">
  <div class="col-md-12">
    <div class="m-portlet m-portlet--full-height mb-30">
      <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
          <div class="m-portlet__head-title">
            <h3 class="m-portlet__head-text colorPurpel"> Asset Details </h3>
            <div class="add-list-btn text-right">
              <a routerLink="/admin/inventory/product-asset/" class="btn btn-brand btn-sm" id="asset-addnew-btn">
                Back
              </a>
            </div>
          </div>
        </div>
      </div>
      <div *ngIf="loader" class="table-load m-loader m-loader--brand"></div>
      <div class="m-portlet__body" *ngIf="asset">
        <div class="description-fields row">
          <div class="col-md-12">
              <p><span class="description-field-title">Product: </span>
                <span>{{asset.product.name}} </span>
                <span *ngIf="asset.product.variant_chain!='Unassigned: Unassigned'"> ({{asset.product.variant_chain}})</span>
                <span>&nbsp; <strong>Location</strong> :{{getLocation(asset?.location_id)}}</span>

              </p>
          </div>
        </div>
        <div class="description-fields row">
          <div class="col-md-4">
            <p><span class="description-field-title">Asset Serial Number: </span><span>
                {{asset.serial_no}}</span></p>
            <p><span class="description-field-title">Date of Purchase: </span><span>
                {{asset.purchase_date | customDate}}
              </span></p>
            <p><span class="description-field-title">Created on: </span><span>
                {{asset.created | customDate}}</span></p>
            <p><span class="description-field-title">Last Modified: </span><span>
                {{asset.modified | customDate}}</span></p>
          </div>
          <div class="col-md-4">

            <p><span class="description-field-title">Vendor: </span><span>
                {{asset?.vendor?.name}}</span></p>
            <p><span class="description-field-title">Vendor Id: </span><span>
                {{asset.vendor_id}} </span></p>
            <p><span class="description-field-title">Current Condition: </span>
              <span (click)="changeStatus(asset)">
                <span [ngStyle]="{'background': getCurrentCondition(asset.current_condition).color}" class="status m-badge m-badge--wide cusrsorPointer">{{
                  getCurrentCondition(asset.current_condition).text
                  }}</span>
              </span></p>
            <p><span class="description-field-title">Current Status: </span>
              <span (click)="changeStatus(asset)" [ngStyle]="{'background': getCurrentStatus(asset.current_status).color}" class="status m-badge m-badge--wide cusrsorPointer">{{
                getCurrentStatus(asset.current_status).text
                }}</span>
            </p>
          </div>
          <div class="col-md-4">
            <p><span class="description-field-title">Available for Sale: </span><span>
                {{asset.available_for_sale?'Yes':'No' }}
              </span></p>
            <p *ngIf="asset.available_for_sale"><span class="description-field-title">Sell price: </span><span>
              ${{asset.sale_price}}
            </span></p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
