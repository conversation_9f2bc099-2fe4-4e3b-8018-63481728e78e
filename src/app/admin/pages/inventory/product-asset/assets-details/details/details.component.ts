import { Component, OnInit, ElementRef, ViewChild } from "@angular/core";
import { InventoryService } from "../../../inventory-serveice/inventory.service";
import { Asset } from "../../asset-models/asset.models";
import {
  ASSETS_CONDITION,
  ASSETS_STATUS,
  ASSETS_STATUS_CONDITION
} from "../../../../../../globals/_classes/functions";
import { ActivatedRoute } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { StatusDialogBoxComponent } from './../../dialog-box/dialog-box.component';
import { AlertService } from "./../../../../../../modules/alert/alert.service";
import { HttpErrorResponse } from "@angular/common/http";

@Component({
  selector: "app-details",
  templateUrl: "./details.component.html",
  styleUrls: ["./details.component.css"]
})
export class DetailsComponent implements OnInit {
  @ViewChild("hasCusAlert") alertContainer: ElementRef;
  asset: Asset;
  loader: boolean;
  status_condition = ASSETS_STATUS_CONDITION;
  changedStatusConditionedObject;

  constructor(
    private route: ActivatedRoute,
    private alertS: AlertService,
    private modalService: NgbModal,
    private inventoryS: InventoryService) {
    this.getAsset();
  }

  ngOnInit() {}
  getAsset() {
    this.loader = true;
    this.inventoryS.getAssetDetails(this.route.snapshot.params.id).subscribe(res => {
      this.asset = res;
      this.loader = false;
    });
  }

  getCurrentCondition(id) {
    const obj = ASSETS_CONDITION.find(
      item => Number(item.value) === Number(id)
    );
    return obj;
  }
  getCurrentStatus(id) {
    const obj = ASSETS_STATUS.find(item => Number(item.value) === Number(id));
    return obj;
  }

  getLocation(id) {
    const obj = JSON.parse(localStorage.getItem("locations")).find(
      item => Number(item.id) === Number(id)
    );
    return obj? obj.name:"";
  }

  changeStatus(asset) {
    const modalStatus = this.modalService.open(StatusDialogBoxComponent, {
      centered: true
    });
    modalStatus.componentInstance.assetStatus = asset.current_condition;
    modalStatus.componentInstance.status = this.status_condition;
    modalStatus.result.then(
      result => {
        if (result) {
          this.changedStatusConditionedObject = result;
          const assetId = asset.id;
          this.updateStatusCondition(assetId);
        }
      },
      res => { }
    );
  }

  updateStatusCondition(id) {
    if (Object.keys(this.changedStatusConditionedObject).length !== 0) {
      this.loader = true;
      this.inventoryS.updateAssetStatusCondition(this.changedStatusConditionedObject, id).subscribe(
        (res: any) => {
          this.loader = false;
          if (res.status === 'OK') {
            this.asset.current_condition = this.changedStatusConditionedObject.current_condition;
            this.asset.current_status = this.changedStatusConditionedObject.current_status;
          } else {
            this.alertS.error(this.alertContainer, 'Something wrong Please try again !!!', true, 5000);
          }
        }, (err: HttpErrorResponse) => {
          this.loader = false;
          this.alertS.error(
            this.alertContainer,
            "Something wrong Please try again !!!",
            true,
            5000
          );
        }
      );
    }
  }
}
