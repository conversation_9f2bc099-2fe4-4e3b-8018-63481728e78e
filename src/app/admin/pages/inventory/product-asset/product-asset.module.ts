import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { Routes, RouterModule } from "@angular/router";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { Select2AddOptionModule } from "../../../../modules/select2-add-option/select2.module";
import { ProductAssetComponent } from "./product-asset.component";
import { AddAssetComponent } from "./add-asset/add-asset.component";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { ProductAssetsListComponent } from "./product-assets-list/product-assets-list.component";
import { PaginationModule } from "../../../../modules/pagination/pagination.module";
import { EditAssetComponent } from "./edit-asset/edit-asset.component";
import { AssetFilterComponent } from "./product-assets-list/asset-filter/asset-filter.component";
import { InventoryResolveService } from "./../inventory-serveice/inventory-resolve.service";
import { ProductSearchComponent } from './product-search/product-search.component';
import { AddBulkAssetComponent } from './add-bulk-asset/add-bulk-asset.component';
import { AddServiceComponent } from "./add-service/add-service.component";
import { NumberOnlyDirectiveModule } from "../../../../modules/directive/directive.module";
import { StatusDialogBoxModule } from './dialog-box/dialog-box.module';
import { StatusDialogBoxComponent } from './dialog-box/dialog-box.component';
import { DateFormatModule } from "../../../../modules/date-format/date-format-pipe";

const route: Routes = [
  {
    path: "",
    component: ProductAssetComponent,
    children: [
      {
        path: "",
        component: ProductAssetsListComponent,
        children: [
          {
            path: "edit/:id",
            component: AddAssetComponent,
            data: {edit: true}
          },
          {
            path: "add",
            component: AddAssetComponent,
         //   resolve: {'supplier': InventoryResolveService}
          },
          {
            path: "add/bulk-asset",
            component: AddBulkAssetComponent,
         //   resolve: {'supplier': InventoryResolveService}
          },
          {
            path: "add-service/:id",
            component: AddServiceComponent
          }
        ]
      },
      {
        path: "details/:id",
        loadChildren:
          () => import('./assets-details/assets-details.module').then(m => m.ProductAssetDetailsModule)
      }
    ]
  }
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(route),
    FormsModule,
    ReactiveFormsModule,
    Select2AddOptionModule,
    NgbModule,
    PaginationModule,
    NumberOnlyDirectiveModule,
    StatusDialogBoxModule,
    DateFormatModule
  ],
  exports: [RouterModule],
  declarations: [
    EditAssetComponent,
    ProductAssetComponent,
    AddAssetComponent,
    ProductAssetsListComponent,
    AssetFilterComponent,
    ProductSearchComponent,
    AddBulkAssetComponent,
    AddServiceComponent
  ],
  entryComponents: [StatusDialogBoxComponent]
})
export class ProductAssetModule {}
