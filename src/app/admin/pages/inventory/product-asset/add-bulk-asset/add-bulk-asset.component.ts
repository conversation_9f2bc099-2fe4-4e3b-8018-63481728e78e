import {
  Component,
  OnInit,
  AfterViewInit,
  ViewChild,
  ElementRef
} from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { Asset } from "../asset-models/asset.models";
import { Supplier } from "./../../product-models/inventory.models";
import { InventoryService } from "./../../inventory-serveice/inventory.service";
import { AlertService } from "./../../../../../modules/alert/alert.service";
import { SidebarService } from "./../../../sidebar-service/sidebar.service";
import { HttpErrorResponse } from "@angular/common/http";
import { FormGroup, FormBuilder, Validators } from "@angular/forms";
import { ASSETS_STATUS } from "../../../../../globals/_classes/functions";

declare let $: any;

@Component({
  selector: "app-add-bulk-asset",
  templateUrl: "./add-bulk-asset.component.html",
  styleUrls: ["./add-bulk-asset.component.css"]
})
export class AddBulkAssetComponent implements OnInit {
  showDetailsForm = false;
  form: FormGroup;
  loader: boolean;
  assets_status = ASSETS_STATUS;
  supliers: Supplier[] = [];
  inValid: boolean;
  currency:any;
  @ViewChild("hasCusAlert") alertContainer: ElementRef;
  locations = [];
  asset = new Asset();
  constructor(
    private sidebarS: SidebarService,
    private router: Router,
    private inventoryS: InventoryService,
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private alertS: AlertService
  ) {
    this.locations = JSON.parse(localStorage.getItem("locations"));
    this.supliers = this.route.parent.snapshot.data["supplier"].data;
    if (this.supliers) {
      this.supliers = this.supliers;
    }

    this.form = this.fb.group({
      sale_price: [""],
      purchase_cost: [""],
      bulk_size: [""],
      current_status: [""],
      series: ["", Validators.required],
      description: [""],
      vendor_id: [""],
      vendor_serial_no:[""],
      location_id: [this.locations[0].id],
      purchase_date: [""],
      variants_products_id: ["", Validators.required],
      available_for_sale: [false],
      product_id: ["", Validators.required]
    });
  }

  ngOnInit() {
    this.currency = JSON.parse(localStorage.getItem('currency'));
  }

  ngOnChanges() {}
  selectProduct(product) {
    this.asset.product = product;
    //  console.log(product);
    this.form
      .get("variants_products_id")
      .setValue(product.variants_products_id);
    this.form.get("product_id").setValue(product.product_id);
  }
  ngAfterViewInit() {
    this.datePicker();
  }

  orderDateChange() {
    $("#order-date")
      .datepicker()
      .on("changeDate", e => {
        let date = e.date;
        const purchase_date =
          date.getFullYear() +
          "-" +
          (date.getMonth() + 1) +
          "-" +
          date.getDate();
        this.form.get("purchase_date").setValue(purchase_date);
        // this.asset.purchase_date =
        // console.log(this.asset.purchase_date);
      });
  }
  private datePicker() {
    $("#order-date").datepicker({
      todayHighlight: true,
      orientation: "bottom left",
      format: "yyyy-mm-dd",
      templates: {
        leftArrow: '<i class="la la-angle-left"></i>',
        rightArrow: '<i class="la la-angle-right"></i>'
      }
    });
  }

  changed(e: any): void {
    if (e.messag) {
      this.alertS.error(this.alertContainer, e.message, true, 3000);
    } else {
      this.form.get("vendor_id").setValue(Number(e.id));
    }
  }

  submit() {
    //  console.log(this.form.getRawValue());
    let promise: any;
    promise = this.inventoryS.addBulkAsset(this.form.getRawValue()).toPromise();
    promise
      .then(res => {
        //  console.log(res);
        if (res.result.error) {
          this.alertS.error(this.alertContainer, res.result.error, true, 5000);
        } else {
          setTimeout(() => {
            this.closeSidebar();
          }, 3000);
          this.alertS.success(
            this.alertContainer,
            res.result.message,
            true,
            5000
          );
        }
      })
      .catch(err => {
        this.alertS.error(
          this.alertContainer,
          "Something went wrong ! Please try again !!!",
          true,
          5000
        );
      });
  }

  closeSidebar() {
    this.executeAction();
  }

  executeAction() {
    this.sidebarS.removeSidebar();
    $(".native-routing").css("display", "none");
    this.router.navigate(["/admin/inventory/product-asset/"]);
  }
  SelectOption(value) {
    //  console.log(value);
    this.form.get("available_for_sale").setValue(value);
    if (!this.form.get("available_for_sale").value) {
      this.form.get("sale_price").setValue("");
    }
  }

  // focusOutFunction() {
  //   const slNO = this.form.get("serial_no").value;
  //   this.inventoryS.checkAssetSLNo(slNO).subscribe(res => {
  //     this.inValid = res;
  //   });
  // }

  getLocation(id) {
    const obj = JSON.parse(localStorage.getItem("locations")).find(
      item => Number(item.id) === Number(id)
    );
    return obj.name;
  }
  IsSelectDetails(value) {
    this.showDetailsForm = value;
    if (!this.showDetailsForm) {
      this.form.get("purchase_date").setValue("");
      this.form.get("vendor_id").setValue("");
      this.form.get("purchase_cost").setValue("");
      this.form.get("sale_price").setValue("");
      this.form.get("description").setValue("");
      this.form.get("available_for_sale").setValue(false);
      this.form.get("current_status").setValue("");
    }else {
      this.form.get("current_status").setValue(this.assets_status[0].value);
     
    }
  }
}
