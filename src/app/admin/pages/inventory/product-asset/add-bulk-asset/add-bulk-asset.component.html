<div class="custom-alert" #hasCusAlert></div>

<div class="m-quick-sidebar--skin-light inventory-sidebar">
  <div class="m-content product-sidebar-description">
    <h4 style="padding: 20px 0;">Add Bulk Asset</h4>
    <div *ngIf="loader" class="table-load m-loader m-loader--brand"></div>
    <div>

      <form [formGroup]="form" (submit)="submit()"
        class="m-form m-form--fit m-form--label-align-right ng-untouched ng-pristine ng-valid" novalidate="">
        <div class="row">
          <div class="form-group col-md-6">
              <label for="title"> Search Product*</label>
            <app-product-search (onSelectedProduct)="selectProduct($event)"></app-product-search>
          </div>
          <div class="form-group col-md-6">
            <label for="merchantID">Select Location* </label>
            <select formControlName="location_id" class="form-control m-input ng-untouched ng-pristine ng-valid">
              <option [value]="location.id" *ngFor="let location of locations">{{location.name}}</option>

            </select>
          </div>
          <div class="form-group col-md-6">
            <label for="title"> Series Serial Number* </label>
            <input  formControlName="series" required type="text"
              class="form-control m-input sl_no" placeholder="#RN-001">
          </div>
          <div class="form-group col-md-6">
            <label for="title"> Assets Quantity* </label>
            <input formControlName="bulk_size" numberOnly required type="text" class="form-control m-input sl_no">
          </div>
          <div class="col-md-12">
            <label for="title"> Do you want to add asset details ? </label>
            <div class="m-radio-inline" style="margin-top: 10px;">
              <label class="m-radio">
                <input type="radio" (click)="IsSelectDetails(true)" [checked]="showDetailsForm"
                  name="example_4" value="1"> Yes
                <span></span>
              </label>
              <label class="m-radio">
                <input type="radio" (click)="IsSelectDetails(false)" [checked]="!showDetailsForm"
                  name="example_4" value="2"> No
                <span></span>
              </label>
            </div>
          </div>
        </div>

        <div class="row mt-4" *ngIf="showDetailsForm">
          <div class="form-group col-md-6">
            <label for="title"> Asset Description </label>
            <input formControlName="description" autocomplete="off"
              class="form-control m-input ng-untouched ng-pristine ng-valid" id="title" name="name"
              placeholder="Item Description" type="text">
          </div>
          <div class="form-group col-md-6">
            <div class="form-group m-form__group">
              <label for="order-date"> Date of Purchase </label>
              <div class="input-group date">
                <input [value]="form.get('purchase_date').value" type="text" class="form-control m-input"
                  (click)="orderDateChange()" placeholder="-Date of Purchase-" id="order-date" readonly />
                <div class="input-group-append">
                  <span class="input-group-text">
                    <i class="la la-calendar-check-o"></i>
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div class="form-group col-md-6">
            <label for="title"> Purchase Cost </label>
            <div class="input-group m-input-group">
              <div class="input-group-prepend">
                <span class="input-group-text" id="table-cost-addon">
                  {{ currency.symbol? currency.symbol : '$'}}
                </span>
              </div>
              <input formControlName="purchase_cost" autocomplete="off"
              class="form-control m-input ng-untouched ng-pristine ng-valid" placeholder="Purchase Cost" type="text">
              <div class="input-group-append">
                <small class="input-group-text" id="table-cost-addon">
                  {{ currency.code? currency.code : 'USD'}}
                </small>
              </div>
            </div>

          </div>
          <div class="form-group m-form__group col-md-6">
            <label for="Merchant">
              Manufacturer
            </label>
            <select2-add-option [data]="supliers" [prop]="'name'" [url]="'suppliers'" [domId]="'suppliers'"
              [placeholder]="'Manufacturer'" (changeValue)="changed($event)"></select2-add-option>
          </div>
          <div class="form-group col-md-6">
            <label for="Merchant">
              Manufacturer's serial number
            </label>
            <input  formControlName="vendor_serial_no"  type="text"
            class="form-control m-input " placeholder="Manufacturer's serial number">
          </div>
          <div class="form-group col-md-6">
            <label for="merchantID"> Current Status </label>
            <select formControlName="current_status" class="form-control m-input ng-untouched ng-pristine ng-valid"
              name="status">
              <option [value]="item.value" *ngFor="let item of assets_status">{{item.text}}</option>

            </select>
          </div>
          <div class="form-group col-md-6">
            <label for="title"> Available for Sale </label>
            <div class="m-radio-inline" style="margin-top: 10px;">
              <label class="m-radio">
                <input type="radio" (click)="SelectOption(true)" [checked]="form.get('available_for_sale').value"
                  name="example_3" value="1"> Yes
                <span></span>
              </label>
              <label class="m-radio">
                <input type="radio" (click)="SelectOption(false)" [checked]="!form.get('available_for_sale').value"
                  name="example_3" value="2"> No
                <span></span>
              </label>
            </div>
          </div>
          <div class="form-group m-form__group col-md-12" *ngIf="form.get('available_for_sale').value">
            <label for="sellPrice">
              Sell price
            </label>
            <div class="input-group m-input-group">
              <div class="input-group-prepend">
                <span class="input-group-text" id="table-cost-addon">
                  {{ currency.symbol? currency.symbol : '$'}}
                </span>
              </div>
              <input type="text" id="sellPrice" class="form-control m-input" placeholder="Sell price" name="sale_price"
              formControlName="sale_price">
              <div class="input-group-append">
                <small class="input-group-text" id="table-cost-addon">
                  {{ currency.code? currency.code : 'USD'}}
                </small>
              </div>
            </div>

          </div>
        </div>
        <div class="row mt-4 ">
          <div class="form-group col-12">
            <button class="btn btn-brand" [disabled]="!form.valid" type="submit"><i class="fa fa-save"></i><span
                style="padding-left:10px;">{{'Submit'}}</span></button>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
