import {
  Component,
  OnInit,
  AfterViewInit,
  ViewChild,
  ElementRef
} from "@angular/core";
import { ActivatedRoute, Router, NavigationEnd } from "@angular/router";
import { Asset } from "../asset-models/asset.models";
import { Supplier } from "./../../product-models/inventory.models";
import { InventoryService } from "./../../inventory-serveice/inventory.service";
import { AlertService } from "./../../../../../modules/alert/alert.service";
import { SidebarService } from "./../../../sidebar-service/sidebar.service";
import { Observable } from "rxjs/internal/Observable";
import {
  debounceTime,
  distinctUntilChanged,
  tap,
  switchMap,
  map,
  retry,
  catchError
} from "rxjs/operators";
import { CartService } from "./../../../../cart-service/cart.service";
import { HttpErrorResponse } from "@angular/common/http";
import { FormGroup, FormBuilder, Validators } from "@angular/forms";
import {
  ASSETS_STATUS,
  FORMATE_DATE,
  ASSETS_CONDITION
} from "../../../../../globals/_classes/functions";

declare let $: any;

@Component({
  selector: "app-add-asset",
  templateUrl: "./add-asset.component.html",
  styleUrls: ["./add-asset.component.css"]
})
export class AddAssetComponent implements OnInit, AfterViewInit {
  form: FormGroup;
  loader: boolean;
  assets_status = ASSETS_STATUS;
  assets_conditions = ASSETS_CONDITION;
  supliers: Supplier[] = [];
  inValid: boolean;
  editMode: boolean;
  @ViewChild("hasCusAlert") alertContainer: ElementRef;
  locations = [];
  asset = new Asset();
  currency: any;
  constructor(
    private sidebarS: SidebarService,
    private router: Router,
    private inventoryS: InventoryService,
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private alertS: AlertService
  ) {
    this.locations = JSON.parse(localStorage.getItem("locations"));
    this.supliers = this.route.parent.snapshot.data["supplier"].data;
    if (this.supliers) {
      this.supliers = this.supliers;
    }

    this.form = this.fb.group({
      sale_price: [""],
      comments: [""],
      purchase_cost: [""],
      current_status: [this.assets_status[0].value],
      current_condition: [this.assets_conditions[0].value],
      serial_no: ["", Validators.required],
      description: [""],
      vendor_id: [""],
      location_id: [this.locations[0].id],
      purchase_date: [""],
      variants_products_id: ["", Validators.required],
      available_for_sale: [false],
      product_id: ["", Validators.required],
      vendor_serial_no:[""]
    });

    this.getAsset();
  }

  ngOnInit() {
    this.currency = JSON.parse(localStorage.getItem("currency"));
  }

  ngOnChanges() {}
  selectProduct(product) {
    this.asset.product = product;
    //  console.log(product);
    this.form
      .get("variants_products_id")
      .setValue(product.variants_products_id);
    this.form.get("product_id").setValue(product.product_id);
  }
  ngAfterViewInit() {
    this.datePicker();
  }

  orderDateChange() {
    $("#order-date")
      .datepicker()
      .on("changeDate", e => {
        let date = e.date;
        const purchase_date =
          date.getFullYear() +
          "-" +
          (date.getMonth() + 1) +
          "-" +
          date.getDate();
        this.form.get("purchase_date").setValue(purchase_date);
        // this.asset.purchase_date =
        // console.log(this.asset.purchase_date);
      });
  }
  private datePicker() {
    $("#order-date").datepicker({
      todayHighlight: true,
      orientation: "bottom left",
      format: "yyyy-mm-dd",
      templates: {
        leftArrow: '<i class="la la-angle-left"></i>',
        rightArrow: '<i class="la la-angle-right"></i>'
      }
    });
  }

  changed(e: any): void {
    if (e.messag) {
      this.alertS.error(this.alertContainer, e.message, true, 3000);
    } else {
      this.form.get("vendor_id").setValue(Number(e.id));
    }
  }

  submit() {
    //  console.log(this.form.getRawValue());
    let promise: any;
    if (this.editMode) {
      promise = this.inventoryS
        .updateAsset(this.form.getRawValue(), this.route.snapshot.params.id)
        .toPromise();
    } else {
      promise = this.inventoryS.addAsset(this.form.getRawValue()).toPromise();
    }
    promise
      .then(res => {
        //  console.log(res);
        if (res.result.error) {
          this.alertS.error(this.alertContainer, res.result.error, true, 5000);
        } else {
          setTimeout(() => {
            this.closeSidebar();
          }, 3000);
          this.alertS.success(
            this.alertContainer,
            res.result.message,
            true,
            5000
          );
        }
      })
      .catch(err => {
        this.alertS.error(
          this.alertContainer,
          "Something went wrong ! Please try again !!!",
          true,
          5000
        );
      });
  }

  closeSidebar() {
    this.executeAction();
  }

  executeAction() {
    this.sidebarS.removeSidebar();
    $(".native-routing").css("display", "none");
    if (sessionStorage.getItem("xa_params_route")) {
      this.router.navigate(["/admin/inventory/product-asset/"], {
        relativeTo: this.route,
        queryParams: { query: sessionStorage.getItem("xa_params_route") }
      });
    } else {
      this.router.navigate(["/admin/inventory/product-asset/"]);
    }
  }
  SelectOption(value) {
    //  console.log(value);
    this.form.get("available_for_sale").setValue(value);
    if (!this.form.get("available_for_sale").value) {
      this.form.get("sale_price").setValue("");
    }
  }

  focusOutFunction() {
    const slNO = this.form.get("serial_no").value;
    this.inventoryS.checkAssetSLNo(slNO).subscribe(res => {
      this.inValid = res;
    });
  }

  getAsset() {
    // Edit asset function
    if (this.route.snapshot.data.edit && this.route.snapshot.params.id) {
      this.editMode = true;
      this.loader = true;
      this.inventoryS.getAssetById(this.route.snapshot.params.id).subscribe(
        (res: any) => {
          this.loader = false;
          this.asset = res.result.data;
          // this.asset.serial_no
          this.form.patchValue(res.result.data);
          const purchase_date = res.result.data.purchase_date
            ? res.result.data.purchase_date.split("T")[0]
            : "";
          this.form.get("purchase_date").setValue(purchase_date);
        },
        (err: HttpErrorResponse) => {
          this.alertS.error(
            this.alertContainer,
            "Something wrong Please try again !!!",
            true,
            5000
          );
        }
      );
    }
  }

  getLocation(id) {
    const obj = JSON.parse(localStorage.getItem("locations")).find(
      item => Number(item.id) === Number(id)
    );
    return obj.name;
  }
}
