import { Component, OnInit, AfterViewInit, ViewChild, ElementRef} from '@angular/core';
import { AssetService, Asset } from "./../asset-models/asset.models";
import { InventoryService } from './../../inventory-serveice/inventory.service';
import { HttpErrorResponse } from '@angular/common/http';
import { ActivatedRoute } from '@angular/router';
import { AlertService } from "./../../../../../modules/alert/alert.service";

declare let $: any;

@Component({
  selector: 'app-add-service',
  templateUrl: './add-service.component.html',
  styleUrls: ['./add-service.component.css']
})
export class AddServiceComponent implements OnInit, AfterViewInit {

  assetService: AssetService = new AssetService();
  serviceList = [];
  loader: boolean;
  createService: boolean;
  editMode: boolean = false;
  currency: any;
  @ViewChild("hasCusAlert") alertContainer: ElementRef;

  constructor(
    private inventoryS: InventoryService,
    private route: ActivatedRoute,
    private alertS: AlertService
  ) {
   }

  ngOnInit() {
    this.assetService.asset_id = +this.route.snapshot.params.id;
    this.createService = false;
    this.loader = true;
    this.getServiceList();
    this.currency = JSON.parse(localStorage.getItem("currency"));
  }

  getServiceList() {
    this.inventoryS.getAssetService(this.assetService.asset_id).subscribe(
      (res: any) => {
        this.serviceList = res.result.data;
        console.log(this.serviceList);
        this.loader = false;
      }, (err: HttpErrorResponse) => {
        console.log(err);
      }
    );
  }

  ngDoCheck(){
    this.datePicker();
  }

  ngAfterViewInit() {
    this.datePicker();
  }

  showAssetService() {
    this.createService = true;
  }

  serviceDateChange() {
    $('#service-date').datepicker().on('changeDate', (e) => {
      let date = e.date;
      this.assetService.start_date = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();
    });
  }

  returnDateChange() {
    $('#return-date').datepicker().on('changeDate', (e) => {
      let date = e.date;
      this.assetService.return_date = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();
    });
  }

  private datePicker() {
    $('#service-date').datepicker({
      todayHighlight: true,
      orientation: "bottom left",
      format: 'yyyy-mm-dd',
      templates: {
          leftArrow: '<i class="la la-angle-left"></i>',
          rightArrow: '<i class="la la-angle-right"></i>'
      }
    });
    $('#return-date').datepicker({
      todayHighlight: true,
      orientation: "bottom right",
      format: 'yyyy-mm-dd',
      templates: {
          leftArrow: '<i class="la la-angle-left"></i>',
          rightArrow: '<i class="la la-angle-right"></i>'
      }
    });
  }

  editAsset(id) {
    this.loader = true;
    this.showAssetService();
    this.editMode = true;
    this.inventoryS.getAssetServiceById(id).subscribe(
      (res: any) => {
        this.loader = false;
        this.assetService = res.result.data;
        console.log(this.assetService);
      }, (err: HttpErrorResponse) => {
        this.loader = false;
        console.log(err);
      }
    );
  }

  cancel() {
    this.createService = false;
    this.editMode = false;
    this.assetService.service_charge = 0;
    // this.assetService = {} as AssetService;
  }

  addService() {
    this.loader = true;
    if (this.editMode === false) {
      if (!this.assetService.start_date) {
        const date = new Date();
        this.assetService.start_date = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();
      }
      console.log(this.assetService);
      this.inventoryS.addAssetService(this.assetService).subscribe(
        (res: any) => {
          this.createService = false;
          this.getServiceList();
          this.loader = false;
          console.log(res);
          if (res.result.error) {
            this.alertS.error(this.alertContainer, res.result.error, true, 5000);
          } else {
            this.alertS.success(
              this.alertContainer,
              res.result.message,
              true,
              5000
            );
          }
        }, (err: HttpErrorResponse) => {
          this.loader = false;
          this.alertS.error(
            this.alertContainer,
            "Something wrong Please try again !!!",
            true,
            5000
          );
        }
      );
    } else {
      console.log(this.assetService);
      this.inventoryS.updateAssetService(this.assetService, this.assetService.id).subscribe(
        (res: any) => {
          this.createService = false;
          this.getServiceList();
          this.loader = false;
          console.log(res);
          if (res.result.error) {
            this.alertS.error(this.alertContainer, res.result.error, true, 5000);
          } else {
            this.alertS.success(
              this.alertContainer,
              res.result.message,
              true,
              5000
            );
          }
        }, (err: HttpErrorResponse) => {
          this.loader = false;
          this.alertS.error(
            this.alertContainer,
            "Something wrong Please try again !!!",
            true,
            5000
          );
        }
      );
    }
  }

}
