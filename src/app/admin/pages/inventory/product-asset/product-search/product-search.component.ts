import { Component, OnInit, Output, EventEmitter } from "@angular/core";
import { Observable } from "rxjs/internal/Observable";
import {
  debounceTime,
  distinctUntilChanged,
  tap,
  switchMap,
  map,
  retry,
  catchError
} from "rxjs/operators";
import { InventoryService } from "../../inventory-serveice/inventory.service";
@Component({
  selector: "app-product-search",
  templateUrl: "./product-search.component.html",
  styleUrls: ["./product-search.component.css"]
})
export class ProductSearchComponent implements OnInit {
  productList: any[];
  selectedValue: string;
  locationList: any[];
  showSellPriceInput = false;
  loader = false;
  inValid: boolean;
  // autocomplete search
  fromInventory = true;
  searchData: any[] = [];
  selectedProduct: string;
  searchText: string;
  params: any;
  @Output() onSelectedProduct = new EventEmitter();
  constructor(private inventoryS: InventoryService) {}

  ngOnInit() {}

  search = (text$: Observable<string>) => {
    return text$.pipe(
      debounceTime(700),
      distinctUntilChanged(),
      tap(() => {
        $(".admin-asset-search").click();
        this.searchData = [];
        this.loader = true;
      }),
      switchMap(term => {
        return this.getProduct(term);
      }),
      tap(() => {
        this.loader = false;
        setTimeout(() => {
          $(".admin-cart .dropdown-menu.show .dropdown-item").unbind();
          const perId = $(".admin-cart .dropdown-menu.show").attr("id");
          var c =
            document.getElementById(perId) &&
            document.getElementById(perId).children
              ? document.getElementById(perId).children
              : [];
          // console.log(c)
          for (let j = 0; j < c.length; j++) {
            var id = c[j].getAttribute("id");
            var original = document.getElementById(id);
            var replacement = document.createElement("div");
            for (var i = 0, l = original.attributes.length; i < l; ++i) {
              var nodeName = original.attributes[i].name;
              var nodeValue = original.attributes[i].value;
              if (nodeName != "type") {
                replacement.setAttribute(nodeName, nodeValue);
              }
            }
            replacement.innerHTML = `<div class='inner-serch' data-item='${
              this.searchData[j].id
            }'>${this.searchData[j].name}</div>`;
            original.parentNode.replaceChild(replacement, original);
          }
          $(".inner-serch").click(e => {
            e.preventDefault();
            let id = $(e.target).data("item");
            const data = this.searchData.find(f => f.id === parseInt(id));
            this.selectedProduct = data.name;
            //     this.setProduct(data.id, data.variants_products_id);
            // const product = {
            //   product_id: data.id,variants_products_id: data.variants_products_id }
            // this.asset.product_id = data.id;
            // this.asset.variants_products_id = data.variants_products_id;
            (<HTMLInputElement>(
              document.getElementById("typeahead-http")
            )).value = "hello";
          });
        }, 100);
      })
    );
  };

  setProduct(data) {
    //  console.log(vp_id);
    const product = {
      name: data.name,
      variants_products_id: data.variants_products_id,
      product_id: data.id,
      variant_chain: data.variant_chain ? data.variant_chain : ""
    };
    this.onSelectedProduct.emit(product);
  }
  getProduct(params): any {
    this.loader = true;
    if (!params && params === "") {
      this.loader = false;
      return [];
    }
    const search = "search=" + params;
    return this.inventoryS.searchProduct(search).pipe(
      map(res => {
        this.searchData = res.result.data;
        //  console.log(this.searchData);
        this.loader = false;
        // this.searchText = null;
        setTimeout(() => {
          $(".home .dropdown-menu.show button").hover(
            () => {
              // this.searchText = $('.home input').val();
            },
            () => {
              // this.searchText = $('.home input').val();
            }
          );
        }, 100);
        return res.result.data.map(r => r);
      }),
      retry(3),
      catchError(() => {
        this.loader = false;
        return [];
      })
    );
  }

  formatter = x => x.name;
}
