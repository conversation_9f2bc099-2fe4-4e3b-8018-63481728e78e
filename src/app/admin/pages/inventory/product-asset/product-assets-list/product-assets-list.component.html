<div class="custom-alert" #hasCusAlert></div>
<div class="m-grid__item m-grid__item--fluid m-wrapper">
  <div class="m-subheader product-section-list">
    <div class="d-flex align-items-center">
      <div class="mr-auto">
        <h3 class="m-subheader__title m-subheader__title--separator"> Product Asset </h3>
        <ul class="m-subheader__breadcrumbs m-nav m-nav--inline">
          <li class="m-nav__item m-nav__item--home"><a class="m-nav__link m-nav__link--icon" routerLink="/admin"><i
                class="m-nav__link-icon la la-home"></i></a></li>
          <li class="m-nav__separator"><i class="fa fa-angle-right"></i></li>
          <li class="m-nav__item"><a class="m-nav__link"><span class="m-nav__link-text"> Product Asset </span></a></li>
        </ul>
      </div>
    </div>
  </div>


  <div class="m-content animated fadeIn">

    <div class="stocklist-body mb-0">
      <app-asset-filter (loadList)="filterList($event)"></app-asset-filter>
    </div>

    <div class="m-portlet m-portlet--mobile product-list">
        <div class="m-portlet__head assetitems-head">
            <div class="m-portlet__head-caption">
              <div class="m-portlet__head-title m-product-title">
                  <h3 class="m-portlet__head-text colorPurpel">
                      Asset List
                  </h3>
                <div class="add-list-btn text-right assetlist-btn-area">
                  <a class="btn btn-brand btn-sm" id="asset-addnew-btn" style="color:#fff" (click)="openSidebar('/admin/inventory/product-asset/add')">
                    <i class="fa fa-plus"></i> Add Asset
                  </a>
                  <a class="btn btn-brand btn-sm ml-4" id="asset-addnew-btn" style="color:#fff" (click)="openSidebar('/admin/inventory/product-asset/add/bulk-asset')">
                    <i class="fa fa-plus"></i> Add Bulk  Asset
                  </a>
                </div>
              </div>
            </div>
        </div>
        <div class="m-portlet__body">
          <div class="m-section">
            <div class="m-section__content price-table" style="position: relative;">
                <div *ngIf="loader" class="table-load m-loader m-loader--brand"></div>
              <div class="table-responsive" style="margin-bottom: 10px;">
                <table class="table table-hover">
                  <thead>
                    <tr>
                      <th>Product</th>
                      <th>Asset Details</th>
                      <!-- <th>Date of Purchase </th>
                      <th>Created on</th> -->
                      <th>Last Modified </th>
                      <th>Vendor</th>
                      <th>Current Condition </th>
                      <th>Current Status </th>
                      <!-- <th>Available for Sale </th> -->
                      <th>Actions </th>
                    </tr>
                  </thead>
                  <tbody *ngIf="!assets.length">
                      <tr *ngIf="!loader">
                        <td colspan="11">
                          <h4 class="text-center">No Asset Found</h4>
                        </td>
                      </tr>
                    </tbody>
                  <tbody >
                    <tr class="even-tr" *ngFor="let asset of assets ">
                      <td style=" width: 255px;">
                        <a [routerLink]="['./','details',asset.id]"><b>{{asset?.product?.name}}</b></a>
                        <p style="margin-bottom:0;" *ngIf="asset.product.variant_chain==='Unassigned: Unassigned'? '': asset.product.variant_chain">{{asset.product?.variant_chain}}</p>
                        <!-- <p>{{ getLocation(asset?.location_id)}}</p> -->
                      </td>
                      <td>{{asset.serial_no}}
                        <br/>
                        {{asset.description}}
                      </td>
                      <!-- <td>{{asset.purchase_date | date }}</td> -->
                      <!-- <td>{{asset.created | date }}</td> -->
                      <td>{{asset.modified | customDate }}</td>
                      <td>{{asset?.vendor?.name }}
                        <br/>
                        {{asset?.vendor_serial_no}}
                      </td>
  
                      <td>
                        <span (click)="changeStatus(asset)" [ngStyle]="{'background': getCurrentCondition(asset.current_condition).color}" class="status m-badge m-badge--wide cusrsorPointer">{{
                          getCurrentCondition(asset.current_condition).text
                          }}</span>
                      </td>
                      <td>
                        <span (click)="changeStatus(asset)" [ngStyle]="{'background': getCurrentStatus(asset.current_status).color}" class="status m-badge m-badge--wide cusrsorPointer">{{
                          getCurrentStatus(asset.current_status).text
                          }}</span>
                      </td>
                      <!-- <td>{{asset.available_for_sale?'Yes':'No' }}</td> -->
                      <td>
                        <a (click)="assetClone(asset.id)" class="m-portlet__nav-link btn m-btn m-btn--hover-accent m-btn--icon m-btn--icon-only m-btn--pill"
                          title="clone"><i class="fa fa-clone"></i></a>
                        <a (click)="openSidebar('/admin/inventory/product-asset/edit/'+asset.id)" class="m-portlet__nav-link btn m-btn m-btn--hover-accent m-btn--icon m-btn--icon-only m-btn--pill"
                          title="Edit Product" id="asset-edit-btn"><i class="fa fa-edit"></i></a>
                        <a (click)="assetRetire(asset.id)" class="m-portlet__nav-link btn m-btn m-btn--hover-accent m-btn--icon m-btn--icon-only m-btn--pill"
                          title="retire an asset"><i class="la la-registered"></i></a>
                        <a (click)="openSidebar('/admin/inventory/product-asset/add-service/'+asset.id)" class="m-portlet__nav-link btn m-btn m-btn--hover-accent m-btn--icon m-btn--icon-only m-btn--pill"
                          title="Service"><i class="fa fa-cog"></i></a>
                        <a (click)="permanentDeleteAsset(asset.id)" class="m-portlet__nav-link btn m-btn m-btn--hover-danger m-btn--icon m-btn--icon-only m-btn--pill"
                          title="Archive Product"><i class="fa fa-trash"></i></a>
                      </td>
                    </tr>
                  </tbody>
  
                </table>
              </div>
              <boot-pagination [totalSize]="pagi.total" [page]="pagi.page" [listSize]="pagi.limit" [pagelimit]="pagi.limit"
              (pageChange)="reloadTable($event)"></boot-pagination>
            </div>
          </div>
        </div>
      </div>

  </div>

</div>
<!-- end:: Body -->


<div class="native-routing animated"  style="display:none">
    <button class="close-sidebar btn btn-sm btn-brand">
      <i class="fa fa-chevron-right"></i>
    </button>
    <span class="close-sidebar-upper">
      <i class="la la-close"></i>
    </span>
    <div class="native-routing-container" >
      <router-outlet></router-outlet>
    </div>

  </div>
