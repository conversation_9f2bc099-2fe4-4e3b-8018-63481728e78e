import {
  <PERSON>mpo<PERSON>,
  OnInit,
  HostListener,
  AfterViewInit,
  ElementRef,
  ViewChild
} from "@angular/core";
import { Asset } from "../asset-models/asset.models";
import { InventoryService } from "../../inventory-serveice/inventory.service";
import {
  ASSETS_CONDITION,
  ASSETS_STATUS,
  ASSETS_STATUS_CONDITION
} from "../../../../../globals/_classes/functions";
import { Pagi } from "../../../../../modules/pagination/pagi.model";
import { SidebarService } from "../../../sidebar-service/sidebar.service";
import { FormGroup, FormBuilder } from "@angular/forms";
import {
  Router,
  ActivatedRoute,
  NavigationEnd,
  ActivationEnd
} from "@angular/router";
import { AlertService } from "../../../../../modules/alert/alert.service";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { DialogBoxComponent } from "./../../../../../modules/dialog-box/dialog-box.component";
import { StatusDialogBoxComponent } from './../dialog-box/dialog-box.component';
import { HttpErrorResponse } from "@angular/common/http";

@Component({
  selector: "app-product-assets-list",
  templateUrl: "./product-assets-list.component.html",
  styleUrls: ["./product-assets-list.component.css"]
})
export class ProductAssetsListComponent implements OnInit, AfterViewInit {
  assets: Asset[] = [];
  @ViewChild("hasCusAlert") alertContainer: ElementRef;
  pagi: Pagi = { page: 1, limit: 20, total: 0 };
  filter;
  loader;
  sideBaropen: boolean;
  form: FormGroup;
  opened = false;
  assets_status = ASSETS_STATUS;
  current_condition = ASSETS_CONDITION;
  status_condition = ASSETS_STATUS_CONDITION;
  changedStatusConditionedObject;
  searchQuery;

  constructor(
    private alertS: AlertService,
    private route: ActivatedRoute,
    private router: Router,
    private fb: FormBuilder,
    private sidebarS: SidebarService,
    private inventoryS: InventoryService,
    private modalService: NgbModal
  ) {
    this.form = this.fb.group({
      searchText: [""]
    });
    this.checkRoute();
  }

  // @HostListener("window:resize", ["$event"])
  // onResize(event) {
  //   if (this.sideBaropen) {
  //     $(".native-routing").css("display", "block");
  //     this.sidebarS.openSidebar();
  //   }
  // }

  changeStatus(asset) {
    const modalStatus = this.modalService.open(StatusDialogBoxComponent, {
      centered: true
    });
    modalStatus.componentInstance.assetStatus = asset.current_condition;
    modalStatus.componentInstance.status = this.status_condition;
    modalStatus.result.then(
      result => {
        if (result) {
          this.changedStatusConditionedObject = result;
          const assetId = asset.id;
          this.updateStatusCondition(assetId);
        }
      },
      res => { }
    );
  }

  updateStatusCondition(id) {
    if (Object.keys(this.changedStatusConditionedObject).length !== 0) {
      this.loader = true;
      this.inventoryS.updateAssetStatusCondition(this.changedStatusConditionedObject, id).subscribe(
        (res: any) => {
          this.loader = false;
          if (res.status === 'OK') {
            const selectedAsset = this.assets.find(asset => id === asset.id);
            selectedAsset.current_condition = this.changedStatusConditionedObject.current_condition;
            selectedAsset.current_status = this.changedStatusConditionedObject.current_status;
          } else {
            this.alertS.error(this.alertContainer, 'Something wrong Please try again !!!', true, 5000);
          }
        }, (err: HttpErrorResponse) => {
          this.loader = false;
          this.alertS.error(
            this.alertContainer,
            "Something wrong Please try again !!!",
            true,
            5000
          );
        }
      );
    }
  }

  submit() {
    let query = "";
    const obj = {
      page_no: this.pagi.page,
      limit: this.pagi.limit,
      search: this.form.get("searchText").value
    };
    for (let c in obj) {
      query += `${c}=${obj[c]}&`;
    }
    this.getAssets(query);
  }
  getAssets(filter?) {
    if (filter) {
      this.filter = filter;
      this.router.navigate(["."], {
        relativeTo: this.route,
        queryParams: { query: filter }
      });
    }
    this.loadData();
  }

  loadData() {
    this.loader = true;
    if (this.filter) {
      sessionStorage.setItem("xa_params_route", this.filter);
    }
    this.inventoryS.getProductAssets(this.filter, false).subscribe(res => {
      this.assets = res.data && res.data.length ? res.data : [];
      // console.log(this.assets);
      // this.pagi.limit = res.limit;
      // this.pagi.page = res.page_no;
      this.pagi.total = res.total;
      this.loader = false;
    });
  }

  ngOnInit() {
    this.loadData();
    sessionStorage.removeItem("xa_params_route");
    this.router.events.subscribe(event => {
      if (event instanceof NavigationEnd) {
        const url = String(event.urlAfterRedirects);
        if (url.includes("/product-asset")) {
          const obj = this.route.snapshot.queryParams;
          if (obj && obj.hasOwnProperty("query")) {
            this.filter = obj.query;
          }
          this.loadData();
        }
      }
    });
  }

  getCurrentCondition(id) {
    const obj = ASSETS_CONDITION.find(
      item => Number(item.value) === Number(id)
    );
    if (obj) {
      return obj;
    }
  }
  getCurrentStatus(id) {
    const obj = ASSETS_STATUS.find(item => Number(item.value) === Number(id));
    return obj;
  }
  reloadTable(e, filter?) {
    let query = "";
    this.pagi.page = e.page;
    const obj = {
      page_no: this.pagi.page,
      limit: this.pagi.limit,
      param: filter ? filter : null
    };

    if (this.pagi.limit != e.limit) {
      this.pagi.page =
        Math.ceil(this.pagi.total / e.limit) <= this.pagi.page
          ? Math.ceil(this.pagi.total / e.limit)
          : this.pagi.page;
      this.pagi.limit = e.limit;
      obj.limit = e.limit;
      obj.page_no = this.pagi.page;
    }
    for (let c in obj) {
      if (obj[c]) {
        query += `${c}=${obj[c]}&`;
      }
    }

    if(this.searchQuery){
      query = this.searchQuery + query;
    }
    this.getAssets(query);
  }

  ngAfterViewInit() {
    //window.scrollTo(0, 0);
    this.closeEdit();
  }

  filterList(e) {
    /// console.log(e)
    this.searchQuery=e;
    let query = "";
    const obj = {
      page_no: this.pagi.page,
      limit: this.pagi.limit
      // param: e
    };
    for (let c in obj) {
      query += `${c}=${obj[c]}&`;
    }
    query = e + query;
    //  console.log(query)

    this.getAssets(query);
  }

  openSidebar(route) {
    $(".native-routing").css("display", "block");
    this.router.navigate([route], {
      relativeTo: this.route
    });
  }

  private checkRoute() {
    //  console.log('cal')
    this.router.events.subscribe(e => {
      if (e instanceof NavigationEnd) {
        const url = String(e.urlAfterRedirects);
        if (
          url.includes("/add") ||
          url.includes("/add/") ||
          url.includes("/edit/")
        ) {
          $(".native-routing").css("display", "block");
          this.sidebarS.openSidebar();
          this.sideBaropen = true;
        } else {

          this.sidebarS.removeSidebar();
          this.sideBaropen = false;
         $(".native-routing").css("display", "none");
        }
      }
    });
  }

  private closeEdit() {
    $(".close-sidebar").click(e => {
      e.preventDefault();
      this.close();
    });
    $(".close-sidebar-upper").click(e => {
      e.preventDefault();
      this.close();
    });
  }

  private close() {
    this.sideBaropen = null;
    this.sidebarS.removeSidebar();
    if (this.filter) {
      this.router.navigate(["."], {
        relativeTo: this.route,
        queryParams: { query: this.filter }
      });
    } else {
      this.router.navigate(["/admin/inventory/product-asset/"]);
    }

    $(".native-routing").css("display", "none");
  }

  getLocation(id) {
    const obj = JSON.parse(localStorage.getItem("locations")).find(
      item => Number(item.id) === Number(id)
    );
    return obj ? obj.name : "";
  }

  deleteDialog(message) {
    const modalRef = this.modalService.open(DialogBoxComponent, {
      centered: true,
      size: "sm",
      windowClass: "animated fadeIn"
    });
    modalRef.componentInstance.massage = message;
    return modalRef.result;
  }

  permanentDeleteAsset(id) {
    this.deleteDialog("Are you sure you want to permanently delete?").then(
      result => {
        if (result) {
          this.delete(id);
        }
      },
      res => {
        console.log(res);
      }
    );
  }

  assetClone(id: any) {
    this.loader = true;
    this.inventoryS.cloneAssetById(id).subscribe(
      (res: any) => {
        this.loader = false;
        if (res.status === "OK") {
          this.getAssets();
          this.alertS.success(
            this.alertContainer,
            res.result.message,
            true,
            5000
          );
        } else {
          this.alertS.error(this.alertContainer, res.result.error, true, 5000);
        }
      },
      (err: HttpErrorResponse) => {
        this.loader = false;
        this.alertS.error(
          this.alertContainer,
          "Something wrong Please try again !!!",
          true,
          5000
        );
      }
    );
  }

  assetRetire(id: any) {
    this.loader = true;
    this.inventoryS.retireAssetById(id).subscribe(
      (res: any) => {
        this.loader = false;
        if (res.status === "OK") {
          // this.pagi.page ? this.reloadTable(this.pagi.page) : this.getAssets();
          this.getAssets();
          this.alertS.success(
            this.alertContainer,
            res.result.message,
            true,
            5000
          );
        } else {
          this.alertS.error(this.alertContainer, res.result.error, true, 5000);
        }
      },
      (err: HttpErrorResponse) => {
        this.loader = false;
        this.alertS.error(
          this.alertContainer,
          "Something wrong Please try again !!!",
          true,
          5000
        );
      }
    );
  }

  delete(id) {
    this.loader = true;
    this.inventoryS
      .deleteAsset(id)
      .then(res => {
        this.loader = false;
        if (res.result.error) {
          this.alertS.error(this.alertContainer, res.result.error, true, 5000);
        } else {
          // this.pagi.page > 1 ? this.reloadTable(this.pagi.page) : this.getAssets();
          this.getAssets();
          this.alertS.success(
            this.alertContainer,
            res.result.message,
            true,
            5000
          );
        }
      })
      .catch(err => {
        this.loader = false;
        this.alertS.error(
          this.alertContainer,
          "Something wrong Please try again !!!",
          true,
          5000
        );
      });
  }
}
