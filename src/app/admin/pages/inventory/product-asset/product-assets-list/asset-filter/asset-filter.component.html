<div class="m-portlet m-portlet--mobile">
  <div class="m-portlet__head search-title">
    <div class="m-portlet__head-caption">
      <div class="m-portlet__head-title">
        <h3 class="m-portlet__head-text">
          <i class="la la-search"></i>
          Search
        </h3>
      </div>
    </div>
  </div>
  <div class="m-portlet__body search-panel search-content-body">
    <!--begin::Form-->
    <form class="m-form m-form--fit m-form--label-align-right second-form"
      #form="ngForm" (ngSubmit)="searchAsset()">
      <div class="row">

          <div class="col-md-3 col-sm-6">
              <div class="form-group m-form__group">
                <app-product-search (onSelectedProduct)="selectProduct($event)"></app-product-search>
              </div>
            </div>

        <div class="col-md-3 col-sm-6">
          <div class="form-group m-form__group">
            <input
              class="form-control m-input"
              type="text"
              placeholder="Asset serial no"
              name="serial_no"
              [(ngModel)] ="asset.serial_no"
              autocomplete="off">
          </div>
        </div>

        <div class="col-md-3 col-sm-6">
          <div class="form-group m-form__group">
            <input
              class="form-control m-input"
              type="text"
              placeholder="Asset description"
              name="email"
              [(ngModel)] ="asset.description"
              autocomplete="off">
          </div>
        </div>

        <div class="col-md-3 col-sm-6">
          <div class="form-group m-form__group">
            <select class="form-control m-input" name="location_id" [(ngModel)] ="asset.location_id">
              <option [value]="location.id" *ngFor="let location of locations">{{location.name? location.name : "Location not found"}}</option>
            </select>
          </div>
        </div>

        <div class="col-md-3 col-sm-6">
          <div class="form-group m-form__group">
            <select class="form-control m-input" name="current_condition" [(ngModel)] ="asset.current_condition">
              <option *ngFor="let item of current_condition" [value]="item.value">{{item.text}}</option>
            </select>
          </div>
        </div>

        <div class="col-md-3 col-sm-6">
          <div class="form-group m-form__group">
            <select class="form-control m-input" name="current_status" [(ngModel)] ="asset.current_status">
              <option *ngFor="let item of assets_status" [value]="item.value">{{item.text}}</option>
            </select>
          </div>
        </div>

        <div class="col-md-3 col-sm-6">
          <div class="form-group m-form__group">
            <div class="input-group date">
              <input type="text" class="form-control m-input" (click)="createdDateChange()"
              placeholder="-Created date-" id="created-date" readonly/>
              <div class="input-group-append">
                <span class="input-group-text">
                  <i class="la la-calendar-check-o"></i>
                </span>
              </div>
            </div>
          </div>
        </div>

        <div class="col-md-3 col-sm-6">
          <div class="form-group m-form__group">
            <div class="input-group date">
              <input type="text" class="form-control m-input" (click)="modifiedDateChange()"
              placeholder="-Last modified date-" id="modified-date" readonly/>
              <div class="input-group-append">
                <span class="input-group-text">
                  <i class="la la-calendar-check-o"></i>
                </span>
              </div>
            </div>
          </div>
        </div>

        <div class="col-md-9 col-sm-6">
          <div class="form-group m-form__group">
            <button type="submit" class="btn m-btn--pill m-btn--air btn-brand btn-sm"
            style="margin-right:10px">
              <i class="fa fa-calendar-check-o"></i>
                Search
            </button>
            <button type="reset" class="btn m-btn--pill m-btn--air btn-danger btn-sm"
            (click)="resetSearch()">
              <i class="fa fa-history"></i>
                Reset
            </button>
          </div>
        </div>
      </div>
    </form>
    <!--end::Form-->
  </div>
</div>
