import {
  Component,
  OnInit,
  Output,
  AfterViewInit,
  EventEmitter
} from "@angular/core";
import { Asset } from "./../../asset-models/asset.models";
import {
  ASSETS_STATUS,
  ASSETS_CONDITION,
  FORMAT_SEARCH,
  changeNullToEmpty
} from "./../../../../../../globals/_classes/functions";

declare let $: any;

@Component({
  selector: "app-asset-filter",
  templateUrl: "./asset-filter.component.html",
  styleUrls: ["./asset-filter.component.css"]
})
export class AssetFilterComponent implements OnInit, AfterViewInit {
  asset: Asset = new Asset();
  filter: string;
  search: boolean;
  locations = [];
  assets_status = ASSETS_STATUS;
  current_condition = ASSETS_CONDITION;

  @Output("loadList") loadList: EventEmitter<string> = new EventEmitter();

  constructor() {
    this.locations = JSON.parse(localStorage.getItem("locations"));
    const locationPlaceholder = {
      name: "-Location-",
      id: null
    };
    if (this.locations) {
      this.locations = [locationPlaceholder, ...this.locations];
      this.asset.location_id = this.locations[0].id;
    }
    const statusPlaceholder = {
      text: "-Status-",
      value: null,
      color: "#000"
    };
    this.assets_status = [statusPlaceholder, ...this.assets_status];
    // this.assets_status.unshift(statusPlaceholder);
    this.asset.current_status = this.assets_status[0].value;
    const conditionPlaceholder = {
      text: "-Condition-",
      value: null,
      color: "#000"
    };
    this.current_condition = [conditionPlaceholder, ...this.current_condition];
    // this.current_condition.unshift(conditionPlaceholder);
    this.asset.current_condition = this.current_condition[0].value;
  }

  ngOnInit() {
    $(function () {
      $(".search-title").on("click", function () {
          $(".search-content-body").slideToggle();
      });
    })
  }

  ngAfterViewInit() {
    this.datePicker();
  }

  selectProduct(product) {
    this.asset.product_id = product.product_id;
    this.asset.variants_products_id = product.variants_products_id;
    // this.form
    //   .get("variants_products_id")
    //   .setValue(product.variants_products_id);
    // this.form.get("product_id").setValue(product.product_id);
  }

  createdDateChange() {
    $("#created-date")
      .datepicker()
      .on("changeDate", e => {
        let date = e.date;
        this.asset.created =
          date.getFullYear() +
          "-" +
          (date.getMonth() + 1) +
          "-" +
          date.getDate();
      });
  }

  modifiedDateChange() {
    $("#modified-date")
      .datepicker()
      .on("changeDate", e => {
        let date = e.date;
        this.asset.modified =
          date.getFullYear() +
          "-" +
          (date.getMonth() + 1) +
          "-" +
          date.getDate();
      });
  }

  private datePicker() {
    $("#created-date").datepicker({
      todayHighlight: true,
      orientation: "bottom left",
      format: "yyyy-mm-dd",
      templates: {
        leftArrow: '<i class="la la-angle-left"></i>',
        rightArrow: '<i class="la la-angle-right"></i>'
      }
    });
    $("#modified-date").datepicker({
      todayHighlight: true,
      orientation: "bottom right",
      format: "yyyy-mm-dd",
      templates: {
        leftArrow: '<i class="la la-angle-left"></i>',
        rightArrow: '<i class="la la-angle-right"></i>'
      }
    });
  }

  searchAsset() {
    let query = "";
    this.asset.is_search = true;
    const obj = changeNullToEmpty(this.asset);
    for (let c in obj) {
      if (obj[c]) {
        query += `${c}=${obj[c]}&`;
      }
    }
    if (query) {
      this.loadList.emit(query);
      this.search = true;
    }
  }

  resetSearch() {
    this.reset();
    this.filter = null;
    if (this.search) {
      this.loadList.emit("");
      this.search = false;
    }
  }

  reset() {
    this.asset = new Asset();
    this.asset.created = null;
  }
}
