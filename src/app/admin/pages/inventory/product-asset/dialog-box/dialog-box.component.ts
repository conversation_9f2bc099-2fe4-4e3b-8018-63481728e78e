import { Component, Input } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';


@Component({
  selector: 'delete-dialog-box',
  templateUrl: './dialog-box.component.html',
  styleUrls: ['./dialog-box.component.css']
})
export class StatusDialogBoxComponent {

  @Input('assetStatus') assetStatus;
  @Input('status') status;
  selectedStatusObject = {};

  constructor(
    public activeModal: NgbActiveModal
    ) { }

  changeStatus(assetStatus) {
    this.selectedStatusObject = this.status.find(item => item.value == assetStatus);
    console.log(this.selectedStatusObject);
  }


}
