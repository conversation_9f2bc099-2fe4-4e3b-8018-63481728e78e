import { AlertService } from './../../../../modules/alert/alert.service';
import { Helpers } from './../../../../helpers';
import { Book } from './../../../../home/<USER>/pages/books-list/books-list.component';
import { InventoryService } from './../inventory-serveice/inventory.service';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-books-inventory',
  templateUrl: './books-inventory.component.html',
  styleUrls: ['./books-inventory.component.css']
})
export class BooksInventoryComponent implements OnInit {

  form: FormGroup;
  booksList: Book[] = [];
  searchString: string;
  // pagi
  page = 1;
  limit = 20;
  total_products: number;
  pageNumbers = [20, 40, 60, 80, 100];

  @ViewChild("hasCusAlert") alertContainer: ElementRef;

  constructor(
    private fb: FormBuilder,
    private inventoryService: InventoryService,
    private alertS: AlertService,
    private router: Router
  ) {
    this.initSearchForm();
  }

  ngOnInit() {
  }

  private initSearchForm(): void {
    this.form = this.fb.group({
      searchString: ['', Validators.required]
    })
  }

  submit(): void {
    const data = this.form.getRawValue();
    this.searchString = data.searchString;
    this.getBooksList();
  }

  private getBooksList(): void {
    Helpers.setLoading(true);
    this.inventoryService.getBooksSearchResult(this.searchString, this.page).subscribe(
      res => {
        Helpers.setLoading(false);
        if (res.data) {
          this.booksList = res.data.books;
          this.total_products = res.data.total;
        } else {
          this.booksList = [];
        }
      }, err => {
        Helpers.setLoading(false);
        this.booksList = [];
      }
    );
  }

  reloadTable(e) {
    this.page = e.page;
    this.getBooksList();
  }

  errorHandler(event) {
    event.target.src = "assets/img/home/<USER>";
  }

  addToInventory(book: Book): void {
    Helpers.setLoading(true);
    this.inventoryService.addToInventoryFromISBN(book).then(
      res => {
        console.log(res);
        Helpers.setLoading(false);
        const timer = 3000;
        if (res.status === 'OK') {
          this.alertS.success(this.alertContainer, res.result.message, true, timer);
          const product_id = res.result.data ? res.result.data.product_id : null;
          if (product_id === null) {
            return;
          }
          setTimeout(
            () => {
              this.router.navigate([`/admin/inventory/edit/${product_id}/description`]);
            }, timer
          )
        } else {
          this.alertS.error(this.alertContainer, res.result ? res.result.message : "Something went wrong!", true, timer);
        }
      }
    ).catch(
      err => {
        Helpers.setLoading(false);
        this.alertS.error(this.alertContainer, err.error.result ? err.error.result.message : err.message, true, 3000);
      }
    )
  }

}
