import { HttpService } from './../../../../modules/http-with-injector/http.service';
import { Component, OnInit, HostListener, ViewChild, ElementRef } from '@angular/core';
import { SidebarService } from '../../sidebar-service/sidebar.service';
import { AlertService } from '../../../../modules/alert/alert.service';
import { ActivatedRoute } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Pagi } from '../../../../modules/pagination/pagi.model';
import { DialogBoxComponent } from '../../../../modules/dialog-box/dialog-box.component';
import { Helpers } from '../../../../helpers';
import { ExactDateTime } from './model/exact-date-time.model';
import { ExactDateTimeService } from './service/exact-date-time.service';
import { isJson } from './../../../../globals/_classes/functions';

@Component({
  selector: 'app-exact-date-time',
  templateUrl: './exact-date-time.component.html',
  styleUrls: ['./exact-date-time.component.css']
})
export class ExactDateTimeComponent implements OnInit {

  exactDateTimeList: ExactDateTime[] = [];
  sideBaropen: boolean;
  exactDateTime: ExactDateTime;
  loader: boolean;
  pagi: Pagi = new Pagi();
  type = '-1';
  is_showAddExactDate:boolean=false;
  is_showSelectPanel:boolean=true;
  is_showResetOption:boolean=false;

  @HostListener('window:resize', ['$event'])
  onResize(event) {
    if (this.sideBaropen) {
      $('.native-routing').css('display', 'block');
      this.sidebarS.openSidebar();
    }
  }

  @ViewChild('hasCusAlert') alertContainer: ElementRef;

  constructor(
    private sidebarS: SidebarService,
    private exactDateTimeS: ExactDateTimeService,
    private alertS: AlertService,
    private activeRoute: ActivatedRoute,
    private modalService: NgbModal,
    private http: HttpService
  ) {

  }

  ngOnInit() {
    this.exactDateTime = new ExactDateTime();
    //this.getExactDateTimeList(1, 20);
    this.getDatetimeConfig();
  }

  ngAfterViewInit() {
    //window.scrollTo(0, 0);
    this.closeSidebar();
    this.executeAction();
  }

  track(index, co) {
    return co ? co.id : null;
  }


  getDatetimeConfig(){
    this.exactDateTimeS
    .getDateTimeConfig()
    .then(res => {
      if (res.status == "OK") {
        let data=res.result.data;
        if(data !==""){
          if(res.result.data.exact_start_data==false && res.result.data.exact_start_time==false){
            this.is_showSelectPanel=true;
            this.is_showResetOption=false;
          }
          else{
            this.is_showResetOption=true;
            this.type=res.result.data.exact_start_time ? 'time' : 'date';
            this.is_showAddExactDate=true;
            this.getExactDateTimeList(1, 20);
          }
        }
        else{
          this.is_showSelectPanel=true;
          this.is_showResetOption=false;
        }
      }

    })
    .catch(err => {
      console.log(err);
    });
  }

  reloadTable(e) {
    // console.log(e);
    this.getExactDateTimeList(e.page, e.limit);
  }

  getExactDateTimeList(p, l) {
    this.loader = true;
    this.dataRender(p, l);
  }

  private dataRender(p?, l?) {
    this.exactDateTimeS.getExactDateTimeList(p, l,this.type).subscribe(
      res => {
        this.dataList(res);
        this.loader = false;
      },
      err => console.log(err)
    );
  }

  private dataList(res) {
    this.exactDateTimeList = res.data;
    this.pagi.total = res['total'] || 0;
    this.pagi.page = parseInt(res['page']) || 1;
    this.pagi.limit = parseInt(res['limit']) || 10;
  }

  closeSidebar() {
    $('.close-sidebar').click((e) => {
      e.preventDefault();
      this.executeAction();
    });
    $('.close-sidebar-upper').click((e) => {
      e.preventDefault();
      this.executeAction();
    });
  }

  executeAction() {
    this.sideBaropen = null;
    this.sidebarS.removeSidebar();
    $('.native-routing').css('display', 'none');
  }

  addExactDatetime() {
    this.exactDateTime = new ExactDateTime();
    this.exactDateTimeS.addEditChange({ open: true, edit: false ,formType:this.type});
    this.sideBaropen = true;
    $('.native-routing').css('display', 'block');
    this.sidebarS.openSidebar();
  }

  setContent(){
    this.http.get('contents').toPromise()
        .then(res => {
            if (res.status === 'OK' && res.result.data.length > 0) {
                const content = {};
                const data = res.result.data.filter(f => {
                    return f['config'].status === 1;
                });

                for (const c of data) {
                    const tag = this.formatTag(c.config.tag);
                    content[tag] = isJson(c.contents) ? JSON.parse(c.contents) : c.contents;
                }
                localStorage.setItem('contents', JSON.stringify(content));
                return content;
            } else {
                return {};
            }
        }).catch(err => console.log(err));
  }

  formatTag(text: String) {
    return text.replace(/(\-[a-z])/g, function ($1) { return $1.toUpperCase().replace('-', ''); });
  }

  editExactDate(obj) {
    this.exactDateTimeS
      .getExactDateTime(obj.uid)
      .then(res => {
        console.log(res);
        if (res.status == "OK") {
          this.exactDateTime = res.result.data;

          setTimeout(t => {
            this.exactDateTimeS.addEditChange({ open: true, edit: true,formType:this.type });
            this.sideBaropen = true;

            $('.native-routing').css('display', 'block');
            this.sidebarS.openSidebar();
          })

        }
      })
      .catch(err => {
        console.log(err);
      });

  }

  openSidebar() {
    $('.native-routing').css('display', 'block');
    this.sidebarS.openSidebar();
    this.sideBaropen = true;
  }

  deleteExectDate(id) {
    const modalRef = this.modalService.open(DialogBoxComponent, {
      centered: true,
      size: 'sm',
      windowClass: 'animated fadeIn'
    });
    modalRef.componentInstance.massage = 'Are you sure you want to delete?';
    modalRef.result
      .then((result) => {
        if (result) {
          Helpers.setLoading(true);
          this.archiveExactDate(id);
        }
      }, (res) => {
        console.log(res);
      });

  }

  archiveExactDate(id) {
    this.exactDateTimeS.deleteExactDateTime(id).then(
      res => {
        this.dataRender(this.pagi.page, this.pagi.limit);
        this.alert({ error: false, message: 'Successfully deleted' });
      }
    ).catch(
      err => {
        console.log(err);
        this.alert({ error: true, message: 'Something wrong!' });
      }
    )
  }

  submitExactDate(event) {
    if (!event.alert.error) {
      this.getExactDateTimeList(this.pagi.page, this.pagi.limit);
      this.executeAction();
    }
    this.alert(event.alert);
  }

  alert(data) {
    Helpers.setLoading(false);
    if (data.error) {
      this.alertS.error(this.alertContainer, data.message, true, 5000);
    } else {
      this.alertS.success(this.alertContainer, data.message, true, 5000);
    }
  }

  getDate(d) {
    return new Date(d);
  }

  onChangeType(){
    if(this.type !='-1')
    {
      let sendData={};
      if(this.type=='date'){
        sendData['exact_start_date']=true;
      }
      else if(this.type=='time'){
        sendData['exact_start_time']=true;
      }
      else{
        sendData['reset']=true;
      }
  
      this.exactDateTimeS
        .updateExactDateTime(sendData)
        .then(res => {
          if (res.status == "OK") {
            if(this.type !='reset')
            {
              this.is_showAddExactDate=true;
              this.is_showResetOption=true;
            }
            else{
              this.is_showAddExactDate=false;
              this.is_showResetOption=false;
              
            }
            this.getExactDateTimeList(1, 20);
            this.setContent();
          }
  
        })
        .catch(err => {
          console.log(err);
        });
    }
    
  }

  getDurationValues(data){
    let durations="";

    let durationList=JSON.parse(data);
    durations= durationList.join(',');

    return durations;
  }

}
