<div class="custom-alert" #hasCusAlert></div>

<div class="m-subheader">
    <div class="d-flex align-items-center">
        <div class="mr-auto">
            <h3 class="m-subheader__title m-subheader__title--separator">
              Exact Start Time
            </h3>
            <ul class="m-subheader__breadcrumbs m-nav m-nav--inline">
              <li class="m-nav__item m-nav__item--home">
                <a routerLink="/admin" class="m-nav__link m-nav__link--icon">
                  <i class="m-nav__link-icon la la-home"></i>
                </a>
              </li>
              <li class="m-nav__separator">
                <i class="fa fa-angle-right"></i>
              </li>
              <li class="m-nav__item">
                <a class="m-nav__link">
                  <span class="m-nav__link-text">
                    Inventory
                  </span>
                </a>
              </li>
              <li class="m-nav__separator" style="padding-left: 10px">
                <i class="fa fa-angle-right"></i>
              </li>
              <li class="m-nav__item">
                <a class="m-nav__link">
                  <span class="m-nav__link-text">
                    Exact Start Time
                  </span>
                </a>
              </li>
            </ul>
        </div>
    </div>
  </div>
  <!-- END: Subheader -->
  <div class="m-content animated fadeIn">
    <div class="m-portlet">
        <div class="m-portlet__head title-head">
            <div class="m-portlet__head-caption">
              <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text colorPurpel">
                  Exact Start Time
                </h3>
                <div *ngIf="is_showSelectPanel" class="add-list-btn text-right">
                    <select class="form-control m-input" id="type" name="type" [(ngModel)]="type" (change)="onChangeType()">
                      <option value="-1">Select Type</option>
                      <option value="time">Exact Time</option>
                      <option value="date">Exact Date</option>
                      <option *ngIf="is_showResetOption" value="reset">Reset</option>
                    </select>

                    <button *ngIf="is_showAddExactDate" class="btn btn-brand" (click)="addExactDatetime()">
                        Add
                    </button>
                </div>
              </div>
            </div>
      </div>
      <div class="m-portlet__body">
        <div class="">
          <div class="m-section__content">
            <div *ngIf="loader" class="table-load m-loader m-loader--brand"></div>
            <div class="price-table table-responsive">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th *ngIf="type=='time'">
                      Time
                    </th>
                    <th *ngIf="type=='date'">
                      Date
                    </th>
                    <th>
                      Duration Type
                    </th>
                    <th>
                      Duration Value
                    </th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody *ngIf="exactDateTimeList.length > 0; else noDate">
                  <tr *ngFor="let exact_datetime of exactDateTimeList; let i = 'index'; trackBy: track; let o='odd'; let e='even'" [ngClass]="{'odd-tr':o, 'even-tr':e}">
                    <td *ngIf="type=='time'">
                      {{exact_datetime.time.split(':')[0]+':'+exact_datetime.time.split(':')[1] }}
                    </td>

                    <td *ngIf="type=='date'">
                      {{exact_datetime.date | date:'shortDate'}}
                    </td>

                    <td>
                      {{exact_datetime?.duration_type}}
                    </td>

                    <td>
                      {{getDurationValues(exact_datetime.duration_values)}}
                    </td>
                    
                    <td>       
                      <a id="m_quick_sidebar_toggle" (click)="editExactDate(exact_datetime)"
                        class="m-portlet__nav-link btn m-btn m-btn--hover-info m-btn--icon m-btn--icon-only m-btn--pill">
                        <i class="fa fa-edit"></i>
                      </a>
                      <a id="m_quick_sidebar_toggle" (click)="deleteExectDate(exact_datetime.uid)"
                        class="m-portlet__nav-link btn m-btn m-btn--hover-danger m-btn--icon m-btn--icon-only m-btn--pill">
                        <i class="fa fa-trash"></i>
                      </a>
                    </td>
                  </tr>
                </tbody>
                <ng-template #noDate>
                  <tbody><tr *ngIf="!loader"><td colspan="7"><h5 class="text-center">No Exact Start Time Found</h5></td></tr></tbody>
                </ng-template>
              </table>
              </div>
            <!-- pagination Start-->
            <boot-pagination [totalSize]="pagi.total" [listSize]="pagi.limit" (pageChange)="reloadTable($event)"></boot-pagination>
            <!-- pagination End-->
          </div>

        </div>
			<!--end::Section-->
      </div>
    </div>
  </div>


  <!-- sidebar -->

<div class="native-routing animated">
	<button class="close-sidebar btn btn-sm btn-brand">
		<i class="fa fa-chevron-right"></i>
  </button>
  <span class="close-sidebar-upper">
    <i class="la la-close"></i>
  </span>
    <div class="native-routing-container">
        <app-exact-date-time-form [exactDateTime]="exactDateTime" (submit)="submitExactDate($event)"></app-exact-date-time-form>
    </div>
</div>
<!-- <div class="backdrop animated"></div> -->



