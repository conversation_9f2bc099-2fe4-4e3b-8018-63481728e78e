import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { DialogBoxComponent } from '../../../../modules/dialog-box/dialog-box.component';
import { PaginationModule } from '../../../../modules/pagination/pagination.module';
import { DialogBoxModule } from '../../../../modules/dialog-box/dialog-box.module';
import { FormsModule } from '@angular/forms';
import { NumberOnlyDirectiveModule } from '../../../../modules/directive/directive.module';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { InventoryService } from '../../inventory/inventory-serveice/inventory.service';
import { ExactDateTimeComponent } from './exact-date-time.component';
import { ExactDateTimeFormComponent } from './exact-date-time-form/exact-date-time-form.component';
import { ExactDateTimeService } from './service/exact-date-time.service';
import { SearchProductComponent } from './exact-date-time-form/search-type/search-type.component';

const route: Routes = [
  {
    path: '',
    component: ExactDateTimeComponent
  }
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(route),
    FormsModule,
    DialogBoxModule,
    PaginationModule,
    NumberOnlyDirectiveModule,
    NgbModule
  ],
  entryComponents: [DialogBoxComponent],
  exports: [RouterModule],
  declarations: [ExactDateTimeComponent, ExactDateTimeFormComponent, SearchProductComponent],
  providers:[InventoryService,ExactDateTimeService]
})
export class ExactDateTimeModule { }
