<div style="padding-top: 35px;">
  <h4 class="colorPurpel" *ngIf="edit; else add">Update Exact Start Time</h4>
  <ng-template #add class="colorPurpel">
    <h4 class="colorPurpel">Add Exact Start Time</h4>
  </ng-template>
  <form class="m-form m-form--fit m-form--label-align-right">
    <div class="row">
      <div class="col-sm-12">
        <div class="row">
          <div class="col-sm-6">
            <div class="form-group m-form__group">
              <label for="type">Type</label>
              <select class="form-control m-input" id="type" name="type" [(ngModel)]="exactDateTime.content_type">
                <option value="1">All Product</option>
                <option value="2">Specific Product</option>
              </select>
            </div>
          </div>


          <div *ngIf="exactDateTime.content_type == 2" class="col-sm-12">
            <div class="row">
              <div class="col-sm-6">
                <div class="form-group m-form__group">
                  <label for="type">Select products(s)</label>
                  <app-search-product (onSelectedProduct)="selectProduct($event)"></app-search-product>

                  <div class="type-table table-responsive">
                    <table class="table table-hover">
                      <tbody *ngIf="selectedProduct.length > 0">
                        <tr *ngFor="let product of selectedProduct; let i = index">
                          <td>
                            {{ product.name }}
                          </td>
                          <td>
                            <a id="m_quick_sidebar_toggle" (click)="deleteProduct(product.product_id)"
                              class="m-portlet__nav-link btn m-btn m-btn--hover-danger m-btn--icon m-btn--icon-only m-btn--pill">
                              <i class="fa fa-trash"></i>
                            </a>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>

                </div>
              </div>
            </div>
          </div>

        </div>
      </div>

      <div class="col-sm-6" *ngIf="formType =='date'">
        <div class="form-group m-form__group">
          <label for="Start-date">Exact Date</label>
          <div class="input-group date">
            <input type="text" class="form-control m-input"  placeholder="-Start date-"
              id="Start-date" readonly />
            <div class="input-group-append">
              <span class="input-group-text">
                <i class="la la-calendar-check-o"></i>
              </span>
            </div>
          </div>
        </div>
      </div>
      <div class="col-sm-6" *ngIf="formType =='time'">
        <div class="form-group m-form__group">
          <label class="colorPurpel">
            Start Time
          </label>
          <div class="input-group timepicker">
            <input type="text" class="form-control m-input"  id="Start-time" readonly placeholder="-Select Time-">
            <div class="input-group-append">
              <span class="input-group-text">
                <i class="la la-clock-o"></i>
              </span>
            </div>
          </div>
        </div>
      </div>

      <div class="col-sm-12">
        <div class="row">
          <div class="col-sm-6">
            <div class="form-group m-form__group">
              <label for="unit">Duration Type</label>
              <select class="form-control m-input" id="unit" name="duration_type" [(ngModel)]="exactDateTime.duration_type">
                <option value="hour">Hour</option>
                <option value="day">Day</option>
                <option value="week">Week</option>
                <option value="month">Month</option>
              </select>
            </div>
          </div>
        </div>
       
      </div>

      
        <div class="col-sm-12">
          <div class="row" *ngFor="let duration of exactDateTime?.duration_values;let i=index; trackBy:trackByFn">
            <div class="col-sm-6">
              <div class="form-group m-form__group">
                <label for="code">Duration Value</label>
                <input class="form-control m-input"  type="text" [value]="duration" placeholder="duration Value" #timeDuration (keyup)="onTimeDurationKeyup(timeDuration.value,i)"
                  autocomplete="off" />
              </div>
            </div>
            <div class="col-sm-6 pt-5" *ngIf="exactDateTime?.duration_values.length==(i+1) && formType =='time'">
              <span class="btn btn-dark m-btn m-btn--icon btn-sm m-btn--icon-only  m-btn--pill m-btn--air" (click)="onClickAddDuration()" ><i class="fa fa-plus"></i></span> &nbsp;&nbsp; 
              <span class="btn btn-danger m-btn m-btn--icon btn-sm m-btn--icon-only  m-btn--pill m-btn--air" (click)="onClickRemoveDuration()" *ngIf="i !==0"><i class="fa fa-minus"></i></span>
           </div>
          </div>
        </div>


    </div>
    <div class="m-portlet__foot m-portlet__foot--fit">
      <div class="m-form__actions m-form__actions" style="padding: 30px 0px;">
        <div *ngIf="loader; else button" class="m-loader m-loader--brand"
          style="width: 30px; padding-left: 30px; display: inline-block;"></div>
        <ng-template #button>
          <button type="button" class="btn btn-brand" *ngIf="edit; else addbtn" (click)="update()">
            <i class="fa fa-save"></i>
            <span style="padding-left:10px;">Update</span>
          </button>
          <ng-template #addbtn>
            <button type="button" class="btn btn-brand" (click)="submit()">
              <i class="fa fa-save"></i>
              <span style="padding-left:10px;">Submit</span>
            </button>
          </ng-template>
        </ng-template>
      </div>
    </div>
  </form>
</div>