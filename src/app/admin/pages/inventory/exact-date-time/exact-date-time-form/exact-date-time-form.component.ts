import {
  Component,
  OnInit,
  Output,
  Input,
  EventE<PERSON>ter,
  <PERSON><PERSON><PERSON><PERSON>,
  AfterViewInit
} from "@angular/core";
import { SidebarService } from "../../../sidebar-service/sidebar.service";
import { Subscription } from "rxjs";
import { ExactDateTime } from "../model/exact-date-time.model";
import { ExactDateTimeService } from "../service/exact-date-time.service";
import { convertTime12to24 } from "../../../../../globals/_classes/functions";

declare let $: any;

@Component({
  selector: "app-exact-date-time-form",
  templateUrl: "./exact-date-time-form.component.html",
  styleUrls: ["./exact-date-time-form.component.css"]
})
export class ExactDateTimeFormComponent implements OnInit, OnDestroy, AfterViewInit {
  loader: boolean;
  sub: Subscription;
  edit: boolean;
  is_show_coupon_code: boolean = true;
  selectedProduct = [];
  formType;

  @Input("exactDateTime") exactDateTime: ExactDateTime;
  @Output("submit") submitForm: EventEmitter<any> = new EventEmitter();

  constructor(
    private exactDateTimeS: ExactDateTimeService
  ) { }

  ngOnInit() {
    this.selectedProduct = [];
    this.sub = this.exactDateTimeS.addEditOpen.subscribe(val => {
      if (val.open) {
        this.selectedProduct = [];
        if (val.edit) {
          this.edit = val.edit;
          this.selectedProduct = this.exactDateTime.content_id;
          this.formType = val.formType;

          setTimeout(() => {
            $("#Start-date").datepicker(
              "update",
              new Date(this.exactDateTime.date)
            );

            $("#Start-time").timepicker('setTime', this.exactDateTime.time);
            this.dateTimeChangeEvent();

          }, 100)

        } else {
          this.exactDateTime = new ExactDateTime();
          this.edit = false;
          this.formType = val.formType;

          setTimeout(t => {
            this.initDateTime();
            this.dateTimeChangeEvent();
            if (this.formType == 'time') {
              this.exactDateTime.time = convertTime12to24('12:00 AM');
            }
            else {
              this.exactDateTime.date = this.exactDateTimeS.getDate(new Date());
            }

          }, 100)
        }
      }
    });
  }

  ngOnDestroy() {
    this.sub.unsubscribe();
  }

  ngAfterViewInit() {
    this.initDateTime();
  }

  private initDateTime() {
    $("#Start-date").datepicker(this.exactDateTimeS.datePickerObj());
    $("#Start-date").datepicker("update", new Date());

    $('#Start-time').timepicker({
      defaultTime: '12:00 AM',
      minuteStep: 1,
      showSeconds: false,
      showMeridian: true,
      snapToStep: true
    });
  }

  private dateTimeChangeEvent() {
    $("#Start-date")
      .datepicker()
      .on("changeDate", e => {
        let date = e.date;
        this.exactDateTime.date = this.exactDateTimeS.getDate(date);
      });


    $('#Start-time').on('change', () => {
      let time = $('#Start-time').data('timepicker').getTime();
      this.exactDateTime.time = convertTime12to24(time);
    });
  }


  submit() {
    this.exactDateTime.content_id = this.selectedProduct.map(p => {
      return p.product_id;
    });
    console.log(this.exactDateTime);

    this.loader = true;
    this.exactDateTimeS
      .addExactDateTime(this.exactDateTime)
      .then(res => {
        console.log(res);
        if (res.status == "OK") {
          this.sendEmitedData(
            res.result.data,
            false,
            "Successfully added",
            true
          );
          this.exactDateTime = new ExactDateTime();
        } else {
          this.sendEmitedData([], true, res.result.message);
        }
      })
      .catch(err => {
        console.log(err);
        this.sendEmitedData(
          [],
          true,
          "Something wrong!"
        );
      });
  }

  sendEmitedData(result, error, message, add?) {
    this.loader = false;
    const emit_data = {
      data: result,
      alert: { error: error, message: message },
      add: add
    };
    this.submitForm.emit(emit_data);
  }

  update() {
    this.loader = true;
    this.exactDateTime.content_id = this.selectedProduct.map(p => {
      return p.product_id;
    });


    this.exactDateTimeS
      .UpdateExactDateTime(this.exactDateTime)
      .then(res => {
        // console.log(res);
        if (res.status == "OK") {
          this.sendEmitedData(
            res.result.data,
            false,
            "Successfully updated",
            false
          );
        } else {
          this.sendEmitedData([], true, res.result.message);
        }
      })
      .catch(err => {
        console.log(err);
        this.sendEmitedData(
          [],
          true,
          "Something wrong!"
        );
      });
  }

  selectProduct(product) {
    this.selectedProduct.push(product);

  }

  deleteProduct(id) {
    var index = this.selectedProduct.findIndex(x => x.product_id == id);
    if (index > -1) {
      this.selectedProduct.splice(index, 1);
    }
  }

  onTimeDurationKeyup(value, index) {
    this.exactDateTime.duration_values[index] = value;
  }

  onClickAddDuration() {
    this.exactDateTime.duration_values.push('');
  }

  onClickRemoveDuration() {
    this.exactDateTime.duration_values.pop();
  }

  trackByFn(index: any, item: any) {
    return index;
  }
}
