import { Component, OnInit, Output, Input, EventEmitter, OnChanges, AfterViewInit, OnDestroy } from "@angular/core";
import { ProductSearch } from "../product-models/inventory.models";
import { FORMAT_SEARCH } from "../../../../globals/_classes/functions";
import { ProductListService } from "../product-list/product-service/product-list.service";
import { InventoryService } from "../inventory-serveice/inventory.service";
import { CartService } from "../../../cart-service/cart.service";
import { ActivatedRoute } from "@angular/router";
import { Subscription } from "rxjs";
import { Supplier } from "../../settings/models/settings.models";
import { Category } from "../../settings/models/category.models";


@Component({
  selector: "app-advanced-product-search",
  templateUrl: "./advanced-product-search.component.html",
  styleUrls: ["./advanced-product-search.component.css"]
})
export class AdvancedProductSearchComponent implements OnChanges, OnInit, AfterViewInit, OnDestroy {

  search: ProductSearch = new ProductSearch();
  filter:string;
  categories: Category[] = [];
  reset: boolean;
  suplliers: Supplier[] = [];
  location = [];
  sub: Subscription[] = [];
  loader: boolean;
  allcategories;
  // abc = true;

  @Input('checkFilter') checkFilter: boolean;
  @Input () abc;
  @Output('search') searching:EventEmitter<string> = new EventEmitter();


  constructor(
    private activeRoute:ActivatedRoute,
    private cartS: CartService,
    private inventoryS:InventoryService,
    private proS: ProductListService
  ) {
    // if (this.abc === false) {
    //   this.suplliers = this.activeRoute.snapshot.data['supplier'].data;
    //   if(this.suplliers) {
    //     this.suplliers = this.suplliers.map((s) => {
    //       s['text'] = s['name'];
    //       return s;
    //     });
    //   }
    // } else {
    //   this.inventoryS.getSupplier().subscribe(
    //     (res: any) => {
    //       console.log(res);
    //       this.suplliers = res.data;
    //     }
    //   );
    //   if(this.suplliers) {
    //     this.suplliers = this.suplliers.map((s) => {
    //       s['text'] = s['name'];
    //       return s;
    //     });
    //   }
    // }
    this.suplliers = this.activeRoute.snapshot.data['supplier'].data;
      if(this.suplliers) {
        this.suplliers = this.suplliers.map((s) => {
          s['text'] = s['name'];
          return s;
        });
      }
    this.sub[0] = this.cartS.location.subscribe(
      val => {
        if(val) {
          this.location = val;
        }
      }
    );
  }

  ngOnChanges() {
    if(this.checkFilter) {
      this.checkFilterData();
    }
  }

  ngOnInit() {
    this.inventoryS.getAllCategory().subscribe(
      res =>{
          this.allcategories = res;
      },
      err => console.log(err)
    );
  }

  ngAfterViewInit() {
    this.checkFilterData();
  }

  ngOnDestroy() {
    for(let s of this.sub) {
      s.unsubscribe();
    }
  }

  checkFilterData() {
    const param = this.activeRoute.snapshot.queryParamMap.get('param');
    const data = param ? param : null;
    if(data && (!data.includes('search'))) {
      if(!$('.search-panel#advanceSearch').hasClass('dis-block')) {
        this.openAdvanceSearch();
      }
      setTimeout(() => {
        this.search = this.proS.formatFilter(data);
        this.reset = true;
        this.checkFilter = false;
      }, 100);
    }
  }

  categoryTrack(index,cat){
    return cat? cat.id:null;
  }

  searchProduct(value){
    this.filter = FORMAT_SEARCH(value);
    console.log(this.filter);
    if(this.filter) {
      this.loader = true;
      this.searching.emit(this.filter);
      this.reset = true;
    }
  }

  resetSearch(){
    this.filter = null;
    if(this.reset) {
      this.searching.emit('');
      this.reset = false;
    }
    this.openAdvanceSearch();
  }

  closeFilter() {
    this.search = new ProductSearch();
    if(this.reset) {
      this.openAdvanceSearch();
      this.reset = false;
    }
  }

  openAdvanceSearch() {
    $('.search-panel#advanceSearch').toggleClass('dis-block');
    $('#advanceSearch').toggleClass('la-angle-down la-angle-up');
  }

}