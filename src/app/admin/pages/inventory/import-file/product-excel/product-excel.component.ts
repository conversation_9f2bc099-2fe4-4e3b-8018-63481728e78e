import { Helpers } from './../../../../../helpers';
import { InventoryService } from './../../inventory-serveice/inventory.service';
import { AlertService } from './../../../../../modules/alert/alert.service';
import { EndPoint } from './../../../../../globals/endPoint/config';
import { Component, OnInit, OnDestroy, ViewChild, ElementRef } from '@angular/core';
import { Subscription, interval } from 'rxjs';
import { Router } from '@angular/router';
import { GET_USER, downloadFile } from "../../../../../globals/_classes/functions";

class User {
  store_id: number;
  user_id: number;
  type: string;
}

@Component({
  selector: 'app-product-excel',
  templateUrl: './product-excel.component.html',
  styleUrls: ['./product-excel.component.css']
})
export class ProductExcelComponent implements OnInit, <PERSON><PERSON><PERSON>roy {
  
  uploadInfo: User = new User();
  url = EndPoint + "products/import";
  imageUrl = EndPoint + "image-upload";
  is_showDownloadLink = false;
  fileName;
  downloadSample;
  @ViewChild("hasCusAlert") alertContainer: ElementRef;
  activeTabName = "Excel";
  totalUploadRecords = 0;
  importHistory:any=[];

  private updateSubscription: Subscription;

  constructor(
    private router: Router,
    private alertS: AlertService,
    private inventoryS: InventoryService
  ) {
    this.downloadSample = EndPoint + "products/download/sample";
    // this.getImportHistory();
  }

  ngOnInit() {
    let user = GET_USER();
    this.uploadInfo.store_id = user.store_id;
    this.uploadInfo.user_id = user.user_id;
    this.uploadInfo.type = "initial";
    this.updateSubscription = interval(10000).subscribe(val => {
      this.showUploadFileProgress();
    });
  }

  ngOnDestroy() {
    this.updateSubscription.unsubscribe();
  }


  getImportHistory(){
    this.inventoryS.getImportHistory().then(res => {
      if (res.result) {
        this.importHistory = res.result.data;
      }
    });
  }


  showUploadFileProgress() {
    const type = "import_initial";
    this.inventoryS.getUploadFileProgress(type).subscribe(res => {
      if(res.status=="OK")
      {
        console.log(res.result.data);
        const data=res.result.data;

        if (data.records) {
          this.totalUploadRecords = data.records;
        }
  
        if (data.file !="") {
          this.fileName = data.file;
          this.is_showDownloadLink = true;
        }
      }
    });
  }

  uploadFiles(e) {
    if (e.status.status=="OK") {
      this.alertS.success(
        this.alertContainer,
        e.status.result.message,
        true,
        5000
      );
    } else {
      this.alertS.error(
        this.alertContainer,
        e.status.result.message,
        true,
        5000
      );
    }
  }

  

  uploadImages(e) {
    this.alertS.success(
      this.alertContainer,
      "Image(s) have been successfully uploaded",
      true,
      5000
    );
  }

  gotoList() {
    this.router.navigate(["/admin/inventory"]);
  }

  downloadFile() {
    let param = "?file=" + this.fileName;
    Helpers.setLoading(true);
    this.inventoryS.downloadImportedFile(param).then(
      res => {
        Helpers.setLoading(false);
        downloadFile(res,this.getDownLoadFileName());
      },
      err => {
        Helpers.setLoading(false);
        console.log(err);
        this.alertS.error(
          this.alertContainer,
          "Something wrong!!!",
          true,
          3000
        );
      }
    );
  }

  getDownLoadFileName(){
   let storeName=GET_USER().store_name;
   let today = new Date();
   let dd = String(today.getDate()).padStart(2, '0');
   let mm = String(today.getMonth() + 1).padStart(2, '0'); //January is 0!
   let yyyy = today.getFullYear().toString().substr(-2);
   
   return `${storeName}_${yyyy}${mm}${dd}.xls`;
  }
}
