<div class="custom-alert" #hasCusAlert></div>

<div class="m-subheader">
  <div class="d-flex align-items-center">
    <div class="mr-auto">
      <h3 class="m-subheader__title m-subheader__title--separator">
        Import Inventory
      </h3>
      <ul class="m-subheader__breadcrumbs m-nav m-nav--inline">
        <li class="m-nav__item m-nav__item--home">
          <a routerLink="/admin" class="m-nav__link m-nav__link--icon">
            <i class="m-nav__link-icon la la-home"></i>
          </a>
        </li>
        <li class="m-nav__separator">
          <i class="fa fa-angle-right"></i>
        </li>
        <li class="m-nav__item">
          <a routerLink="/admin/inventory" class="m-nav__link">
            <span class="m-nav__link-text">
              Product List
            </span>
          </a>
        </li>
        <li class="m-nav__separator" style="padding-left: 10px">
          <i class="fa fa-angle-right"></i>
        </li>
        <li class="m-nav__item">
          <a routerLink="/admin/inventory/import-inventory" class="m-nav__link">
            <span class="m-nav__link-text">
              Import Inventory
            </span>
          </a>
        </li>
        <li class="m-nav__separator" style="padding-left: 10px">
          <i class="fa fa-angle-right"></i>
        </li>
        <li class="m-nav__item">
          <a class="m-nav__link">
            <span class="m-nav__link-text">
              Update Assets
            </span>
          </a>
        </li>
      </ul>
    </div>
  </div>
</div>

<div class="m-content animated fadeIn">
  <div class="m-portlet m-portlet--tabs">

    <div class="m-portlet__head assetitems-head">
      <div class="m-portlet__head-caption">
        <div class="m-portlet__head-title m-product-title">
            <h3 class="m-portlet__head-text colorPurpel">
              <i class="la la-cloud-upload"></i> Update Assets
            </h3>
          <div class="add-list-btn text-right assetlist-btn-area">
            <a routerLink="/admin/inventory/import-inventory" class="btn btn-brand btn-sm" id="asset-addnew-btn" style="color: #fff;">
              back
            </a>
          </div>
        </div>
      </div>
    </div>
    <div class="m-portlet__body">
      <div class="row">
        <div class="col-md-7">
          <h4>
            Upload CSV or Excel File
            <small>(Maximium File Size is 5MB)</small>
          </h4>
          <div style="position: relative; padding-top: 20px;">
            <drag-drop [multi]="false" [resetOff]="false" [url]="url" [fileName]="'file'" [limit]="5"
              [info]="uploadAssetInfo" [accept]="
                '.csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel'
              " (upload)="uploadFiles($event)">
            </drag-drop>
          </div>
        </div>
        <div class="col-md-5">
          <h4>Import instructions</h4>
          <div>
            Please download excel from initial upload section or export
            existing products for updating asset.
          </div>
          <div>&nbsp;</div>
          <table class="table">
            <tr>
              <td width="30%">Product ID</td>
              <td>
                Required - You will get it from exported or from initial
                excel data.
              </td>
            </tr>
            <tr>
              <td>Variant Product ID</td>
              <td>
                Required - You will get it from exported or from initial
                excel data.
              </td>
            </tr>
            <tr>
              <td>Product Name</td>
              <td>
                This is used for product identification for filling pricing.
                There is no effect on updating price with this field data.
              </td>
            </tr>
            <tr>
              <td>Location Name</td>
              <td>
                This is used for product identification for filling pricing.
                There is no effect on updating price with this field data.
              </td>
            </tr>
            <tr>
              <td>Variant Values</td>
              <td>
                This is used for product identification for filling pricing.
                There is no effect on updating price with this field data.
              </td>
            </tr>
            <tr>
              <td>Serial Number</td>
              <td>
                Required - Unique serial no for any item. Duplicate or
                existing serial item data will not be saved.
              </td>
            </tr>
            <tr>
              <td>Description</td>
              <td>Optional - Asset details</td>
            </tr>
            <tr>
              <td>Purchase Date</td>
              <td>Optional- Asset purchase date. Format : m-d-Y</td>
            </tr>
            <tr>
              <td>Purchase Cost</td>
              <td>Optional - Asset purchase cost</td>
            </tr>
            <tr>
              <td>Sale Price</td>
              <td>Optional - If assets can sold also.</td>
            </tr>
            <tr>
              <td>Manufacturer</td>
              <td>Optional - Manufacturer name</td>
            </tr>
            <tr>
              <td>Manufaturer Serial Number</td>
              <td>Optional - Manufacturer serial number</td>
            </tr>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>