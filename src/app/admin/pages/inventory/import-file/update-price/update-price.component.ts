import { AlertService } from './../../../../../modules/alert/alert.service';
import { EndPoint } from './../../../../../globals/endPoint/config';
import { Component, OnInit, ElementRef, ViewChild } from '@angular/core';
import { GET_USER } from "../../../../../globals/_classes/functions";

class User {
  store_id: number;
  user_id: number;
  type: string;
}

@Component({
  selector: 'app-update-price',
  templateUrl: './update-price.component.html',
  styleUrls: ['./update-price.component.css']
})
export class UpdatePriceComponent implements OnInit {

  uploadPriceInfo: User = new User();
  url = EndPoint + "products/import";
  imageUrl = EndPoint + "image-upload";
  is_showDownloadLink = false;
  fileName;
  downloadSample;
  @ViewChild("hasCusAlert") alertContainer: ElementRef;
  activeTabName = "Excel";
  totalUploadRecords = 0;
  importHistory:any=[];

  constructor(
    private alertS: AlertService
  ) {
    this.downloadSample = EndPoint + "products/download/sample";
  }

  ngOnInit() {
    let user = GET_USER();
    this.uploadPriceInfo.store_id = user.store_id;
    this.uploadPriceInfo.user_id = user.user_id;
    this.uploadPriceInfo.type = "price";
  }

  uploadFiles(e) {
    if (e.status.status=="OK") {
      this.alertS.success(
        this.alertContainer,
        e.status.result.message,
        true,
        5000
      );
    } else {
      this.alertS.error(
        this.alertContainer,
        e.status.result.message,
        true,
        5000
      );
    }
  }

  uploadImages(e) {
    this.alertS.success(
      this.alertContainer,
      "Image(s) have been successfully uploaded",
      true,
      5000
    );
  }

}
