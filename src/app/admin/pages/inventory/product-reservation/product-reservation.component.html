<div class="custom-alert" #hasCusAlert></div>
<!-- BEGIN: Subheader -->
<div class="m-subheader">
	<div class="d-flex align-items-center">
		<div class="mr-auto">
			<h3 class="m-subheader__title m-subheader__title--separator">
				Reservations
			</h3>
			<ul class="m-subheader__breadcrumbs m-nav m-nav--inline">
				<li class="m-nav__item m-nav__item--home">
					<a routerLink="/admin" class="m-nav__link m-nav__link--icon">
						<i class="m-nav__link-icon la la-home"></i>
					</a>
				</li>
				<li class="m-nav__separator">
					<i class="fa fa-angle-right"></i>
        </li>
        <li class="m-nav__item">
					<a class="m-nav__link" routerLink="/admin/inventory">
						<span class="m-nav__link-text">
							Inventory
						</span>
					</a>
        </li>
        <li class="m-nav__separator" style="padding-left: 10px">
					<i class="fa fa-angle-right"></i>
        </li>
				<li class="m-nav__item">
					<a class="m-nav__link">
						<span class="m-nav__link-text">
							Reservation List 
						</span>
					</a>
				</li>
			</ul>
		</div>
	</div>
</div>
<!-- END: Subheader -->
<div class="m-content product-list animated fadeIn">
	<!--begin::Portlet-->
	<div class="m-portlet m-portlet--mobile">
		<div class="m-portlet__head">
			<div class="m-portlet__head-caption">
				<div class="m-portlet__head-title">
					<h3 class="m-portlet__head-text">
						Reservation List
					</h3>
				</div>
			</div>
    </div>

      <div class="m-portlet__body" style="position: relative;">
        <!--begin: Datatable -->
        <!--begin::Section-->
          <div class="m-section">
            <div class="m-section__content price-table">
              <div *ngIf="loader" class="table-load m-loader m-loader--brand"></div>
                <div class="table-responsive" style="margin-bottom: 10px;">
                  <table class="table table-hover">
                    <thead>
                      <tr>
                        <th>
                          Product Name
                        </th>
                        <th>
                          Start Date
                        </th>
                        <th>
                          End Date
                        </th>
                        <th>
                          Quantity
                        </th>
                        <th>
                          Order Id
                        </th>
                        <th>
                          Order Status
                        </th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody *ngIf="reservations.length<1; else table">
                      <tr><td colspan="7"><h4 class="text-center">No Data Found</h4></td></tr>
                    </tbody>
                    <ng-template #table>
                      <tbody>
                        <tr *ngFor="let tran of reservations; let o='odd'; let e='even'; trackBy: trackTransaction" [ngClass]="{'odd-tr':o, 'even-tr':e}">
                          <td>{{tran?.product?.name}}</td>
                          <td>{{getDate(tran.start_date) | date: 'mediumDate'}}</td>
                          <td>{{getDate(tran.end_date) | date: 'mediumDate'}}</td>
                          <td>{{tran?.quantity}}</td>
                          <td>{{tran?.order?.id}}</td>
                          <td><span *ngIf="tran?.order?.status" class="m-badge m-badge--wide" [ngClass]="checkStatus(tran?.order?.status)" style="cursor: pointer;">{{getStatus(tran?.order?.status)}}</span></td>
                          <td>
                            <a id="m_quick_sidebar_toggle" (click)="details(tran.order.id)" *ngIf="tran?.order?.id; else ADDORDER" title="View Order Details"
                              class="m-portlet__nav-link btn m-btn m-btn--hover-brand m-btn--icon m-btn--icon-only m-btn--pill">
                              <i class="fa fa-eye"></i>
                            </a>
                            <ng-template #ADDORDER>
                              <a id="m_quick_sidebar_toggle" (click)="deleteReservation(tran.id)" title="Delete Availability"
                                class="m-portlet__nav-link btn m-btn m-btn--hover-danger m-btn--icon m-btn--icon-only m-btn--pill">
                                <i class="la la-trash"></i>
                              </a>
                              <a id="m_quick_sidebar_toggle" (click)="createOrder(tran, i)" title="Add Order"
                                class="m-portlet__nav-link btn m-btn m-btn--hover-brand m-btn--icon m-btn--icon-only m-btn--pill">
                                <i class="fa fa-plus"></i>
                              </a>
                            </ng-template>
                          </td>
                        </tr>
                      </tbody>
                    </ng-template>
                  </table>
                </div>
              <!-- pagination Start-->
              <boot-pagination [totalSize]="pagi.total" [listSize]="pagi.limit" (pageChange)="reloadTable($event)"></boot-pagination>
              <!-- pagination End-->
            </div>
          </div>
          <!--end::Section-->
        <!--end: Datatable -->
      </div>
      
	</div>
		<!--end::Portlet-->
</div>

  <!-- sidebar -->

<div class="native-routing animated">
	<button class="close-sidebar btn btn-sm btn-brand">
		<i class="fa fa-chevron-right"></i>
	</button>
    <div class="native-routing-container">
        <app-create-order (submitOrder)="submitOrder($event)"></app-create-order>
    </div>
</div>
<!-- <div class="backdrop animated"></div> -->

