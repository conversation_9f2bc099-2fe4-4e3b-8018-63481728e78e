import { Injectable, Optional } from "@angular/core";
import {
  Resolve,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  Router
} from "@angular/router";
import { InventoryServiceConfig } from "../product-models/inventory.models";
import { Location } from "@angular/common";

import { HttpService } from "../../../../modules/http-with-injector/http.service";
import { map, catchError } from "rxjs/operators";
import { BehaviorSubject, throwError, of, Observable } from "rxjs";
import {
  FormatPrice,
  FormateAttribute,
  GET_USER,
  GET_PosCartToken
} from "../../../../globals/_classes/functions";

declare let $: any;

export class Reload {
  reload?: boolean;
  id?: number;
  from?: string;
  data?: any;
}

interface RentalPriceSetting {
  reload?: boolean;
  price_type?: number;
}

@Injectable()
export class InventoryService {
  config: InventoryServiceConfig;
  private subject = new BehaviorSubject<any>(new Reload());
  reloadDetails = this.subject.asObservable();
  reload(load: Reload) {
    this.subject.next(load);
  }

  private rentSub: RentalPriceSetting = { reload: false };
  rentalPriceSetting = new BehaviorSubject(this.rentSub);
  private subjectRese = new BehaviorSubject<any>(null);
  reserve = this.subjectRese.asObservable();
  reserveReload(load) {
    this.subjectRese.next(load);
  }

  constructor(
    @Optional() config: InventoryServiceConfig,
    private http: HttpService,
    private location: Location,
    private router: Router
  ) {
    this.config = config;
  }

  getAllProduct(p, l, query?) {
    const params = query ? query : "";
    const loc = GET_USER().location_id;
    return this.http
      .get(
        `products?location=${loc ? loc : ""}&page_no=${p ? p : 1}&limit=${
          l ? l : 20
        }${params}`
      )
      .pipe(map(res => res));
  }


  getAllPosProduct(p, l,search_type,query?) {
    const params = query ? query : "";
    const loc = GET_USER().location_id;
    return this.http
      .get(`products/pos?location=${loc}&page_no=${p ? p : 1}&limit=${
        l ? l : 20
      }&rental_type=${search_type}&token=${GET_PosCartToken()}${params}`)
      .pipe(map(res => res));
  }

  searchPosProduct(p, l,search) {
    const loc = GET_USER().location_id;
    return this.http.get(`products?location=${loc}&page_no=${p ? p : 1}&limit=${
      l ? l : 20
    }&search=${search}`);
  }

  getAllPosProductBysearch(p, l,searchValue) {
    const loc = GET_USER().location_id;
    var data={"limit":l?l:20,"page_no":p ? p : 1,"category_id":"","search":searchValue,"location":loc ? loc : ""}

    return this.http
      .post(`search/products`,data)
      .pipe(map(res => res));
  }

  getAllPackages(p, l, query?) {
    const params = query ? query : "";
    // const loc = GET_USER().location_id;
    return this.http
      .get(
        `package?page_no=${p ? p : 1}&limit=${
          l ? l : 20
        }${params}`
      )
      .pipe(map(res => res));
  }

  // getAllProducts() {
  //   return this.http.get("assets/search");
  // }
  // /assets/search?location=73&search=test

  searchProduct(search) {
    const loc = GET_USER().location_id;
    return this.http.get(`assets/search?location=${loc}&${search}`);
  }

  searchStoreProduct(search) {
    return this.http.get(`store-products?${search}`);
  }

  addAsset(data) {
    return this.http.post("assets", data);
  }
  addBulkAsset(data) {
    return this.http.post("assets/bulk", data);
  }

  updateAsset(data, id) {
    return this.http.post(`assets/${id}`, data);
  }

  cloneAssetById(asset_id) {
    const data = {
      asset_id: asset_id
    };
    return this.http.post("assets/clone", data);
  }

  retireAssetById(asset_id) {
    const data = {
      asset_id: asset_id
    };
    return this.http.post("assets/retire", data);
  }

  getAssetComment(content_id, query?) {
    console.log(content_id);
    let url = "";
    if (content_id && !query) {
      url = `assets/comments?content_id=${content_id}`;
    } else {
      url = `assets/comments?content_id=${content_id}/${query}`;
    }
    // assets/comments?content_id=${content_id}
    return this.http.get(url).pipe(
      map(res => {
        return res.result;
      }),
      catchError(err => {
        return of([]);
      })
    );
  }

  postAssetComment(data) {
    return this.http.post("assets/add-comment", data);
  }

  getAssetHistory(content_id) {
    return this.http.get(`assets/tracking-history?content_id=${content_id}`);
  }

  getAssetService(id) {
    return this.http.get(`asset-servicing/${id}/list`);
  }

  addAssetService(data) {
    return this.http.post("asset-servicing", data);
  }

  updateAssetService(data, id) {
    return this.http.post(`asset-servicing/${id}`, data);
  }

  getAssetServiceById(id) {
    return this.http.get(`asset-servicing/${id}`);
  }

  formateSearchList(data) {
    console.log(data);
    $(".admin-cart .dropdown-menu.show .dropdown-item").unbind();
    const perId = $(".admin-cart .dropdown-menu.show").attr("id");
    var c = document.getElementById(perId).children;
    //   console.log(c.length, data.length);
    for (let j = 0; j < data.length; j++) {
      var id = c[j].getAttribute("id");
      var original = document.getElementById(id);
      // Create a replacement tag of the desired type
      var replacement = document.createElement("div");

      // Grab all of the original's attributes, and pass them to the replacement
      for (var i = 0, l = original.attributes.length; i < l; ++i) {
        var nodeName = original.attributes[i].name;
        var nodeValue = original.attributes[i].value;
        if (nodeName != "type") {
          replacement.setAttribute(nodeName, nodeValue);
        }
      }

      // Persist contents
      const r = data[j];
      // console.log(r);
      const chain = `<div class="colorPurpel"><small style="font-style: italic">${
        r.chain
      }</small></div>`;
      const buy = `<button class="btn btn-sm btn-xsm btn-outline-dark buy-search" data-attr="${
        r.variants_products_id
      }" style="margin-right: 10px;">Buy</button>`;
      const rent = `<button class="btn btn-sm btn-xsm btn-outline-danger rent-search" data-attr="${
        r.variants_products_id
      }">Rent</button>`;
      const notAdded = `<small>(Price not added)</small>`;
      replacement.innerHTML = `<div>${r.name}</div>${
        r.chain ? chain : ""
      }<div>${r.buy ? buy : ""} ${r.rent ? rent : ""} ${
        r.rent || r.buy ? "" : notAdded
      }</div>`;

      original.parentNode.replaceChild(replacement, original);
    }
  }

  getSupplier() {
    return this.http.get("suppliers/list").pipe(map(res => res.result));
  }

  getCustomFields(page, limit,searchTerm?) {
    let url='';
    if(searchTerm){
      url=`products/custom-fields?page_no=${page}&limit=${limit}&search=${searchTerm}`
    }
    else{
      url=`products/custom-fields?page_no=${page}&limit=${limit}`
    }

    return this.http
      .get(url)
      .pipe(map(res => res.result));
  }


  getCustomFieldByName(name) {
  return this.http.get(`products/custom-fields/${name}`).toPromise();
  }

  addOrCustomField(data,id?) {
    if(id){
      return this.http.post(`products/custom-fields/${id}`, data).toPromise();
    }
    else{
      return this.http.post(`products/custom-fields`, data).toPromise();
    }
   
  }


  deleteCustomField(id) {
    return this.http.delete(`products/custom-fields/${id}`).toPromise();
  }


  addProduct(data) {
    return this.http.post("products", data).toPromise();
  }

  addAbbrevietedProduct(data) {
    return this.http.post("products/create", data).toPromise();
  }

  getProductDescription(product_id) {
    return this.http.get(`products/${product_id}`).pipe(map(res => res.result));
  }

  updateProductDescription(product_id, data) {
    let format_data = Object.assign({}, data);
    delete format_data.id;
    return this.http.post(`products/${product_id}`, format_data).toPromise();
  }

  getAllCategory() {
    return this.http.get(`categories/lists`).pipe(map(res => res.result));
  }

  getInventoryCount() {
    return this.http.get("inventory-summary?product=true").toPromise();
  }

  deleteProduct(product_id) {
    return this.http.delete(`products/${product_id}`).toPromise();
  }

  changeRentalPriceType(vp_id, price_type) {
    return this.http
      .post(`product-prices/${vp_id}/type`, { price_type: price_type })
      .toPromise();
  }
  getRelatedProductList(product_id) {
    return this.http
      .get(`products/${product_id}/related`)
      .pipe(map(res => res.result));
  }

  addRelatedProduct(product_id, data) {
    return this.http.post(`products/${product_id}/related`, data).toPromise();
  }

  deleteRelatedProduct(pro_id, related_product_id) {
    return this.http
      .delete(`products/${pro_id}/${related_product_id}/related/remove`)
      .toPromise();
  }

  getAttributeList(product_id) {
    //  console.log(product_id);
    return this.http
      .get(`products/${product_id}/variant`)
      .pipe(map(res => res));
  }

  getAttributeChain(product_id) {
    return this.http.get(`products/${product_id}/variant-set/list`).pipe(
      map(res => res.result),
      catchError(err =>
        err.code === 404 ? throwError("Not found") : throwError({ data: [] })
      )
    );
  }

  getLocation() {
    return this.http.get(`products/locations`).pipe(
      map(res => res.result),
      catchError(e => of({ data: [] }))
    );
  }

  getStoraLocations() {
    return this.http.get('locations').toPromise();
  }

  getAtrributeSet() {
    return this.http.get(`variants/value`).pipe(
      map(res => res.result),
      catchError(e => of({ data: [] }))
    );
  }

  updateAttribute(pro_id, data) {
    return this.http.post(`products/${pro_id}/variant/edit`, data).toPromise();
  }

  deleteAttribute(pro_id, data) {
    return this.http
      .post(`products/${pro_id}/variant/delete`, data)
      .toPromise();
  }

  deleteAttributeSet(pro_id) {
    return this.http
      .delete(`products/${pro_id}/variant-set/delete`)
      .toPromise();
  }

  addAttributeSet(product_id, data) {
    return this.http
      .post(`products/${product_id}/variant-set/add`, data)
      .toPromise();
  }

  addAttribute(product_id, data) {
    return this.http.post(`products/${product_id}/variant`, data).toPromise();
  }

  getImageList(product_id, attr_id) {
    return this.http
      .get(`products/${product_id}/${attr_id}/images`)
      .pipe(map(res => res.result));
  }

  makeFeatureImage(product_id, data) {
    return this.http.post(`products/${product_id}/feature`, data).toPromise();
  }

  deleteProductImage(img_id) {
    return this.http.delete(`media/${img_id}/delete`).toPromise();
  }

  addVideoLink(product_id, data) {
    return this.http.post(`products/${product_id}/video`, data).toPromise();
  }

  getCategoryList(product_id) {
    return this.http
      .get(`products/${product_id}/category`)
      .pipe(map(res => res.result));
  }

  getPriceList(product_id, attr_id, is_vp?: boolean) {
    const url = is_vp
      ? `products/${product_id}/${attr_id}/price?is_vp=1`
      : `products/${product_id}/${attr_id}/price`
    return this.http
      .get(url)
      .pipe(map(res => res.result));
  }

  addProductPrice(product_id, attr_id, data) {
    return this.http
      .post(`products/${product_id}/${attr_id}/price`, data)
      .toPromise();
  }

  addPrice(attr_id, data) {
    return this.http.post(`product-prices/${attr_id}`, data).toPromise();
  }

  deletePrice(price_id) {
    return this.http.delete(`product-prices/${price_id}`).toPromise();
  }

  getrentalPrice(vp_id, type, price_type?: string) {
    let url = `product-prices/${vp_id}/${type}`;
    if (price_type === 'vp') {
      url = url + `?is_vp=1`;
    } 
    return this.http.get(url).pipe(
      map(res => {
        return res.result.data;
      }),
      catchError(err => {
        return of([]);
      })
    );
  }

  addProductPriceInfo(product_id, attr_id, data) {
    return this.http
      .post(`products/${product_id}/${attr_id}/product/price`, data)
      .toPromise();
  }


  addProductBuyPrice(attr_id, data) {
    return this.http
      .post(`product-prices/${attr_id}/buy`, data)
      .toPromise();
  }

  getPriceInfo(product_id, attr_id, is_vp) {
    const url = is_vp
      ? `products/${product_id}/${attr_id}/product/getprice?is_vp=1`
      : `products/${product_id}/${attr_id}/product/getprice`;
    return this.http
      .get(url)
      .pipe(map(res => res.result));
  }

  updateProductPrice(attr_id, data) {
    return this.http.post(`prices/${data.id}/${attr_id}`, data).toPromise();
  }
  updateFlexPrice(id, data) {
    return this.http.post(`product-prices/${id}/edit`, data).toPromise();
  }
  deleteFlexPrice(id) {
    return this.http.delete(`product-prices/${id}/delete`).toPromise();
  }

  deleteProductPrice(id) {
    return this.http.delete(`prices/delete/${id}`).toPromise();
  }

  getDetailsPageImage(id) {
    return this.http.get(`products/${id}/feature`).pipe(map(res => res.result));
  }

  getDetailsPageSummary(id) {
    return this.http.get(`products/${id}/summary`).pipe(map(res => res.result));
  }

  getTags() {
    return this.http.get(`tags`).pipe(map(res => res.result));
  }

  getCustomFieldList(id?) {
    if(id)
    {
      return this.http.get(`products/custom-fields/values/${id}`).pipe(map(res => res.result));
    }
    else{
      return this.http.get(`products/custom-fields/values`).pipe(map(res => res.result));
    }
  }

  getAvailability(product_id) {
    return this.http
      .get(`products-availabilities/${product_id}/reservation`)
      .pipe(map(res => res.result));
  }

  addReservation(product_id, data) {
    return this.http
      .post(`products-availabilities/${product_id}/add`, data)
      .toPromise();
  }

  updateReservation(data) {
    return this.http.post(`products-availabilities/edit`, data).toPromise();
  }

  deleteReservation(id) {
    return this.http.delete(`products-availabilities/${id}/delete`).toPromise();
  }

  getReservationList(page, limit) {
    return this.http
      .get(
        `products/reservation/list?page_no=${page ? page : 1}&limit=${
          limit ? limit : 10
        }`
      )
      .pipe(map(res => res.result));
  }

  getProduct(product_id) {
    return this.http
      .get(`products/${product_id}/view`)
      .pipe(map(res => res.result));
  }

  createOrder(data) {
    return this.http.post(`products/reservation/order`, data).toPromise();
  }

  getOrderStatus() {
    return this.http.get("order/status").pipe(map(res => res.result.data));
  }

  exportProduct(data): any {
    return this.http.getBlob(`products/export${data}`);
  }

  downloadImportedFile(data) {
    return this.http.getBlob(`products/download${data}`);
  }

  getUploadFileProgress(type) {
    return this.http.get(`products/import/status?type=${type}`).pipe(map(res => res));
  }

  getImportHistory() {
    return this.http.get(`products/import/history`).toPromise();
  }

  bulkDeleteProduct(data) {
    return this.http.post(`products/delete`, data).toPromise();
  }

  getQtyListPro(page?, filter?, location_id?) {
    return this.http.get(
      `get-quantity-list?page_no=${
        page ? page : 1
      }&limit=100&location=${location_id}${filter ? filter : ""}`
    );
  }

  saveQtyPro(data) {
    return this.http.post(`update-quantity`, data).toPromise();
  }

  getCalenderData(id, params, v_p_id) {
    return this.http
      .get(
        `calendar/available?location=${
          GET_USER().location_id
        }&product_id=${id}&variants_products_id=${v_p_id}&start_date=${
          params.start_date
        }&end_date=${params.end_date}`
      )
      .pipe(
        map(res => res.result),
        catchError(err => of({}))
      );
  }

  // get product id for sidebar

  summarNote() {
    return {
      height: 220,
      toolbar: [
        ["style", ["style"]],
        [
          "font",
          [
            "bold",
            "italic",
            "underline",
            "strikethrough",
            "superscript",
            "subscript",
            "clear"
          ]
        ],
        ["fontname", ["fontname"]],
        ["fontsize", ["fontsize"]],
        ["color", ["color"]],
        ["para", ["ol", "ul", "paragraph", "height"]],
        ["table", ["table"]],
        ["insert", ["link"]],
        ["view", ["undo", "redo", "fullscreen", "codeview", "help"]]
      ]
    };
  }

  summarNoteMinimum() {
    return {
      height: 220,
      toolbar: [
        ["style", ["style"]],
        [
          "font",
          [
            "bold",
            "italic",
            "underline",
            "clear"
          ]
        ],
        ["fontname", ["fontname"]],
        ["fontsize", ["fontsize"]],
        ["color", ["color"]],
        ["para", ["ol", "ul", "paragraph"]],
        ["view", ["undo", "redo", "codeview"]]
      ]
    };
  }

  formatePrice(data) {
    if (data && data.length > 0) {
      return FormatPrice(data);
    }
    return data;
  }

  formateAttribute(data) {
    return FormateAttribute(data);
  }

  datePicker() {
    $("#purchase-date-start").datepicker({
      todayHighlight: true,
      orientation: "bottom left",
      format: "yyyy-mm-dd",
      templates: {
        leftArrow: '<i class="la la-angle-left"></i>',
        rightArrow: '<i class="la la-angle-right"></i>'
      },
      startDate: new Date()
    });
    $("#purchase-date-end").datepicker({
      todayHighlight: true,
      orientation: "bottom left",
      format: "yyyy-mm-dd",
      templates: {
        leftArrow: '<i class="la la-angle-left"></i>',
        rightArrow: '<i class="la la-angle-right"></i>'
      },
      startDate: new Date()
    });
  }

  getAvailableQty(res, quant, sd?, dur?) {
    const qty = res
      .filter(d => {
        return this.checkDate(d.start_date, d.end_date, sd, dur) && !d.order_id;
      })
      .map(q => {
        return q.quantity;
      })
      .reduce((t, i) => {
        return t + i;
      }, 0);
    return quant - qty;
  }

  checkDate(s, e, i, d) {
    let date = new Date();
    if (i) {
      date = new Date(i);
    }
    let cur = new Date(
      date.getFullYear(),
      date.getMonth(),
      date.getDate(),
      12,
      0
    ).getTime();
    let curEnd = cur + 86400000 * (d - 1);
    let st = new Date(
      new Date(s).getFullYear(),
      new Date(s).getMonth(),
      new Date(s).getDate(),
      0,
      0
    ).getTime();
    let end = new Date(
      new Date(e).getFullYear(),
      new Date(e).getMonth(),
      new Date(e).getDate(),
      23,
      59
    ).getTime();
    return (st <= cur && end >= cur) || (end > cur && st < curEnd);
  }

  calculateEndFromArray(list) {
    let index = list.findIndex(d => {
      return d["rent_type"] == "daily";
    });
    if (index > -1) {
      return index;
    } else {
      let h = list.findIndex(d => {
        return d["rent_type"] == "hourly";
      });
      if (h > -1) {
        return h;
      } else {
        return list.findIndex(d => {
          return d["rent_type"] == "weekly";
        });
      }
    }
  }

  calculateEndDate(Startdate, d) {
    let date = new Date(Startdate);
    let cur = new Date(
      date.getFullYear(),
      date.getMonth(),
      date.getDate(),
      12,
      0
    ).getTime();
    let end = new Date(cur + 86400000 * (d - 1));
    return end.getFullYear() + "-" + (end.getMonth() + 1) + "-" + end.getDate();
  }

  getProId(route) {
    let id = route.parent.parent.parent.snapshot.params.product_id;
    id = id ? id : route.parent.parent.parent.parent.snapshot.params.product_id;
    return id;
  }

  FormatSelect2OptionData(data) {
    let formated_data = [];
    formated_data.push({ id: 0, text: "-Select Attribute-" });
    for (let att of data) {
      let obj = {};
      obj["id"] = att.id;
      obj["text"] = att.name;
      obj["children"] = this.formatSelect2List(att.attributes, "name");
      formated_data.push(obj);
    }

    return formated_data;
  }

  formatToNumber(data) {
    let newData = [];
    for (let d of data) {
      newData.push(parseInt(d));
    }
    return newData;
  }

  formatSelect2List(data, prop) {
    for (let d of data) {
      d["name"] = d[prop];
    }
    return data;
  }

  formatAttributeList(data) {
    const arr = [],
      ids = [];
    for (let i of data) {
      const d = {
        id: i.attribute_set.map(a => a.attribute_id).join("-"),
        name: i.attribute_set
          .map(a => a.attribute_set_name + "(" + a.attribute_name + ")")
          .join(", "),
        default: i.default
      };
      const index = ids.indexOf(d.id);
      if (index > -1) {
        arr[index].location.push(i.location.id);
        if (d.default) {
          arr[index]["default"]++;
        }
      } else {
        const obj = {};
        obj["id"] = d.id;
        obj["chain"] = d.name;
        obj["location"] = [];
        obj["location"].push(i.location.id);
        obj["default"] = 0;
        if (d.default) {
          obj["default"]++;
        }
        arr.push(obj);
        ids.push(d.id);
      }
    }
    arr.sort((a, b) => {
      if (b.default == a.default) {
        return b.location.length - a.location.length;
      } else {
        return b.default - a.default;
      }
    });
    return arr;
  }

  formatQtyList(data, loc) {
    let i = 1;
    return data.map(d => {
      d["id"] = i++;
      d.barcode = d.barcode ? d.barcode : "";
      const arr = [];
      d.location_data = d.location_data.map(l => {
        l.location = parseInt(l.location);
        l["chain"] = d.chain;
        return l;
      });
      for (let l of loc) {
        let fl = d.location_data.find(f => l.id == f.location);
        arr.push(
          fl
            ? fl
            : {
                attributes_products_id: null,
                location: l.id,
                quantity: 0,
                chain: d.chain
              }
        );
      }
      d.location_data = arr;
      return d;
    });
  }

  checkRentalPriceValidation(value) {
    let str = "";
    let mgs = [];
    let data = [];
    let items = [];
    data = value;
    //   console.log(data)
    for (let i = 0; i < data.length; i++) {
      for (let c in data[i]) {
        if (
          !data[i][c].duration &&
          !data[i][c].price &&
          !data[i][c].flex_price
        ) {
        } else {
          //  console.log(data[i][c])
          items.push(data[i][c]);
        }
      }
    }
    for (let i = 0; i < items.length; i++) {
      for (let c in items[i]) {
        if (items[i][c] === "" && mgs.indexOf(c) === -1) {
          mgs.push(c);
        }
      }
    }
    for (let i = 0; i < mgs.length; i++) {
      let m = "";
      if (String(mgs[i]) == "price") {
        m = "amount";
      }
      if (String(mgs[i]) == "duration") {
        m = "duration";
      }
      if (String(mgs[i]) == "flex_price") {
        //  console.log(mgs[i]);
        m = "additional amount ";
      }
      if (String(mgs[i]) == "flex_duration") {
        // console.log(mgs[i]);
        m = "additional duration  ";
      }

      str += m + ",";
    }
    return { str: str, count: items.length };
  }

  getProductAssets(filter?, query?) {
    // console.log(filter, query);
    let url = "";
    if (filter && query) {
      // url = `assets?${filter}product_id=${product_id}`;
    } else if (!filter && query) {
      url = `assets?${query}`;
    } else if (filter && !query) {
      url = `assets?${filter}`;
    } else {
      url = "assets";
    }

    return this.http.get(url).pipe(
      map(res => {
        return res.result;
      }),
      catchError(err => {
        return of([]);
      })
    );
  }
  getAssetDetails(id) {
    return this.http.get(`assets/${id}/details`).pipe(
      map(res => {
        return res.result.data;
      }),
      catchError(err => {
        return of(null);
      })
    );
  }

  deleteAsset(id) {
    return this.http.delete("assets/" + id).toPromise();
  }

  getAssetById(id) {
    return this.http.get(`assets/${id}`);
    // .pipe(map(res => res.result))
  }
  checkAssetSLNo(slNo) {
    return this.http.get("assets/check-serial?search=" + slNo).pipe(
      map(res => {
        return res.result.data;
      }),
      catchError(err => {
        return of(null);
      })
    );
  }

  //Package Api
  addProductInPackage(data) {
    return this.http.post("product-packages", data).toPromise();
  }
  deleteProductInPackage(item_id) {
    return this.http.delete("product-packages/"+item_id).toPromise();
  }
  UpdateProductQtyInPackage(item_id,quantity) {
    return this.http.post("product-packages/"+item_id, {quantity:quantity}).toPromise();
  }
  getProductsInpackage(package_id) {
    return this.http.get("product-packages/" + package_id).pipe(
      map(res => {
        return res.result.data;
      })
    );
  }

  updateAssetStatusCondition(obj, id) {
    const data = {
      'current_status' : obj.current_status,
      'current_condition' : obj.current_condition
    };
    return this.http.post(`update-value/Assets/${id}`, data);
  }

  GetAvailableProductList(data: any) {
    return this.http.post('products-availabilities', data).toPromise();
  }

  getNoOfRecords(): Promise<any> {
    return this.http.get('products/export/records').toPromise();
  }

  getBooksSearchResult(book_name: string, page: number | string): Observable<any> {
    const queryParams = {
      query: book_name,
      page_no: page
    };
    return this.http
      .get("books/search", { params: queryParams })
      .pipe(
        map(res => res.result),
        catchError(err => throwError(err))
      );
  }

  addToInventoryFromISBN(book: any) {
    return this.http.post('books/create', book).toPromise();
  }

  cloneProduct(id: number) {
    const data = {
      product_id: id
    }
    return this.http.post('products/copy', data).toPromise();
  }

  savePackageContent(data, id) {
    return this.http.post(`product/settings/${id}`, data).toPromise();
  }

  getPackageContent(id) {
    return this.http.get(`product/settings/${id}/package_content`).toPromise();
  }

}
