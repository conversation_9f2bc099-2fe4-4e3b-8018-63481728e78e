

export interface ProductList{
    data:ProductListData;
    categories?:{}
}

export interface ProductListData{
    id: number;
    name: string;
    user_id: number;
    sub_title: string;
    description: string;
    supplier_id: number;
    quantity: number | string;
    sales_tax: number;
    idw_tax: number;
    barcode: string;
    minimum_rental_period: number;
    deposit_required: null;
    deposit_amount: number;
    creator_name: string;
    creator_email: string;
    price_id: number;
    price: number;
    hourly_status: boolean;
    daily_status: boolean;
    weekly_status: boolean;
    hourly_price: number;
    daily_price: number;
    weekly_price: number;
    category_id: number;
    category_name: string;
    supplier_name: string;
    check: boolean;
    client_specific_id: string;
    type?: number;
}

export class InventoryServiceConfig {
    settings: any;
}

export class ProductServiceConfig {
    settings: any;
}

export class Descriptuion{
    id?: number;
    name: string;
    tags?: any[];
    description?: string;
    supplier_name?: string;
    supplier_id?: number;
    supply_id?: string;
    suppliers?: any[];
    status?: number = 2;
    driving_license?: boolean = false;
    keyword: string;
    featured: boolean;
    is_default_weight?: boolean = true;
    weight_amount?: number;
    weight_unit?: string = 'pound';
    sales_tax: string;
    deposit_amount: string;
    ldw_tax: string;
    is_tracked?: number = 0;
    product_asset?: number;
    product_quantity?: number;
    do_track ?= false;
    do_untrack ?= false;
    type?: number;
    client_specific_id: string;
    free_shipping?: boolean;
}

export class Supplier{
    id:number;
    text:string;
}

export class ProductSearch{
    name:string;
    type_id:string='';
    category_id:string = '';
    status:string = '';
    supplier_name:string = '';
    supplier_id: string;
    barcode:string;
    secondary_barcode:string;
    image: boolean;
    rental_type: string = '';
    avd_search: boolean = true ;
}


export class CustomerSearch{
    first_name:string;
    last_name:string='';
    mobile:string = '';
    email:string = '';
    avd_search: boolean = true ;
}

export class OrderItem {
    product_id: number;
    store_id: number;
    user_id: number;
    attributes: any;
    price: number;
    quantity: number;
    rent_start: string;
    rent_end: string;
    rental_duration: number;
    rental_type: string;
    term: number;
    sales_tax: string;
    deposit_amount: number;
    deposite_tax: string;
}

export class OrderCustomer {
    first_name: string;
    last_name: string;
    country_id: string;
    phone: string;
    mobile: string;
    email: string;
    address_line1: string;
    city: string;
    state_id: string;
    zipcode: string;
    status: number;
}

export class ProdyctQty {
    product_id: number;
    chain_name: string;
    chain: string;
    imgae: string;
    quantity:string
    barcode: string;
    supplier_name: string;
    cost: string;
    purchase_date: string;
}

export class Location {
    attributes_products_id: number;
    location: number;
    quantity: number;
}
