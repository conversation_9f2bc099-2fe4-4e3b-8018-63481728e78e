import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-inventory-dashboard',
  templateUrl: './inventory-dashboard.component.html',
  styleUrls: ['./inventory-dashboard.component.css']
})
export class InventoryDashboardComponent implements OnInit {

  constructor(private router: Router) { }

  ngOnInit() {
    //window.scrollTo(0,0);
  }

  navigateTo(url) {
    this.router.navigate([url]);
  }

}
