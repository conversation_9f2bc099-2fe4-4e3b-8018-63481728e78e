import { <PERSON>mponent, OnInit, OnDestroy, ElementRef, ViewChild, HostListener, AfterViewInit } from '@angular/core';
import { Helpers } from '../../../../helpers';
import { DialogBoxComponent } from '../../../../modules/dialog-box/dialog-box.component';
import { SidebarService } from '../../sidebar-service/sidebar.service';
import { AlertService } from '../../../../modules/alert/alert.service';
import { ActivatedRoute } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Pagi } from '../../../../modules/pagination/pagi.model';
import { CustomFields } from '../../settings/models/settings.models';
import { CustomFieldsService } from './custom-fields.service';
import { InventoryService } from '../inventory-serveice/inventory.service';





@Component({
  selector: 'app-custom-fields',
  templateUrl: './custom-fields.component.html',
  styleUrls: ['./custom-fields.component.css']
})
export class CustomFieldsComponent implements OnInit, AfterViewInit {

  customFieldList: CustomFields[] = [];
  sideBaropen: boolean;
  customField: CustomFields;
  EditMode: boolean;
  copyDone: boolean;
  pagi: Pagi = new Pagi();
  search='';

  @HostListener('window:resize', ['$event'])
  onResize(event) {
    if (this.sideBaropen) {
      $('.native-routing').css('display', 'block');
      this.sidebarS.openSidebar();
    }
  }

  @ViewChild('hasCusAlert') alertContainer: ElementRef;

  constructor(
    private customFieldService: CustomFieldsService,
    private sidebarS: SidebarService,
    private inventoryService: InventoryService,
    private alertS: AlertService,
    private activeRoute: ActivatedRoute,
    private modalService: NgbModal
  ) {
    const list = this.activeRoute.snapshot.data['list'];
    if (list) {
      this.customFieldList = list.data;
      this.pagi.page=parseInt(list.page_no);
      this.pagi.limit=parseInt(list.limit);
      this.pagi.total=parseInt(list.limit);
    }
  }

  ngOnInit() {}

  ngAfterViewInit() {
    //window.scrollTo(0, 0);
    this.closeSidebar();
    this.executeAction();
  }


  getCustomFieldList(page,limit,searchTerm?){
     Helpers.setLoading(true);

      this.inventoryService.getCustomFields(page,limit,searchTerm).subscribe(res=>{
        Helpers.setLoading(false);
        this.customFieldList=res.data;
        this.pagi.page=parseInt(res.page_no);
        this.pagi.limit=parseInt(res.limit);
      },err=>{
        Helpers.setLoading(false);
      })
    
  }

  trackTag(index, tag) {
    return tag ? tag.id : null;
  }

  getName(data:any=[]){
    let nameList='';
    let index=0;

    if(data && data.length>0)
    {
      data.map(x=>{
        index++;
        nameList=nameList+x.name
        nameList= data.length==index ? nameList+'' : nameList+', '

      })
    }
    else{
      nameList='All';
    }
    return nameList;
  }

  editCustomField(custom_fields) {
    this.openSidebar();
    this.EditMode = true;
    this.inventoryService.getCustomFieldByName(custom_fields.name).then(res=>{
     this.customField=res.result.data;
     this.customFieldService.customFieldReload.next({ reload: false, editMode: true, custom_fields:  this.customField });
    }).catch(err=>{
      console.log(err);
    })

  }

  AddCustomField() {
    this.openSidebar();
    this.customField = new CustomFields();
    this.EditMode = false;
    this.customFieldService.customFieldReload.next({ reload: true, editMode: false});
  }


  reloadTable(e) {
    if(e.page && e.limit)
    {
      this.getCustomFieldList(e.page,e.limit);
    }
    
  }
  
  openSidebar() {
    $('.native-routing').css('display', 'block');
    this.sidebarS.openSidebar();
    this.sideBaropen = true;
  }

  deleteCustomField(id, i) {
    const modalRef = this.modalService.open(DialogBoxComponent, {
      centered: true,
      size: 'sm',
      windowClass: 'animated fadeIn'
    });
    modalRef.componentInstance.massage = 'Are you sure you want to delete?';
    modalRef.result
      .then((result) => {
        if (result) {
          Helpers.setLoading(true);
          this.archiveCustomField(id, i);
        }
      }, (res) => {
        console.log(res);
      });

  }

  archiveCustomField(id, i) {
    this.inventoryService.deleteCustomField(id).then(
      res => {
        if(res.status=="OK")
        {
          this.customFieldList.splice(i, 1);
          this.alert({error: false, message: res.result.message});
        }
        else{
          this.alert({error: true, message: res.result.message});
        }
       
        
      }
    ).catch (
      err => {
        console.log(err);
        this.alert({error: true, message: 'Something wrong! Custom field has been not deleted'});
      }
    )
  }

  SubmitCustomField(event) {
    if (!event.alert.error) {
      // if (this.EditMode) {
      //   let fieldData=event.data;
      //   this.customFieldList.map(data=>{
      //     if(data.name==fieldData.name)
      //     {
      //       data.name=fieldData.name;
      //       data.label=fieldData.label;
      //       data.type=fieldData.type;
      //       data.products=fieldData.products;
      //       data.status=fieldData.status;
      //     }
      //     return data;
      //   })
      // } else {
      //   this.customFieldList.push(event.data);
      // }

      this.getCustomFieldList(this.pagi.page,this.pagi.limit);
      this.executeAction();
    }
    // console.log(event);
    this.alert(event.alert);
  }

  closeSidebar() {
    $('.close-sidebar').click((e) => {
      e.preventDefault();
      this.executeAction();
    });
    $('.close-sidebar-upper').click((e) => {
      e.preventDefault();
      this.executeAction();
    });
  }

  executeAction() {
    this.sideBaropen = null;
    this.sidebarS.removeSidebar();
    $('.native-routing').css('display', 'none');
  }

  alert(data) {
    Helpers.setLoading(false);
    if (data.error) {
      this.alertS.error(this.alertContainer, data.message, true, 5000);
    } else {
      this.alertS.success(this.alertContainer, data.message, true, 5000);
    }
  }

 


}