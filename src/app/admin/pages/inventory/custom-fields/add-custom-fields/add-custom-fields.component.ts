import { Component, OnInit, OnDestroy, Output, EventEmitter } from '@angular/core';
import { CustomFields } from '../../../settings/models/settings.models';
import { CustomFieldsService } from '../custom-fields.service';
import { InventoryService } from '../../inventory-serveice/inventory.service';

@Component({
  selector: 'app-add-custom-fields',
  templateUrl: './add-custom-fields.component.html',
  styleUrls: ['./add-custom-fields.component.css']
})
export class AddCustomFieldsComponent implements OnInit, OnDestroy {

  loader = false;
  customField: CustomFields
  edit = false;
  selectedProduct = [];
  is_show_search_product = false;
  selected_product_type=1;
  old_custom_field_name='';


  @Output() submitForm = new EventEmitter();

  constructor(
    private customFieldsService: CustomFieldsService,
    private inventoryService: InventoryService
  ) {

  }

  ngOnInit() {

    this.customFieldsService.customFieldReload.subscribe(val => {
      if (val.reload) {
        this.customField = new CustomFields();
        this.edit = val.editMode;
      }
      if (val.editMode) {
        this.edit = val.editMode;
        this.customField = val.custom_fields;
        this.selectedProduct = this.customField.products;

        this.is_show_search_product=this.customField.products && this.customField.products.length>0 ? true:false;
        this.selected_product_type=this.is_show_search_product ? 2:1;
        this.old_custom_field_name=this.customField.name;
      }
      else {
        this.customField = new CustomFields();
        this.selectedProduct = [];
      }
    });
  }

  ngOnDestroy() {

  }



  onChangeType(type_id) {
    let id = parseInt(type_id)
    if (id != -1) {
      if (id == 1) {
        this.selectedProduct = [];
        this.is_show_search_product = false;
      } else {
        this.is_show_search_product = true;
      }
    this.selected_product_type=id;
    }
    else{
      this.is_show_search_product=false;
      this.selectedProduct=[];
    }

  }

  selectProduct(product) {
    let prod = {
      id: product.product_id,
      name: product.name
    }
    this.selectedProduct.push(prod);

  }

  deleteProduct(id) {
    var index = this.selectedProduct.findIndex(x => x.id == id);
    if (index > -1) {
      this.selectedProduct.splice(index, 1);
    }
  }


  submit() {
    this.loader = true;
    let sendData = {
      name: this.customField.name,
      label: this.customField.label,
      type:this.customField.type,
      products: (this.selected_product_type==1 ? null: this.selectedProduct.map(d => { return d.id })),
      status: this.customField.status,
      is_private:this.customField.is_private
    };


    this.loader = true;

    this.inventoryService.addOrCustomField(sendData).then(res => {
      if (res.status == "OK") {
        this.customField.products = this.selectedProduct;
        this.sendEmitedData(res.result.data, false, res.result.message);
      }
      else {
        this.sendEmitedData([], true, res.result.message);
      }
      this.loader = false;
    }).catch(err => {
      this.sendEmitedData([], true, 'Something wrong! Custom field has been not added');
      this.loader = false;
    })


  }


  update() {
    this.loader = true;

    let sendData = {
      name: this.customField.name,
      label: this.customField.label,
      type: this.customField.type,
      products: (this.selected_product_type==1 ? null: this.selectedProduct.map(d => { return d.id })),
      status: this.customField.status,
      is_private:this.customField.is_private
    };

    this.inventoryService.addOrCustomField(sendData, this.old_custom_field_name)
      .then(
        res => {
          if (res.status == "OK") {
            this.sendEmitedData(res.result.data, false, res.result.message)
          }
          else {
            this.sendEmitedData([], true, res.result.message)
          }

          this.loader = false;
        }
      ).catch(
        err => {
          console.log(err);
          this.sendEmitedData([], true, 'Something wrong! Custom field has been not updated')
        }
      );
  }

  sendEmitedData(result, error, message) {
    this.loader = false;
    // console.log(result);
    const emit_data = { data: result, alert: { error: error, message: message } }
    this.submitForm.emit(emit_data);
  }

}
