<div style="padding-top: 35px;">
  <h4 class="colorPurpel">Add-Custom-Field</h4>

  <form class="m-form m-form--fit m-form--label-align-right">
    <div class="row">

      <div class="col-sm-6">
        <div class="row">
          <div class="col-sm-12">
            <div class="form-group m-form__group">
              <label for="type">Field Name<sup>*</sup></label>
              <input class="form-control m-input" id="name" type="text" name="name" [(ngModel)]="customField.name"
                required />
            </div>
          </div>
        </div>

      </div>

      <div class="col-sm-6">
        <div class="row">
          <div class="col-sm-12">
            <div class="form-group m-form__group">
              <label for="type">Field label<sup>*</sup></label>
              <input class="form-control m-input" id="label" type="text" name="label" [(ngModel)]="customField.label"
                required />
            </div>
          </div>
        </div>
      </div>

      <div style="display: none;" class="col-sm-12">
        <div class="row">
          <div class="col-sm-6">
            <div class="form-group m-form__group">
              <label for="type">Type</label>
              <select class="form-control m-input" id="type" name="type" [(ngModel)]="customField.type">
                <option value="-1">--Select Type--</option>
                <option value="textBox">Text box</option>
                <!-- <option value="radio">Radio</option>
                  <option value="select">Select</option> -->
              </select>
            </div>
          </div>
        </div>
      </div>

      <div class="col-sm-6">
        <div class="row">
          <div class="col-sm-12">
            <div class="form-group m-form__group">
              <label for="type">Product Type</label>
              <select class="form-control m-input" name="selected_product_type" [(ngModel)]="selected_product_type"
                (change)="onChangeType($event.target.value)">
                <option value="-1">--Select Product Type--</option>
                <option value="1">All</option>
                <option value="2">Specific Product</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      <div class="col-sm-6">
        <div class="row">
          <div class="col-sm-12">
            <div class="form-group">
              <label for="status">Status</label>
              <select class="form-control m-input" id="status" name="status" [(ngModel)]="customField.status">
                <option value="null">-Select Status-</option>
                <option value="false">Inactive</option>
                <option value="true">Active</option>
              </select>
            </div>
          </div>
        </div>
      </div>


      <div *ngIf="is_show_search_product" class="col-sm-12">
        <div class="row">
          <div class="col-sm-6">
            <div class="form-group m-form__group">
              <label for="type">Select products(s)</label>
              <app-search-product (onSelectedProduct)="selectProduct($event)"></app-search-product>

              <div class="type-table table-responsive">
                <table class="table table-hover">
                  <tbody *ngIf="selectedProduct.length > 0">
                    <tr *ngFor="let product of selectedProduct; let i = index">
                      <td>
                        {{ product.name }}
                      </td>
                      <td>
                        <a id="m_quick_sidebar_toggle" (click)="deleteProduct(product.id)"
                          class="m-portlet__nav-link btn m-btn m-btn--hover-danger m-btn--icon m-btn--icon-only m-btn--pill">
                          <i class="fa fa-trash"></i>
                        </a>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

            </div>
          </div>
        </div>
      </div>
      

      <div class="col-sm-6">
        <div class="form-group m-form__group">
            <label class="m-checkbox">
            <input type="checkbox" name="is_private" [(ngModel)]="customField.is_private">
            Is Private?
              <span></span>
            </label>
        </div>
      </div>

      <div class="col-sm-12">
        <div class="row">
          <div class="col-sm-6">
            <div class="m-form__actions m-form__actions" style="padding: 30px 0px;">
              <div *ngIf="loader; else button" class="m-loader m-loader--brand"
                style="width: 30px; padding-left: 30px; display: inline-block;"></div>
              <ng-template #button>
                <button type="button" class="btn btn-brand btn-sm" *ngIf="edit; else addbtn" (click)="update()">
                  <i class="fa fa-save"></i> <span style="padding-left:10px;">Update</span>
                </button>
                <ng-template #addbtn>
                  <button type="button" class="btn btn-brand btn-sm" (click)="submit()">
                    <i class="fa fa-save"></i> <span style="padding-left:10px;">Submit</span>
                  </button>
                </ng-template>
              </ng-template>
            </div>
          </div>
        </div>
      </div>



    </div>
  </form>
</div>