<div class="custom-alert" #hasCusAlert></div>

<div class="m-subheader">
    <div class="d-flex align-items-center">
        <div class="mr-auto">
            <h3 class="m-subheader__title m-subheader__title--separator">
                Custom Fields
            </h3>
            <ul class="m-subheader__breadcrumbs m-nav m-nav--inline">
                <li class="m-nav__item m-nav__item--home">
                    <a routerLink="/admin" class="m-nav__link m-nav__link--icon">
                        <i class="m-nav__link-icon la la-home"></i>
                    </a>
                </li>
                <li class="m-nav__separator">
                    <i class="fa fa-angle-right"></i>
                </li>
                <li class="m-nav__item">
                    <a class="m-nav__link">
                        <span class="m-nav__link-text">
                            Inventory
                        </span>
                    </a>
                </li>
                <li class="m-nav__separator" style="padding-left: 10px">
                    <i class="fa fa-angle-right"></i>
                </li>
                <li class="m-nav__item">
                    <a class="m-nav__link">
                        <span class="m-nav__link-text">
                           Custom-fields
                        </span>
                    </a>
                </li>
            </ul>
        </div>
    </div>
</div>
<!-- END: Subheader -->
<div class="m-content animated fadeIn">
    <div class="m-portlet">
        <div class="m-portlet__head title-head">
            <div class="m-portlet__head-caption">
                <div class="m-portlet__head-title">
                    <h3 class="m-portlet__head-text colorPurpel">
                        Add-Custom-Fields
                    </h3>
                    <div class="add-list-btn text-right">
                        <button class="btn btn-brand" (click)="AddCustomField()">
                            Add
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="m-portlet__body">
            <!--begin::Section-->
            <div class="">
                <div class="m-section__content price-table table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>
                                  Field Name
                                </th>
                                <th>
                                    Field label
                                  </th>
                                <!-- <th>
                                    Products
                                </th> -->
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody *ngIf="customFieldList.length>0; else noData">
                            <tr *ngFor="let customField of customFieldList; let i = 'index'; trackBy: trackTag; let o='odd'; let e='even'"
                                [ngClass]="{'odd-tr':o, 'even-tr':e}">
                                <td>{{ customField?.name }} </td>
                                <td>{{ customField?.label }} </td>
                                <!-- <td>
                                    {{getName(customField?.products) }}
                                </td> -->
                                <td>
                                    <span [ngClass]="{'green': customField?.status, 'red': !customField?.status}">
                                      {{customField?.status ? 'Active' : 'Inactive'}}
                                    </span>
                                  </td>
                                <td>
                                    <a (click)="editCustomField(customField)" title="Edit"
                                        class="m-portlet__nav-link btn m-btn m-btn--hover-info m-btn--icon m-btn--icon-only m-btn--pill">
                                        <i class="fa fa-edit"></i>
                                    </a>
                                    <a (click)="deleteCustomField(customField.name, i)" title="Delete"
                                        class="m-portlet__nav-link btn m-btn m-btn--hover-danger m-btn--icon m-btn--icon-only m-btn--pill">
                                        <i class="fa fa-trash"></i>
                                    </a>
                                </td>
                            </tr>
                        </tbody>
                        <ng-template #noData>
                            <tbody>
                                <tr>
                                    <td colspan="3">
                                        <h5 class="text-center">No Data Found</h5>
                                    </td>
                                </tr>
                            </tbody>
                        </ng-template>
                    </table>
                </div>

                 <!-- pagination Start-->
                <boot-pagination [totalSize]="pagi.total" [page]="pagi.page" [listSize]="pagi.limit" [pagelimit]="pagi.limit"
                (pageChange)="reloadTable($event)"></boot-pagination>
                <!-- pagination End-->
            </div>
            <!--end::Section-->
        </div>
    </div>
</div>


<!-- sidebar -->

<div class="native-routing animated">
    <button class="close-sidebar btn btn-sm btn-brand">
        <i class="fa fa-chevron-right"></i>
    </button>
    <span class="close-sidebar-upper">
        <i class="la la-close"></i>
    </span>
    <div class="native-routing-container">
        <app-add-custom-fields  (submitForm)="SubmitCustomField($event)"></app-add-custom-fields>
    </div>

</div>