import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { CustomFields } from '../../settings/models/settings.models';



export class CustomFieldsConfig {
    reload?: Boolean;
    custom_fields?: CustomFields;
    editMode?: boolean;
}

@Injectable()
export class CustomFieldsService {
    private subject: CustomFieldsConfig = { reload: false,  editMode: false};
    customFieldReload = new BehaviorSubject(this.subject);
}
