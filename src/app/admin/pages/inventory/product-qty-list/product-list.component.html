<div class="custom-alert" #hasCusAlert></div>

<!-- BEGIN: Subheader -->
<div class="m-subheader">
  <div class="d-flex align-items-center">
    <div class="mr-auto">
      <h3 class="m-subheader__title m-subheader__title--separator">
        Inventories
      </h3>
      <ul class="m-subheader__breadcrumbs m-nav m-nav--inline">
        <li class="m-nav__item m-nav__item--home">
          <a routerLink="/admin" class="m-nav__link m-nav__link--icon">
            <i class="m-nav__link-icon la la-home"></i>
          </a>
        </li>
        <li class="m-nav__separator">
          <i class="fa fa-angle-right"></i>
        </li>
        <li class="m-nav__item">
          <a class="m-nav__link">
            <span class="m-nav__link-text">
              Product List
            </span>
          </a>
        </li>
      </ul>
    </div>
  </div>
</div>
<!-- END: Subheader -->
<div class="m-content product-list animated fadeIn">
  <product-search #searchPro (search)="loadSearchData($event)"></product-search>
  <!--begin::Portlet-->
  <div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__head">
      <div class="m-portlet__head-caption">
        <div class="m-portlet__head-title m-product-title">
          <h3 class="m-portlet__head-text" style="width:100%;">
            Product List
          </h3>
        </div>
      </div>
    </div>
    <div class="m-portlet__body" style="position: relative;">
      <!--begin: Datatable -->
      <div class="row" style="padding-bottom: 20px;">
        <div
          class="col-sm-6 col-md-8"
          style="padding-top: 5px; padding-bottom: 5px;"
        >
          <button
            class="btn btn-sm btn-outline-dark"
            [disabled]="pagi.page == 1"
            style="margin-right: 20px;"
            (click)="reloadTable(false)"
          >
            << Previous
          </button>
          <button
            class="btn btn-sm btn-outline-dark"
            [disabled]="pagi.page == totalPages"
            style="margin-right: 20px;"
            (click)="reloadTable(true)"
          >
            Next >>
          </button>
          <span> Displaying page {{ pagi.page }} out of {{ totalPages }}</span>
        </div>
        <div class="col-sm-6 col-md-4">
          <div class="input-group m-input-group">
            <div class="input-group-prepend">
              <span class="input-group-text"> Current: {{ pagi.page }} </span>
            </div>
            <input
              type="number"
              min="0"
              class="form-control m-input"
              name="gotoPage"
              [(ngModel)]="goto"
              (keyup.enter)="gotoPage()"
              placeholder="Page No"
              autocomplete="off"
            />
            <div
              class="input-group-append"
              (click)="gotoPage()"
              style="cursor: pointer;"
            >
              <span class="input-group-text">
                Go
              </span>
            </div>
          </div>
          <small *ngIf="goto == 0 || goto > totalPages" class="info"
            >Minimum 1 and Maximum {{ totalPages }}</small
          >
        </div>
      </div>
      <!--begin::Section-->
      <div class="m-section">
        <div class="m-section__content price-table">
          <div *ngIf="loader" class="table-load m-loader m-loader--brand"></div>
          <div class="table-responsive" style="margin-bottom: 10px;">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>
                    Image
                  </th>
                  <th>
                    Product
                  </th>
                  <th>
                    Suppiler
                  </th>
                  <th class="text-center" style="min-width: 140px;">
                    Barcode
                  </th>
                  <th class="text-center" style="min-width: 140px;">
                    Quantity
                  </th>
                  <th class="text-center" style="min-width: 140px;">
                    Edit Quantity
                  </th>
                </tr>
              </thead>
              <tbody *ngIf="productLst.length < 1; else table">
                <tr *ngIf="!loader">
                  <td [attr.colspan]="4 + location.length">
                    <h4 class="text-center">No Data Found</h4>
                  </td>
                </tr>
              </tbody>
              <ng-template #table>
                <tbody>
                  <tr
                    *ngFor="
                      let pro of productLst;
                      let i = index;
                      let o = odd;
                      let e = even;
                      trackBy: trackPro
                    "
                    [ngClass]="{ 'odd-tr': o, 'even-tr': e }"
                  >
                    <td>
                      <img
                        *ngIf="pro.image; else alterImage"
                        id="product_home_details"
                        class="img-fluid img-avatar img-thumbnail"
                        src="{{
                          image + '/' + pro.product_id + '/' + pro.image
                        }}"
                        style="max-width:90px; cursor:pointer;"
                        onError="this.src='./assets/img/home/<USER>';"
                      />
                      <ng-template #alterImage>
                        <img
                          id="product_home_details"
                          class="img-fluid img-avatar img-thumbnail"
                          src="./assets/img/home/<USER>"
                          style="max-width:90px; cursor:pointer;"
                        />
                      </ng-template>
                    </td>
                    <td class="left-align">
                      <a
                        routerLink="/admin/inventory/{{
                          pro?.product_id
                        }}/details"
                        >{{ pro?.name }}</a
                      >
                      <br />
                      <span class="variant-text">{{ pro?.variants }}</span>
                    </td>
                    <td>{{ pro?.supplier_id }}</td>
                    <td>
                      {{ pro?.barcode }}
                      <!-- <input type="text" class="form-control m-input" [(ngModel)]="pro.barcode" autocomplete="off"> -->
                    </td>
                    <td>{{ pro?.quantity }}{{ pro?.update_quantity }}</td>
                    <td>
                      <div
                        *ngIf="saveClick == i; else SAVEBTN"
                        class="m-loader m-loader--brand"
                        style="width: 30px; display: inline-block;"
                      ></div>
                      <ng-template #SAVEBTN>
                        <div class="input-group edit-quantity">
                          <div class="input-group-prepend">
                            <button
                              id="add{{ i }}"
                              class="btn btn-default btn-sm btn-active"
                              type="button"
                              (click)="
                                onClickChangeQty(pro?.quantity, 'add', i)
                              "
                            >
                              Add
                            </button>
                            <button
                              id="set{{ i }}"
                              class="btn btn-default btn-sm"
                              type="button"
                              (click)="
                                onClickChangeQty(pro?.quantity, 'set', i)
                              "
                            >
                              Set
                            </button>
                          </div>
                          <input
                            id="q{{ i }}"
                            type="number"
                            value="0"
                            class="form-control quantity-input"
                          />
                          <div class="input-group-append">
                            <button
                              [disabled]="!pro.hasOwnProperty('type')"
                              class="btn btn-warning btn-sm"
                              type="button"
                              (click)="saveProduct(pro, i)"
                            >
                              Save
                            </button>
                          </div>
                        </div>
                      </ng-template>
                    </td>
                  </tr>
                </tbody>
              </ng-template>
            </table>
          </div>
        </div>
      </div>
      <!--end::Section-->
      <div class="row">
        <div
          class="col-sm-6 col-md-8"
          style="padding-top: 5px; padding-bottom: 5px;"
        >
          <button
            class="btn btn-sm btn-outline-dark"
            [disabled]="pagi.page == 1"
            style="margin-right: 20px;"
            (click)="reloadTable(false)"
          >
            << Previous
          </button>
          <button
            class="btn btn-sm btn-outline-dark"
            [disabled]="pagi.page == totalPages"
            style="margin-right: 20px;"
            (click)="reloadTable(true)"
          >
            Next >>
          </button>
          <span> Displaying page {{ pagi.page }} out of {{ totalPages }}</span>
        </div>
        <div class="col-sm-6 col-md-4">
          <div class="input-group m-input-group">
            <div class="input-group-prepend">
              <span class="input-group-text"> Current: {{ pagi.page }} </span>
            </div>
            <input
              type="number"
              min="0"
              class="form-control m-input"
              name="gotoPage"
              [(ngModel)]="goto"
              (keyup.enter)="gotoPage()"
              placeholder="Page No"
              autocomplete="off"
            />
            <div
              class="input-group-append"
              (click)="gotoPage()"
              style="cursor: pointer;"
            >
              <span class="input-group-text">
                Go
              </span>
            </div>
          </div>
          <small *ngIf="goto == 0 || goto > totalPages" class="info"
            >Minimum 1 and Maximum {{ totalPages }}</small
          >
        </div>
      </div>
      <!--end: Datatable -->
    </div>
  </div>
  <!--end::Portlet-->
</div>
