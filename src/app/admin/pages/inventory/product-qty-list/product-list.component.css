.btn-text{
    margin: 0 10px;
}
.btn-active{
    background-color: #eeeeee !important;
    color: black !important;
}

.edit-quantity{
width: 225px !important;
}


.decoration-none:hover{
    text-decoration: none!important;
}
.product-list-right-btn>.first-child-btn{
    margin-right: 30px;
}


.product-list .search-panel input{
    text-align: center;
}
.product-list .search-panel select{
    text-align-last: center;
    padding-right: 29px;
}

.product-list .search-panel .form1-search-btn{
    padding-left: 2.6rem!important;
}

.product-list .search-panel .form-group,.product-list .search-panel .m-form__group {
    padding:1rem;
}

.search-panal-action button{
    margin-right: 10px; 
}

.custom-alert{
    position: fixed;
    top: 90px;
    right: 30px;
    z-index: 5000;
}


.m-portlet__head-title{
    width: 100%;
}

.w-100{
    width: 100%;
}
.p-ib{
    padding-left: 10px;
    padding-right: 10px;
    display: inline-block;
    min-width: 75px;
    margin-bottom: 0px;
}
.quantity-input {
    max-width: 100px;
}
.table tr td, 
.table tr th {
    text-align: center;
}
table tr td a {
    float: unset;
}
/* .pagination .input-group .input-group-append > .input-group-text, .pagination .input-group .input-group-prepend > .input-group-text {
    color: #fff;
    background-color: #343a40;
    border-color: #343a40;
} */

.left-align{
    text-align: left !important;
}
.variant-text{
    font-size: 10px;
}
.img-thumbnail{
    max-width: 100px!important;
    height: 100px!important;
    object-fit: contain;
}
/*-- adition product list --*/
@media (max-width: 575px) {
    .mr-auto {
        width: 80%
    }
    .first-child-btn {
        margin-bottom: 10px;
    }
    .m-portlet .m-portlet__body {
        padding: 15px 15px;
    }
}


