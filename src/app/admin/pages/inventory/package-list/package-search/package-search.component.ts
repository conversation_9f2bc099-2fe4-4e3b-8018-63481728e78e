import { Component, OnInit, Output, <PERSON><PERSON><PERSON>ter, OnDestroy, Input, AfterViewInit, OnChanges } from '@angular/core';
import { ProductSearch, Supplier } from '../../product-models/inventory.models';
import { FORMAT_SEARCH } from '../../../../../globals/_classes/functions';
import { InventoryService } from '../../inventory-serveice/inventory.service';
import { Category } from '../../../settings/models/category.models';
import { ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { CartService } from '../../../../cart-service/cart.service';
import { ProductListService } from '../../product-list/product-service/product-list.service';

@Component({
  selector: 'app-package-search',
  templateUrl: './package-search.component.html',
  styleUrls: ['../package-list.component.css']
})
export class PackageSearchComponent implements OnInit {

  search: ProductSearch = new ProductSearch();
  filter:string;
  categories: Category[] = [];
  reset: boolean;
  suplliers: Supplier[] = [];
  location = [];
  sub: Subscription[] = [];
  loader: boolean;
  // abc = true;

  @Input('checkFilter') checkFilter: boolean;
  @Input () abc;
  @Output('search') searching:EventEmitter<string> = new EventEmitter();


  constructor(
    private activeRoute:ActivatedRoute,
    private cartS: CartService,
    private inventoryS:InventoryService,
    private proS: ProductListService
  ) {
    this.suplliers = this.activeRoute.snapshot.data['supplier'].data;
      if(this.suplliers) {
        this.suplliers = this.suplliers.map((s) => {
          s['text'] = s['name'];
          return s;
        });
      }
    this.sub[0] = this.cartS.location.subscribe(
      val => {
        if(val) {
          this.location = val;
        }
      }
    );
  }

  ngOnChanges() {
    if(this.checkFilter) {
      this.checkFilterData();
    }
  }

  ngOnInit() {
    console.log('abc ' + this.abc);
    this.inventoryS.getAllCategory().subscribe(
      res =>{
        this.categories = [];
        for(let d in res) {
          let obj: Category = new Category();
          obj.id = parseInt(d);
          obj.name = res[d];
          this.categories.push(obj);
        }
      },
      err => console.log(err)
    );
  }

  ngAfterViewInit() {
    this.checkFilterData();
  }

  ngOnDestroy() {
    for(let s of this.sub) {
      s.unsubscribe();
    }
  }

  checkFilterData() {
    const param = this.activeRoute.snapshot.queryParamMap.get('param');
    const data = param ? param : null;
    if(data && (!data.includes('search'))) {
      if(!$('.search-panel#advanceSearch').hasClass('dis-block')) {
        this.openAdvanceSearch();
      }
      setTimeout(() => {
        this.search = this.proS.formatFilter(data);
        this.reset = true;
        this.checkFilter = false;
      }, 100);
    }
  }

  categoryTrack(index,cat){
    return cat? cat.id:null;
  }

  searchProduct(value){
    value.avd_search=true;
    this.filter = FORMAT_SEARCH(value);
    console.log(this.filter);
    if(this.filter) {
      this.loader = true;
      this.searching.emit(this.filter);
      this.reset = true;
    }
  }

  resetSearch(){
    this.filter = null;
    if(this.reset) {
      this.searching.emit('');
      this.reset = false;
    }
    this.openAdvanceSearch();
  }

  closeFilter() {
    this.search = new ProductSearch();
    if(this.reset) {
      this.openAdvanceSearch();
      this.reset = false;
    }
  }

  openAdvanceSearch() {
    $('.search-panel#advanceSearch').toggleClass('dis-block');
    $('#advanceSearch').toggleClass('la-angle-down la-angle-up');
  }

}
