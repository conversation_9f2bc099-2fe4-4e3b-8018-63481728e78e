.inventory-sidebar #m_quick_sidebar_tabs{
    position: sticky!important;
    top: 0;
    z-index: 2;
    background: white;
    padding-top: 25px;
    padding-right:20px;
}

.pricing-tab .nav-tabs {
    border-bottom: 1px solid #f2f3f8;
}
.pricing-tab .nav-tabs .nav-link.active, .pricing-tab .nav-tabs .nav-item.show .nav-link {
    border-color: #f2f3f8 #f2f3f8 #fff;
}
.pricing-tab .nav-tabs .nav-link.active, .pricing-tab .nav-tabs .nav-item.show .nav-link{
    border-color: #f2f3f8 !important;
    background-color: #f2f3f8 !important;
}
.pricing-tab .tab-content {
    background-color: #f2f3f8;
    padding: 15px 5px;
}

.pricing-tab .nav.nav-pills, 
.pricing-tab .nav.nav-tabs {
    margin-bottom: 0px;
}
.pricing-tab .form-control[disabled] {
    background-color: #ffff;
}
.pricing-tab .table thead tr {
    background-color: #fff;
}
.pricing-tab .form-control[readonly] {
    background-color: #fff;
}
.pricing-tab .table {
    padding: 0 15px;
    margin: 0 15px;
}
.popover{
    max-width:400px !important;
    width:400px !important;

}
.popover-content {
    width: 400px;
}