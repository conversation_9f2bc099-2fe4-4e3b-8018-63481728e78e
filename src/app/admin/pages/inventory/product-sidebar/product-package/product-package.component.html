<div class="custom-alert" #hasCusAlert></div>
<div class="row justify-content-center">
  <div class="col-md-12">
    <app-product-search
      (onSelectedProduct)="onSelectedProduct($event)"
      [searchProductFor]="'package'"
    ></app-product-search>
  </div>
</div>

<h4 class="mb-0 mt-4"> Package Items </h4>

<app-package-product-list
  #packageList
  [proId]="package_id"
  [packageProduct]="packageProduct"
  [loader]="loader"
></app-package-product-list>


<div class="row">
  <div class="col-md-12">
    <div class="form-group pb-0">
      <h4 class="mb-3"> Package contents </h4>
      <div class="summernote-description addproduct-summernote-description"></div>
    </div>
    <div class="form-group mt-4">
      <!-- <button (click)="postPackageContent()">Submit</button> -->
      <div *ngIf="loader; else buttonSub" class="m-loader m-loader--brand"
          style="width: 30px; display: inline-block;"></div>
      <ng-template #buttonSub>
        <button type="submit" class="btn btn-brand" (click)="postPackageContent()">
          <i class="fa fa-save"></i> <span style="padding-left:10px;">Update</span>
        </button>
      </ng-template>
    </div>
  </div>
</div>