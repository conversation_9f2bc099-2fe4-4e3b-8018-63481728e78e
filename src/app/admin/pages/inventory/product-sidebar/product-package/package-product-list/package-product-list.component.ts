import {
  Component,
  OnInit,
  ViewChild,
  ElementRef,
  Input,
  OnChanges
} from "@angular/core";
import { AlertService } from "../../../../../../modules/alert/alert.service";
import { InventoryService } from "../../../inventory-serveice/inventory.service";
import { PackageProduct } from "./model";

@Component({
  selector: "app-package-product-list",
  templateUrl: "./package-product-list.component.html",
  styleUrls: ["./package-product-list.component.css"]
})
export class PackageProductListComponent implements OnInit, OnChanges {
  loaded = false;
  @Input() packageProduct;
  @Input() loader: boolean;
  saveClick;
  @Input() proId;
  @ViewChild("hasCusAlert") alertContainer: ElementRef;
  package_id;
  packageProducts: PackageProduct[] = [];
  constructor(
    private alertS: AlertService,
    private inventoryS: InventoryService
  ) {}

  ngOnInit() {
    this.getProductsInPackage();
  }
  ngOnChanges() {
    this.package_id = this.proId;
    if (this.packageProduct) {
      const found = this.packageProducts.findIndex(item=>item.id===this.packageProduct.id)
      if(!(found>-1)){
        this.packageProducts.push(this.packageProduct);
       // console.log(found , this.packageProduct)
      }
     
    }
  }
  getProductsInPackage() {
    this.loader = true;
    this.inventoryS.getProductsInpackage(this.package_id).subscribe(res => {
      this.loaded = true;
      this.loader = false;
      this.packageProducts = res;
    });
  }

  showAlert(res) {
    if (res.status) {
      this.alertS.success(this.alertContainer, res.message, true, 5000);
    } else {
      this.alertS.error(this.alertContainer, res.message, true, 5000);
    }
  }

  updateQuantity(packageProduct: PackageProduct) {
    this.saveClick = packageProduct.id;
    this.inventoryS
      .UpdateProductQtyInPackage(packageProduct.id, packageProduct.quantity)
      .then(res => {
        this.saveClick = null;
        if (res.status === "OK") {
          this.showAlert({
            status: true,
            message: "Quantity updated successfully "
          });
        } else {
          this.showAlert({ status: false, message: res.result.error });
        }
      })
      .catch(err => {
        this.saveClick = null;
        this.showAlert({
          status: false,
          message: "Something went wrong !! Please try again !! "
        });
      });
  }
  deleteItem(id) {
    this.inventoryS
      .deleteProductInPackage(id)
      .then(res => {
        if (res.status === "OK") {
          this.packageProducts = this.packageProducts.filter(
            item => item.id !== id
          );
        } else {
          this.showAlert({ status: false, message: res.result.error });
        }
      })
      .catch(err => {
        this.showAlert({
          status: false,
          message: "Something went wrong !! Please try again !! "
        });
      });
  }
}
