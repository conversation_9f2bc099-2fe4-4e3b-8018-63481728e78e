<div class="custom-alert" #hasCusAlert></div>
<div class="row">
  <div class="col-md-12 mt-4">
    <div *ngIf="loader" class="table-load m-loader m-loader--brand"></div>
    <div class="table-responsive" style="margin-bottom: 10px;">
      <table class="table table-hover">
        <thead>
          <tr>
            <th>Product Id</th>
            <th>Product Name</th>
            <th>Quantity</th>
            <th>Action</th>
          </tr>
        </thead>
        <tbody *ngIf="packageProducts.length < 1">
          <tr *ngIf="!loader && loaded">
            <td colspan="11">
              <h4 class="text-center">No Product Found</h4>
            </td>
          </tr>
        </tbody>
        <tbody>
          <tr class="even-tr" *ngFor="let item of packageProducts">
            <td>{{ item.product.id }}</td>
            <td>{{ item.product.name }}</td>
            <td>
              <div class="p-ib" style="width: 100px;">
                <input
                  autocomplete="off"
                  class="form-control m-input ng-valid ng-dirty ng-touched"
                  min="0"
                  [(ngModel)]="item.quantity"
                  [value]="item.quantity"
                  type="number"
                />
              </div>
            </td>
            <td>
                <div *ngIf="saveClick === item.id; else SAVEBTN" class="m-loader m-loader--brand"
                style="width: 30px; display: inline-block;"></div>
          <ng-template #SAVEBTN >
              <button
              (click)="updateQuantity(item)"
              class="btn mr-3 btn-sm btn-outline-dark"
              id="m_quick_sidebar_toggle"
              title="Update Product Quantity"
            >
            <i class="fa fa-save"></i>
            </button>
          </ng-template>
              <button
                (click)="deleteItem(item.id)"
                class="btn ml-3 btn-sm btn-danger"
                id="m_quick_sidebar_toggle"
                title="Delete Product"
              >
              <i class="fa fa-trash"></i>
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
