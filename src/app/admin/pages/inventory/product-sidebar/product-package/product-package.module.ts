import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { ProductPackageComponent } from "./product-package.component";
import { RouterModule, Routes } from "@angular/router";
import { ProductSearchModule } from "../../../../../modules/product-search/product-search.module";
import { FormsModule } from "@angular/forms";
import { PackageProductListModule } from "./package-product-list/package-product-list.module";

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild([
      {
        path: "",
        component: ProductPackageComponent
      }
    ]),
    ProductSearchModule,
    FormsModule,
    PackageProductListModule
  ],
  declarations: [ProductPackageComponent]
})
export class ProductPackageModule {}
