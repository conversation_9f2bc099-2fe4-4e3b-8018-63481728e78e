import {
  Component,
  OnInit,
  ViewChild,
  ElementRef,
  Output,
  EventEmitter
} from "@angular/core";
import { InventoryService } from "../../inventory-serveice/inventory.service";
import { ActivatedRoute } from "@angular/router";
import { AlertService } from "../../../../../modules/alert/alert.service";
import { PackageProduct } from "./package-product-list/model";
import { PackageProductListComponent } from "./package-product-list/package-product-list.component";
import { FormGroup, FormBuilder } from "@angular/forms";
declare let $: any;

@Component({
  selector: "app-product-package",
  templateUrl: "./product-package.component.html",
  styleUrls: ["./product-package.component.css"]
})
export class ProductPackageComponent implements OnInit {
  package_id: number;
  packageProduct: PackageProduct;
  loader: boolean;
  form: FormGroup;
  // @Output() showAlert = new EventEmitter();
  @ViewChild("packageList") packageList: PackageProductListComponent;
  @ViewChild("hasCusAlert") alertContainer: ElementRef;
  package_contents: any;
  constructor(
    private alertS: AlertService,
    private route: ActivatedRoute,
    private inventoryS: InventoryService,
    private fb: FormBuilder
  ) {
    this.package_id = this.route.parent.parent.snapshot.params.product_id; // package id
    this.form = this.fb.group({
      package_contents: ['']
    });
    this.getPackageContent();
  }

  ngOnInit() {}

  
  ngAfterViewInit(): void {
    this._description();
  }

  private _description = () => {
    $(".summernote-description").summernote(this.inventoryS.summarNoteMinimum());
    $(".summernote-description").on("summernote.blur", () => {
      this.form
        .get("package_contents")
        .setValue($(".summernote-description").summernote("code"));
    });
  }

  getPackageContent() {
    this.inventoryS.getPackageContent(this.package_id)
      .then(res => {
        if (res.status === 'OK') {
          this.package_contents = res.result.data ? res.result.data.value: null;
          $('.summernote-description').summernote('code', this.package_contents);
        }
      })
  }

  postPackageContent() {
    this.loader = true;
    const data = {
      key: 'package_content',
      value: this.form.getRawValue().package_contents
    }
    this.inventoryS.savePackageContent(data, this.package_id)
      .then(res => {
        this.loader = false;
        if (res.status === 'OK') {
          this.alertS.success(this.alertContainer, res.result.message, true, 5000);
        } else {
          this.alertS.error(this.alertContainer, res.result ? res.result.message : 'Something went wrong!', true, 5000);
        }
      })
      .catch(err => {
        this.loader = false;
        this.alertS.error(this.alertContainer, err.error.result ? err.error.result.message : err.message, true, 5000);
      })
  }

  onSelectedProduct(product) {
    // Add Product In Package
    this.loader = true;
    const obj = {
      product_id: product.product_id,
      package_id: this.package_id
    };
    this.inventoryS
      .addProductInPackage(obj)
      .then(res => {
        //console.log(res);
        if (res.status === "OK") {
          this.loader = false;
          this.packageProduct = res.result.data;
        } else {
          if (this.packageList) {
            this.packageList.showAlert({
              status: false,
              message: res.result.error
            });
          }
        }
      })
      .catch(err => {
        if (this.packageList) {
          this.packageList.showAlert({
            status: false,
            message: "Something went wrong !! Please try again !! "
          });
        }
      });
  }
}
