import { Component, OnInit, ViewEncapsulation, OnD<PERSON>roy } from "@angular/core";
import { Helpers } from "../../../../helpers";
import { Router, NavigationEnd, ActivatedRoute } from "@angular/router";
import { Subscription } from "rxjs";

@Component({
  selector: "admin-product-sidebar",
  templateUrl: "./product-sidebar.component.html",
  styleUrls: ["./product-sidebar.component.css"],
  encapsulation: ViewEncapsulation.None
})

export class ProductSidebarComponent implements OnInit, OnDestroy {

  product_type_id:number;
  isPackage: boolean = false;

  constructor(private router: Router, private route: ActivatedRoute) {

    this.route.queryParamMap.subscribe(val => {
        this.product_type_id = parseInt(val.get('type_id'));

        if ( this.product_type_id==1) {
          this.isPackage = false;
        } else if(this.product_type_id==2) {
          this.isPackage = true;
        }

      }
    );
  }


  ngOnInit() {

  }

  

  ngOnD<PERSON>roy() {

  }
}
