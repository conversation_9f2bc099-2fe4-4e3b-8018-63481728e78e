<!-- Add asset toggle area -->
<div class="custom-alert" #hasCusAlert></div>
<div class="m-content product-sidebar-description add-asset-toggle-body">
  <div>
    <form [formGroup]="form" (submit)="submit()"
      class="m-form m-form--fit m-form--label-align-right ng-untouched ng-pristine ng-valid" novalidate="">
      <div class="row">
        <div class="form-group col-md-6">
          <label for="title"> Item's Serial Number* </label>
          <input  (focusout)="focusOutFunction()" formControlName="serial_no"  required type="text" class="form-control m-input sl_no" placeholder="#RN-001">
          <span class="check" > <i  [ngStyle]="{'color':inValid?'red':'green'}" [ngClass]="{'fa-times':inValid , 'fa-check':inValid===false}" class="fa "></i></span>
        </div>
        <div class="form-group col-md-6">
          <label for="merchantID">Select Location* </label>
          <select formControlName="location_id" class="form-control m-input ng-untouched ng-pristine ng-valid">
            <option [value]="location.id" *ngFor="let location of locations">{{location.name}}</option>

          </select>
        </div>
        <div class="form-group col-md-6">
          <label for="title"> Item Description </label>
          <input formControlName="description" autocomplete="off"
            class="form-control m-input ng-untouched ng-pristine ng-valid" id="title" name="name"
            placeholder="Item Description"  type="text">
        </div>
        <div class="form-group col-md-6">
          <div class="">
            <label for="order-date"> Date of Purchase </label>
            <div class="input-group date">
              <input type="text" class="form-control m-input" (click)="orderDateChange()"
                placeholder="-Date of Purchase-" id="order-date" readonly />
              <div class="input-group-append">
                <span class="input-group-text">
                  <i class="la la-calendar-check-o"></i>
                </span>
              </div>
            </div>
          </div>
        </div>
        <div class="form-group col-md-6">
          <label for="title"> Purchase Cost </label>
          <div class="input-group m-input-group">
            <div class="input-group-prepend">
              <span class="input-group-text" id="table-cost-addon">
                {{ currency.symbol? currency.symbol : '$'}}
              </span>
            </div>
            <input formControlName="purchase_cost" autocomplete="off"
            class="form-control m-input ng-untouched ng-pristine ng-valid" placeholder="Purchase Cost"
            type="text">
            <div class="input-group-append">
              <small class="input-group-text" id="table-cost-addon">
                {{ currency.code? currency.code : 'USD'}}
              </small>
            </div>
          </div>
        </div>
        <div class="form-group col-md-6">
          <label for="Merchant">
            Manufacturer
          </label>
          <select2-add-option [data]="supliers" [prop]="'name'" [url]="'suppliers'" [domId]="'suppliers'"
            [placeholder]="'Manufacturer'" (changeValue)="changed($event)"></select2-add-option>
        </div>
        <div class="form-group col-md-6">
          <label for="Merchant">
            Manufacturer's serial number
          </label>
          <input  formControlName="vendor_serial_no"  type="text"
          class="form-control m-input " placeholder="Manufacturer's serial number">
        </div>
        <div class="form-group col-md-6">
          <label for="merchantID"> Current Status </label>
          <select formControlName="current_status" class="form-control m-input ng-untouched ng-pristine ng-valid"
            name="status">
            <option [value]="item.value" *ngFor="let item of assets_status">{{item.text}}</option>

          </select>
        </div>
        <div class="form-group col-md-6">
          <label for="title"> Available for Sale </label>
          <div class="m-radio-inline" style="margin-top: 10px;">
            <label class="m-radio">
              <input type="radio" (click)="SelectOption(true)" [checked]="form.get('available_for_sale').value" name="example_3" value="1"> Yes
              <span></span>
            </label>
            <label class="m-radio">
              <input type="radio" (click)="SelectOption(false)" [checked]="!form.get('available_for_sale').value" name="example_3" value="2"> No
              <span></span>
            </label>
          </div>
        </div>
        <div class="form-group col-md-12" *ngIf="form.get('available_for_sale').value">
          <label for="sellPrice">
            Sell price
          </label>
          <div class="input-group m-input-group">
            <div class="input-group-prepend">
              <span class="input-group-text" id="table-cost-addon">
                {{ currency.symbol? currency.symbol : '$'}}
              </span>
            </div>
            <input type="text" id="sellPrice" class="form-control m-input" placeholder="Sell price" name="sale_price"
            formControlName="sale_price">
            <div class="input-group-append">
              <small class="input-group-text" id="table-cost-addon">
                {{ currency.code? currency.code : 'USD'}}
              </small>
            </div>
          </div>

        </div>
        <div class="form-group col-12">

          <button class="btn btn-brand" [disabled]="!form.valid" type="submit"><i class="fa fa-save"></i><span
              style="padding-left:10px;">Submit</span></button>
          <button type="button" (click)="cancel()" class="btn btn-secondary addasset-toggle-cancel ml-3"
            type="button">Cancel</button>

        </div>
      </div>
    </form>
  </div>
</div>
<!-- add toggle end -->
