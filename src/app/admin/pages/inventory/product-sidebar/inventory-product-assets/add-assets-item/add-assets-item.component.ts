import {
  Component,
  OnInit,
  EventEmitter,
  Output,
  AfterViewInit,
  ViewChild,
  ElementRef,
  Input,
  OnChanges
} from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { Supplier } from "../../../product-models/inventory.models";
import { AlertService } from "../../../../../../modules/alert/alert.service";
import { FormGroup, FormBuilder, Validators } from "@angular/forms";
import { ASSETS_STATUS } from "../../../../../../globals/_classes/functions";
import { InventoryService } from "../../../inventory-serveice/inventory.service";
declare let $: any;
@Component({
  selector: "app-add-assets-item",
  templateUrl: "./add-assets-item.component.html",
  styleUrls: ["./add-assets-item.component.css"]
})
export class AddAssetsItemComponent implements OnInit, AfterViewInit , OnChanges {
  form: FormGroup;
  assets_status = ASSETS_STATUS;
  @Output() onsubmit = new EventEmitter();
  supliers: Supplier[] = [];
  inValid: boolean;
  @Input() variantId;
  @ViewChild("hasCusAlert") alertContainer: ElementRef;
  locations = [];
  currency:any;
  constructor(
    private inventoryS: InventoryService,
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private alertS: AlertService
  ) {
    this.locations = JSON.parse(localStorage.getItem("locations"));
    this.supliers = this.route.snapshot.data["supplier"].data;
    // console.log(this.route.snapshot.data["supplier"].data);
    if (this.supliers) {
      this.supliers = this.supliers;
    }

    this.form = this.fb.group({
      sale_price: [""],
      purchase_cost: [""],
      current_status: [this.assets_status[0].value],
      serial_no: [""],
      description: [""],
      vendor_id: [""],
      vendor_serial_no:[""],
      location_id: [this.locations[0].id],
      purchase_date: [""],
      variants_products_id :[""],
      available_for_sale: [false],
      product_id: this.route.parent.parent.snapshot.params.product_id
    });
  }

  ngOnInit() {
    this.currency = JSON.parse(localStorage.getItem('currency'));
  }
  cancel() {
    this.onsubmit.emit(true);
   
  }
  ngOnChanges() {
    this.form.get("variants_products_id").setValue(this.variantId);
  }

  ngAfterViewInit() {
    this.datePicker();
  }

  orderDateChange() {
    $("#order-date")
      .datepicker()
      .on("changeDate", e => {
        let date = e.date;
        const purchase_date =
          date.getFullYear() +
          "-" +
          (date.getMonth() + 1) +
          "-" +
          date.getDate();
        this.form.get("purchase_date").setValue(purchase_date);
        // this.asset.purchase_date =
        // console.log(this.asset.purchase_date);
      });
  }
  private datePicker() {
    $("#order-date").datepicker({
      todayHighlight: true,
      orientation: "bottom left",
      format: "yyyy-mm-dd",
      templates: {
        leftArrow: '<i class="la la-angle-left"></i>',
        rightArrow: '<i class="la la-angle-right"></i>'
      }
    });
  }

  changed(e: any): void {
    if (e.messag) {
      this.alertS.error(this.alertContainer, e.message, true, 3000);
    } else {
      this.form.get("vendor_id").setValue(Number(e.id));
    }
  }

  submit() {
   // console.log();
    this.inventoryS
      .addAsset(this.form.getRawValue())
      .toPromise()
      .then(res => {
      //  console.log(res);
        if (res.result.error) {
          this.alertS.error(this.alertContainer, res.result.error, true, 5000);
        } else {
          this.alertS.success(
            this.alertContainer,
            res.result.message,
            true,
            5000
          );
          setTimeout(()=>{
            this.onsubmit.emit(true);
          },3000)
        }
      })
      .catch(err => {
        this.alertS.error(
          this.alertContainer,
          "Something went wrong ! Please try again !!!",
          true,
          5000
        );
      });
  }

  SelectOption(value) {
    //  console.log(value);
    this.form.get("available_for_sale").setValue(value);
    if (!this.form.get("available_for_sale").value) {
      this.form.get("sale_price").setValue("");
    }
  }

  focusOutFunction() {
    const slNO = this.form.get("serial_no").value;
    this.inventoryS.checkAssetSLNo(slNO).subscribe(res => {
      this.inValid = res;
    });
  }
}
