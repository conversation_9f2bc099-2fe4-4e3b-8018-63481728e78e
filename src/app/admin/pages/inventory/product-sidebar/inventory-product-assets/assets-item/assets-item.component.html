<div class="custom-alert" #hasCusAlert></div>
<div class="row">
  <div class="col-md-12">
  <div *ngIf="loader" class="table-load m-loader m-loader--brand"></div>
  <div class="table-responsive" style="margin-bottom: 10px;">
    <table class="table table-hover">
      <thead>
        <tr>
          <th>Item #</th>
          <th>Location </th>
          <th>Current Condition </th>
          <th>Current Status </th>
          <th>Available for Sale? </th>
        </tr>
      </thead>
      <tbody *ngIf="assets.length<1">
        <tr *ngIf="!loader">
          <td colspan="11">
            <h4 class="text-center">No Asset Found</h4>
          </td>
        </tr>
      </tbody>
      <tbody>
        <tr class="even-tr" *ngFor="let asset of assets ">
          <td>
            <a style="    color: #0740c5;cursor: pointer;" (click)="navigate(asset.id)"
              title="View Details"><b>{{asset.product.name}} #{{asset.serial_no}}</b></a><br>
            <span *ngIf="asset.product?.variant_chain!=='Unassigned: Unassigned'" class="description-field-title">
              {{asset.product.variant_chain}}
            </span>

          </td>
          <td>{{ getLocation(asset.location_id)}}</td>
          <td>
            <span [ngStyle]="{'background': getCurrentCondition(asset.current_condition).color}"
              class="status m-badge m-badge--wide">{{
                          getCurrentCondition(asset.current_condition).text
                          }}</span>
          </td>
          <td>
            <span [ngStyle]="{'background': getCurrentStatus(asset.current_status).color}"
              class="status m-badge m-badge--wide">{{
                          getCurrentStatus(asset.current_status).text
                          }}</span>
          </td>
          <td>{{asset.available_for_sale?'Yes':'No' }}</td>
          <!-- <td>
            <a (click)="delete(asset.id)"
              class="m-portlet__nav-link btn m-btn m-btn--hover-accent m-btn--icon m-btn--icon-only m-btn--pill"
              title="Delete"><i class="fa fa-trash"></i></a>
          </td> -->
        </tr>

      </tbody>

    </table>
  </div>

     <!-- pagination Start-->
     <boot-pagination [totalSize]="pagi.total" [page]="pagi.page" [listSize]="pagi.limit" [pagelimit]="pagi.limit"
     (pageChange)="reloadTable($event)"></boot-pagination>
     <!-- pagination End-->
     
  </div>
</div>
