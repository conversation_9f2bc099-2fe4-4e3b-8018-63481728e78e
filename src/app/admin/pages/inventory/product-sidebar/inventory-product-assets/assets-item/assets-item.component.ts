import {
  Component,
  OnInit,
  ViewChild,
  ElementRef,
  Input,
  OnChanges,
  EventEmitter,
  Output
} from "@angular/core";
import { InventoryService } from "../../../inventory-serveice/inventory.service";
import { Asset } from "../../../product-asset/asset-models/asset.models";
import {
  ASSETS_CONDITION,
  ASSETS_STATUS
} from "../../../../../../globals/_classes/functions";
import { AlertService } from "../../../../../../modules/alert/alert.service";
import { ActivatedRoute, Router } from "@angular/router";
import { Pagi } from "../../../../../../modules/pagination/pagi.model";


@Component({
  selector: "assets-item",
  templateUrl: "./assets-item.component.html",
  styleUrls: ["./assets-item.component.css"]
})
export class AssetsItemComponent implements OnInit, OnChanges {
  assets: Asset[] = [];
  loader: boolean;
  pagi: Pagi = new Pagi();
  @ViewChild("hasCusAlert") alertContainer: ElementRef;
  @Input() variantId;
  @Input() filterQuery;
  constructor(
    private router:Router,
    private route: ActivatedRoute,
    private inventoryS: InventoryService,
    private alertS: AlertService
  ) {
    // console.log(this.route);
     this.getQueryParams();
  }


  getQueryParams(){

    const query= this.route.snapshot.queryParams
    for(let c in query){
      if(c==='vp_id'){
        this.variantId = query[c];
        this.getAssets();
        // console.log(c, query[c])
      }
    }
  }
  ngOnChanges() {
    console.log()
  
   this.getAssets(this.filterQuery)
   // console.log(this.filterQuery)
  }

  ngOnInit() {
    const obj = this.route.snapshot.queryParams
    if(!Object.keys(obj).length){
      this.getAssets();
    }
  }
  getAssets(filter?,page?,limit?) {
    this.loader = true;
    let query = "";
    if (this.variantId && !filter) {
      query = `variants_products_id=${this.variantId}&is_search=true`;
    }
    if(this.variantId && filter){
      query = `${filter}variants_products_id=${this.variantId}&is_search=true`;
    }


    if(page !=undefined && limit !=undefined)
    {
      query=query+`&page_no=${page}&limit=${limit}`;
    }
    

  //  console.log(query)
    this.inventoryS
      .getProductAssets(
        false,
        query
      )
      .subscribe(res => {
        this.assets = res.data ? res.data : [];
        this.loader = false;

        this.pagi.page=parseInt(res.page_no);
        this.pagi.limit=parseInt(res.limit);
        this.pagi.total=parseInt(res.limit);
      });
  }


  reloadTable(e) {
    this.getAssets(this.filterQuery,e.page,e.limit);
  }

  getCurrentCondition(id) {
    const obj = ASSETS_CONDITION.find(
      item => Number(item.value) === Number(id)
    );
    return obj;
  }
  getCurrentStatus(id) {
    const obj = ASSETS_STATUS.find(item => Number(item.value) === Number(id));
    return obj;
  }

  delete(id) {
    this.inventoryS
      .deleteAsset(id)
      .then(res => {
        if (res.result.error) {
          this.alertS.error(this.alertContainer, res.result.error, true, 5000);
        } else {
          this.getAssets();
          this.alertS.success(
            this.alertContainer,
            res.result.message,
            true,
            5000
          );
        }
      })
      .catch(err => {
        this.alertS.error(
          this.alertContainer,
          "Something wrong Please try again !!!",
          true,
          5000
        );
      });
  }

  getLocation(id) {
    const obj = JSON.parse(localStorage.getItem("locations")).find(
      item => Number(item.id) === Number(id)
    );
    return obj.name;
  }

  navigate(id) {
    this.router.navigateByUrl('admin/inventory/product-asset/details/'+id)
  }
}
