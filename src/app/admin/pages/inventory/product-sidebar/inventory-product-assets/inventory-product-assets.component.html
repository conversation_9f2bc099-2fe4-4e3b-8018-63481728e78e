<div *ngIf="show; else untracked" class="m-content product-sidebar-description row" style="padding:10px 0px;">

  <div *ngIf="!hasVarientList;else showAssets" class="col-md-12">
    <p style="text-align: center;">You didn't select any variants for this product. Please add variants for this product from variants tab.</p>
  </div>

  <ng-template #showAssets >
    <h4 class="col-md-12" style="padding: 2px 15px;"> Product Assets
      <button (click)="openForm=!openForm" class="btn btn-sm btn-brand addasset-toggle-btn" style="float:right;" type="submit">
        Add  New
      </button>
    </h4>
  
    <form class="col-md-12 m-form m-form--fit m-form--label-align-right" *ngIf="!unassign && showVarientList">
      <div class="form-group">
        <div class="row">
          <div class="col-sm-12">
            <label>Select Variant</label>
            <select class="form-control m-input" name="attribute" [(ngModel)]="selectedId.variants_products_id"
              (change)="attributeChange()">
              <option *ngFor="let a of attributeList" [value]="a.id">
                {{a.chain}}
              </option>
            </select>
          </div>
          <!-- <div class="col-sm-6">
              <label class="m-checkbox">
                <input type="checkbox" name="all" [(ngModel)]="applyAll" (change)="applyToAll()">
                  Apply For All
                <span></span>
              </label>
            </div> -->
        </div>
      </div>
    </form>
  
    <app-add-assets-item (onsubmit)="openForm=!openForm" *ngIf="openForm" [variantId]="selectedId.variants_products_id">
    </app-add-assets-item>
  
    <div class="col-md-12">
      <app-assets-filter (onSubmit)="reloadTable($event)"  *ngIf="!openForm"></app-assets-filter>
    </div>
  
  
    <assets-item *ngIf="!openForm" [filterQuery]="filterQuery" [variantId]="selectedId.variants_products_id"></assets-item>
  </ng-template>


</div>

<ng-template #untracked>
  <p style="text-align: center;margin-top: 50px;">This product is set as an untracked product.</p>
  <p style="text-align: center;">If you wish to track items individually, please set this product as a tracked product in product description tab.</p>
</ng-template>
