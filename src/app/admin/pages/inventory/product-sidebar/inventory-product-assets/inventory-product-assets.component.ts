import { Component, OnInit } from "@angular/core";
import { AttributManage } from "../product_models";
import { ActivatedRoute } from "@angular/router";

@Component({
  selector: "app-inventory-product-assets",
  templateUrl: "./inventory-product-assets.component.html",
  styleUrls: ["./inventory-product-assets.component.css"]
})
export class InventoryProductAssetsComponent implements OnInit {
  filterQuery;
  openForm: boolean;
  pro_id: number;
  showVarientList = true;
  loader: boolean;
  listLoader: boolean;
  hasVarientList: boolean;
  selectedId = {
    variants_products_id: 0,
    product_id: 0
  };
  attributeList: AttributManage[] = [];
  applyAll: boolean;
  is_tracked: number;
  show: boolean;

  constructor(private activeRoute: ActivatedRoute) {
    const attList = this.activeRoute.snapshot.data["list"].result.data;
    this.is_tracked = this.activeRoute.snapshot.data["list"].result.is_tracked;
    this.is_tracked === 1 ? this.show = true : this.show = false;
    if (attList && attList.length > 0) {
      this.hasVarientList = true;
      this.getAttribute(attList);
    } else {
      this.hasVarientList = false;
    }
    const vp_id = this.activeRoute.snapshot.queryParams["vp_id"];

    if(!isNaN(Number(vp_id)))
    {
      this.selectedId.variants_products_id=vp_id;
    }

    // if (Object.keys(query)) {
    //   for (let c in query) {
    //     if (c === "vp_id") {
    //      this.showVarientList = false;
    //     }
    //   }
    // }
  }

  ngOnInit() {}

  get unassign() {
    return this.attributeList.map(m => m["set_id"]).includes(1);
  }

  reloadTable(query){
    this.filterQuery = query;
  }

  hideVarient(e) {
    console.log(e);
    if (!e) {
      this.showVarientList = false;
    }
  }
  getAttribute(attList) {
    this.attributeList = attList.map(m => {
      m["id"] = m.ids[m.ids.length - 1];
      m["chain"] = m.variant_set
        .map(a => a.variant_set_name + "(" + a.name + ")")
        .join(", ");
      m["set_id"] =
        m.variant_set.length > 0 ? m.variant_set[0].variant_set_id : null;
      return m;
    });

    if (this.attributeList.length > 0) {
      let attr = this.attributeList.find(f => {
        return f.default;
      });
      this.selectedId.variants_products_id = attr
        ? attr.id
        : this.attributeList[0].id;
      this.selectedId.product_id = this.pro_id;
    }

    this.listLoader = true;
  }

  applyToAll() {
    if (this.applyAll) {
      this.selectedId["all"] = this.attributeList
        .filter(f => f.id != this.selectedId.variants_products_id)
        .map(m => m.id);
    } else {
      delete this.selectedId["all"];
    }
    console.log(this.applyAll, this.selectedId);
  }

  attributeChange() {
    this.applyAll = false;
    this.listLoader = true;
    this.selectedId.product_id = this.pro_id;
  }
}
