import { Component, OnInit, Output, EventEmitter } from '@angular/core';
import { FormGroup, FormBuilder } from '@angular/forms';
import { ASSETS_STATUS, ASSETS_CONDITION } from '../../../../../../globals/_classes/functions';

@Component({
  selector: 'app-assets-filter',
  templateUrl: './assets-filter.component.html',
  styleUrls: ['./assets-filter.component.css']
})
export class AssetsFilterComponent implements OnInit {
form:FormGroup;
locations=[]
asset_status= ASSETS_STATUS;
asset_condition=ASSETS_CONDITION;
@Output() onSubmit= new EventEmitter();
  constructor(private fb:FormBuilder) {
    this.locations = JSON.parse(localStorage.getItem('locations'))
    this.form =this.fb.group({
      search:[""],
      current_status:[this.asset_status[0].value],
      current_condition:[this.asset_condition[0].value],
      location_id:[this.locations[0].id]
    })
   }

  ngOnInit() {
  }

  submit() {
    let query ="";
    const obj = this.form.getRawValue();
    for(let c in obj){
      query+=`${c}=${obj[c]}&`
    }
   
    this.onSubmit.emit(query);
  }
  reset() {
    this.form.get("location_id").setValue(this.locations[0].id);
    this.form.get("current_condition").setValue(this.asset_condition[0].value);
    this.form.get("current_status").setValue(this.asset_status[0].value);
    this.form.get("search").setValue("");
  //  this.form.reset()
  this.onSubmit.emit("")
  }
}
