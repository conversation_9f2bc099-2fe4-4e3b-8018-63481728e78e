<form class="row" [formGroup]="form" (submit)="submit()">
  <div class="col-md-3">
    <div class="form-group">
      <label for="title"> Item Name / Serial Number</label>
      <input formControlName="search" autocomplete="off" class="form-control m-input ng-untouched"
        placeholder="Search Asset" required="" type="text">
    </div>
  </div>
  <div class="col-md-2">
    <label for="">Location</label>
    <select formControlName="location_id" class="form-control m-input">
      <option [value]="location.id" *ngFor="let location of locations">{{location.name}}</option>
    </select>
  </div>
  <div class="col-md-2">
    <label for="">Current Condition</label>
    <select formControlName="current_condition" class="form-control m-input" name="attribute">
      <option [value]="item.value" *ngFor="let item of asset_condition">{{item.text}}</option>
    </select>
  </div>
  <div class="col-md-3">
    <label for="">Current Status</label>
    <select formControlName="current_status" class="form-control m-input" name="attribute">
      <option [value]="item.value" *ngFor="let item of asset_status">{{item.text}}</option>

    </select>
  </div>
  <div class="col-md-1" style="margin-top:33px;"><button class="btn btn-sm btn-brand" type="submit">Search</button>
  </div>
  <div (click)="reset()" class="col-md-1" style="margin-top:33px;"><button class="btn btn-sm btn-danger" type="button">Reset</button>
  </div>
</form>
