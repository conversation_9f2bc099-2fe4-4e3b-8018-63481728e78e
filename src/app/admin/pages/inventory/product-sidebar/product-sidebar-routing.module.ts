import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";
import { ProductSidebarComponent } from "./product-sidebar.component";
import { InventoryResolveService } from "../inventory-serveice/inventory-resolve.service";

const routes: Routes = [
  {
    path: "",
    component: ProductSidebarComponent,
    children: [
      {
        path: "description",
        loadChildren:
          () => import('./description/description.module').then(m => m.DescriptionModule),
        resolve: { description: InventoryResolveService }
      },
      {
        path: "pricing",
        loadChildren:
          () => import('./pricing/pricing.module').then(m => m.PricingModule),
        resolve: { list: InventoryResolveService }
      },
      {
        path: "assets",
        loadChildren:
          () => import('./inventory-product-assets/inventory-product-assets.module').then(m => m.InventoryProductAssetsModule),
        resolve: { list: InventoryResolveService }
      },

      {
        path: "product-pacakge",
        loadChildren:
          () => import('./product-package/product-package.module').then(m => m.ProductPackageModule)
      },
      {
        path: "categories",
        loadChildren:
          () => import('./categories/categories.module').then(m => m.CategoriesModule),
        resolve: { list: InventoryResolveService }
      },
      {
        path: "images",
        loadChildren:
          () => import('./images/images.module').then(m => m.ImagesModule),
        resolve: { list: InventoryResolveService }
      },
      {
        path: "variant",
        loadChildren:
          () => import('./product-attribute/product-attribute.module').then(m => m.ProductAttributeModule),
        resolve: { list: InventoryResolveService }
      },
      {
        path: "related-product",
        loadChildren:
          () => import('./related-product/related-product.module').then(m => m.RelatedProductModule),
        resolve: { list: InventoryResolveService }
      },
      {
        path: "availability",
        loadChildren:
          () => import('./availability/availability.module').then(m => m.AvailabilityModule),
        resolve: { list: InventoryResolveService }
      },
      {
        path: "calendar",
        loadChildren:
          () => import('./product-calender/product-calender.module').then(m => m.ProductCalenderModule),
        resolve: { list: InventoryResolveService }
      },
      {
        path: "**",
        redirectTo: "description"
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ProductSidebarRoutingModule {}
