import {
    Component,
    AfterView<PERSON>nit,
    ViewChild,
    ElementRef,
    OnDestroy,
    OnInit
} from "@angular/core";
import {Supplier, Descriptuion} from "../../product-models/inventory.models";
import {ActivatedRoute, Router, NavigationEnd} from "@angular/router";
import {InventoryService} from "../../inventory-serveice/inventory.service";
import {AlertService} from "../../../../../modules/alert/alert.service";
import {Subscription} from "rxjs";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {DialogBoxComponent} from "../../../../../modules/dialog-box/dialog-box.component";


declare let $: any;

@Component({
    selector: "product-sidebar-description",
    templateUrl: "./description.component.html",
    styleUrls: ["./description.component.css"]
})
export class DescriptionComponent implements OnInit, AfterViewInit, On<PERSON><PERSON>roy {
    suplliers: Supplier[] = [];
    product: Descriptuion = new Descriptuion();
    loader: boolean = false;
    tagsList: any[] = [];
    tagValues: any[];
    sub: Subscription;
    loadSummary: boolean;
    pro_id: number;
    varinats: any[] = [];
    showVar: boolean;
    variantIds: number[];
    customFieldList: any[] = [];
    customFieldResult=[];
    chain: string;
    showChain: boolean;
    subs_plan: any;
    inventory_items: number;
    isPackage = false;
    currency;
    messageUntrack = "This product has assets that will be lost if you stop tracking assets. Are you sure?";
    messageTrack = "This product's quantity will only be determined by the assets added. Are you sure?";
    tr_status = [
      { name: 'Track quantity only', value : 0},
      { name: 'Track specific assets', value : 1}
    ];

    @ViewChild("hasCusAlert") alertContainer: ElementRef;

    constructor(
                private router:Router,
                private modalService: NgbModal,
                private activeRoute: ActivatedRoute,
                private alertS: AlertService,
                private inventoryS: InventoryService) {
        this.getVariants();
        this.getInventoryCount();
        this.currency = JSON.parse(localStorage.getItem('currency'));
    }


    getInventoryCount() {
        const user = localStorage.getItem("currentUser")
            ? JSON.parse(localStorage.getItem("currentUser"))
            : {};
        this.subs_plan = user
            ? String(user.subscription.account_type).toUpperCase()
            : "FREE";

        this.inventoryS.getInventoryCount().then(res => {
            if (res.status === "OK") {
                this.inventory_items = res.result.data;
            }
        });
    }

    ngOnInit() {
        this.checkRoute();
        this.pro_id = this.inventoryS.getProId(this.activeRoute);
        this.sub = this.activeRoute.data.subscribe(val => {
            this.product = val["description"].data;
            console.log(this.product);
            if (this.product.is_tracked === 0) {
              this.product.is_tracked = this.tr_status[0].value;
            } else {
              this.product.is_tracked = this.tr_status[1].value;
            }
            if (this.loadSummary) {
                this._description();
            }
            this.product.supply_id = this.product.supply_id
                ? this.product.supply_id
                : "";
            this.loadSummary = true;
            this.tagsList = [];
            this.getTags();
        });
        $(".native-routing-container").scrollTop(0, 0);
        this.getSupplier();

        this.getCustomFieldList();
    }

    ngAfterViewInit() {
        this._description();
    }

    ngOnDestroy() {
        this.sub.unsubscribe();
    }

    onChange(track_status_id) {
      console.log(track_status_id);
      if (this.product.product_asset > 0 && track_status_id == 0) {
        this.pickup(this.messageUntrack);
      } else if (track_status_id == 1) {
        this.pickup(this.messageTrack);
      }
    }

    pickup(message) {
      const modalRef = this.modalService.open(DialogBoxComponent, {
        centered: true,
        size: "sm",
        windowClass:'track-quantity-modal'
      });
      modalRef.componentInstance.massage = message;
      modalRef.result.then(
        result => {
          if (result) {
            if (message === this.messageTrack) {
              this.product.do_track = true;
            } else {
              this.product.do_untrack = true;
            }
          }
        },
        res => {
          console.log(res);
        }
      );
    }

    private getVariants() {
        this.inventoryS.getAtrributeSet().subscribe(
            res => {
                this.varinats = res.data.filter(f => f.id !== 1);
                this.updateChain();
                this.showChain = true;
            },
            err => console.log(err)
        );
    }

    private updateChain() {
        if (
            !this.unassign &&
            this.product["variant_set"] &&
            this.product["variant_set"].length > 0
        ) {
            this.chain = this.varinats
                .filter(f => this.product["variant_set"].includes(f.id))
                .map(m => m.name)
                .join(" -> ");
        }
    }

    loadDes() {
        const url: string = this.activeRoute.snapshot["_routerState"].url;
        if (url.includes("details/edit")) {
            const des = Object.assign({}, this.product);
            des.tags = this.tagsList.filter(t => {
                return this.product.tags.includes(t.id);
            });
            this.inventoryS.reload({
                reload: true,
                id: this.pro_id,
                from: "DESCRIPTION",
                data: des
            });
        }
    }

    getTags() {
        this.inventoryS.getTags().subscribe(res => {
            this.tagsList = res.data;
            this.tagValues = this.product.tags.map(m => m.id);
            //  console.log(this.tagValues,  this.tagsList)
        });

    }

    getCustomFieldList() {
        this.inventoryS.getCustomFieldList(this.pro_id).subscribe(res => {
          this.customFieldList = res.data;

          if(this.customFieldList.length>0){
            this.customFieldList.forEach(data=>{
                this.customFieldResult.push(
                    {
                        id: data.id,
                        name: data.name,
                        label: data.label,
                        type: data.type,
                        field_values: data.field_value
                      }
                )
              })
          }
         

        });
      }

      onChangeCustomFieldData(value, customField) {
        var customFields = this.customFieldResult.filter(x => x.id == customField.id);
    
        if (value !== "null" && value != "") {
          var data = {
            id: customField.id,
            name: customField.name,
            label: customField.label,
            type: customField.type,
            field_values: value
          };
    
          if (customFields.length === 0) {
            this.customFieldResult.push(data);
            // console.log(this.customCheckoutResult)
          } else {
            // let index = this.customFieldResult.indexOf(customFields);
            // this.customFieldResult.splice(index, 1);
    
            this.customFieldResult.map(d=>{
                if(d.id==customFields[0].id)
                {
                    d.id=customFields[0].id
                    d.name=customFields[0].name
                    d.label=customFields[0].label
                    d.type=customFields[0].type
                    d.field_values=value
                }

                return d;
            });
            // console.log(this.customCheckoutResult)
          }
        } else {
          let index = this.customFieldResult.indexOf(customFields);
          this.customFieldResult.splice(index, 1);
        }
      }


    getSupplier() {
        this.inventoryS.getSupplier().subscribe(
            res => {
                this.suplliers = res.data;
                this.product.supplier_id = this.product.supplier_id
                    ? this.product.supplier_id
                    : 0;
            },
            err => console.log(err)
        );
    }

    private _description = () => {
        $(".summernote-description").summernote(this.inventoryS.summarNote());

        $(".summernote-description").on("summernote.blur", () => {
            this.product.description = $(".summernote-description").summernote(
                "code"
            );
        });

        $("#summernote-description_id").summernote("code", this.product.description);
    };

    changed(e: any): void {
        if (e.messag) {
            this.alertS.error(this.alertContainer, e.message, true, 3000);
        } else {
            this.product.supplier_id = parseInt(e.id);
        }
    }

    changedTagList(e) {
        if (e.messag) {
            this.alertS.error(this.alertContainer, e.message, true, 3000);
        } else {
            this.product.tags = this.inventoryS.formatToNumber(e.id);
            console.log(this.product.tags);
        }
    }

    upadteProduct() {
        this.loader = true;
        if (this.variantIds && this.variantIds.length > 0) {
            this.product["variant_set"] = this.variantIds;
        } else {
            delete this.product["variant_set"];
        }

        if(this.customFieldResult.length>0)
        {
          this.product["custom"] = this.customFieldResult;
        }

        console.log(this.product);
        this.inventoryS
            .updateProductDescription(this.product.id, this.product)
            .then(res => {
                this.loader = false;
                this.alertS.success(
                    this.alertContainer,
                    "Data have been successfully updated",
                    true,
                    5000
                );
                this.loadDes();
                this.cancelVariants();
                this.updateChain();
            })
            .catch(err => {
                console.log(err);
                this.loader = false;
                this.alertS.error(
                    this.alertContainer,
                    "Data have been not updated",
                    true,
                    5000
                );
            });
    }

    // ********************* Variant **********************

    get unassign() {
        return (
            this.product["variant_set"] && this.product["variant_set"].includes(1)
        );
    }

    reloadVarient() {
        this.getVariants();
    }

    varinatValues(e) {
        this.variantIds = e.filter(f => f.set_id).map(m => m.set_id);
    }

    showVariants() {
        this.showVar = true;
        this.variantIds = [];
    }

    cancelVariants() {
        this.showVar = false;
        this.variantIds = [];
    }

    deletelVariants() {
        const modalRef = this.modalService.open(DialogBoxComponent, {
            centered: true
        });
        modalRef.componentInstance.massage =
            "This will delete variant and variant value list of this product. Are you sure you want to delete?";
        modalRef.result.then(
            result => {
                if (result) {
                    this.archive();
                }
            },
            res => console.log(res)
        );
    }

    archive() {
        this.inventoryS
            .deleteAttributeSet(this.pro_id)
            .then(res => {
                if(res.status=="OK")
                {
                    this.chain = null;
                    this.variantIds = [];
                    this.alertS.success(
                        this.alertContainer,
                        res.result.message,
                        true,
                        3000
                    );
                }
                else{

                    this.alertS.error(
                        this.alertContainer,
                        res.result.message,
                        true,
                        3000
                    );
                }
               
            })
            .catch(err =>
                this.alertS.error(
                    this.alertContainer,
                    "Something wrong! Variants has been not deleted.",
                    true,
                    3000
                )
            );
    }


    private checkRoute() {
        let url = this.router.url;
        this.sub = this.router.events.subscribe(e => {
          if (e instanceof NavigationEnd) {
            url = String(e.urlAfterRedirects);
          }
        });
        if (url.includes("type_id=2")) {
          this.isPackage = true;
        } else {
          this.isPackage = false;
        }
      }
}
