<!--<plan-upgrade-alert -->
<!--[message]="'You must be a silver or Platinum tier client to activate inventory items.'"-->
<!--*ngIf="subs_plan==='FREE' && inventory_items>=11"></plan-upgrade-alert>-->
<!--<plan-upgrade-alert [message]="'You must be a  Platinum tier client to activate inventory. '" *ngIf="subs_plan==='SILVER' && inventory_items>=51" ></plan-upgrade-alert>-->


<div class="custom-alert" #hasCusAlert></div>

<div class="m-content product-sidebar-description">
  <!--begin::Portlet-->
  <div>
    <form class="m-form m-form--fit m-form--label-align-right" #form="ngForm" (ngSubmit)="upadteProduct()">
      <div class="form-group">
        <label for="title">
          Title*
        </label>
        <input type="text" class="form-control m-input" placeholder="Title" id="title" name="name"
          [(ngModel)]="product.name" required autocomplete="off">
      </div>
      <div class="form-group">
        <label>
          Description
        </label>
        <div id="summernote-description_id" class="summernote-description"></div>
      </div>
      <div class="form-group">
        <label for="keyword">
          Tags
        </label>
        <select2-add-option [multi]="true" [data]="tagsList" [prop]="'name'" [url]="'tags'" [domId]="'tags'"
          [placeholder]="'Tag'" [value]="tagValues" [edit]="true" (changeValue)="changedTagList($event)">
        </select2-add-option>
      </div>
      <div class="form-group m-form__group">
        <label for="primary">
          Keyword
        </label>
        <input type="text" autocomplete="off" id="primary" class="form-control m-input" placeholder="Keyword"
          name="keyword" [(ngModel)]="product.keyword">
      </div>
      <div class="row">
        <div class="form-group col-md-4">
          <label for="Merchant">
            Select Vendor
          </label>
          <select2-add-option [data]="suplliers" [prop]="'name'" [url]="'suppliers'" [domId]="'suppliers'"
            [placeholder]="'Supplier'" [edit]="true" [value]="product.supplier_id" (changeValue)="changed($event)">
          </select2-add-option>
        </div>
        <div class="form-group col-md-4">
          <label for="merchantID">
            Vendor Product ID
          </label>
          <input type="text" id="merchantID" class="form-control m-input" placeholder="Vendor Product ID"
            name="supply_id" #supId="ngModel" [(ngModel)]="product.supply_id" autocomplete="off">
          <span *ngIf="supId.errors && supId.touched">
            <small class="error" *ngIf="supId.errors.required">Supplier Product ID required</small>
          </span>
        </div>
        <div class="form-group col-md-4">
          <label for="clientSpecifID">
            Your SKU
          </label>
          <input type="text" id="clientSpecifID" class="form-control m-input" placeholder="Your SKU"
            name="client_specific_id" #clientId="ngModel" [(ngModel)]="product.client_specific_id" autocomplete="off">
          <span *ngIf="clientId.errors && clientId.touched">
            <small class="error" *ngIf="clientId.errors.required">Merchant Product ID required</small>
          </span>
        </div>
      </div>
      <div class="row">
        <div class="col-lg-6">
          <div class="form-group m-form__group">
            <label for="sale_tax">
              Sales Tax
            </label>
            <div class="input-group m-input-group">
              <input id="sale_tax" numberOnly class="form-control m-input" type="text" name="sale_tax"
                [(ngModel)]="product.sales_tax" placeholder="0.00" autocomplete="off">
              <div class="input-group-append">
                <span class="input-group-text" id="table-cost-addon">
                  %
                </span>
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-6">
          <div class="form-group m-form__group">
            <label for="deposit">
              Deposit Amount
            </label>
            <div class="input-group m-input-group">
              <div class="input-group-prepend">
                <span class="input-group-text" id="table-cost-addon">
                  {{ currency.symbol? currency.symbol : '$'}}
                </span>
              </div>
              <input numberOnly class="form-control m-input" name="deposit" [(ngModel)]="product.deposit_amount"
                type="text" placeholder="0.00" autocomplete="off">
              <div class="input-group-append">
                <small class="input-group-text" id="table-cost-addon">
                  {{ currency.code? currency.code : 'USD'}}
                </small>
              </div>
            </div>
          </div>

        </div>
      </div>

      <div class="row" style="margin-top: 15px;">
        <!-- <div class="col-md-6">
          <div class="form-group m-form__group">
            <label for="lwd">
              Loss/Damage Waiver Amount
            </label>
            <div class="input-group m-input-group">
              <div class="input-group-prepend">
                <span class="input-group-text" id="table-cost-addon">
                  {{ currency.symbol? currency.symbol : '$'}}
                </span>
              </div>
              <input id="lwd" numberOnly class="form-control m-input" type="text" name="ldw_tax"
                [(ngModel)]="product.ldw_tax" placeholder="0.00" autocomplete="off">
              <div class="input-group-append">
                <span class="input-group-text" id="table-cost-addon">
                  {{ currency.code? currency.code : 'USD'}}
                </span>
              </div>
            </div>
          </div>
        </div> -->
        <div class=" col-md-6">
          <div class="form-group">
            <label for="merchantID">
              Product Status
            </label>
            <select class="form-control m-input" [(ngModel)]="product.status" name="status">
              <option value="1">Active</option>
              <option value="2">Inactive</option>
              <option value="3">Out of Stock</option>
              <option value="4">Faulty</option>
              <option value="5">Deleted</option>
            </select>
          </div>

        </div>

      </div>

      <div class="row">
        <div class="col-md-12">
          <div class="form-group">
            <label class="m-checkbox">
              <input type="checkbox" name="featured" [(ngModel)]="product.featured">
              Make as featured
              <span></span>
            </label>
          </div>
        </div>
        <div class="col-md-12" *ngIf="!isPackage">
          <div class="form-group">
            <label class="m-checkbox">
              <input type="checkbox" name="image" [(ngModel)]="product.driving_license">
              Driver's licence required?
              <span></span>
            </label>
          </div>
        </div>
        <div class="col-md-12">
          <div class="form-group">
            <label class="m-checkbox">
              <input type="checkbox" name="shipping" [(ngModel)]="product.free_shipping">
              Free shipping
              <span></span>
            </label>
          </div>
        </div>
        <!-- <div class="col-md-12">
          <div class="form-group">
            <label class="m-checkbox">
              <input type="checkbox" name="deposit_tax" [(ngModel)]="product.deposit_tax">
              Apply sales tax on deposit amount
              <span></span>
            </label>
          </div>
        </div> -->
      </div>
      <hr />

      <div class="form-group m-form__group row">
        <div class="col-md-6">
          <label class="m-checkbox" style="margin-top: 20px;">
            <input type="checkbox" name="is_default_weight" [(ngModel)]="product.is_default_weight">
            Enable default weight
            <span></span>
          </label>
        </div>
        <div class="col-md-6">
          <label for="unit">
            Weight
          </label>
          <div class="input-group m-input-group">
            <input type="text" numberOnly autocomplete="off" id="unit" class="form-control m-input" placeholder="0.00"
              name="weight_amount" [(ngModel)]="product.weight_amount">
            <div class="input-group-append">
              <select class="form-control cursor-pointer" name="weight_unit" [(ngModel)]="product.weight_unit">
                <option value="pound">lb</option>
                <option value="kilogram">kg</option>
                <option value="ounce">oz</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      <div class="form-group m-form__group" *ngIf="showChain && !isPackage">
        <div class="row">
          <div class="col-6">
            <h5>Options</h5>
          </div>
          <div class="col-6 text-right" *ngIf="!chain">
            <button type="button" *ngIf="!showVar" class="btn btn-sm btn-dark" (click)="showVariants()">Add</button>
            <button type="button" *ngIf="showVar" class="btn btn-sm btn-danger" style="margin-left: 5px;"
              (click)="cancelVariants()">Cancel</button>
          </div>
        </div>
        <div class="d-flex" style="border: 1px solid rgba(0, 0, 0, 0.1) ; align-items: center; padding: 10px;"
          *ngIf="chain">
          <div style="flex: 8;">
            <h6 style="margin: 0px">{{chain}}</h6>
          </div>
          <div class="text-right" style="flex: 1;">
            <button type="button"
              class="m-portlet__nav-link btn m-btn m-btn--hover-danger m-btn--icon m-btn--icon-only m-btn--pill"
              (click)="deletelVariants()"><i class="fa fa-trash"></i></button>
          </div>
        </div>
        <p *ngIf="!chain">Add options if the product comes in different versions, like different sizes or colors</p>
        <div *ngIf="showVar">
          <app-variants (reloadItem)="reloadVarient()" [variants]="varinats" (values)="varinatValues($event)">
          </app-variants>
        </div>

      </div>

      <div class="row" *ngIf="!isPackage">
        <div class=" col-md-12">
          <h5>Product Tracking</h5>
          <p>You can track details of a specific item in your inventory by using assets
            <ng-template #popContentRange>Tip:To track bulk products by quantity, select 'Track quantity', or to track specific items/serialized products, create a unique asset ID for each product.</ng-template>
           &nbsp;&nbsp;<b><span triggers="mouseenter:mouseleave" [ngbPopover]="popContentRange">?</span></b>
          </p>

          <div class="form-group">
            <ng-container *ngFor="let item of tr_status">
              <br>
              <label class="m-radio" >
                <input type="radio" [(ngModel)]="product.is_tracked" name="is_tracked" [value]="item.value"
                  (click)="onChange(item.value)" />
                {{item.name}}<span></span>
              </label>
            </ng-container>
              &nbsp;&nbsp;
            <a routerLink="/admin/inventory/edit/{{pro_id}}/assets" class="btn btn-brand btn-sm" style="cursor: pointer;">
              Track Items
            </a>
          </div>
        </div>
      </div>


      <div *ngIf="customFieldList.length>0" class="row">
        <div class=" col-md-12">
          <h5>Custom Fields</h5>

          <div class="row">
            <div class="col-md-6" *ngFor="let customField of customFieldList">
              <div class="form-group">
                <label for="">{{ customField.label }}</label>

                <input *ngIf="customField.type == 'textBox'" type="text" class="form-control" name="name1"
                  [value]="customField?.field_value" autocomplete="none" (change)="
                  onChangeCustomFieldData(
                      $event.target.value,
                      customField
                    )
                  " />

                <!-- <select
                  name="howto_hear"
                  class="form-control"
                  *ngIf="customField.field_type == 1"
                  (change)="
                    onChangeCustomFieldData(
                      $event.target.value,
                      customField
                    )
                  "
                >
                  <option value="null">--Select--</option>
                  <option
                    *ngFor="let opt of customField.field_values.split(';')"
                    [value]="opt"
                    name="name2"
                    >{{ opt }}</option
                  >
                </select>
    
                <input 
                *ngIf="customField.type == 2" 
                type="file" 
                class="form-control"
                name="file"
                (change)="onUpload($event.target.files[0],customField)" /> -->

              </div>
            </div>
          </div>

        </div>
      </div>


      <div class="form-group col-12" style="padding:20px 0px;">
        <div *ngIf="loader; else buttonSub" class="m-loader m-loader--brand"
          style="width: 30px; display: inline-block;"></div>
        <ng-template #buttonSub>
          <button type="submit" class="btn btn-brand" [disabled]="!form.form.valid">
            <i class="fa fa-save"></i> <span style="padding-left:10px;">Update</span>
          </button>
        </ng-template>
      </div>
    </form>
    <!--end::Form-->
  </div>

</div>