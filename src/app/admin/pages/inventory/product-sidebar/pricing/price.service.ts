import { Injectable, Optional } from '@angular/core';
import { Resolve, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { HttpService } from '../../../../../modules/http-with-injector/http.service';
import { map, catchError } from 'rxjs/operators';
import { of } from 'rxjs';

@Injectable()
export class PriceService {

    constructor(
        private http: HttpService) { 

        }
    

    getStoreConfig() {
        return this.http.get('currency-config').toPromise();
    }

    
}
