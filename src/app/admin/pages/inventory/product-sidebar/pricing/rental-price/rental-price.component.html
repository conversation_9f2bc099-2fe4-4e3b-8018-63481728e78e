<div class="custom-alert" #hasCusAlert></div>
<h5 class="colorPurpel"  style="padding: 5px 0px;margin-bottom: 3px;">Rental Price</h5>
<div *ngIf="loader" class="table-load m-loader m-loader--brand"></div>
<div class="row">
  <div class="col-md-3 form-group m-form__group" style="border-right: 2px solid #eee;">
    <label class="m-radio" *ngIf="!is_arb">
      <input type="radio" (change)="selectRentalType('fixed')" name="positions" value="toast-top-right" [checked]="rental_price_type==='fixed'">
      Basic Rental Rate 
      <span></span>
    </label>
    <label class="m-radio">
      <input type="radio" (change)="selectRentalType('flat')" name="positions" value="toast-top-right" [checked]="rental_price_type==='flat'">
      Standard Rental Rate 
      <span></span>
    </label>
    <label class="m-radio" *ngIf="!is_arb">
      <input type="radio" (change)="selectRentalType('flex')" name="positions" value="toast-top-right" [checked]="rental_price_type==='flex'">
      Advanced Rental Rate 
      <span></span>
    </label>

  </div>
  <div class="col-md-9" *ngIf="rental_price_type==='fixed'">
    <fixed-rental-price
      (onAlert)="showAlert($event)"
      [price_type]="price_type"
      [variantId]="variantId"
      [applyAll]="applyAll"
      [attribute_list]="attribute_list">
    </fixed-rental-price>
  </div>
  <div class="col-md-9" *ngIf="rental_price_type==='flat'">
    <flat-rental-price
      (onAlert)="showAlert($event)"
      [price_type]="price_type"
      [variantId]="variantId"
      [applyAll]="applyAll"
      [attribute_list]="attribute_list"
      [is_arb]="is_arb">
    </flat-rental-price>
  </div>
  <div class="col-md-9" *ngIf="rental_price_type==='flex'">
    <flex-rental-price
      (onAlert)="showAlert($event)"
      [price_type]="price_type"
      [variantId]="variantId" 
      [applyAll]="applyAll" 
      [attribute_list]="attribute_list">
    </flex-rental-price>
  </div>
</div>
