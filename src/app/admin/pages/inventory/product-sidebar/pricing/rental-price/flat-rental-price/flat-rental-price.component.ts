import {
  Component,
  ElementRef,
  Input,
  OnInit,
  ViewChild,
  Output,
  EventEmitter,
  OnChanges
} from "@angular/core";
import { FormGroup, FormControl, FormBuilder, Validators } from "@angular/forms";
import { Helpers } from "../../../../../../../helpers";
import { ProductPrice } from "../../../product_models";
import { Router } from "@angular/router";
import { AlertService } from "../../../../../../../modules/alert/alert.service";
import { InventoryService } from "../../../../inventory-serveice/inventory.service";
import { PriceService } from './../../price.service';

@Component({
  selector: "flat-rental-price",
  templateUrl: "./flat-rental-price.component.html",
  styleUrls: ["./flat-rental-price.component.css"]
})
export class FlatRentalPriceComponent implements OnInit, OnChanges {
  label_types = ["hourly", "daily", "weekly", "monthly"];
  form: FormGroup;

  price: ProductPrice = new ProductPrice();
  pro_id: number;
  priceList: ProductPrice[] = [];
  priceForm: boolean;
  loaderInfo: boolean;
  selected_varient_id: number;
  apply_all: boolean = false;
  store_config;
  db_prices;

  @Input() variantId: number;
  @Input() applyAll: boolean;
  @Input() attribute_list: any;
  @Input() price_type: string;
  @Input() is_arb: boolean;

  loader: boolean = false;
  @Output() onAlert = new EventEmitter();

  constructor(
    private router: Router,
    private fb: FormBuilder,
    private inventoryS: InventoryService,
    private priceService: PriceService
  ) {
    this.form = this.createForm();
    this.priceService.getStoreConfig().then(res => {
      this.store_config = res.result.data;
    });
  }

  ngOnInit() { }

  ngOnChanges() {
    this.selected_varient_id = this.variantId;
    this.apply_all = this.applyAll;
    this.getPrice();
    // if (this.is_arb) {
    //   this.form.addControl('term', this.fb.control('', Validators.required))
    // }
  }

  getPrice() {
    this.inventoryS
      .getrentalPrice(this.selected_varient_id, 3, this.price_type)
      .subscribe(res => {
        if (res) {
          this.db_prices = res;
          this.form.patchValue(res);
        } else {
          this.form.reset();
        }
      });
  }

  createForm() {
    return new FormGroup({
      hourly: new FormGroup({
        duration: new FormControl(),
        price: new FormControl(),
        term: new FormControl()
      }),
      daily: new FormGroup({
        duration: new FormControl(),
        price: new FormControl(),
        term: new FormControl()
      }),
      weekly: new FormGroup({
        duration: new FormControl(),
        price: new FormControl(),
        term: new FormControl()
      }),
      monthly: new FormGroup({
        duration: new FormControl(),
        price: new FormControl(),
        term: new FormControl()
      })
    });
  }

  submit() {
    const val_state_arr = this.checkFormValidation(this.form.getRawValue());
    const val_state = val_state_arr.every(x => x === false);
    if (val_state) {
      this.onAlert.emit({
        status: false,
        message: "Invalid price"
      });
      return;
    }
    let obj = {};
    obj["data"] = this.form.getRawValue();
    obj["price_type"] = 3;
    this.price = obj;
    if (this.price_type === 'vp') {
      this.price.is_vp = 1;
    }
    this.loader = true;
    if (this.applyAll) {
      this.price["all"] = this.attribute_list
        .filter(f => f.id != this.selected_varient_id)
        .map(m => m.id);
    }
    this.inventoryS
      .addPrice(this.selected_varient_id, this.price)
      .then(res => {
        this.loader = false;
        if (res.status == "OK") {
          this.onAlert.emit({
            status: true,
            message: "Price has been successfully added"
          });
          this.inventoryS.rentalPriceSetting.next({ reload: true, price_type: 3 });
          //  this.form.reset();
          this.priceForm = false;
          this.applyAll = false;
          
          this.getPrice();
        } else {
          this.onAlert.emit({
            status: false,
            message: "Price can not be added "
          });
        }
      })
      .catch(err =>
        this.onAlert.emit({
          status: false,
          message: "Something wrong! Price has been not added"
        })
      );
  }

  onClickDeletePrice(price_item) {
    if (this.db_prices.hasOwnProperty(price_item)) {
      let price_id = this.db_prices[price_item].id;
      this.inventoryS
        .deletePrice(price_id)
        .then(res => {
          this.loader = false;
          if (res.status == "OK") {
            this.onAlert.emit({
              status: true,
              message: res.result.message
            });
            delete this.db_prices[price_item];
            this.form.get(price_item).get('duration').setValue(null);
            this.form.get(price_item).get('price').setValue(null);
            this.form.get(price_item).get('term').setValue(null);
          } else {
            this.onAlert.emit({
              status: false,
              message: res.result.message
            });
          }
        })
        .catch(err =>
          this.onAlert.emit({
            status: false,
            message: "Something wrong! Price has been not deleted"
          })
        );
    }
  }

  private checkFormValidation(data): boolean[] {
    let validation = [];
    for(let key of Object.keys(data)) {
      if (data[key].duration != null && data[key].price != null) {
        validation.push(true);
      } else {
        validation.push(false);
      }
    }
    return validation;
  }
}
