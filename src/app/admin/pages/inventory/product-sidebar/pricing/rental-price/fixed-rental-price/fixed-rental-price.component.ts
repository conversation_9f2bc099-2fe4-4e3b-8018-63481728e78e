import {
  Component,
  ElementRef,
  Input,
  OnChanges,
  OnInit,
  ViewChild,
  Output,
  EventEmitter
} from "@angular/core";
import { FormGroup, FormBuilder, Validators } from "@angular/forms";
import { InventoryService } from "../../../../inventory-serveice/inventory.service";
import { Router } from "@angular/router";
import { AlertService } from "../../../../../../../modules/alert/alert.service";
import { ProductPrice } from "../../../product_models";
import { Helpers } from "../../../../../../../helpers";
import { PriceService } from './../../price.service';

@Component({
  selector: "fixed-rental-price",
  templateUrl: "./fixed-rental-price.component.html",
  styleUrls: ["./fixed-rental-price.component.css"]
})
export class FixedRentalPriceComponent implements OnInit, OnChanges {
  price: ProductPrice = new ProductPrice();
  pro_id: number;
  store_config;
  priceForm: boolean;
  loaderInfo: boolean;
  selected_varient_id: number;
  apply_all: boolean = false;
  @Output() onAlert = new EventEmitter();
  @Input() variantId: number;
  @Input() applyAll: boolean;
  @Input() attribute_list: any;
  @Input() price_type: string;
  loader: boolean = false;
  form: FormGroup;
  price_id: number;
  submitted = false;
  constructor(
    private router: Router,
    private fb: FormBuilder,
    private inventoryS: InventoryService,
    private priceService : PriceService
  ) {
    this.form = this.fb.group({
      price: ["", Validators.required]
    });
    this.priceService.getStoreConfig().then( res => {
        this.store_config = res.result.data;
    });
  }

  ngOnInit() {
    // console.log(this.variantId);
    // console.log(this.apply_all);
    // console.log(this.attribute_list);
  }

  ngOnChanges() {
    this.selected_varient_id = this.variantId;
    this.apply_all = this.applyAll;
    this.getPrice();
  }

  getPrice() {
    this.inventoryS
      .getrentalPrice(this.selected_varient_id, 2, this.price_type)
      .subscribe(res => {
        if (res) {
          this.form.patchValue(res);
          this.price_id = res.id;
        } else {
          this.form.reset();
        }
      });
  }

  onClickDeletePrice() {
    this.inventoryS
    .deletePrice(this.price_id)
    .then(res => {
      this.loader = false;
      if (res.status == "OK") {
        this.onAlert.emit({
          status: true,
          message: res.result.message
        });
       this.form.get('price').setValue(null);
       this.price_id = null;
      } else {
        this.onAlert.emit({
          status: false,
          message: res.result.message
        });
      }
    })
    .catch(err =>
      this.onAlert.emit({
        status: false,
        message: "Something wrong! Price has been not deleted"
      })
    );
  }
  submit() {
    this.submitted = true;
    if (this.form.invalid) {
      return;
    }
    let obj = {};
    obj["data"] = this.form.getRawValue();
    obj["price_type"] = 2;
    this.price = obj;
    if (this.price_type === 'vp') {
      this.price.is_vp = 1;
    }
    this.loader = true;
    if (this.applyAll) {
      this.price["all"] = this.attribute_list
        .filter(f => f.id != this.selected_varient_id)
        .map(m => m.id);
    }
    this.inventoryS
      .addPrice(this.selected_varient_id, this.price)
      .then(res => {
        this.loader = false;
        this.submitted = false;
        if (res.status == "OK") {
          this.onAlert.emit({
            status: true,
            message: "Price has been successfully added"
          });
          this.price_id = res.result.data.id;
          this.inventoryS.rentalPriceSetting.next({reload:true,price_type:2});
        //  this.form.reset();
          this.priceForm = false;
          this.applyAll = false;
        } else {
          this.onAlert.emit({
            status: false,
            message: "Price can not be added "
          });
        }
      })
      .catch(err => {
        this.submitted = false;
        this.onAlert.emit({
          status: false,
          message: "Something wrong! Price has been not added"
        })
      });
  }
}
