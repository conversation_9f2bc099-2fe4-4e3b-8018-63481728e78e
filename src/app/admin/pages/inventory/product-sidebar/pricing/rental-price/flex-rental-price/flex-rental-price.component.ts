import {Component, ElementRef, Input, OnInit, ViewChild, Output, EventEmitter} from "@angular/core";
import {FormGroup, FormBuilder, FormControl, FormArray, Validators} from "@angular/forms";
import {Helpers} from "../../../../../../../helpers";
import {ProductPrice, FlexPrice} from "../../../product_models";
import {Router} from "@angular/router";
import {AlertService} from "../../../../../../../modules/alert/alert.service";
import {InventoryService} from "../../../../inventory-serveice/inventory.service";

@Component({
    selector: "flex-rental-price",
    templateUrl: "./flex-rental-price.component.html",
    styleUrls: ["./flex-rental-price.component.css"]
})
export class FlexRentalPriceComponent implements OnInit {
    label_types = ["hourly", "daily", "weekly", "monthly"];
    form: FormGroup;
    editForm: FormGroup;
    items: FormArray;
    editMode: boolean;
    data: FlexPrice[] = [];
    price: ProductPrice = new ProductPrice();
    pro_id: number;
    priceList: ProductPrice[] = [];
    priceForm: boolean;
    loaderInfo: boolean;
    selected_varient_id: number;
    apply_all: boolean = false;
    loader: boolean = false;
    @Input() variantId: number;
    @Input() applyAll: boolean;
    @Input() attribute_list: any;
    @Input() price_type: string;
    @Output() onAlert = new EventEmitter();

    constructor(private router: Router,
                private fb: FormBuilder,
                private inventoryS: InventoryService) {
        this.form = this.fb.group({
            items: this.fb.array([this.createForm()])
        });
        this.editForm = this.fb.group({
            label: [""],
            duration_type: [""],
            duration: ["", [Validators.required]],
            price: ["", [Validators.required]],
            flex_price: ["", [Validators.required]],
            flex_duration: ["", [Validators.required]],
            max_range: ["", [Validators.required]],
            id: [""]
        });

        // this.onChanges();
    }

    ngOnInit() {
    }

    ngOnChanges() {
        this.selected_varient_id = this.variantId;
        this.apply_all = this.applyAll;
        this.getPriceList();
    }

    getPriceList() {
        this.inventoryS
            .getrentalPrice(this.selected_varient_id, 4, this.price_type)
            .subscribe(res => {
                if (res) {
                    this.data = res;
                    this.form.patchValue(res);
                } else {
                    this.form.reset();
                    this.data = [];
                }
            });
    }

    createForm() {
        return new FormGroup({
            hourly: new FormGroup({
                // label: new FormControl({value: 'hourly', disabled: true}),
                duration: new FormControl("", Validators.required),
                price: new FormControl("", Validators.required),
                flex_price: new FormControl("", Validators.required),
                flex_duration: new FormControl("", Validators.required),
                max_range: new FormControl("", Validators.required)
            }),
            daily: new FormGroup({
                //  label: new FormControl({value: 'daily', disabled: true}),
                duration: new FormControl("", Validators.required),
                price: new FormControl("", Validators.required),
                flex_price: new FormControl("", Validators.required),
                flex_duration: new FormControl("", Validators.required),
                max_range: new FormControl("", Validators.required)
            }),
            weekly: new FormGroup({
                // label: new FormControl({value: 'weekly', disabled: true}),
                duration: new FormControl("", Validators.required),
                price: new FormControl("", Validators.required),
                flex_price: new FormControl("", Validators.required),
                flex_duration: new FormControl("", Validators.required),
                max_range: new FormControl("", Validators.required)
            }),
            monthly: new FormGroup({
                // label: new FormControl({value: 'monthly', disabled: true}),
                duration: new FormControl("", Validators.required),
                price: new FormControl("", Validators.required),
                flex_price: new FormControl("", Validators.required),
                flex_duration: new FormControl("", Validators.required),
                max_range: new FormControl("", Validators.required)
            })
        });
    }

    submit() {
        if (this.form.invalid) {
            const mgs = this.inventoryS.checkRentalPriceValidation(
                this.form.get("items").value
            );
            if (mgs.str || !mgs.count) {
                this.onAlert.emit({
                    status: false,
                    message: mgs.str ? mgs.str + " required " : "no data found "
                });
                return;
            }
        }
        // console.log(this.form.getRawValue());
        let obj = {};
        obj["data"] = this.form.get("items").value;
        obj["price_type"] = 4;
        //  console.log(obj);

        this.price = obj;
        if (this.price_type === 'vp') {
            this.price.is_vp = 1;
        }
        this.loader = true;
        if (this.applyAll) {
            this.price["all"] = this.attribute_list
                .filter(f => f.id != this.selected_varient_id)
                .map(m => m.id);
        }

        this.inventoryS
            .addPrice(this.selected_varient_id, this.price)
            .then(res => {
                this.loader = false;
                if (res.status == "OK" && res.result.data.length > 0) {
                    this.data = res.result.data;
                    this.onAlert.emit({
                        status: true,
                        message: "Price has been successfully added"
                    });

                    this.form.reset();
                    this.removeItemsFromarray();
                    this.priceForm = false;
                    this.applyAll = false;
                    this.inventoryS.rentalPriceSetting.next({
                        reload: true,
                        price_type: 4
                    });
                } else {
                    this.onAlert.emit({
                        status: false,
                        message: "Price can not be added "
                    });
                }
            })
            .catch(err =>
                this.onAlert.emit({
                    status: false,
                    message: "Something wrong! Price has been not added"
                })
            );
    }

    addItem(): void {
        this.items = this.form.get("items") as FormArray;
        this.items.push(this.createForm());
    }

    removeItemsFromarray() {
        const control = <FormArray>this.form.controls["items"];
        let i = control.length;
        while (control.length > 1) {
            control.removeAt(i);
            i--;
        }
    }

    editPrice(item: FlexPrice) {
        this.editMode = true;
        this.editForm.patchValue(item);
    }

    cancel() {
        this.editMode = false;
    }

    deletePrice(id) {
        this.inventoryS
            .deleteFlexPrice(id)
            .then(res => {
                if (res.status === "OK") {
                    this.onAlert.emit({status: true, message: res.result.message});
                    this.getPriceList();
                } else {
                    this.onAlert.error({status: false, message: res.result.error});
                }
            })
            .catch(err => {
                this.onAlert.error({
                    status: false,
                    message: "Something went  wrong! Please try again "
                });
            });
    }

    editSubmit() {
        if (this.editForm.invalid) {
            this.onAlert.emit({
                status: false,
                message: "Please insert  all required field "
            });
            return;
        }
        const item = this.editForm.getRawValue();
        this.inventoryS
            .updateFlexPrice(item.id, item)
            .then(res => {
                if (res.status == "OK") {
                    this.onAlert.emit({
                        status: true,
                        message: res.result.message
                    });
                    this.editMode = false;
                    this.getPriceList();
                } else {
                    this.onAlert.emit({
                        status: false,
                        message: "Price can not be saved "
                    });
                }
            })
            .catch(err =>
                this.onAlert.emit({
                    status: false,
                    message: "Something wrong! Please try again !! "
                })
            );
    }
}
