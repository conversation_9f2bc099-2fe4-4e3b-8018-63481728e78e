<div class="row">
    <div class="col-md-12">
        <h6>Set prices for different duration (hour , days, week, month)</h6>
        <small>Simple per-interval pricing (ex. {{5| currency}}/day x 3 days = {{15|currency}})</small>
    </div>
</div>
<div class="row mt-4">
    <form [formGroup]="form" (submit)="submit()" class="col-md-12">
        <div class="row">
            <div class="col-md-2"></div>
            <div class="col-md-3 text-center">
                <h6>Duration</h6>
            </div>
            <div class="col-md-4 text-center">
                <h6>Amount</h6>
            </div>
            <div class="col-md-2 text-center">
                <h6>Term</h6>
            </div>
        </div>
        <div class="row" [formGroupName]="item" *ngFor="let item of label_types">
            <div class="form-group m-form__group col-md-2">
                <div class="input-group m-input-group">
                    <input disabled class="form-control m-input" type="text" [placeholder]="item" autocomplete="off">
                </div>
            </div>
            <div class="form-group m-form__group col-md-3">
                <div class="input-group m-input-group">
                    <input numberOnly formControlName="duration" class="form-control m-input" type="text" placeholder="Duration" autocomplete="off">
                </div>
            </div>
            <div class="form-group m-form__group col-md-4">
                <div class="input-group m-input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text">
                            {{store_config && store_config.symbol? store_config.symbol : "$"}}
                        </span>
                    </div>
                    <input numberOnly formControlName="price" class="form-control m-input" type="text" placeholder="Amount" autocomplete="off">
                    <div class="input-group-append">
                        <small class="input-group-text">
                            {{store_config && store_config.code ? store_config.code : "USD"}}
                        </small>
                    </div>
                    <!-- <span *ngIf="db_prices && db_prices.hasOwnProperty(item)" class="delete-price" style="cursor: pointer;" (click)="onClickDeletePrice(item)" title="Delete Price">
                        <i class="fa fa-trash"></i>
                    </span> -->
                </div>
            </div>
            <div class="form-group m-form__group col-md-2" *ngIf="is_arb">
                <div class="input-group m-input-group">
                    <input numberOnly formControlName="term" class="form-control m-input" type="text" placeholder="Term" autocomplete="off">
                </div>
            </div>
            <div class="form-group m-form__group col-md-1">
                <div class="input-group m-input-group">
                    <span *ngIf="db_prices && db_prices.hasOwnProperty(item)" class="delete-price" style="cursor: pointer;" (click)="onClickDeletePrice(item)" title="Delete Price">
                        <i class="fa fa-trash"></i>
                    </span>
                </div>
            </div>

        </div>
        <div class="row">
            <div class="col-md-4">
                <button type="submit" class="btn btn-brand btn-sm">Submit</button>
            </div>

        </div>
    </form>
    <!-- <div class="col-md-3">
          
        
        </div> -->

</div>