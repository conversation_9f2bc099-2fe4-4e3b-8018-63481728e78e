import {
  Component,
  Input,
  OnChanges,
  OnInit,
  ViewChild,
  ElementRef
} from "@angular/core";
import { AlertService } from "../../../../../../modules/alert/alert.service";
import { InventoryService } from "../../../inventory-serveice/inventory.service";
import { getRentalPriceTypeId } from "../../../../../../globals/_classes/functions";

@Component({
  selector: "app-rental-price",
  templateUrl: "./rental-price.component.html",
  styleUrls: ["./rental-price.component.css"]
})
export class RentalPriceComponent implements OnInit, OnChanges {
  @Input() rental_price_type: string;
  @Input() variantId: number;
  @Input() applyAll: boolean;
  @Input() attribute_list: boolean;
  @Input() price_type: string;
  @ViewChild("hasCusAlert") alertContainer: ElementRef;
  loader = true;
  is_arb: boolean;

  constructor(
    private inventoryS: InventoryService,
    private alertS: AlertService
  ) {
    const contents = localStorage.getItem('contents')
      ? JSON.parse(localStorage.getItem('contents'))
      : null;
    if (contents) {
      const arb_sup_admin = contents.site_specific.confg.hasOwnProperty('arb')
        ? contents.site_specific.confg.arb.hasOwnProperty('active')
          ? contents.site_specific.confg.arb.active
          : false
        : false;
      if (arb_sup_admin) {
        this.is_arb = contents.site_specific.confg.arb.hasOwnProperty('store_active')
          ? contents.site_specific.confg.arb.store_active === 'before_rental' || contents.site_specific.confg.arb.store_active === 'after_rental'
            ? true
            : false
          : false;
      }
    }
  }

  ngOnInit() {
    setTimeout(() => {
      this.loader = false;
    }, 3000);
  }

  ngOnChanges() {
    if (this.is_arb) {
      this.rental_price_type = 'flat';
    }
  }

  selectRentalType(type: String) {
    this.rental_price_type = type.toLowerCase();
    this.inventoryS
      .changeRentalPriceType(
        this.variantId,
        getRentalPriceTypeId(this.rental_price_type)
      )
      .then(res => {
        //  console.log(res)
      });
  }

  showAlert(e) {
    console.log(e);
    if (e.status) {
      this.alertS.success(this.alertContainer, e.message, true, 5000);
    } else {
      this.alertS.error(this.alertContainer, e.message, true, 5000);
    }
  }
}
