import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RentalPriceComponent } from "./rental-price.component";
import { FixedRentalPriceComponent } from "./fixed-rental-price/fixed-rental-price.component";
import { FlatRentalPriceComponent } from "./flat-rental-price/flat-rental-price.component";
import { FlexRentalPriceComponent } from "./flex-rental-price/flex-rental-price.component";
import { ReactiveFormsModule } from "@angular/forms";
import { NumberOnlyDirectiveModule } from "../../../../../../modules/directive/directive.module";
import { CurrencyFormatModule } from "../../../../../../modules/currency-format/currency-format.pipe";
import {NgbModule} from "@ng-bootstrap/ng-bootstrap";

@NgModule({
  imports: [CommonModule, ReactiveFormsModule, NumberOnlyDirectiveModule,CurrencyFormatModule,NgbModule],
  declarations: [
    RentalPriceComponent,
    FixedRentalPriceComponent,
    FlatRentalPriceComponent,
    FlexRentalPriceComponent

    
  ],
  exports: [RentalPriceComponent]
})
export class RentalPriceModule {}
