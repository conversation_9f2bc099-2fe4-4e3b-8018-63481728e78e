<div class="row">
    <div class="col-md-12">
        <h6>Set prices for different duration and additional hour days, week, month</h6>
        <small>Tiered pricing based on rental duration (ex. {{5 | currency}}/1 day and {{3| currency}} each extra day)
        </small>
    </div>
</div>
<div class="row mt-4" *ngIf="!editMode ; else edit">
    <form [formGroup]="form" (submit)="submit()" class="col-md-12">
        <div class="row">
            <div class="col-md-3"></div>
            <div class="col-md-3 text-center">
                <ng-template #popContentRange>Enter the base(minimum) term for the rental in the
                    Duration field. Add the number of base periods that this pricing applies to.
                    <br/>For example, if you offer a one day rental
                    for {{15  | currency}}, and you charge that price for up to 5 days, enter 1 in the 'Duration' field and 15 in the 'Amount' field.</ng-template>
                <h6><span triggers="mouseenter:mouseleave" [ngbPopover]="popContentRange">Duration / Range ? </span></h6>
            </div>
            <div class="col-md-3 text-center">
                <h6>Amount</h6>
            </div>
            <div class="col-md-3 text-center">
                <ng-template #popContentAdditional>If you offer a price break after the base period, use these fields.
                   </ng-template>
                <h6><span  placement="left" triggers="mouseenter:mouseleave" [ngbPopover]="popContentAdditional">Additional Price / Duration ?</span></h6>
            </div>
        </div>
        <div class="rental" formArrayName="items" *ngFor="let item of form.get('items').controls; let i = index;">

            <div [formGroupName]="i">
                <div class="row" [formGroupName]="item" *ngFor="let item of label_types">
                    <div class="form-group m-form__group col-md-3">
                        <div class="input-group m-input-group">
                            <input disabled [value]="item" class="form-control m-input" type="text" autocomplete="off"
                                   style="background-color: #eee;">
                        </div>
                    </div>
                    <div class="form-group m-form__group col-md-3">
                        <div class="input-group m-input-group">
                            <input numberOnly formControlName="duration" class="form-control m-input" type="text"
                                   placeholder="Duration"
                                   autocomplete="off">


                            <input numberOnly formControlName="max_range" class="form-control m-input" type="text"
                                   placeholder="range"
                                   autocomplete="off">
                        </div>
                    </div>
                    <div class="form-group m-form__group col-md-3">
                        <div class="input-group m-input-group">
                            <input numberOnly formControlName="price" class="form-control m-input" type="text"
                                   placeholder="Amount"
                                   autocomplete="off">
                        </div>
                    </div>
                    <div class="form-group m-form__group col-md-3">
                        <div class="input-group m-input-group ">
                            <input numberOnly formControlName="flex_price" class="form-control m-input" type="text"
                                   placeholder="Amount" autocomplete="off">
                            <input numberOnly formControlName="flex_duration" class="form-control m-input ml-2"
                                   type="text" placeholder="Duration"
                                   autocomplete="off">
                        </div>
                    </div>

                </div>
            </div>

        </div>

        <div class="row">
            <div class="col-md-4">
                <button type="submit" class="btn btn-brand btn-sm">Submit</button>
            </div>
            <div class="col-md-8 text-right">
                <button (click)="addItem()" type="button" class="btn btn-brand btn-sm">Add More Rental Price</button>
            </div>
        </div>
    </form>
    <!-- <div class="col-md-3">


          </div> -->

</div>
<ng-template #edit>
    <div class="row mt-5 ">
        <form [formGroup]="editForm" class="col-md-12">
            <div class="row">
                <div class="col-md-2"></div>
                <div class="col-md-2 text-center"><h6>Duration</h6></div>
                <div class="col-md-2 text-center"><h6>Range</h6></div>
                <div class="col-md-2 text-center"><h6>Amount</h6></div>
                <div class="col-md-2 text-center"><h6>Additional Price</h6></div>
                <div class="col-md-2 text-center"><h6>Additional Duration</h6></div>
                <div class="form-group m-form__group col-md-2">
                    <div class="input-group m-input-group">
                        <input formControlName="duration_type" class="form-control m-input" type="text"
                               placeholder="Label" autocomplete="off" disabled style="background-color: #eee;">

                    </div>
                </div>
                <div class="form-group m-form__group col-md-2">
                    <div class="input-group m-input-group">
                        <input numberOnly formControlName="duration" class="form-control m-input" type="text"
                               placeholder="Duration"
                               autocomplete="off">
                    </div>
                </div>
                <div class="form-group m-form__group col-md-2">
                    <div class="input-group m-input-group">
                        <input numberOnly formControlName="max_range" class="form-control m-input" type="text"
                               placeholder="Range"
                               autocomplete="off">
                    </div>
                </div>
                <div class="form-group m-form__group col-md-2">
                    <div class="input-group m-input-group">
                        <input numberOnly formControlName="price" class="form-control m-input" type="text"
                               placeholder="Amount"
                               autocomplete="off">
                    </div>
                </div>
                <div class="form-group m-form__group col-md-2">
                    <div class="input-group m-input-group">
                        <input numberOnly formControlName="flex_price" class="form-control m-input" type="text"
                               placeholder="Additional Amount"
                               autocomplete="off">
                    </div>
                </div>
                <div class="form-group m-form__group col-md-2">
                    <div class="input-group m-input-group">
                        <input numberOnly formControlName="flex_duration" class="form-control m-input" type="text"
                               placeholder="Additional Duration"
                               autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <button type="button" (click)="editSubmit()" class="btn btn-brand btn-sm mr-4 ">Submit</button>
                    <button type="button" (click)="cancel()" class="btn btn-danger btn-sm">Cancel</button>
                </div>

            </div>
        </form>
    </div>
</ng-template>

<div class="row mt-5 ">
    <table class="table table-bordered">
        <thead>
        <tr>
            <!-- <th>Label</th> -->
            <th class="text-center">Term</th>
            <th class="text-center">Duration</th>
            <th class="text-center">Range</th>
            <th class="text-center">Amount</th>
            <th class="text-center"> Additional Price / Duration</th>

            <th class="text-center">
                Actions
            </th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let item of data" style="text-align:center">
            <!-- <td>{{item.label}}</td> -->
            <td>{{item.duration_type}}</td>
            <td>{{item.duration}}</td>
            <td>{{item?.max_range}}</td>
            <td>{{item.price | currency}}</td>
            <td>{{item.flex_price | currency}} / {{item.flex_duration}}</td>
            <td>
                <button class="m-portlet__nav-link btn m-btn m-btn--hover-accent m-btn--icon
          m-btn--icon-only m-btn--pill"
                        (click)="editPrice(item)">
                    <i class="la la-edit"></i>
                </button>
                <a id="m_quick_sidebar_toggle" (click)="deletePrice(item.id)"
                   class="m-portlet__nav-link btn m-btn m-btn--hover-danger m-btn--icon m-btn--icon-only m-btn--pill">
                    <i class="la la-trash"></i>
                </a></td>
        </tr>
        </tbody>
    </table>
    <div>&nbsp;</div>
</div>
