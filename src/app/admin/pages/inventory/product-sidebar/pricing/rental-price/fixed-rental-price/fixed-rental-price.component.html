<div class="row">
    <div class="col-md-12">
        <h6>Set fixed price</h6>
        <small>One rental price, regardless of rental duration.</small>
    </div>
</div>
<div class="row mt-4">
    <form [formGroup]="form" (submit)="submit()" class="col-md-12">
        <div class="row">
            <div class="form-group m-form__group col-md-8">
              
                <div class="input-group m-input-group">
                    <div class="input-group-prepend">
                          <span class="input-group-text">
                            {{store_config && store_config.symbol ? store_config.symbol : "$"}}
                          </span>
                    </div>
                    <input numberOnly formControlName="price" class="form-control m-input" type="text"
                           placeholder="Amount" autocomplete="off">
                    <div class="input-group-append">
                        <small class="input-group-text">
                            {{store_config && store_config.code ? store_config.code : "USD"}}
                        </small>
                    </div>
                    <span *ngIf="price_id" class="delete-price" style="cursor: pointer;" (click)="onClickDeletePrice()" title="Delete Price">
                        <i class="fa fa-trash"></i>
                    </span>
                </div>
                <div *ngIf="submitted && form.get('price').invalid">
                    <small style="color: red;">Invalid price</small>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-4">
                <button type="submit" class="btn btn-brand btn-sm">Submit</button>
            </div>
        </div>
    </form>
</div>
