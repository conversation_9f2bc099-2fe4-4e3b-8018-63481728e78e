<div class="custom-alert" #hasCusAlert></div>

<div class="m-content product-sidebar-pricing" *ngIf="!noData">
  <h5 class="colorPurpel" *ngIf="!unassign" style="padding: 5px 0px;">Selected Variant:</h5>
  <div class="m-portlet__body add-image" style="padding-bottom: 5px;" *ngIf="!unassign">
    <div class="row">
      <div class="col-sm-6">
        <select class="form-control m-input" name="attribute" [(ngModel)]="selectedId.variants_products_id" (change)="attributeChange()">
          <option *ngFor="let a of attributeList" [value]="a.id">
            {{a.chain}}
          </option>
        </select>
      </div>
      <!-- <div class="col-sm-6">
        <label class="m-checkbox">
          <input type="checkbox" name="all" [(ngModel)]="applyAll">
          Apply For All
          <span></span>
        </label>
      </div> -->
    </div>
    <hr/>
  </div>

  <div class="pricing-tab">

    <div class="sale_price" *ngIf="!isPackage">
      <div *ngIf="loader" class="table-load m-loader m-loader--brand"></div>
      <div class="m-portlet--mobile">
        <form #formInfo="ngForm">
          <div class="m-portlet__body">
            <div class="row" *ngIf="is_vp_price_show">
              <div class="col-md-12">
                <label class="m-radio" style="margin-right: 20px;">
                  <input type="radio" (change)="onClickChangePriceType('regular')" name="positions" value="toast-top-right" [checked]="price_type==='regular'">
                  Public
                  <span></span>
                </label>
                <label class="m-radio">
                  <input type="radio" (change)="onClickChangePriceType('vp')" name="positions" value="toast-top-right" [checked]="price_type==='vp'">
                  Partner
                  <span></span>
                </label>
              </div>
            </div>
            <h5 class="colorPurpel"  style="padding: 5px 0px;margin-bottom: 3px;">Sell Price</h5>
            <small>Enter zero if not available for purchase</small>
            <div class="row">
              <div class="col-lg-6">
                <div class="form-group m-form__group">
                  <div class="input-group m-input-group">
                    <div class="input-group-prepend">
                      <span class="input-group-text">
                       {{store_config && store_config.symbol ? store_config.symbol: "$"}}
                      </span>
                    </div>
                    <input numberOnly class="form-control m-input" name="base_price" [(ngModel)]="priceInfo.base_price"
                      type="text" placeholder="0.00" autocomplete="off">
                    <div class="input-group-append">
                      <small class="input-group-text">
                        {{store_config && store_config.code ? store_config.code : "USD"}}
                      </small>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="m-form__actions" style="padding-left: 15px;">
                  <div *ngIf="loaderInfo; else buttonInfo" class="m-loader m-loader--brand" style="width: 30px; display: inline-block;"></div>
                  <ng-template #buttonInfo>
                    <button type="button" class="btn btn-brand btn-sm" [disabled]="!formInfo.form.valid" (click)="addPriceInfo(formInfo)">Submit</button>
                  </ng-template>
                </div>
              </div>
            </div>
          </div>
        </form>
        <hr/>
      </div>
    </div>
    <div class="rent_price mt-2">
      <div class="m-portlet--mobile rentalprice-body">
        <app-rental-price 
          [rental_price_type]="rentalPriceType"
          [variantId]="selectedId.variants_products_id"
          [applyAll]="applyAll"
          [price_type]="price_type"
          [attribute_list]="attributeList">
        </app-rental-price>
      </div>
    </div>
  </div>


</div>

<div class="m-content product-sidebar-pricing" *ngIf="noData">
  <h3 class="colorPurpel">To add price, please add product variant at variants section.</h3>
</div>
