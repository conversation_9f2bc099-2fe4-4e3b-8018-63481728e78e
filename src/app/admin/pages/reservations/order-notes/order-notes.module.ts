import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { OrderNotesComponent } from './order-notes.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AddNoteComponent } from './add-note/add-note.component';
import { DragDropModule } from '../../../../modules/drag-drop/drag-drop.module';
import { ImagePopupComponent } from '../../../../modules/image-popup/image-popup.component';
import { ImagePopupModule } from '../../../../modules/image-popup/image-popup.module';
import { DateFormatModule } from '../../../../modules/date-format/date-format-pipe';


@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    DragDropModule,
    ImagePopupModule,
    DateFormatModule
  ],
  declarations: [OrderNotesComponent, AddNoteComponent],
  exports:[OrderNotesComponent],
  entryComponents:[ImagePopupComponent]
})
export class OrderNotesModule { }
