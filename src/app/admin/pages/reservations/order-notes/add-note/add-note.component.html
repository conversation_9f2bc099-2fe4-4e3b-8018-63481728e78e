<div class="custom-alert" #hasCusAlert></div>

<div class="pt-4">
  <!-- <h5 class="colorPurpel" *ngIf="edit; else add">Add Note</h5> -->
  <form
    #OrderNoteForm="ngForm"
    class="m-form m-form--fit m-form--label-align-right"
  >
    <div class="form-group m-form__group note-form pl-0 pr-0 pb-2">
      <span>
        <label for="Note">
          Note
        </label>
      </span>

      <textarea
        row="10"
        id="Note"
        name="note"
        [(ngModel)]="orderNoteForm.note"
        class="form-control m-input"
        placeholder="Enter note"
        autocomplete="off"
      ></textarea>
      <br />
      <span>
        <div class="custom-file">
          <input
            class="custom-file-input"
            type="file"
            id="file"
            name="file"
            (change)="onChangeNoteFile($event)"
          />
          <label class="custom-file-label" for="file">
            Choose file
          </label>
        </div>
        <br /><br />
        <p *ngIf="fileName !=''">{{contents?.site_specific?.others?.lbl_file || 'File'}} : {{ fileName }}</p>

        <div class="m-form__actions m-form__actions note-btn-area">
          <div
            *ngIf="loader; else button"
            class="m-loader m-loader--brand"
            style="width: 30px; padding-left: 30px; display: inline-block;"
          ></div>
          <ng-template #button>
            <button
              type="button"
              [disabled]="!OrderNoteForm.valid"
              class="btn btn-brand btn-sm"
              *ngIf="edit; else addbtn"
              (click)="update()"
            >
              <i class="fa fa-save"></i>
              <span style="padding-left:10px;">Update</span>
            </button>
            <ng-template #addbtn>
              <button
                type="button"
                [disabled]="getValidation()"
                class="btn btn-brand btn-sm"
                (click)="submit()"
              >
                <i class="fa fa-save"></i>
                <span style="padding-left:10px;">Submit</span>
              </button>
            </ng-template>
          </ng-template>
        </div>
      </span>
    </div>
  </form>
</div>
