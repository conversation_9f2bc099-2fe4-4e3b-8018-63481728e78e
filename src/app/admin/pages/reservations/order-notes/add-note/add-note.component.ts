import { Component, OnInit, EventEmitter, Output, ViewChild, ElementRef } from "@angular/core";
import { OrderService } from "../../order.service/order.service";
import { ActivatedRoute } from "@angular/router";
import { AlertService } from "../../../../../modules/alert/alert.service";
import { ContentResoveService } from "src/app/home/<USER>/contetn-resolve.service";

class Note {
  note: string;
  file: File;
}

@Component({
  selector: "app-add-note",
  templateUrl: "./add-note.component.html",
  styleUrls: ["./add-note.component.css"]
})
export class AddNoteComponent implements OnInit {
  edit: boolean;
  loader: boolean;
  orderNoteForm: Note;
  order_id;
  fileName='';
  contents: any = {};

  @Output("noteAdd") noteAdd: EventEmitter<any> = new EventEmitter();
  @ViewChild('hasCusAlert') alertContainer: ElementRef;
  
  constructor(
    private orderS: OrderService,
     private route: ActivatedRoute,
     private alertS: AlertService,
     private contentService: ContentResoveService
     ) {
    this.order_id = this.route.snapshot.params.order_id;
    this.orderNoteForm = new Note();
  }

  ngOnInit() {
    this.contents = this.contentService.contents;
  }


  onChangeNoteFile(e) {
    let file = e.target.files[0];
    this.orderNoteForm.file = file;
    this.fileName=file.name;
  }

  getValidation(){
    let result=false;
    if(this.orderNoteForm.note  || this.orderNoteForm.file )
    {
      result=false;
    }
    else{
      result=true;
    }

    return result;
  }

  submit() {
      this.loader = true;
      const formData = new FormData();
      formData.append("note", this.orderNoteForm.note ? this.orderNoteForm.note : '');
      formData.append("file", this.orderNoteForm.file);
  
      this.orderS
        .addOrUpdateOrderNote(formData, this.order_id)
        .then(res => {
          if(res.status=="OK")
          {
            this.loader=false;
            console.log(res);
            this.alertS.success(this.alertContainer, res.result.message, true, 5000);
            this.noteAdd.emit({add:true});
            this.orderNoteForm=new Note();
            this.fileName='';
          }
          else{
            this.alertS.error(this.alertContainer, res.result.message, true, 5000);
          }
         
        })
        .catch(err => {
          this.loader=false;
          this.noteAdd.emit({add:false});
          console.log(err);
        });
   
   
  }

 
}
