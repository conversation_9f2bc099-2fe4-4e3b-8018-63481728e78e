import { Component, OnInit, ViewChild, ElementRef } from "@angular/core";
import { OrderService } from "../order.service/order.service";
import { ActivatedRoute } from "@angular/router";
import { AlertService } from "../../../../modules/alert/alert.service";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { ImagePopupComponent } from "../../../../modules/image-popup/image-popup.component";

@Component({
  selector: "app-order-notes",
  templateUrl: "./order-notes.component.html",
  styleUrls: ["./order-notes.component.css"]
})
export class OrderNotesComponent implements OnInit {
  is_showAddNoteSection = false;
  order_id;
  noteList=[];

  @ViewChild('hasCusAlert') alertContainer: ElementRef;

  constructor(
    private orderS: OrderService, 
    private route: ActivatedRoute,
    private alertS: AlertService,
    private modalService: NgbModal
    ) {
    this.order_id = this.route.snapshot.params.order_id;
  }

  ngOnInit() {
    this.loadNoteList();
  }

  onNoteAdd(e) {
    if (e.add) {
      this.loadNoteList();
    }
  }


  getDate(d) {
    if(d) {
        return new Date(d);
    }
    return '';
  }

  loadNoteList() {
    this.orderS
      .getOrderNoteList(this.order_id)
      .then(res => {
        if(res.status=="OK")
        {
          this.noteList=res.result.data;
          console.log(res);
        }
        else{
          console.log(res);
        }
        
      })
      .catch(err => {
        console.log(err);
      });
  }

  onDeleteNote(id){
    this.orderS
    .deleteOrderNote(id)
    .then(res => {
      if(res.status=="OK")
      {
        this.alertS.success(this.alertContainer, res.result.message, true, 5000);
       this.loadNoteList();
      }
      else{
        this.alertS.error(this.alertContainer, res.result.message, true, 5000);
        console.log(res);
      }
      
    })
    .catch(err => {
      console.log(err);
    });
  }

  onClickAddNote(){
    this.is_showAddNoteSection=true;
  }

  showBigImage(img) {
    const modalImage = this.modalService.open(ImagePopupComponent, {
      centered: true
    });
    modalImage.componentInstance.image = img;
  }
}
