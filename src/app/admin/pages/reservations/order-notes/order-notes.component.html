<div class="custom-alert" #hasCusAlert></div>

<div class="m-portlet m-portlet--full-height">
  <div class="m-portlet__head">
    <div class="m-portlet__head-caption">
      <div class="m-portlet__head-title">
        <h3 class="m-portlet__head-text colorPurpel">
          Notes
        </h3>
        <div class="m-portlet__head-text text-right">
          <a id="m_product_sidebar_toggle" (click)="onClickAddNote()"
             class="btn btn-outline-brand mr-3 btn-sm m-btn m-btn--custom close-sidebar-asset" data-toggle="tooltip" data-placement="bottom" title="Add Note" >
            <span>Add Note</span>
          </a>
        </div>
      </div>
    </div>
  </div>
  <div class="m-portlet__body">
    <div class="row" style="padding-bottom: 10px">
      <div class="col-md-12">
        <app-add-note
          *ngIf="is_showAddNoteSection"
          (noteAdd)="onNoteAdd($event)"
        ></app-add-note>
      </div>

      <div class="col-md-12">
        <div *ngIf="noteList.length > 0" class="">
          <div class="note-show-area" *ngFor="let note of noteList">
            <div class="note-pragraph">
              <p>
                {{ note?.note }}
              </p>
              <a 
              *ngIf="note?.file_type =='application' && note?.file_path !=''" 
              [attr.href]="note?.file_path"
              title="Download file">Download File</a>

              <img 
              *ngIf="note?.file_type =='image' && note?.file_path !=''" 
               class="img pointer" style="cursor: pointer" 
                [src]="note?.file_path" height="40px" (click)="showBigImage(note?.file_path)" />
          
              
              
            </div>
            <div class="note-right">
              <span class="w-100"> {{ note?.user_name }}</span
              ><br />
              <span class="w-100">{{
                note?.created |  customDate}}</span
              ><br />
              <button
                type="button"
                class="btn btn-sm btn-danger mt-2"
                (click)="onDeleteNote(note.id)"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
