import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ShippingOrderListComponent } from './shipping-order-list/shipping-order-list.component';
import { ShippingOrderDetailsComponent } from './shipping-order-details/shipping-order-details.component';
import { Routes, RouterModule } from '@angular/router';
import { ReactiveFormsModule } from '@angular/forms';
import { PaginationModule } from '../../../../modules/pagination/pagination.module';
import { DateTimeRangeModule } from '../../../../modules/date-time-range/date-time-range.module';
import { ShipAddressModalComponent } from './shipping-order-details/address-modal/address-modal.component';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { DialogBoxModule } from '../../../../modules/dialog-box/dialog-box.module';
import { DialogBoxComponent } from '../../../../modules/dialog-box/dialog-box.component';
import { Select2NormalModule } from '../../../../modules/select2-normal/select2-normal.module';



const route: Routes = [
  {
    path: '',
    component: ShippingOrderListComponent,
  },
  {
    path: ':order_id',
    component: ShippingOrderDetailsComponent
  },
  // {
  //   path: ':order_id/create',
  //   loadChildren: "app/admin/pages/reservations/shipping-order/create-shipping-order/create-shipping-order.module#CreateShippingOrderModule"
  // }
];


@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(route),
    ReactiveFormsModule,
    PaginationModule,
    DateTimeRangeModule,
    NgbModule,
    DialogBoxModule,
    Select2NormalModule
  ],
  declarations: [ShippingOrderListComponent, ShippingOrderDetailsComponent, ShipAddressModalComponent],
  entryComponents: [ShipAddressModalComponent, DialogBoxComponent]
})
export class ShippingOrderModule { }
