<div class="custom-alert" #hasCusAlert></div>
<!-- BEGIN: Subheader -->
<div class="m-subheader">
  <div class="d-flex align-items-center">
    <div class="mr-auto">
      <h3 class="m-subheader__title m-subheader__title--separator">
        Shipping Order List
      </h3>
      <ul class="m-subheader__breadcrumbs m-nav m-nav--inline">
        <li class="m-nav__item m-nav__item--home">
          <a routerLink="/admin" class="m-nav__link m-nav__link--icon">
            <i class="m-nav__link-icon la la-home"></i>
          </a>
        </li>
        <li class="m-nav__separator">
          <i class="fa fa-angle-right"></i>
        </li>
        <li class="m-nav__item">
          <a class="m-nav__link">
            <span class="m-nav__link-text" [routerLink]="['/admin/reservations/all']">
              Reservations
            </span>
          </a>
        </li>
        <li class="m-nav__separator" style="padding-left: 10px">
          <i class="fa fa-angle-right"></i>
        </li>
        <li class="m-nav__item">
          <a class="m-nav__link">
            <span class="m-nav__link-text">
              Shipping Order List
            </span>
          </a>
        </li>
      </ul>
    </div>
  </div>
</div>

<!-- END: Subheader -->
<div class="m-content product-list animated fadeIn">
  <div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__head orderlist-head">
      <div class="m-portlet__head-caption">
        <div class="m-portlet__head-title">
          <h3 class="m-portlet__head-text mb-0">
            Shipping Order List
          </h3>
        </div>
      </div>
    </div>
    <div class="m-portlet__body">
      <!--begin: Datatable -->
      <!--begin::Section-->
      <div class="m-section">
        <div class="m-section__content price-table">
          <div *ngIf="loader" class="table-load m-loader m-loader--brand"></div>
          <div class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>
                    Order ID
                    <i class="la icon"></i>
                  </th>
                  <th>
                    Ship From
                  </th>
                  <th>
                    Ship To
                  </th>
                  <th>
                    Shipping Method
                  </th>
                  <th>
                    Total Customer Charge
                  </th>
                  <th>
                    Rental Start
                  </th>
                  <th>
                    Rental End
                  </th>
                  <th>
                    Ship Date
                  </th>

                  <th>
                   Action
                  </th>
                </tr>
              </thead>
              <tbody *ngIf="orderList.length < 1; else hasData">
                <tr *ngIf="!loader">
                  <td colspan="10">
                    <h4 class="text-center">No Order Found</h4>
                  </td>
                </tr>
              </tbody>
              <ng-template #hasData>
                <tbody>
                  <ng-template ngFor let-order let-i='index' let-o='odd' let-e='even' [ngForOf]="orderList"
                    [ngForTrackBy]="trackOrder">
                    <tr>
                      <td (click)="onClickOrderId(order.order_id)" class="cursor-pointer">{{order?.order_id}}</td>
                      <td>
                        {{order?.ship_from}}
                      </td>
                      <td>
                        {{order?.ship_to }}
                      </td>
                      <td>
                        {{order?.carrier }}
                      </td>
                      <td>
                        {{order?.charge_amount | currency }}
                      </td>
                      <td>
                        {{order?.rent_start }}
                      </td>
                      <td>
                        {{order?.rent_end }}
                      </td>
                      <td>
                        {{order?.ship_date}}
                      </td>
                      <td>
                        <i *ngIf="order?.is_shipping" (click)="onClickDetails(order)"class="fa fa-eye cursor-pointer"></i> &nbsp;
                        <i *ngIf="!order?.is_shipping" (click)="onClickDetails(order)"class="fa fa-plus cursor-pointer"></i> &nbsp;
                        <i (click)="onClickDelete(order.id)" class="cursor-pointer fa fa-trash"></i>
                      </td>
                    </tr>

                  </ng-template>

                </tbody>
              </ng-template>
            </table>
          </div>
          <!-- pagination Start-->
          <boot-pagination [totalSize]="pagi.total" [page]="pagi.page" [pagelimit]="pagi.limit" [listSize]="pagi.limit"
            (pageChange)="reloadTable($event)"></boot-pagination>
          <!-- pagination End-->
        </div>
      </div>
    </div>
  </div>
  <!--end::Portlet-->
</div>