import { HttpErrorResponse } from '@angular/common/http';
import { AlertService } from './../../../../../modules/alert/alert.service';
import { Helpers } from './../../../../../helpers';
import { DialogBoxComponent } from './../../../../../modules/dialog-box/dialog-box.component';
import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { Router } from '@angular/router';
import { Pagi } from '../../../../../modules/pagination/pagi.model';
import { Order } from '../../models/order-models';
import { OrderService } from '../../order.service/order.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-shipping-order-list',
  templateUrl: './shipping-order-list.component.html',
  styleUrls: ['./shipping-order-list.component.css']
})
export class ShippingOrderListComponent implements OnInit {


  pagi: Pagi = new Pagi();
  filter: string = "";
  loader: boolean;
  orderList: Order[] = [];

  @ViewChild("hasCusAlert") alertContainer: ElementRef;

  constructor(
    private router: Router,
    private orderS: OrderService,
    private modalService: NgbModal,
    private alertS: AlertService
  ) { }

  ngOnInit() {
    this.getOrderList(1, 20)
  }


  getOrderList(p, l) {
    this.loader = true;
    this.dataRender(p, l);
  }

  private dataRender(p?, l?) {
    this.orderS
      .getShipOrderList(p, l)
      .subscribe(
        res => {
           console.log(res);
          this.dataList(res);
          this.loader = false;
        },
        err => console.log(err)
      );
  }

  private dataList(res) {
    var data = res.data;
    this.orderList = data;
    this.pagi.total = res["total"] || 0;
    this.pagi.page = parseInt(res["page_no"]) || 1;
    this.pagi.limit = parseInt(res["limit"]) || 20;
  }



  trackOrder(index, order) {
    return order ? order.id : null;
  }

  reloadTable(e) {
    this.getOrderList(e.page, e.limit);
  }

  onClickOrderId(order_id) {
    this.router.navigate(["./admin/reservations/" + order_id + "/details"]);
  }


  onClickDetails(ship) {
    if (ship.is_shipping) {
      this.router.navigate(["./admin/reservations/shipping-orders/" + ship.id]);
    } else {
      this.router.navigate([`./admin/reservations/shipping/create/${ship.order_id}`], {
        queryParams: {
          shipment_id: ship.id
        }
      });
    }
  }

  onClickDelete(ship_id): void {
    const modalRef = this.modalService.open(DialogBoxComponent, {
      centered: true,
      size: "sm",
      backdrop: 'static'
    });
    modalRef.componentInstance.massage = "Are you sure to delete this shipment?";
    modalRef.result.then(
      result => {
        if (result) {
          this.deleteShipment(ship_id);
        }
      }
    );
  }

  private deleteShipment(ship_id): void {
    Helpers.setLoading(true);
    this.orderS.deleteShipment(ship_id).then(res => {
      Helpers.setLoading(false);
      if (res.status === 'OK') {
        this.getOrderList(this.pagi.page, this.pagi.limit);
        this.alertS.success(this.alertContainer, res.result.message, true, 3000);
      } else {
        this.alertS.error(this.alertContainer, res.result ? res.result.message : "Something went wrong!", true, 3000);
      }
    }).catch((err: HttpErrorResponse) => {
      Helpers.setLoading(false);
      this.alertS.error(this.alertContainer, err.error.result ? err.error.result.message : err.message, true, 3000);
    });
  }

}
