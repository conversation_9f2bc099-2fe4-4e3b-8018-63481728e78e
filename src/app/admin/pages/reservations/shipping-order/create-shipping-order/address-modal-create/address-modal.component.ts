import { Component, OnInit, ViewChild, ElementRef, Input, Output, EventEmitter } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { allCountry } from "../../../../../../globals/_classes/country";
import { AlertService } from "../../../../../../modules/alert/alert.service";
import { OrderService } from "../../../order.service/order.service";
import { SettingService } from "../../../../../../admin/pages/settings/setting-service/setting.service";



@Component({
  selector: "app-address-modal",
  templateUrl: "./address-modal.component.html",
  styleUrls: ["./address-modal.component.css"]
})
export class ShipAddressModalComponent implements OnInit {
  loading: boolean;
  loader: boolean;
  distanceForm: FormGroup;
  countries = allCountry;
  allState;
  countryId;
  stateCode;


  @Input("address_type") address_type;
  @Input("ship_id") ship_id;
  @Input("address_data") address_data;
  @Output() ship_from_update: EventEmitter<any> = new EventEmitter();
  @Output() ship_to_update: EventEmitter<any> = new EventEmitter();
  @ViewChild("hasCusAlert") alertContainer: ElementRef;

  constructor(
    public activeModal: NgbActiveModal,
    private fb: FormBuilder,
    private service: OrderService,
    private SettingService: SettingService,
    private alert: AlertService
  ) { }

  ngOnInit() {

    this.countryId = this.address_data.country_code ?
      this.countries.find(x => x.code == String(this.address_data.country_code).toLowerCase()).id : 230;

    this.getState();
    this.distanceForm = this.fb.group(this.initForm());

  }

  private initForm() {
    return {
      name: [this.address_data.name, Validators.required],
      company: [this.address_data.company_name,""],
      address_line1: [this.address_data.address_line1, Validators.required],
      address_line2: [this.address_data.address_line2],
      address_line3: [this.address_data.address_line3],
      phone: [this.address_data.phone, Validators.required],
      city: [this.address_data.city_locality, Validators.required],
      state: [this.address_data.state_province, Validators.required],
      zipcode: [this.address_data.postal_code, Validators.required],
      country: [this.address_data.country_code ? String(this.address_data.country_code).toLowerCase() : 'us', Validators.required],
      address_residential_indicator: [(this.address_data.address_residential_indicator == "unknown" || this.address_data.address_residential_indicator == "no") ? false : true, Validators.required]
    };
  }


  submitForm() {
    this.loading = true;

    let sendData = {};
    if (this.address_type == "Shipping from") {
      const ship_from = {
        name: this.distanceForm.get('name').value,
        company_name: this.distanceForm.get('company').value,
        address_line1: this.distanceForm.get('address_line1').value,
        phone: this.distanceForm.get('phone').value,
        city_locality: this.distanceForm.get('city').value,
        state_province: this.distanceForm.get('state').value,
        postal_code: this.distanceForm.get('zipcode').value,
        country_code: this.distanceForm.get('country').value,
        address_residential_indicator: this.distanceForm.get('address_residential_indicator').value ? 'yes' : 'no'
      }
      const data = {
        'type': 'from',
        'ship_from': ship_from
      }
      this.activeModal.close(data);
    }
    else if (this.address_type == "Shipping to") {
      const ship_to = {
        name: this.distanceForm.get('name').value,
        company_name: this.distanceForm.get('company').value,
        address_line1: this.distanceForm.get('address_line1').value,
        phone: this.distanceForm.get('phone').value,
        city_locality: this.distanceForm.get('city').value,
        state_province: this.distanceForm.get('state').value,
        postal_code: this.distanceForm.get('zipcode').value,
        country_code: this.distanceForm.get('country').value,
        address_residential_indicator: this.distanceForm.get('address_residential_indicator').value ? 'yes' : 'no'
      }
      const data = {
        'type': 'to',
        'ship_to': ship_to
      }
      this.activeModal.close(data);
    }
    else {
      const return_to = {
        name: this.distanceForm.get('name').value,
        company_name: this.distanceForm.get('company').value,
        phone: this.distanceForm.get('phone').value,
        address_line1: this.distanceForm.get('address_line1').value,
        city_locality: this.distanceForm.get('city').value,
        state_province: this.distanceForm.get('state').value,
        postal_code: this.distanceForm.get('zipcode').value,
        country_code: this.distanceForm.get('country').value,
        address_residential_indicator: this.distanceForm.get('address_residential_indicator').value ? 'yes' : 'no'
      }
      sendData["return_to"] = return_to;
    }

    // this.service
    //   .updateShipping(sendData, this.ship_id)
    //   .then(res => {
    //     this.loading = false;
    //     if (res.status === "OK") {
    //       this.alert.success(
    //         this.alertContainer,
    //         "Successfully updated",
    //         true,
    //         2000
    //       );
    //       setTimeout(() => {
    //         this.activeModal.close();
    //       }, 1000);
    //     } else {
    //       this.alert.error(this.alertContainer, res.result.message, true, 3000);
    //     }
    //   })
    //   .catch(err => {
    //     this.loading = false;
    //     this.alert.error(
    //       this.alertContainer,
    //       "Someting went wrong!!! Please try again.",
    //       true,
    //       3000
    //     );
    //   });
  }



  changeCountry(code) {
    this.countryId = this.countries.find(x => x.code == code).id;
    this.getState();
  }


  private getState() {
    this.SettingService.getState(this.countryId).subscribe(res => {
      this.allState = res;
      const state = this.distanceForm.get("state");
      if (this.allState && state.value) {
        const data = this.allState.find(
          f => f.code.toUpperCase() == state.value
        );
        this.stateCode = data ? data.id : null;
        if (!data) {
          state.setValue("");
        }
      }
    });
  }

  changeState(e) {
    if (e.id) {
      this.stateCode = e.id;
      const state = this.allState.find(f => f.id === e.id);
      this.distanceForm.get("state").setValue(state.code.toUpperCase());
    } else {
      this.distanceForm.get("state").setValue("");
    }
  }




}
