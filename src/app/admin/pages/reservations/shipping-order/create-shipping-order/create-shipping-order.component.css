.m-portlet.m-portlet--full-height .m-portlet__body {
    height: auto;
}
.description-field-title{
    font-weight: 600;
    padding-right: 5px;
}
.description-fields a {
    /* position: absolute;
    right: 0;
    top: -5px; */
}

.m-badge.m-badge--warning {
    color: #fff!important;
}

.dis-none{
    display: none;
}
.dis-block{
    display: table-row!important;
}
.m-badge.m-badge--warning {
    color: #fff!important;
}

.custom-alert{
    position: fixed;
    top: 90px;
    right: 30px;
    z-index: 5000;
}
.m-portlet__head-title{
    width: 100%;
}
.m-portlet__head-text{
    width: 90%;
}

.status {
    margin-top: 4px;
    margin-left: 16px;
    color: #fff;
}

.img-resize{
    max-width: 100px!important;
    height: 100px!important;
    object-fit: contain;
}
.table-order-asset th{ border-top: 0px !important;}
.btn-primary {
    color: #fff !important;
}
.description-fields p {
    margin-bottom: 10px;
}
.shpping-return-area {
    border-top: 1px solid #f2f3f8;
}
.shipping-area {
    border-right: 1px solid #f2f3f8;
}
.return-area p span, 
.shpping-return-area p span {
    font-size: 13px;
}
.return-area p .description-field-title,
.shpping-return-area p .description-field-title {
    display: inline-block;
    width: 100%;
}
.return-area .shipping-return-title,
.shpping-return-area .shipping-return-title {
    font-size: 15px !important;
}
.description-field-title2 {
    display: unset !important;
}
.shipping-return-list ul {
   list-style: none;
   padding: 0;
   margin: 0;
}
.shipping-return-list li {
    position: relative;
    width: 100%;
    border-bottom: 1px solid #f2f3f8;
    padding: 10px 0;
    transition: all .3s;
    padding-right: 50px;
}
.available-service-active {
    background-color: #f2f3f8;
    padding: 10px 70px 10px 15px !important;
}
.shipping-return-list li h5 {
    width: 100%;
    display: inline-block;
    margin-bottom: 0;
    font-size: 15px;
    color: #575962;
    font-weight: 500;
}
.shipping-return-amount {
    float: right;
    margin-bottom: 0;
    font-size: 15px;
    color: #575962;
    font-weight: 500;
    margin-top: -10px;
    position: absolute;
    top: 50%;
    right: 0px;

}
.available-service-active .shipping-return-amount {
    right: 15px;
}
.btn.btn-success {
    color: #fff !important;
}
.adjust-form .form-control {
    padding-left: 0;
    padding-right: 0;
    text-align: center;
}
.adjust-form span i {
    color: #a4a5a9;
    font-size: 15px;
}
.adjust-form span {
    color: #a4a5a9;
}
.btn.btn-danger {
    color: #fff !important;
    margin-bottom: 5px;
}
.btn.btn-danger,
.btn.btn-success {
    /* margin-top: -59px; */
}
.btn.btn-success {
    margin-bottom: 5px;
}
.create-label-btn {
    padding: 0.65rem 2rem;
}
.description-fields h4 span {
    font-size: 20px;
}
.shipping-return-datatime {
    font-size: 13px;
}
.shpping-return-area .description-fields p {
    padding-right: 25px;
}
.shpping-return-area .description-fields p i {
    position: absolute;
    top: 30%;
    right: 15px;
}
.shpping-return-area .dimension-label {
    word-break: break-word;
}

@media (max-width: 1199px) {
    .adjust-form .btn.m-btn.m-btn--icon {
        margin-left: 5px;
    }
}
@media (max-width: 767px) {
    .shipping-area {
        border-right: none;
    }
    .return-area {
        padding-top: 50px;
    }
}