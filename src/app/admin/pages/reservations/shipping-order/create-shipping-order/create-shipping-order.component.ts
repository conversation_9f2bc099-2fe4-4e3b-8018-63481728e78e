import { HttpErrorResponse } from '@angular/common/http';
import { Subscription, Observable } from 'rxjs';
import { DialogBoxComponent } from './../../../../../modules/dialog-box/dialog-box.component';
import { Helpers } from './../../../../../helpers';
import { AlertService } from './../../../../../modules/alert/alert.service';
import { Component, OnInit, ViewChild, ElementRef, OnDestroy } from '@angular/core';
import { FormGroup, FormArray, FormBuilder } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { OrderService } from '../../order.service/order.service';
import { ShipAddressModalComponent } from './address-modal-create/address-modal.component';

@Component({
  selector: "app-create-shipping-order",
  templateUrl: "./create-shipping-order.component.html",
  styleUrls: ["./create-shipping-order.component.css"]
})
export class CreateShippingOrderComponent implements OnInit, OnDestroy {
  shipLabelForm: FormGroup;
  order_id;
  order;
  ship_Date;
  label_config_list: FormArray;
  shipServiceList = [];
  selectedShipServiceId;
  selectedShipService: any;
  subs: Subscription[] = [];
  shipLabelData = null;
  returnLabelData = null;
  ship_from: any;
  ship_to: any;
  error_message: string;
  ship_id: any;
  addressRequiredStr = '';
  isAddressRequired = false;
  @ViewChild("hasCusAlert") alertContainer: ElementRef;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private orderS: OrderService,
    private modalService: NgbModal,
    private _formBuilder: FormBuilder,
    private alert: AlertService
  ) {
    this.route.queryParams.subscribe(params => {
      if (params.shipment_id) {
        this.ship_id = params.shipment_id;
      }
    })
  }

  ngOnInit() {
    this.shipLabelForm = this._formBuilder.group({
      ship_date: [""],
      label_config_list: this._formBuilder.array([this.createLabelConfig()])
    });
    this.subs.push(
      this.route.params.subscribe(params =>
        params ? (this.order_id = params.order_id) : (this.order_id = null)
      )
    );
    this.getOrderDetails();
    this.getShipServices();
  }
  showAddrError(shipTo){
    let props = [
      "name",
      "address_line1",
      "phone",
      "city_locality",
      "postal_code",
      "state_province"
    ];
    // props.forEach(prop=> delete ship[prop]);
    let required = props.filter(prop=> !shipTo[prop] || !shipTo[prop].trim());
    this.addressRequiredStr = required.join(', ');
    this.isAddressRequired = required.length != 0;
  }
  async getOrderDetails() {
    const subs = await this.orderS.getShipOrderDetailsOrder(this.order_id).subscribe(
      res => {
        this.order = res.data;
        this.showAddrError(this.order.requested_shipping.ship_to)
        this.shipLabelData = this.order.label
          ? this.order.label.hasOwnProperty("shipping")
            ? this.order.label.shipping.label_download
            : null
          : null;
        this.returnLabelData = this.order.label
          ? this.order.label.hasOwnProperty("return")
            ? this.order.label.return.label_download
            : null
          : null;
        this.ship_Date = this.order.requested_shipping.shipping_date
          ?  Helpers.date2str(new Date(this.order.requested_shipping.shipping_date), 'yyyy-MM-dd')
          : Helpers.date2str(new Date(), 'yyyy-MM-dd');
        this.ship_from = this.order.requested_shipping.ship_from;
        this.ship_to = this.order.requested_shipping.ship_to;
        const Control = this.shipLabelForm.get("label_config_list") as FormArray;
        if (this.order.requested_shipping.packages) {
          for (let i = 1; i <= this.order.requested_shipping.packages.length - 1; i++) {
            Control.push(this.createLabelConfig());
          }
        }
        const patchData = {
          ship_date: this.order.requested_shipping.shipping_date,
          label_config_list: this.order.requested_shipping.packages
        };
        this.shipLabelForm.patchValue(patchData);
      },
      err => console.log(err)
    );
    this.subs.push(subs);
  }

  async getShipServices(date?: string, address?: any) {
    this.selectedShipService = null;
    Helpers.setLoading(true);
    let ref: Observable<any>;
    if (address) {
      const data = {
        date: date,
        ship_from: address.ship_from ? address.ship_from : this.order.requested_shipping.ship_from,
        ship_to: address.ship_to ? address.ship_to : this.order.requested_shipping.ship_to,
        packages : address.packages ? address.packages : this.shipLabelForm.get('label_config_list')['controls'].map(formGrp => formGrp.value)
      }
      ref = this.orderS.getShipRatesOrderPost(this.order_id, data);
    } else {
      ref = this.orderS.getShipRatesOrder(this.order_id, date);
    }
    const sub = await ref.subscribe(
      res => {
        Helpers.setLoading(false);
        if (res.status === 'OK') {
          this.shipServiceList = res.result.data;
          this.error_message = null;
        } else {
          this.error_message = res.result.message;
        }
      },
      err => {
        this.error_message = "Something went wrong!";
        console.log(err);
        Helpers.setLoading(false);
      }
    );
    this.subs.push(sub);
  }

  createLabelConfig(): FormGroup {
    return this._formBuilder.group({
      height: [""],
      width: [""],
      length: [""],
      weight: [""]
    });
  }

  addLabelConfig(): void {
    this.label_config_list = this.shipLabelForm.get(
      "label_config_list"
    ) as FormArray;
    this.label_config_list.push(this.createLabelConfig());
  }

  removeLabelConfig(index): void {
    this.label_config_list = this.shipLabelForm.get(
      "label_config_list"
    ) as FormArray;
    this.label_config_list.removeAt(index);
  }

  selectstartDateTime(e: string): void {
    console.log(e);
    this.ship_Date = e.split(" ")[0];
  }
  onClickReload(){
    const address = {
      ship_from: this.order.requested_shipping.ship_from,
      ship_to: this.order.requested_shipping.ship_to,
      packages: this.shipLabelForm.get('label_config_list')['controls'].map(formGrp => formGrp.value)
    }
    this.getShipServices(this.ship_Date, address);
  }

  onClickEditAddress(type) {
    const modalRef = this.modalService.open(ShipAddressModalComponent, {
      centered: true,
      size: "lg",
      backdrop: "static"
    });
    modalRef.componentInstance.address_type = type;
    modalRef.componentInstance.ship_id = this.order_id;

    let addressData = {
      name: "",
      company_name: "",
      address_line1: "",
      address_line2: "",
      address_line3: "",
      phone: "",
      city_locality: "",
      state_province: "",
      postal_code: "",
      country_code: "US",
      address_residential_indicator: "no"
    };

    if (type == "Shipping from") {
      addressData = this.order.requested_shipping.ship_from
        ? this.order.requested_shipping.ship_from
        : addressData;
    } else if (type == "Shipping to") {
      addressData = this.order.requested_shipping.ship_to
        ? this.order.requested_shipping.ship_to
        : addressData;
    } else {
      addressData = this.order.requested_shipping.return_to
        ? this.order.requested_shipping.return_to
        : addressData;
    }
    modalRef.componentInstance.address_data = addressData;
    modalRef.result.then(
      result => {
        this.isAddressRequired = false;
        if (result) {
          let address = {
            ship_from: this.order.requested_shipping.ship_from,
            ship_to: this.order.requested_shipping.ship_to
          }
          if (result.type === 'from') {
            address.ship_from = result.ship_from;
            this.ship_from = result.ship_from;
            const ship_from_combined = Object.values(this.ship_from);
            this.ship_from['combined'] = ship_from_combined;
            this.order.requested_shipping.ship_from = this.ship_from;
          } else if (result.type === 'to') {
            address.ship_to = result.ship_to;
            this.ship_to = result.ship_to;
            const ship_to_combined = Object.values(this.ship_to);
            this.ship_to['combined'] = ship_to_combined;
            this.order.requested_shipping.ship_to = this.ship_to;
          }
          this.getShipServices(this.ship_Date, address);
        }
      },
      res => {
        console.log(res);
      }
    );
  }

  onClickShipService(service?) {
    this.selectedShipServiceId = service?.rate_id;
    this.selectedShipService = service;
    this.shipServiceList.map(s => {
      $("#" + s.rate_id).removeClass("available-service-active");
    });
    $("#" + service.rate_id).addClass("available-service-active");
  }

  onClickCreateShipLabel() {
    let sendData = {};
    sendData["packages"] = this.shipLabelForm.get("label_config_list").value;
    sendData["ship_date"] = this.ship_Date;
    sendData["ship_from"] = this.ship_from;
    sendData["ship_to"] = this.ship_to;
    sendData["order_id"] = this.order_id;
    if (this.shipServiceList && !this.selectedShipService) {
      this.alert.error(this.alertContainer, 'Please select a service from the list.', true, 3000)
      return;
    }
    sendData["rate"] = this.selectedShipService;
    if (this.ship_id) {
      sendData['shipment_id'] = this.ship_id;
    }
    this.createShipment(sendData);
  }

  private createShipment(data) {
    Helpers.setLoading(true);
    this.orderS
      .createShipping(data)
      .then(res => {
        if (res.status === "OK") {
          this.alert.success(this.alertContainer, res.result.message, true, 3000);
          setTimeout(() => {
            this.router.navigate(['admin/reservations/shipping-orders/' + res.result.data.id]);
          }, 2000);
        } else {
          this.alert.error(this.alertContainer, res.result.message, true, 3000);
        }
        Helpers.setLoading(false);
      })
      .catch((err: HttpErrorResponse) => {
        Helpers.setLoading(false);
        this.alert.error(this.alertContainer, err.error.result ? err.error.result.message : err.message, true, 3000);
      });
  }

  createShipLabel() {
    Helpers.setLoading(true);
    this.orderS.createShipLabel(this.order_id, "ship").subscribe(
      res => {
        if (res.status == "OK") {
          console.log(res);
          this.shipLabelData = res.result.data.label.ship.label_download;
        } else {
          this.alert.error(this.alertContainer, res.result.message, true, 5000);
        }
        Helpers.setLoading(false);
      },
      err => console.log(err)
    );
  }

  onClickCreateReturnLabel() {
    Helpers.setLoading(true);
    this.orderS.createShipLabel(this.order_id, "return").subscribe(
      res => {
        if (res.status == "OK") {
          console.log(res);
          this.returnLabelData = res.result.data.label.return.label_download;
        } else {
          this.alert.error(this.alertContainer, res.result.message, true, 5000);
        }

        Helpers.setLoading(false);
      },
      err => console.log(err)
    );
  }

  ngOnDestroy(): void {
    this.subs.forEach(sub => sub.unsubscribe());
  }
}
