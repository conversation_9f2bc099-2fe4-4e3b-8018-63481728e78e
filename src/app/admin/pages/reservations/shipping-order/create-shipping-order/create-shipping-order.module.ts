import { DialogBoxComponent } from './../../../../../modules/dialog-box/dialog-box.component';
import { PaginationModule } from './../../../../../modules/pagination/pagination.module';
import { DateTimeRangeModule } from './../../../../../modules/date-time-range/date-time-range.module';
import { DialogBoxModule } from './../../../../../modules/dialog-box/dialog-box.module';
import { Select2NormalModule } from './../../../../../modules/select2-normal/select2-normal.module';
import { RouterModule, Routes } from '@angular/router';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CreateShippingOrderComponent } from './create-shipping-order.component';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { ReactiveFormsModule } from '@angular/forms';
import { ShipAddressModalComponent } from './address-modal-create/address-modal.component';

const routes: Routes = [
  {
    path: '',
    component: CreateShippingOrderComponent
  }
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    ReactiveFormsModule,
    PaginationModule,
    DateTimeRangeModule,
    NgbModule,
    DialogBoxModule,
    Select2NormalModule
  ],
  declarations: [CreateShippingOrderComponent, ShipAddressModalComponent],
  entryComponents: [ShipAddressModalComponent, DialogBoxComponent]
})
export class CreateShippingOrderModule { }
