import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { OrderService } from '../../order.service/order.service';
import { FormGroup, FormBuilder, FormArray } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ShipAddressModalComponent } from './address-modal/address-modal.component';
import { DialogBoxComponent } from '../../../../../modules/dialog-box/dialog-box.component';
import { AlertService } from '../../../../../modules/alert/alert.service';
import { Helpers } from '../../../../../helpers';

@Component({
  selector: 'app-shipping-order-details',
  templateUrl: './shipping-order-details.component.html',
  styleUrls: ['./shipping-order-details.component.css']
})
export class ShippingOrderDetailsComponent implements OnInit {

  shipLabelForm: FormGroup;
  order_id;
  order;
  ship_Date;
  label_config_list: FormArray;
  shipServiceList = [];
  selectedShipServiceId;

  shipLabelData = null;
  returnLabelData = null;

  shipping_tracking_id: string;
  return_tracking_id: string;

  @ViewChild("hasCusAlert") alertContainer: ElementRef;

  constructor(
    private route: ActivatedRoute,
    private orderS: OrderService,
    private modalService: NgbModal,
    private _formBuilder: FormBuilder,
    private alert: AlertService
  ) { }

  ngOnInit() {
    this.shipLabelForm = this._formBuilder.group({
      ship_date: [""],
      label_config_list: this._formBuilder.array([this.createLabelConfig()])
    })

    this.order_id = this.route.snapshot.params.order_id;
    this.getOrderDetails();
    this.getShipServices();
  }

  async getOrderDetails() {
    await this.orderS.getShipOrderDetails(this.order_id).subscribe(
      res => {
        this.order = res.data;

        this.shipLabelData = this.order.label ?
          (this.order.label.hasOwnProperty('shipping') ? this.order.label.shipping.label_download : null)
          : null;
        this.returnLabelData = this.order.label ?
          (this.order.label.hasOwnProperty('return') ? this.order.label.return.label_download : null)
          : null;

        this.shipping_tracking_id = this.order.label ?
          (this.order.label.hasOwnProperty('shipping') ? this.order.label.shipping.tracking_number : null)
          : null;

        this.return_tracking_id = this.order.label ?
          (this.order.label.hasOwnProperty('return') ? this.order.label.return.tracking_number : null)
          : null;

        this.ship_Date = this.order.requested_shipping.shipping_date;

        let Control = <FormArray>this.shipLabelForm.get('label_config_list');
        if (this.order.requested_shipping.packages) {
          for (let i = 1; i <= this.order.requested_shipping.packages.length - 1; i++) {
            Control.push(this.createLabelConfig());
          }
        }

        const patchData = {
          ship_date: this.order.requested_shipping.shipping_date,
          label_config_list: this.order.requested_shipping.packages
        }

        this.shipLabelForm.patchValue(patchData);
      },
      err => console.log(err)
    );
  }


  async getShipServices() {
    Helpers.setLoading(true)
    await this.orderS.getShipRates(this.order_id).subscribe(
      res => {
        Helpers.setLoading(false)
        this.shipServiceList = res.data;
      },
      err => console.log(err)
    );
  }


  createLabelConfig(): FormGroup {
    return this._formBuilder.group({
      height: [""],
      width: [""],
      length: [""],
      weight: [""]
    })
  }

  addLabelConfig(): void {
    this.label_config_list = this.shipLabelForm.get('label_config_list') as FormArray;
    this.label_config_list.push(this.createLabelConfig());
  }

  removeLabelConfig(index): void {
    this.label_config_list = this.shipLabelForm.get('label_config_list') as FormArray;
    this.label_config_list.removeAt(index);
  }


  selectstartDateTime(e: string): void {
    this.ship_Date = e.split(' ')[0];
  }

  onClickEditAddress(type) {
    const modalRef = this.modalService.open(ShipAddressModalComponent, {
      centered: true,
      size: "lg",
      backdrop: 'static'
    });
    modalRef.componentInstance.address_type = type;
    modalRef.componentInstance.ship_id = this.order_id;

    let addressData = {
      name: '',
      company_name: '',
      address_line1: '',
      address_line2: '',
      address_line3: '',
      phone: '',
      city_locality: '',
      state_province: '',
      postal_code: '',
      country_code: 'US',
      address_residential_indicator: 'no'
    };

    if (type == "Shipping from") {
      addressData = this.order.requested_shipping.ship_from ? this.order.requested_shipping.ship_from : addressData;
    }
    else if (type == "Shipping to") {
      addressData = this.order.requested_shipping.ship_to ? this.order.requested_shipping.ship_to : addressData;
    }
    else {
      addressData = this.order.requested_shipping.return_to ? this.order.requested_shipping.return_to : addressData;
    }


    modalRef.componentInstance.address_data = addressData;
    modalRef.result.then(
      result => {
        console.log(result)
        this.getOrderDetails();
      },
      res => {
        console.log(res);
      }
    );
  }



  onClickShipService(service?) {
    this.selectedShipServiceId = service.rate_id;

    this.shipServiceList.map(s => {
      $('#' + s.rate_id).removeClass('available-service-active');
    })
    $('#' + service.rate_id).addClass('available-service-active');

    const modalRef = this.modalService.open(DialogBoxComponent, {
      centered: true,
      size: "sm",
      backdrop: 'static'
    });
    modalRef.componentInstance.massage = "Are you sure to select this service";

    modalRef.result.then(
      result => {

        if (result) {
          console.log(result)

          this.updateShipData(service, this.order_id, 'shipService');

          setTimeout(t => {
            $('#' + this.selectedShipServiceId).addClass('available-service-active');
          }, 2000)

        }

      },
      res => {
        console.log(res);
      }
    );

  }

  onClickCreateShipLabel() {
    let sendData = {};
    sendData['packages'] = this.shipLabelForm.get('label_config_list').value;
    sendData['ship_date'] = this.ship_Date;
    this.updateShipData(sendData, this.order_id, 'shipPackage')
  }


  updateShipData(data, id, source?) {
    Helpers.setLoading(true);
    this.orderS
      .updateShipping(data, id)
      .then(res => {
        if (res.status === "OK") {
          if (source == "shipService") {
            this.getShipServices();
            this.getOrderDetails();
          }
          else if (source == "shipPackage") {
            this.createShipLabel();
          }
          else {
            this.getOrderDetails();
          }

        }
        else {
          this.alert.error(this.alertContainer, res.result.message, true, 3000);
        }

        Helpers.setLoading(false)
      })
      .catch(err => {

      });

  }


  createShipLabel() {
    Helpers.setLoading(true)
    this.orderS.createShipLabel(this.order_id, 'ship').subscribe(
      res => {
        if (res.status == "OK") {
          console.log(res);
          this.shipLabelData = res.result.data.label.ship.label_download;
        }
        else {
          this.alert.error(this.alertContainer, res.result.message, true, 5000);
        }
        Helpers.setLoading(false)
      },
      err => console.log(err)
    );
  }



  onClickCreateReturnLabel() {
    Helpers.setLoading(true)
    this.orderS.createShipLabel(this.order_id, 'return').subscribe(
      res => {
        if (res.status == "OK") {
          console.log(res);
          this.returnLabelData = res.result.data.label.return.label_download;
        }
        else {
          this.alert.error(this.alertContainer, res.result.message, true, 5000);
        }

        Helpers.setLoading(false)

      },
      err => console.log(err)
    );
  }

  copyToClipboard(id: string) {
    const selBox = document.createElement('textarea');
    selBox.style.position = 'fixed';
    selBox.style.left = '0';
    selBox.style.top = '0';
    selBox.style.opacity = '0';
    selBox.value = id;
    document.body.appendChild(selBox);
    selBox.focus();
    selBox.select();
    document.execCommand('copy');
    document.body.removeChild(selBox);
    this.alert.success(this.alertContainer,'Tracking ID is copied to your clipboard!', true, 3000);
  }

}
