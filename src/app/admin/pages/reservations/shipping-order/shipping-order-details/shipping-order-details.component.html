<div class="custom-alert" #hasCusAlert></div>


<!-- BEGIN: Subheader -->
<div class="m-subheader">
  <div class="d-flex align-items-center">
    <div class="mr-auto">
      <h3 class="m-subheader__title m-subheader__title--separator">
        Shipping Details
      </h3>
      <ul class="m-subheader__breadcrumbs m-nav m-nav--inline">
        <li class="m-nav__item m-nav__item--home">
          <a routerLink="/admin" class="m-nav__link m-nav__link--icon">
            <i class="m-nav__link-icon la la-home"></i>
          </a>
        </li>
        <li class="m-nav__separator">
          <i class="fa fa-angle-right"></i>
        </li>
        <li class="m-nav__item">
          <a class="m-nav__link" [routerLink]="['/admin/reservations/all']">
            <span class="m-nav__link-text">
              Reservations
            </span>
          </a>
        </li>
        <li class="m-nav__separator" style="padding-left: 10px">
          <i class="fa fa-angle-right"></i>
        </li>
        <li class="m-nav__item">
          <a class="m-nav__link" [routerLink]="['/admin/reservations/shipping-orders']">
            <span class="m-nav__link-text">
              Shipping Order List
            </span>
          </a>
        </li>
        <li class="m-nav__separator" style="padding-left: 10px">
          <i class="fa fa-angle-right"></i>
        </li>
        <li class="m-nav__item">
          <a class="m-nav__link">
            <span class="m-nav__link-text">
              Shipping Details
            </span>
          </a>
        </li>
      </ul>
    </div>
  </div>
</div>
<!-- END: Subheader -->

<div class="m-content animated fadeIn">
  <div class="row">
    <div class="col-md-12">
      <div class="m-portlet m-portlet--full-height">
        <div class="m-portlet__head">
          <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title row">
              <h3 class="m-portlet__head-text col-12 colorPurpel"> Shipping Details </h3>
              <button *ngIf="order" style="margin-top: 15px;float: right;" class="btn btn-primary" [routerLink]="'/admin/reservations/shipping/create/' + order.order_id">Create Shipping</button>
            </div>
          </div>
        </div>

        <div class="m-portlet__body">

          <div class="description-fields row">
            <div class="col-md-6 col-sm-6 col-12">
              <h4 class="mb-3"><span class="description-field-title">Order Id: </span><span>{{order?.order_id}}</span></h4>
              <p><span class="description-field-title">Rental Start Date:
                </span><span>{{order?.rent_start | date:'MM/dd/yyyy'}}</span></p>
              <p><span class="description-field-title">Rental End Date:
                </span><span>{{order?.rent_end | date:'MM/dd/yyyy'}}</span></p>
              <p><span class="description-field-title">Shipping Collection:
                </span><span>{{ order?.collected_shipping_amount | currency }}</span></p>
            </div>
            <div class="col-md-6 col-sm-6 col-12">
              <h4><span class="description-field-title">Order Total: </span><span>{{ order?.total_amount | currency }}
                </span>
              </h4>
              <p><span class="description-field-title">Shipping Charge:
                </span><span>{{ order?.shipping_amount | currency }}</span></p>
              <p><span class="description-field-title">Collected Charge:
                </span><span>{{ order?.collected_shipping_amount | currency }}</span></p>
            </div>
          </div>

        </div>

        <div class="m-portlet__body shpping-return-area">
          <div class="description-fields row">

            <div class="col-md-6 shipping-area">
              <h4 class="mb-3"><span class="description-field-title">Shipping</span></h4>

              <div class="row">
                <div class="col-md-12">
                  <p>
                    <span class="description-field-title shipping-return-title">Shipping from:</span>
                    <span class="description-field-title description-field-title2">Address:</span>
                    <span>{{order?.requested_shipping?.ship_from?.combined}}</span>
                    <i class="la la-edit float-right cursor-pointer" (click)="onClickEditAddress('Shipping from')"></i>
                  </p>
                </div>
                <div class="col-md-12">
                  <p>
                    <span class="description-field-title shipping-return-title">Shipping to:</span>
                    <span class="description-field-title description-field-title2">Address:</span>
                    <span>{{order?.requested_shipping?.ship_to?.combined}}</span>
                    <i class="la la-edit float-right cursor-pointer" (click)="onClickEditAddress('Shipping to')"></i>
                  </p>
                </div>

                <div *ngIf="shipping_tracking_id" class="col-md-12 tracking-container">
                  <div class="row">
                    <div class="col-md-6">
                      <span>Tracking ID</span>
                    </div>
                    <div class="col-md-6">
                      <span>{{shipping_tracking_id}}</span>
                      <i 
                        title="Click here to copy the Tracking ID to clipboard" 
                        (click)="copyToClipboard(shipping_tracking_id)"
                        class="la la-copy cursor-pointer float-right copy-clip">
                      </i>
                    </div>
                  </div>
                </div>
              </div>

              <div class="row shipping-return-list">
                <div class="col-md-12 pt-4">
                  <p class="mb-1">
                    <span class="description-field-title shipping-return-title">Requested Service</span>
                  </p>
                  <ul>
                    <li class="cursor-pointer" (click)="onClickShipService()">
                      <h5>{{order?.requested_shipping?.service_name}}</h5>
                      <span class="shipping-return-datatime">Estimated Delivery Date:
                        {{order?.requested_shipping?.delivery_date | date:'mediumDate'}}</span>
                      &nbsp;
                      <span class="shipping-return-datatime">Delivery Days:
                        {{order?.requested_shipping?.delivery_days}}</span>

                      <span class="shipping-return-amount">{{order?.requested_shipping?.charge | currency}}</span>
                    </li>
                  </ul>
                </div>
              </div>

              <form [formGroup]="shipLabelForm">
                <div class="row pt-4 align-items-center adjust-form">
                  <div class="col-md-12">
                    <div class="row mb-4">
                      <div class="col-xl-10 col-lg-10 col-md-10 col-sm-10 col-10">
                        <div class="form-group m-form__group row align-items-center">
                          <label for="example-text-input" class="col-md-3 col-sm-3 col-3 col-form-label">
                            Ship Date
                          </label>
                          <div class="col-9">
                            <div class="row">
                              <div class="col-xl-7 col-lg-7 col-md-7 col-sm-7 col-7 p-0">
                                <app-date-time-range
                                  ngClass="single"
                                  (startDateTime)="selectstartDateTime($event)"
                                  [placeholderEnd]="ship_Date" 
                                  [placeholderStart]="ship_Date" 
                                  [onlyStartDate]="true">
                                </app-date-time-range>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>


                    <ng-container formArrayName="label_config_list">
                      <div
                        *ngFor="let labelConfig of shipLabelForm.get('label_config_list')['controls'];let i = index;">
                        <div class="row mb-4">
                          <div class="col-lg-10 col-md-10 col-sm-10 col-10">
                            <ng-container [formGroupName]="i">
                              <div class="form-group m-form__group row align-items-center">
                                <label for="example-text-input" class="col-lg-3 col-md-3 col-sm-3 col-3 col-form-label dimension-label">
                                  Dimension
                                </label>
                                <div class="col-lg-9 col-md-9 col-sm-9 col-9">
                                  <div class="row align-items-center">
                                    <div class="col-lg-3 col-md-3 col-sm-3 col-3 p-0">
                                      <input class="form-control m-input" type="number" placeholder="H"
                                        formControlName="height">
                                    </div>
                                    <div class="col-lg-1 col-md-1 col-sm-1 col-1 p-0 text-center">
                                      <span><i class="la la-times"></i></span>
                                    </div>
                                    <div class="col-lg-3 col-md-3 col-sm-3 col-3 p-0">
                                      <input class="form-control m-input" type="number" placeholder="W"
                                        formControlName="width">
                                    </div>
                                    <div class="col-lg-1 col-md-1 col-sm-1 col-1 p-0 text-center">
                                      <span><i class="la la-times"></i></span>
                                    </div>
                                    <div class="col-lg-3 col-md-3 col-sm-3 col-3 p-0">
                                      <input class="form-control m-input" type="number" placeholder="L"
                                        formControlName="length">
                                    </div>
                                    <div class="col-lg-1 col-md-1 col-sm-1 col-1 p-0 text-center">
                                      <span>in.</span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div class="form-group m-form__group row">
                                <label for="example-text-input" class="col-3 col-form-label">
                                  Weight
                                </label>
                                <div class="col-9">
                                  <div class="row align-items-center">
                                    <div class="col-md-3 col-sm-3 col-3 p-0">
                                      <input class="form-control m-input" type="number" placeholder="W"
                                        formControlName="weight">
                                    </div>
                                    <div class="col-md-1 col-sm-1 col-1 p-0 text-center">
                                      <span>lb</span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </ng-container>
                          </div>

                          <div class="col-xl-2 col-lg-2 col-md-2 col-sm-2 col-2 p-0 mt-2">
                            <a *ngIf="shipLabelForm.get('label_config_list')['controls'].length>1"
                              class="btn btn-danger m-btn m-btn--icon btn-sm m-btn--icon-only m-btn--pill mr-1"
                              (click)="removeLabelConfig(i)">
                              <i class="la la-minus"></i>
                            </a>

                            <a *ngIf="(i+1) == (shipLabelForm.get('label_config_list')['controls'].length)"
                              class="btn btn-success m-btn m-btn--icon btn-sm m-btn--icon-only m-btn--pill"
                              (click)="addLabelConfig()">
                              <i class="la la-plus"></i>
                            </a>

                          </div>

                        </div>

                      </div>
                    </ng-container>

                    <div class="form-group m-form__group row">
                      <div class="col-lg-10 col-md-10 col-sm-10 col-10">
                        <div class="row">
                          <div class="col-md-9 col-sm-9 col-9 offset-md-3 offset-sm-3 offset-3 p-0 mb-4" *ngIf="shipLabelData ==null">
                            <a class="btn btn-primary create-label-btn"
                              (click)="onClickCreateShipLabel()">
                              Create Shipping Label
                            </a>
                          </div>
                          <div class="col-md-12 col-sm-12 col-12 p-0" *ngIf="shipLabelData !=null">
                            <div class="row m-0">
                              <div class="col-md-9 col-sm-9 col-9 offset-md-3 offset-sm-3 offset-3 p-0">
                                <a [href]="shipLabelData?.pdf" target="_blank" class="cursor-pointer btn btn-info create-label-btn">Download Label Pdf</a>
                              </div>
                              <div class="col-md-12 col-sm-12 col-12">
                                <img [src]="shipLabelData?.png" class="img-fluid shiplabeldate-img mt-4" />
                              </div>
                            </div>
                           </div>
                        </div>
                      </div>
                    </div>
                  </div>

                </div>
              </form>


              <div *ngIf="shipLabelData ==null" class="row shipping-return-list">
                <div class="col-md-12 pt-4">
                  <p class="mb-3">
                    <span class="description-field-title shipping-return-title">Select From Available Service</span>
                  </p>
                  <ul>
                    <li class="cursor-pointer" id="{{ship_service?.rate_id}}"
                      *ngFor="let ship_service of shipServiceList" (click)="onClickShipService(ship_service)">
                      <h5>{{ship_service?.service_name}}</h5>
                      <span class="shipping-return-datatime">Estimated Delivery Date:
                        {{ship_service?.delivery_date | date:'mediumDate'}}</span>
                      &nbsp;
                      <span class="shipping-return-datatime">Delivery Days: {{ship_service?.delivery_days}}</span>
                      <span class="shipping-return-amount">{{ship_service?.charge | currency}}</span>
                    </li>
                  </ul>
                </div>
              </div>

            </div>

            <div class="col-md-6 return-area">
              <h4 class="mb-3"><span class="description-field-title">Return</span></h4>

              <div class="row">
                <div class="col-md-12">
                  <p>
                    <span class="description-field-title shipping-return-title">Return to:</span>
                    <span class="description-field-title description-field-title2">Address:</span>
                    <span>{{order?.requested_shipping?.return_to?.combined}}</span>
                    <i class="la la-edit float-right cursor-pointer" (click)="onClickEditAddress('Return to')"></i>
                  </p>
                </div>
              </div>

              <div *ngIf="return_tracking_id" class="col-md-12 tracking-container">
                <div class="row">
                  <div class="col-md-5">
                    <span>Tracking ID</span>
                  </div>
                  <div class="col-md-7">
                    <span>{{return_tracking_id}}</span>
                    <i 
                      title="Click here to copy the Tracking ID to clipboard" 
                      (click)="copyToClipboard(return_tracking_id)"
                      class="la la-copy cursor-pointer float-right copy-clip">
                    </i>
                  </div>
                </div>
              </div>

              <div class="row pt-4" *ngIf="returnLabelData==null && shipLabelData !=null" >
                <div class="col-md-12">
                  <a class="btn btn-primary create-label-btn"
                    (click)="onClickCreateReturnLabel()">
                    Create Return Label
                  </a>
                </div>
              </div>

              <div class="row pt-4" *ngIf="returnLabelData !=null">
                <div class="col-xl-12 col-lg-10 col-md-10 col-sm-12 col-12">
                  <a [href]="returnLabelData?.pdf" target="_blank" class="cursor-pointer btn btn-info create-label-btn">Download Return Pdf</a>
                  <br />
                  <img [src]="returnLabelData?.png" class="img-fluid shiplabeldate-img mt-4" />
                </div>
              </div>
            </div>

          </div>
        </div>

      </div>
    </div>
  </div>

</div>