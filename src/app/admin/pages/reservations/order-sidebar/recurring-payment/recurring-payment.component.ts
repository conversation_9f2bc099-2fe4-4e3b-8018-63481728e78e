import { Component, OnInit, OnDestroy, ViewChild, ElementRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { FormGroup, FormBuilder } from '@angular/forms';
import { OrderService } from '../../order.service/order.service';
import { AlertService } from './../../../../../modules/alert/alert.service';
import { Helpers } from './../../../../../helpers';

@Component({
  selector: 'app-recurring-payment',
  templateUrl: './recurring-payment.component.html',
  styleUrls: ['./recurring-payment.component.css']
})
export class RecurringPaymentComponent implements OnDestroy {

  subs: Subscription[] = [];
  recurringOrderData: any;
  form: FormGroup;

  @ViewChild("hasCusAlert") alertContainer: ElementRef;

  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private fb: FormBuilder,
    public orderS: OrderService,
    private alertService: AlertService
  ) {
    this.form = this.initForm();
    this.subs.push(
      this.activatedRoute.data.subscribe(res => {
        if (res.recurring) {
          this.recurringOrderData = res.recurring.result.data;
          this.form.patchValue(this.recurringOrderData);
        }
      })
    );
  }

  initForm(): FormGroup {
    return this.fb.group({
      amount: [''],
      duration_type: [''],
      end_date: [''],
      status: ['']
    })
  }

  ngOnDestroy() {
    this.subs.forEach(sub => sub.unsubscribe());
  }

  getDateFromManualChange(e) {
    this.form.get('end_date').setValue(e.startDate);
  }

  submit() {
    Helpers.setLoading(true);
    const formVal = this.form.getRawValue();
    let data = new Object();
    data['recurringType'] = formVal.duration_type;
    data['recurring'] = formVal.status;
    data['recurringAmount'] = formVal.amount;
    data['recurringEndDate'] = formVal.end_date;
    this.orderS.modifyRecurringPayment(this.recurringOrderData.order_id, data)
      .catch(err => {
        Helpers.setLoading(false);
        this.alertService.error(
          this.alertContainer,
          err.error.result ? err.erros.result.message : err.message, 
          true, 
          3000);
      })
      .then(res => {
        Helpers.setLoading(false);
        if (res.status === 'OK') {
          if (res.result.success) {
            this.alertService.success(
              this.alertContainer, 
              res.result.message ? res.result.message : "Successfully modified!", 
              true, 
              3000);
            setTimeout(
              () => {
                this.router.navigate([`/admin/reservations/${this.recurringOrderData.order_id}/details`])
              }, 2000
            );
          }
        } else {
          this.alertService.error(this.alertContainer, res.result ? res.result.message : "Something went wrong!!", true, 3000);
        }
      });
  }

}