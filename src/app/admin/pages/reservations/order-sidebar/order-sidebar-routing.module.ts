import {NgModule} from "@angular/core";
import {Routes, RouterModule} from "@angular/router";
import {OrderSidebarComponent} from "./order-sidebar.component";
import {OrderResolveService} from "../order.service/order-resolve.service";
import {RefundCheckService} from "../order.service/refund-check.service";

const routes: Routes = [
    {
        path: "",
        component: OrderSidebarComponent,
        children: [
            {
                path: "product",
                loadChildren:
                    () => import('./view-product/view-product.module').then(m => m.ViewProductModule),
                resolve: {item: OrderResolveService},
                data : { title:'Update order items'}
            },
            {
                path: "recurring-payment",
                loadChildren:
                    () => import('./recurring-payment/recurring-payment.module').then(m => m.RecurringPaymentModule),
                resolve: { recurring: OrderResolveService }
            },
            {
                path: "customer",
                loadChildren:
                    () => import('./add-customer/add-customer.module').then(m => m.AddCustomerModule),
                resolve: {customer: OrderResolveService},
                data : { title:'Update customer details'}
            },
            {
                path: "payment",
                loadChildren:
                    () => import('./add-payment/add-payment.module').then(m => m.AddPaymentModule),
                resolve: {payment: OrderResolveService},
                data : { title:'Manage payments'}
            },
            {
                path: "delivery",
                loadChildren:
                    () => import('./delivery-details/delivery-details.module').then(m => m.DeliveryDetailsModule),
                resolve: {delivery: OrderResolveService},
                data : { title:'Delivery/Shipping details'}
            },
            {
                path: "refund",
                loadChildren:
                    () => import('./add-delivery/add-delivery.module').then(m => m.AddDeliveryModule),
                resolve: {refund: OrderResolveService},
                canActivate: [RefundCheckService]
            },
            {
                path: "**",
                redirectTo: "customer"
            }
        ]
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class OrderSidebarRoutingModule {
}
