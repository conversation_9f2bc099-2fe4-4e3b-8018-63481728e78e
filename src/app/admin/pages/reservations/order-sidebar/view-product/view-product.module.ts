import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ViewProductComponent } from './view-product.component';
import { RouterModule, Routes } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { DialogBoxModule } from '../../../../../modules/dialog-box/dialog-box.module';
import { DialogBoxComponent } from '../../../../../modules/dialog-box/dialog-box.component';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { CurrencyFormatModule } from '../../../../../modules/currency-format/currency-format.pipe';
import { RentalDatetimepickerModule } from "../../../../../modules/rental-datetime-picker/rental-datetime-picker.module";
import { DateTimeRangeModule } from './../../../../../modules/date-time-range/date-time-range.module';
import { DateFormatModule } from './../../../../../modules/date-format/date-format-pipe';


const routes: Routes = [
  {
      path: '',
      component: ViewProductComponent
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    NgbModule,
    DialogBoxModule,
    RouterModule.forChild(routes),
    CurrencyFormatModule,
    RentalDatetimepickerModule,
    DateTimeRangeModule,
    DateFormatModule
  ],
  entryComponents: [DialogBoxComponent],
  exports: [RouterModule],
  declarations: [ViewProductComponent]
})
export class ViewProductModule { }
