
.form-panel{
    padding: 0px 0px 10px 3px;
}

.m-form__group{
    padding: 0px 0px 10px 0px!important;
}

.select2-ajax{
    margin-top: 3px;
}

.select-pro {
    text-align: right;
}

.admin-cart{
    width: 60%;
}

.table tr td, .table tr th{
    vertical-align: middle;
    border-bottom: 1px solid #ebedf2!important;
    min-width: 60px;
}
.table tr th{
    font-weight: 600;
}
.table tr td a{
    text-decoration: none;
}

.quantity {
    margin-bottom: 10px;
}

.quantity .btn{
    border-radius: 0px;
}

.quantity .cart-qunt {
    width: 50px;
    border: 1px solid;
    cursor: initial;
}


.form-group{
    margin-bottom: 0px;
}

.quant .price{
    padding-top: 5%;
}
.pad-l-zero {
    padding-left: 0px;
}

.custom-alert{
    position: fixed;
    top: 30px;
    right: 30px;
    z-index: 5000;
}

.img-resize{
    max-width: 100px!important;
    height: 100px!important;
    object-fit: contain;
}
.img-orgin{
    max-width: 100%!important;
    height: auto!important;
}
.admin-quantity span {
    height: 30px !important;
    margin-top: -2px;
}

.edit-icon{
    cursor: pointer !important;
    margin-left: 10px;
}


@media only screen and (max-width: 575px){
    .select2-ajax{
        margin-top: 10px;
    }

    .quant .price{
        padding-top: 0px;
    }

    .select-pro {
        text-align: left;
    }
    .pro-image-show {
        padding-bottom: 15px;
        text-align: center;
    }
    .pro-image-show img{
        width: 200px;
    }

}
app-date-time-range {
    display: inline-block;
}
.cancel-btn {
    display: inline-block;
    margin-left: 15px;
    height: 33px;
    border-radius: 3px;
    font-size: 12px;
    padding: 0 15px;
    background-color: #333;
    color: #fff;
    border: none;
}