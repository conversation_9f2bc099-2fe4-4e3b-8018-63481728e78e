import {
  Component,
  OnInit,
  ViewChild,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>
} from "@angular/core";
import { Router, ActivatedRoute } from "@angular/router";
import {
  EndPoint,
  product_image
} from "../../../../../globals/endPoint/config";
import { OrderService } from "../../order.service/order.service";
import { AlertService } from "../../../../../modules/alert/alert.service";
import { Subscription, Observable } from "rxjs";
import { Item } from "../../models/order-models";
import {
  GET_USER,
  convertTime12to24,
  formatProductSearchFilter,
  GETTIME,
  formateRentType,
  changeNullToEmpty,
  preventInputAlphabets,
  calandarDateFormat,
  setCartRentalDate,
  removeCartDate,
  getCartDate,
  isCartDateExist,
  isCartRentalDateExist
} from "../../../../../globals/_classes/functions";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { DialogBoxComponent } from "../../../../../modules/dialog-box/dialog-box.component";
import { Helpers } from "../../../../../helpers";
import {
  map,
  retry,
  catchError,
  debounceTime,
  distinctUntilChanged,
  switchMap,
  tap
} from "rxjs/operators";
import { Location } from "@angular/common";
import { HttpService } from "../../../../../modules/http-with-injector/http.service";
import { ProductDiscountComponent } from "../../../cart-view/product-discount/product-discount.component";
import { ContentResoveService } from "src/app/home/<USER>/contetn-resolve.service";

declare let $: any;

interface Price {
  base: {};
  rent: any[];
}

class RentStart {
  date: string;
  time: string;
}

class Variant {
  variants: Attr[] = [];
  variants_products_id: number;
}

class Attr {
  attr_set_id: number;
  attr_set: string;
  attr: string;
}

class Charge {
  note: string;
  amount: number;
  order_id: number;
}

@Component({
  selector: "app-view-product",
  templateUrl: "./view-product.component.html",
  styleUrls: ["./view-product.component.css"]
})
export class ViewProductComponent implements OnInit, OnDestroy {
  contents;
  charge: Charge;
  chargeList = [];
  is_ShowChargeForm = false;
  is_edit_charge=false;
  charge_id=0;

  url: string = EndPoint + "products/search";
  imageUrl = product_image;
  add_item: boolean;
  loader_sub: boolean;
  loader: boolean;
  path: string;
  sub: Subscription[] = [];
  order_id: number;
  product = null;
  productPrice: Price;
  mode: number = 1;
  base: boolean;
  item: Item;
  rentalStart: RentStart = new RentStart();
  rentPriceId = 0;
  productList;
  available: number;
  variants: Variant = new Variant();
  locationList = [];
  edit: boolean;
  onRent: boolean;
  onBuy: boolean;
  rent_startDate;
  rent_endDate;
  min_date:any;

  isDisableDatePicker=false;
  isRentalDateEditMode:boolean=false;
  isShowCartRentalDate=true;

  editablePrice = false;
  discountAfterOrder = false;
  selectedAdditionalVal: boolean;
  additioanalTrue: boolean;
  additionalFalse: boolean;
  additional = [
    { name: "Add price to the order", value: true },
    { name: "Deduct price to the order", value: false }
  ];

  @ViewChild("hasCusAlert") alertContainer: ElementRef;
  el;

  constructor(
    private http: HttpService,
    private router: Router,
    private activeRoute: ActivatedRoute,
    private alertS: AlertService,
    private modalService: NgbModal,
    private orderS: OrderService,
    private location: Location,
    el: ElementRef,
    private contentService: ContentResoveService
  ) {
    this.el = el.nativeElement;
    this.additioanalTrue = true;
    this.additionalFalse = false;
    this.productList = activeRoute.snapshot.data["item"].data;
    this.getTerminal();

    this.charge = new Charge();
  }

  ngOnInit() {
    this.sub[0] = this.activeRoute.queryParamMap.subscribe(val => {
      const param = val.get("param");
      if (param) {
        const p = this.location.path().split("?");
        this.location.replaceState(p[0]);
        const pro = this.productList.order_items.find(
          f => f.variants_products_id === parseInt(param)
        );
        if (pro) {
          this.editCart(pro);
        }
      }
    });

    $(".native-routing-container").scrollTop(0, 0);
    this.path = this.activeRoute.snapshot["_routerState"].url;
    this.order_id = parseInt(this.orderS.getOrderId(this.activeRoute));


    this.min_date=new Date();
    if(this.productList.purchase_type !== 'buy'){
      this.onRent = true;
    }
    if(this.productList.rent_start !=undefined && this.productList.rent_start !='')
    {
      this.isShowCartRentalDate=true;
      this.rent_startDate=this.productList.rent_start;
      this.rent_endDate=this.productList.rent_end;
      
      setCartRentalDate('adminAfterOrder',this.rent_startDate, this.rent_endDate); 
    }
    else{
      this.isShowCartRentalDate=false;
      removeCartDate('adminAfterOrder')
    }


    this.getChargeList();

    this.contents = this.contentService.contents;
  }

  // Selection(name) {
  //   this.additional.forEach(x => {
  //       if (x.name !== name) {
  //           // x.value = !x.value
  //       }
  //   })
  // }
  // check(input)
  // {
  //   var checkboxes = this.el.getElementsByClassName('additional');

  //   for (var i = 0; i < checkboxes.length; i++)
  //   {
  //     //uncheck all
  //     if (checkboxes[i].checked == true)
  //     {
  //       checkboxes[i].checked = false;
  //     }
  //   }
  //   if (input.checked == true)
  //   {
  //     input.checked = false;
  //   }
  //   else
  //   {
  //     input.checked = true;
  //   }
  // }


  formatDate(date)
  {
   return calandarDateFormat(date)
  }

  get isExactStartDate(): boolean {
    const contents = localStorage.getItem("contents")
    ? JSON.parse(localStorage.getItem("contents"))
    : null;
    if (
      contents.site_specific.confg &&
      contents.site_specific.confg.datetime &&
      contents.site_specific.confg.datetime.hasOwnProperty(
        "exact_start_date"
      ) && contents.site_specific.confg.datetime.exact_start_date
    ) {
      return true;
    }
    return false;
  }

  checkAvailable(e) {
    Helpers.setLoading(true);

    setTimeout(t=>{
      this.http
      .post("products/availability",
      {
        type:'order',
        order_id:this.order_id,
        start_date:this.rent_startDate,
        end_date:this.rent_endDate,
        source:'admin'
      })
      .toPromise()
      .then(res => {
        Helpers.setLoading(false)
        if (res.status === "NOK") {
        this.isRentalDateEditMode=false; 
        }
        else{
          this.isRentalDateEditMode=false;
          this.productList=res.result.data;
  
          this.rent_startDate=this.productList.rent_start;
          this.rent_endDate=this.productList.rent_end;
  
        setCartRentalDate('adminAfterOrder',this.rent_startDate, this.rent_endDate); 
        }
  
      }).catch(err=>{
        Helpers.setLoading(false)
        this.isRentalDateEditMode=true; 
      });
  
    },1000)
    
  }


  onClickEdit(){
    this.isRentalDateEditMode=true;
  }

  change1() {
    if (this.additioanalTrue) {
      this.additionalFalse = false;
      this.additioanalTrue = false;
    } else {
      this.additionalFalse = false;
      this.additioanalTrue = true;
    }
  }
  change2() {
    if (this.additionalFalse) {
      this.additionalFalse = false;
      this.additioanalTrue = false;
    } else {
      this.additionalFalse = true;
      this.additioanalTrue = false;
    }
  }

  ngOnDestroy() {
    if (this.path.includes("details")) {
      this.orderS.update({
        data: this.productList,
        from: "ITEMS",
        update: true
      });
    } else {
      this.orderS.updateOrder({ data: [], add: false });
    }
    for (let s of this.sub) {
      s.unsubscribe();
    }
  }

  addDiscount(price, basePrice, cart) {
    console.log(this.order_id);
    const modalRef = this.modalService.open(ProductDiscountComponent, {
      centered: true,
      windowClass: "discount"
    });
    modalRef.componentInstance.basePrice = basePrice;
    modalRef.componentInstance.price = price;
    modalRef.componentInstance.cart = cart;
    modalRef.componentInstance.token = this.productList.token;
    modalRef.componentInstance.order = true;
    modalRef.componentInstance.orderId = this.order_id;
    modalRef.result.then(
      result => {
        if (result) {
          this.productList = result;
        }
      },
      res => {
        console.log(res);
      }
    );
  }

  getTerminal() {
    this.orderS.getterminals().subscribe(
      res => {
        if (res.status == "OK" && res.result.data) {
          this.locationList = res.result.data;
        } else {
          this.locationList = [];
        }
      },
      err => {
        console.log(err);
        this.locationList = [];
      }
    );
  }

  /* ************** Autocomplete *********** */

  search = (text$: Observable<string>) => {
    return text$.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      switchMap(term => {
        this.loader_sub = true;
        return this.getProductList(term);
      }),
      tap(() => (this.loader_sub = false))
    );
  };

  private getProductList(params): any {
    if (!params && params === "") {
      this.loader_sub = false;
      return [];
    }
    // console.log(params);
    const search = "search=" + params.trim();
    return this.orderS.searchProduct(search).pipe(
      map(res => {
        this.loader_sub = false;
        return formatProductSearchFilter(res.result.data);
      }),
      retry(3),
      catchError(() => {
        this.loader_sub = false;
        return [];
      })
    );
  }

  formatter = (x: { name: string }) => x.name;

  clickedOnRent(data) {
    this.onRent = true;
    this.onBuy = false;
    this.variants = new Variant();
    this.reset();

    this.isDisableDatePicker=isCartRentalDateExist('adminAfterOrder');

    if(this.isDisableDatePicker){
      const cartDateObj=getCartDate('adminAfterOrder');
      this.rent_startDate=cartDateObj.startDate;
      this.rent_endDate=cartDateObj.endDate;
    }
    

    this.formatvariant(data);
    this.getProduct(data.variants_products_id);
  }

  clickedOnBuy(data) {
    this.onBuy = true;
    this.onRent = false;
    this.variants = new Variant();
    this.reset();
    this.formatvariant(data);
    this.getProduct(data.variants_products_id);
  }

  changeValue(data) {
    // console.log(data)
    // let r = data.item;
    // this.variants = new Variant();
    // this.reset();
    // this.formatvariant(r);
    // this.getProduct(r.variants_products_id);
  }

  // editPrice(price) {
  //   this.editablePrice = true;
  // }


  formatvariant(data) {
    this.variants.variants_products_id = data.variants_products_id;
    this.variants.variants = [];
    for (let i = 0; i < data.variant_chain_name.length; i++) {
      let obj: Attr = new Attr();
      obj.attr_set_id = data.variant_set_id[i];
      obj.attr_set = data.variant_set_name[data.variant_set_id[i]];
      obj.attr = data.variant_chain_name[i];
      this.variants.variants.push(obj);
    }
  }

  initProduct() {
    this.item = new Item();
    this.item.quantity = 1;
    this.item.order_id = this.order_id;
    /* New data */
    this.item.variants_products_id = this.variants.variants_products_id;
    this.item.sales_tax = this.product.sales_tax;
    this.item.product_id = this.product.id;
  }

  getProduct(a) {
    this.loader_sub = true;
    this.orderS.getProduct(a,this.order_id).subscribe(
      res => {
        this.loader_sub = false;
        // console.log(res);
        this.product = res.data;
        if (!this.edit) {
          this.initProduct();
        }
        this.productPrice = this.orderS.adminFormatePrice(this.product.prices);
        console.log(this.productPrice);
        this.base =
          Object.keys(this.productPrice.base).length > 0 ? true : false;
        this.checkBuy(this.base);
        if (this.onRent) {
          this.mode = 2;
          if (this.productPrice.rent.length > 0) {
            const ind = this.productPrice.rent.findIndex(
              f =>
                f.price === this.item.price &&
                f.rent_type === this.item.rental_type
            );
            if (ind > -1) {
              this.formateRent(this.productPrice.rent[ind], ind);
            } else {
              this.formateRent(this.productPrice.rent[0], 0);
            }
          }
        }
      },
      err => {
        this.loader_sub = false;
        this.error(err, "Something wrong! Please try again");
      }
    );
  }

  checkBuy(buy) {
    if (buy) {
      this.mode = 1;
      this.formateBuy();
    }
    //  else {
    //   this.mode = 2;
    //   if (this.productPrice.rent.length > 0) {
    //     const ind = this.productPrice.rent.findIndex(
    //       f =>
    //         f.price === this.item.price && f.rent_type === this.item.rental_type
    //     );
    //     if (ind > -1) {
    //       this.formateRent(this.productPrice.rent[ind], ind);
    //     } else {
    //       this.formateRent(this.productPrice.rent[0], 0);
    //     }
    //   }
    // }
  }

  formateBuy() {
    this.item.price = this.productPrice.base["price"];
    this.item.rental_duration = this.productPrice.base["rent_duration"];
    this.item.rental_type = this.productPrice.base["rent_type"];
    this.item.term = this.productPrice.base["duration"];
    // console.log(this.item);
  }

  formateRent(data, i) {
    if(this.isDisableDatePicker){
      const cartDateObj=getCartDate('adminAfterOrder');
      this.rent_startDate = cartDateObj.startDate;
      this.rent_endDate = cartDateObj.endDate;

      this.item.price = this.product.rental_price;
    }
    else{
      this.rent_startDate = data.rent_start;
      this.rent_endDate = data.rent_end;

      this.item.price = data["price"];
    }
    
      const date = this.item.rent_start
        ? new Date(this.item.rent_start)
        : new Date();
      setTimeout(() => this._datePicker(date), 100);
      this.rentPriceId = i;
     // this.item.price = data["price"];
      this.item.price_id = data["id"];
      this.item.rental_duration = this.item.rental_duration
        ? this.item.rental_duration
        : data["rent_duration"];
      this.item.rental_type = data["rent_type"];
      this.item.term = data["duration"];
      // console.log(this.item);
      const rentTime = this.orderS.getCurrentDateTime(date);
      this.rentalStart.date = rentTime["date"];
      this.rentalStart.time = rentTime["time"];
    
   
  }

  decreaseQuant() {
    if (this.item.quantity > 0) {
      this.item.quantity--;
    }
  }

  increaseQuant() {
    this.item.quantity++;
  }

  addItem() {
    this.loader = true;
    this.item.location = this.item.location
      ? this.item.location
      : GET_USER().location_id;
    if (!this.item.rental_type) {
      this.formatSendData();
    } else {
      this.formatSendData(
        this.rent_startDate,
        this.product.deposit_amount,
        this.product.deposite_tax,
        this.rent_endDate
      );
    }
    //console.log(this.item);
    this.item['item_id']=this.item.id;
    this.item.rental_duration = 1;
    if (this.additioanalTrue) {
      this.item["additional"] = "add";
    } else if (this.additionalFalse) {
      this.item["additional"] = "deduct";
    }


    if(isCartRentalDateExist('adminAfterOrder'))
    {
      this.item['source']='admin';
    }
    if (this.isExactStartDate) {
      this.item['rent_start'] = this.product.rent_start;
      this.item['rent_end'] = this.product.rent_end;
    }

    const item = Object.assign({}, this.item);
    const paramsToRemove = ["rental_duration", "term", "price_id", "deposit_amount", "deposite_tax"];
    for(let key of Object.entries(item)) {
      paramsToRemove.map(p => {
        if (p === key[0]) {
          delete item[p]; // needed to remove some extra params
        }
      })
    };

    this.orderS
      .addItem(item, this.edit)
      .then(res => {
        this.loader = false;
        if (res.result.data) {
          this.alertS.success(
            this.alertContainer,
            "Product has been added",
            true,
            5000
          );
          this.productList = res.result.data;

          if(this.productList.rent_start !=undefined && this.productList.rent_start !='')
          {
            this.isShowCartRentalDate=true;
            this.rent_startDate=this.productList.rent_start;
            this.rent_endDate=this.productList.rent_end;

           setCartRentalDate('adminAfterOrder',this.rent_startDate, this.rent_endDate);  
          }
          else{
            this.isShowCartRentalDate=false;
            removeCartDate('adminAfterOrder')
          }
          
          this.reset();
        } else {
          this.alertS.error(this.alertContainer, res.result.message, true, 5000);
        }
      })
      .catch(err =>
        this.error(err, "Something wrong! Product has not been added")
      );
  }
  private formatSendData(date?, dAmount?, dTax?, endDate?) {
    this.item.rent_start = date ? date : null;
    this.item.rent_end = endDate ? endDate : null;
    this.item.deposit_amount = dAmount ? dAmount : 0;
    this.item.deposite_tax = dTax ? dTax : false;
  }

  reset() {
    this.product = null;
    this.edit = null;
    this.item = new Item();
  }

  decreaseRent() {
    if (this.item.rental_duration > 1) {
      this.item.rental_duration--;
    }
  }

  increaseRent() {
    if (this.item.rental_duration < 5) {
      this.item.rental_duration++;
    }
  }

  private _datePicker(d: Date) {
    $("#Rental-time").timepicker({
      defaultTime: GETTIME(d),
      minuteStep: 1,
      showSeconds: false,
      showMeridian: true,
      snapToStep: true
    });
    $("#Renterl-date").datepicker({
      todayHighlight: true,
      orientation: "bottom right",
      format: "yyyy-mm-dd",
      templates: {
        leftArrow: '<i class="la la-angle-left"></i>',
        rightArrow: '<i class="la la-angle-right"></i>'
      },
      startDate: d
    });
    $("#Renterl-date").datepicker("setDate", d);
    this.rentalTimeChange();
  }

  rentalDateChange() {
    $("#Renterl-date")
      .datepicker()
      .on("changeDate", e => {
        let date = e.date;
        this.rentalStart["date"] = this.orderS.getCurrentDateTime(date)["date"];
      });
  }

  rentalTimeChange() {
    $("#Rental-time").on("change", () => {
      let time = $("#Rental-time")
        .data("timepicker")
        .getTime();
      this.rentalStart["time"] = convertTime12to24(time);
      // console.log(this.rentalStart['time'], this.item);
    });
  }

  error(err, message) {
    this.loader = false;
    this.loader_sub = false;
    Helpers.setLoading(false);
    this.alertS.error(this.alertContainer, message, true, 5000);
  }

  getDate(d) {
    return this.orderS.formateListDate(d);
  }

  getType(d, t) {
    return d && t ? formateRentType(d, t) : "";
  }

  trackList(index, pro) {
    return pro ? pro.id : null;
  }

  editCart(cart) {
    this.reset();
    this.item = new Item();
    this.edit = true;
    this.item = JSON.parse(JSON.stringify(cart));
    this.onBuy = cart.rental_type === 'buy';
    this.onRent = !this.onBuy;

    this.getProduct(cart.variants_products_id);

    this.isDisableDatePicker=isCartRentalDateExist('adminAfterOrder');

    if(this.isDisableDatePicker){
      const cartDateObj=getCartDate('adminAfterOrder');
      this.rent_startDate=cartDateObj.startDate;
      this.rent_endDate=cartDateObj.endDate;
    }
  }

  removeCart(id, i) {
    const modalRef = this.modalService.open(DialogBoxComponent, {
      centered: true,
      size: "sm"
    });
    modalRef.componentInstance.massage = "Are you sure you want to delete?";
    modalRef.result.then(
      result => {
        if (result) {
          Helpers.setLoading(true);
          this.archiveItem(id, i);
        }
      },
      res => {
        console.log(res);
      }
    );
  }

  archiveItem(id, i) {
    this.orderS
      .deleteItem(id)
      .then(res => {
        Helpers.setLoading(false);
        this.alertS.success(
          this.alertContainer,
          "Product has been deleted",
          true,
          5000
        );
        this.productList = res.result.data;
        
        if (
          this.productList.cart_items &&
          this.productList.cart_items.length < 1
        ) {
          this.isShowCartRentalDate=false;
          removeCartDate('adminAfterOrder')
        }

      })
      .catch(err =>
        this.error(err, "Something wrong! Product has not been deleted")
      );
  }

  getTotalDepositedAmount(order) {
    return this.orderS.getTotalDeposit(order);
  }

  get checkStatus() {
    const sta = [1, 2, 3, 7];
    return sta.includes(this.productList.status);
  }

  checkPrice() {
    setTimeout(() => {
      this.item.location = GET_USER().location_id;
      this.formatSendData(
        this.rent_startDate,
        this.product.deposit_amount,
        this.product.deposite_tax,
        this.rent_endDate
      );
      this.item.rental_type = this.item.rental_type === "buy" ? "buy" : "rent";
      const cart = changeNullToEmpty(this.item);
      this.http
        .post("get-price-value", cart)
        .toPromise()
        .then(res => {
          if (res.status === "OK") {
            if (res.result.error) {
              return;
            }
            this.item.price = res.result.data;
          }
        });
    }, 1000);
  }

  private rentalDateChangeNew(e) {
    this.rent_startDate = e;
  }
  private rentalEndDateChange(e) {
    this.rent_endDate = e;
  }

  // new date time range picker
  get showStartTime() {
    const contents = localStorage.getItem("contents")
      ? JSON.parse(localStorage.getItem("contents"))
      : null;
    if (
      contents.site_specific.confg &&
      contents.site_specific.confg.hasOwnProperty("show_start_time")
    ) {
      return contents.site_specific.confg.show_start_time;
    }
    return true;
  }

  selectstartDateTime(e) {
    this.rent_startDate = e;
  }

  selectendDateTime(e) {
    this.rent_endDate = e;
  }

  //***************charge section***************** */
  submitCharge(form) {
    this.loader = true;
    console.log(form);
    this.charge.order_id = this.order_id;
    this.orderS
      .addOrUpdateCharge(this.charge)
      .then(res => {
        console.log(res);

        this.alertS.success(
          this.alertContainer,
          "Charge has been added",
          true,
          5000
        );
        this.getChargeList();
        this.loader = false;
      })
      .catch(err => {
        console.log(err);
        this.error(err, "Something wrong! Charge has been not added");
        this.loader = false;
      });
  }

  addCharge() {
    this.is_ShowChargeForm = true;
  }

  editCharge(id) {
    this.is_ShowChargeForm = true;
    this.is_edit_charge=true;
    this.orderS
      .getCharge(this.order_id, id)
      .then(res => {
        this.loader = false;
        let data = res.result.data;

        this.charge.note = data.note;
        this.charge.amount = data.amount;
        this.charge_id=parseInt(id);
        console.log(res);
      })
      .catch(err => {
        console.log(err);
        this.loader = false;
      });
  }

  updateCharge() {
    this.loader = true;

    this.charge.order_id = this.order_id;
    this.charge["id"] = this.charge_id;
    this.orderS
      .addOrUpdateCharge(this.charge)
      .then(res => {
        console.log(res);

        this.alertS.success(
          this.alertContainer,
          "Charge has been updated",
          true,
          5000
        );
        this.loader = false;
        this.is_ShowChargeForm=false;
      })
      .catch(err => {
        console.log(err);
        this.error(err, "Something wrong! Charge has been not updated");
        this.loader = false;
      });
  }

  resetCharge() {
    this.charge = new Charge();
    this.is_ShowChargeForm = false;
    // console.log(this.customer);
  }

  getChargeList() {
    this.loader = true;
    this.orderS
      .getChargeList(this.order_id)
      .then(res => {
        this.loader = false;
        this.chargeList = res.result.data;
        console.log(this.chargeList);
      })
      .catch(err => {
        console.log(err);
        this.loader = false;
      });
  }

  removeCharge(id, i) {
    const modalRef = this.modalService.open(DialogBoxComponent, {
      centered: true,
      size: "sm"
    });
    modalRef.componentInstance.massage = "Are you sure you want to delete?";
    modalRef.result.then(
      result => {
        if (result) {
          Helpers.setLoading(true);
          this.archiveChargeItem(id, i);
        }
      },
      res => {
        console.log(res);
      }
    );
  }

  archiveChargeItem(id, i) {
    this.orderS
      .deleteCharge(id)
      .then(res => {
        Helpers.setLoading(false);
        this.alertS.success(
          this.alertContainer,
          "Charge has been deleted",
          true,
          5000
        );
        this.getChargeList();
      })
      .catch(err =>
        this.error(err, "Something wrong! Charge has not been deleted")
      );
  }


  validateQuantity(e){
    preventInputAlphabets(e);
  }

  onQuantityMouseLeave(value){
   if(value===""){
     this.item.quantity=1;
   }
  }
}
