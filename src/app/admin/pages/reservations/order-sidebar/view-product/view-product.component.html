<div class="custom-alert" #hasCusAlert></div>

<div class="animated fadeIn">
  <div class="m-portlet__body form-panel" style="padding-bottom: 15px;">
    <div class="row">
      <h4 class="colorPurpel col-md-3">
        <span *ngIf="edit">Update Item</span><span *ngIf="!edit">Add Item</span>
      </h4>
      <div class="form-group admin-cart col-md-9" style="display: inline-block;">
        <ng-template #rt let-r="result" let-t="term">
          <div>{{ r.name }}</div>
          <div class="colorPurpel" *ngIf="r.chain">
            <small style="font-style: italic">{{ r.chain }}</small>
          </div>
          <div>
            <button *ngIf="r.buy" (click)="clickedOnBuy(r)" class="btn btn-sm btn-xsm btn-outline-dark"
              style="margin-right: 10px;">
              Buy
            </button>
            <button *ngIf="r.rent" (click)="clickedOnRent(r)" class="btn btn-sm btn-xsm btn-outline-danger">
              Rent
            </button>
          </div>
        </ng-template>
        <input id="typeahead-http" #autoCom name="search" type="text" class="form-control" [inputFormatter]="formatter"
          [ngbTypeahead]="search" placeholder="Search Item" [resultTemplate]="rt" (selectItem)="changeValue($event)"
          style="display: inline; width: 90%;" />
        <div *ngIf="autoCom.value" (click)="autoCom.value = ''; loader_sub = false"
          style="margin: auto; padding: 5px;cursor: pointer;display: inline;">
          <i class="fa fa-close"></i>
        </div>
        <div *ngIf="loader_sub" class="m-loader m-loader--brand" style="width: 30px; display: inline-block;top:-6px;">
        </div>
      </div>
    </div>

    <!-- View Product to add -->

    <div class="row" style="padding-top: 15px;" *ngIf="product">
      <div class="col-sm-3 pro-image-show">
        <img *ngIf="product.images.length == 0; else alter" class="img-fluid img-avatar img-thumbnail img-orgin"
          src="./assets/img/home/<USER>" alt="Product Image" />
        <ng-template #alter>
          <img class="img-fluid img-avatar img-thumbnail img-orgin" src="{{
              imageUrl +
                product.store_id +
                '/' +
                product.id +
                '/' +
                product.images[0]?.image_large
            }}" alt="Product Image" onError="this.src='./assets/img/home/<USER>';" />
        </ng-template>
      </div>
      <div class="col-sm-9">
        <h4>{{ product.name }}</h4>
        <!-- <div class="m-form__group form-group" *ngIf="product.prices && product.prices.length > 0">
          <div class="m-radio-inline">
            <label class="m-radio" (click)="formateBuy()" *ngIf="onBuy && base && !edit">
              <input type="radio" name="main" [value]="1" [(ngModel)]="mode" />
              Buy
              <span></span>
            </label>
            <label *ngIf="productPrice.rent.length > 0 && onRent" class="m-radio"
              (click)="formateRent(productPrice.rent[0], 0)">
              <input type="radio" name="main" [value]="2" [(ngModel)]="mode" />
              Rent
              <span></span>
            </label>
          </div>
        </div> -->

        <div class="row" style="padding-bottom: 20px;">
          <div class="col-sm-6" *ngFor="let a of variants.variants">
            <label *ngIf="a.attr_set_id != 1">
              <span class="colorPurpel">{{ a.attr_set }}:</span>
              {{ " " + a.attr }}</label>
          </div>
        </div>

        <div class="rent" *ngIf="mode == 2">
          <div class="m-form__group form-group row" style="margin:0px;" *ngIf="!isExactStartDate">
            <div class="m-radio-list col-sm-6" *ngIf="onRent">

              <label class="w-100" [ngClass]="[!isDisableDatePicker ? 'm-radio' : '']" 
              *ngFor="let rent of productPrice.rent; let i = index"
              (click)="formateRent(rent, i)">

                <input  *ngIf="!isDisableDatePicker;else arrow" type="radio" [value]="i" name="rent" [(ngModel)]="rentPriceId" />

                <ng-template #arrow>
                  <label><i class="fa fa-arrow-right"></i>&nbsp;</label>
                 </ng-template>

                {{ rent.price | currency }} {{ rent.duration ? "/" : "" }}
                {{ rent?.duration }} {{ rent.rent_type }}
                <span></span>
              </label>

            </div>
          </div>
         
          <div *ngIf="!isExactStartDate; else dueDate">
            <div style="position: relative;" *ngIf="productPrice.rent.length > 0 && onRent">
              <label>Rental date range:</label>
              <br />
              <app-date-time-range (startDateTime)="selectstartDateTime($event)" (endDateTime)="selectendDateTime($event)"
                (reload)="checkPrice($event)" [placeholderEnd]="rent_endDate" [placeholderStart]="rent_startDate"
                [displayTime]="showStartTime" [diabledPicker]="isDisableDatePicker"></app-date-time-range>
            </div>
          </div>
          <ng-template #dueDate>
            <h4>Due Date {{product.rent_end | date: "MM/dd/yyyy"}}</h4>
          </ng-template>

        </div>

        <div class="quant row">
          <div class="col-sm-6">
            <label class="colorPurpel">Quantity</label>
            <div class="quantity admin-quantity clearfix">
              <span class="btn btn-sm btn-dark no-m" (click)="decreaseQuant()">-</span>
              <!-- <span class="cart-qunt btn btn-sm no-m">{{ item.quantity }}</span> -->
              <input type="text" autocomplete="off" class="input-qty" name="qty" [(ngModel)]="item.quantity"
                (keydown)="validateQuantity($event)" (mouseleave)="onQuantityMouseLeave($event.target.value)" />
              <span class="btn btn-sm btn-dark  no-m" (click)="increaseQuant()">+</span>
            </div>
            <!-- <small class="colorPurpel">{{available}} item(s) are available</small> -->
          </div>
          <div class="col-sm-6 price">
            <h4>
              Price:
              {{
                item.quantity *
                  item.price *
                  (item.rental_duration ? item.rental_duration : 1) | currency
              }}
            </h4>
          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <!-- <div *ngFor="let item of additional; let i = index">
              <input type="checkbox" [(ngModel)]="item.value" (click)="Selection(item.name)">
              <label for="">{{item.name}}</label>
            </div> -->
            <input type="checkbox" class="additional" (click)="change1(this)" [(ngModel)]="additioanalTrue" />
            Apply changes to order total
            <span  style="display: none;"><input type="checkbox" class="additional" (click)="change2(this)" [(ngModel)]="additionalFalse"/>
            Deduct price to the order</span>
          </div>
        </div>

        <!-- <div class="row btn-group-toggle w-100 Filtertogglebtns d-block d-md-none" data-toggle="buttons">
          <div class="col-md-12">
            <label class="btn btn-light mr-2">add
              <input [(ngModel)]="additioanalTrue" (click) = "change1()" type="checkbox" name="filteroptions" id="option1" autocomplete="off"> <i class="fa fa-long-arrow-up"></i>
            </label>
            <label class="mt-md-0 btn btn-light"> remove
              <input [(ngModel)]="additionalFalse" (click) = "change2()"  type="checkbox" name="filteroptions" id="option2" autocomplete="off"> <i class="fa fa-long-arrow-down"></i>
            </label>
          </div>
        </div> -->

        <div style="padding-top: 20px;">
          <div *ngIf="loader; else addButton" class="m-loader m-loader--brand"
            style="width: 30px; display: inline-block;"></div>
          <ng-template #addButton>
            <button class="btn btn-sm btn-brand" [disabled]="!item.price || item.quantity == 0" (click)="addItem()">
              <span *ngIf="edit">Update Item</span><span *ngIf="!edit">Add Item</span>
            </button>
            <button class="btn btn-sm btn-danger" (click)="reset()" style="margin-left: 10px;">
              Cancel
            </button>
          </ng-template>
        </div>
      </div>
    </div>
  </div>

  <!-- View List -->

  <div class="row pb-3 pt-3">
   <div class="col-md-3">
    <h4 class="colorPurpel pt-1">
      View Items
    </h4>
   </div>
   <div *ngIf="isShowCartRentalDate && onRent" class="col-md-8">
    <div *ngIf="isRentalDateEditMode;else showText">
      <app-date-time-range 
        (startDateTime)="selectstartDateTime($event)" (endDateTime)="selectendDateTime($event)"
        (reload)="checkAvailable($event)" [placeholderEnd]="rent_endDate" [placeholderStart]="rent_startDate"
        [displayTime]="showStartTime">
      </app-date-time-range>
      <button class="button theme-btn cancel-btn" (click)="isRentalDateEditMode = false">Cancel</button>
    </div>

    <ng-template #showText>
      <label class="pt-2">
        Rental Dates {{rent_startDate | customDate: 'rental'}} - {{rent_endDate | customDate: 'rental'}}
        <i class="fa fa-edit edit-icon" (click)="onClickEdit()"></i>
      </label>
    </ng-template>
   </div>
  </div>
  <div class="m-portlet__body form-panel">
    <div class="table-responsive">
      <table class="table table-hover">
        <thead>
          <tr>
            <th>Image</th>
            <th>Description</th>
            <th>Price</th>
            <th>Sales Tax</th>
            <th>Quantity</th>
            <th>Subtotal</th>
            <th>Action</th>
          </tr>
        </thead>

        <tbody *ngIf="productList && productList.order_items.length > 0; else NoItem">
          <ng-container *ngFor="
              let order of productList?.order_items;
              let i = index;
              trackBy: trackList
            ">
            <tr>
              <td>
                <img *ngIf="
                    order.product.images && order.product.images.length > 0;
                    else alterImage
                  " class="img-fluid img-avatar img-thumbnail img-resize" src="{{
                    imageUrl +
                      order.store_id +
                      '/' +
                      order.product.id +
                      '/' +
                      order.product.images[0].image_small
                  }}" alt="Product Image" onError="this.src='./assets/img/home/<USER>';" />
                <ng-template #alterImage>
                  <img class="img-fluid img-avatar img-thumbnail img-resize"
                    src="./assets/img/home/<USER>" alt="Product Image" />
                </ng-template>
              </td>
              <td>
                <h5>{{ order.product.name }}</h5>
                <div *ngIf="
                order.product.variant_chain &&
                order.product.variant_chain != 'Unassigned: Unassigned'
                  ">
                  <small>({{ order.product.variant_chain }})</small>
                </div>
                <!-- <div>
                  <div *ngIf="order.rent_start">
                    <small>From :
                      {{
                       order?.rent_start
                      }}</small>
                  </div>
                  <div *ngIf="order.rent_end">
                    <small>To :
                      {{
                      order?.rent_end  
                      }}</small>
                  </div>
                </div> -->
                <div *ngIf="order.deposit_amount > 0">
                  Deposited Amount: {{ order.deposit_amount | currency }}
                  {{ order.deposite_tax == "true" ? "(Including Tax)" : "" }}
                </div>
                <ng-container *ngIf="order.product_type === 2 && order.children.length">
                  <h6 class="pt-3 pb-1">Package Includes</h6>
                  <ng-container *ngFor="let packageProduct of order.children">
                      <div *ngIf="packageProduct?.product_type !==3" class="mb-2" >
                          <div style="font-size:13px;">
                            {{ packageProduct.product.name }}({{
                              packageProduct.quantity
                            }})
                          </div>
                          <small *ngIf="
                              packageProduct.product.variant_chain !==
                              'Unassigned: Unassigned'
                            ">
                            <i>{{ packageProduct.product.variant_chain }}</i></small>
                        </div>
                  </ng-container>
                 
                </ng-container>
              </td>
              <td>
                {{ order.price | currency
                }}
              </td>
              <td>
                {{ order.sales_tax }}% <br />
                {{ order.sales_tax_price | currency }}
              </td>
              <td>{{ order.quantity }}</td>
              <td>
                {{ order.sub_total | currency }}
              </td>
              <td>
                <!-- *ngIf="order.rental_type && order.rental_type != 'buy'" -->
                <button (click)="editCart(order)" title="Edit Order Item"
                  class="btn m-btn m-btn--hover-info m-btn--icon m-btn--icon-only m-btn--pill">
                  <i class="fa fa-edit"></i>
                </button>
                <button (click)="removeCart(order.id, i)"
                  class="btn m-btn m-btn--hover-danger m-btn--icon m-btn--icon-only m-btn--pill">
                  <i class="fa fa-trash"></i>
                </button>
                <button (click)="
                    addDiscount(order.sub_total, order.substantive_price, order)
                  " title="Change price" class="btn m-btn m-btn--hover-info m-btn--icon m-btn--icon-only m-btn--pill">
                  <i class="fa fa-dollar"></i>
                </button>
              </td>
            </tr>

          <ng-container *ngFor="let addon_item of order?.children; let i = 'index';"> 
              <tr  *ngIf="addon_item.product_type==3">
                  <td>
                    <img *ngIf="addon_item.product.images && addon_item.product.images.length>0; else alterImage"
                      class="img-fluid img-avatar img-thumbnail img-resize"
                      src="{{imageUrl +  addon_item.store_id + '/' + addon_item.product.id + '/' + addon_item.product.images[0].image_small}}"
                      onError="this.src='./assets/img/home/<USER>';" alt="Product Image"
                      onError="this.src='./assets/img/home/<USER>';" />
                    <ng-template #alterImage>
                      <img class="img-fluid img-avatar img-thumbnail img-resize"
                        src="./assets/img/home/<USER>" alt="Product Image" />
                    </ng-template>
                  </td>
                  <td>
                    <h5>{{addon_item.product.name}}</h5>
                      <small  *ngIf="addon_item.product.variant_chain !== 'Unassigned: Unassigned'"> <i>{{addon_item.product.variant_chain}}</i></small>
                  </td>
                  <td></td>
                  <td></td>
                  <td>{{addon_item.quantity}}</td>
                  <td></td>
                  <td></td>
                </tr>
    
          </ng-container>
           
          </ng-container>
        </tbody>
        <ng-template #NoItem>
          <tbody>
            <tr>
              <td colspan="7">
                <h5 class="text-center">{{contents?.site_specific?.others?.lbl_no_item_found ? contents?.site_specific?.others?.lbl_no_item_found : 'No Item Found'}}</h5>
              </td>
            </tr>
          </tbody>
        </ng-template>
      </table>
    </div>

    <div class="col-12" style="padding-top: 10px;">
      <div *ngIf="is_ShowChargeForm">
        <form class="m-form m-form--fit m-form--label-align-right" #form="ngForm">
          <div class="row">
            <div class="col-sm-6">
              <label for="note" class="colorPurpel">
                Note
              </label>
              <div class="form-group m-form__group">
                <input class="form-control m-input" id="note" type="text" autocomplete="off" placeholder="Note"
                  name="note" #note="ngModel" [(ngModel)]="charge.note" />
                <span *ngIf="note.invalid && note.touched">
                  <small *ngIf="note.errors.required" class="error">Note is required</small>
                </span>
              </div>
            </div>
            <div class="col-sm-6">
              <label for="amount" class="colorPurpel">
                Amount
              </label>
              <div class="form-group m-form__group">
                <input class="form-control m-input" id="amount" type="number" autocomplete="off" placeholder="Amount"
                  name="amount" #amount="ngModel" [(ngModel)]="charge.amount" />
                <span *ngIf="amount.invalid && amount.touched">
                  <small *ngIf="amount.errors.required" class="error">Amount is required</small>
                </span>
              </div>
            </div>
          </div>
          <div style="padding-top: 10px;">
            <div *ngIf="loader; else addCharge" class="m-loader m-loader--brand"
              style="width: 30px; display: inline-block;"></div>
            <ng-template #addCharge>
              <button *ngIf="is_edit_charge; else add" type="submit" [disabled]="!form.form.valid"
                (click)="updateCharge()" class="btn btn-brand btn-sm">
                Update
              </button>
              <ng-template #add>
                <button type="submit" [disabled]="!form.form.valid" (click)="submitCharge(form)"
                  class="btn btn-brand btn-sm">
                  Submit
                </button>
                <button type="button" class="btn btn-danger btn-sm" (click)="resetCharge()" style="margin-left: 5px;">
                  Cancel
                </button>
              </ng-template>
            </ng-template>
          </div>
        </form>
        <!--end::Form-->
      </div>
      <h4 class="pb-2">View Charges</h4>
      <div class="float-right" *ngIf="!is_ShowChargeForm">
        Add charge
        <button (click)="addCharge()" title="Add"
          class="btn m-btn m-btn--hover-info m-btn--icon m-btn--icon-only m-btn--pill">
          <i class="fa fa-plus"></i>
        </button>
      </div>

      <div class="table-responsive">
        <table class="table cart">
          <thead>
            <tr>
              <!--              <th>Date</th>-->
              <th>Note</th>
              <th>Amount</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody *ngIf="chargeList && chargeList.length > 0; else NoItem">
            <ng-container *ngFor="let charge of chargeList; let i = index">
              <tr>
                <!--                <td>-->
                <!--                  {{ getDate(charge.created) | date: "MM/dd/yyyy, hh:mm a" }}-->
                <!--                </td>-->
                <td>
                  {{ charge.note }}
                </td>
                <td>
                  {{ charge.amount | currency }}
                </td>
                <td>
                  <button (click)="editCharge(charge.id)" title="Edit"
                    class="btn m-btn m-btn--hover-info m-btn--icon m-btn--icon-only m-btn--pill">
                    <i class="fa fa-edit"></i>
                  </button>
                  <button (click)="removeCharge(charge.id, i)"
                    class="btn m-btn m-btn--hover-danger m-btn--icon m-btn--icon-only m-btn--pill">
                    <i class="fa fa-trash"></i>
                  </button>
                </td>
              </tr>
            </ng-container>
          </tbody>
          <ng-template #NoItem>
            <tbody>
              <tr>
                <td colspan="7">
                  <h5 class="text-center">No Charges Added</h5>
                </td>
              </tr>
            </tbody>
          </ng-template>
        </table>
      </div>
    </div>

    <div class="col-12" style="padding-top: 10px;">
      <h4 class="pb-2">Order Summary</h4>
      <div class="table-responsive">
        <table class="table cart">
          <tbody>
            <tr>
              <td>
                Total Quantity
              </td>
              <td>
                <span class="cart_p">{{ productList?.total_quantity }}</span>
              </td>
            </tr>
            <tr>
              <td>
                Sub Total
              </td>
              <td>
                <span class="cart_p">{{
                  (productList?.sub_total ? productList.sub_total : 0)
                    | currency
                }}</span>
              </td>
            </tr>
            <tr>
              <td>
                Shipping Charge
              </td>
              <td>
                <span class="cart_p">
                  {{
                    (productList?.delivery_charge
                      ? productList.delivery_charge
                      : 0) | currency
                  }}</span>
              </td>
            </tr>
            <tr>
              <td>
                Discount
              </td>
              <td>
                <span class="cart_p">
                  {{
                    (productList?.total_discount
                      ? productList.total_discount
                      : 0) | currency
                  }}</span>
              </td>
            </tr>
            <tr>
              <td>
                Tax
              </td>
              <td>
                <span class="cart_p">
                  {{
                    (productList?.tax ? productList.tax : 0) | currency
                  }}</span>
              </td>
            </tr>
            <tr>
              <td>
                Total Deposited Amount
              </td>
              <td>
                <span class="cart_p">
                  {{
                    getTotalDepositedAmount(productList.order_items) | currency
                  }}</span>
              </td>
            </tr>
            <tr>
              <td>
                Total Additional Charge
              </td>
              <td>
                <span class="cart_p">
                  {{ productList.total_additional | currency }}</span>
              </td>
            </tr>
            <tr>
              <td>
                <h5>Grand Total</h5>
              </td>
              <td>
                <h5>
                  <span class="cart_p">
                    {{
                      (productList.total ? productList.total : 0) | currency
                    }}</span>
                </h5>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>