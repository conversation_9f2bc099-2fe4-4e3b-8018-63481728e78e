import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AddPaymentComponent } from './add-payment.component';
import { RouterModule, Routes } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { NumberOnlyDirectiveModule } from '../../../../../modules/directive/directive.module';
import { DialogBoxModule } from '../../../../../modules/dialog-box/dialog-box.module';
import { DialogBoxComponent } from '../../../../../modules/dialog-box/dialog-box.component';
import { CurrencyFormatModule } from '../../../../../modules/currency-format/currency-format.pipe';
import { PaymentSettingCheckoutModule } from '../../../admin-checkout/checkout-view/paymnet-settings/paymnet-settings.module';
import { AddCaptureComponent } from './add-capture/add-capture.component';


const routes: Routes = [
  {
      path: '',
      component: AddPaymentComponent
  },
  {
    path: 'capture/:pid',
    component: AddPaymentComponent
}

];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    RouterModule.forChild(routes),
    NumberOnlyDirectiveModule,
    PaymentSettingCheckoutModule,
    DialogBoxModule, CurrencyFormatModule
  ],
  entryComponents: [DialogBoxComponent],
  exports: [RouterModule],
  declarations: [AddPaymentComponent, AddCaptureComponent]
})
export class AddPaymentModule { }
