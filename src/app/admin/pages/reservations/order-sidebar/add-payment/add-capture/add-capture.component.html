
<div class="custom-alert" #hasCusAlert></div>

<div>
  <h5>
    Amount
  </h5>
  <div class="row">
    <div class="col-md-6">
      <div class="input-group m-input-group">
        <div class="input-group-prepend">
          <span class="input-group-text" id="table-cost-addon">
            $
          </span>
        </div>
        <input
          id="cost"
          class="form-control m-input"
          id="amount"
          type="text"
          placeholder="0.00"
          name="amount"
          [maxlength]="maxLength"
          numberOnly
          [(ngModel)]="captureAmount"
          autocomplete="off"
          required
        />
        <div class="input-group-append">
          <span class="input-group-text" id="table-cost-addon">
            USD
          </span>
        </div>
      </div>
      <br />
      <button [disabled]="captureAmount ==''" class="btn btn-brand" (click)="captured()">
        Capture
      </button>
    </div>
  </div>
</div>
