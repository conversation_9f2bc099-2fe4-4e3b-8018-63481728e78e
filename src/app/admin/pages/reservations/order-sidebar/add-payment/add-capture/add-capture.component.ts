import {Component, OnInit, ViewChild, ElementRef, Input, Output, EventEmitter} from '@angular/core';
import {AlertService} from '../../../../../../modules/alert/alert.service';
import {OrderService} from '../../../order.service/order.service';
import {preventInputAlphabets} from '../../../../../../globals/_classes/functions';

@Component({
    selector: 'app-add-capture',
    templateUrl: './add-capture.component.html',
    styleUrls: ['./add-capture.component.css']
})
export class AddCaptureComponent implements OnInit {


    captureAmount = '';
    is_disable_btn;
    @Input('captureData') captureData;
    @Output("loadPaymentList") loadPaymentList: EventEmitter<any> = new EventEmitter();
    @ViewChild('hasCusAlert') alertContainer: ElementRef;
    maxLength: any;

    constructor(private alertS: AlertService, private orderS: OrderService) {
    }

    ngOnInit() {
        this.captureAmount = this.captureData.payment_amount;
    }


    captured() {
        let amount = Number(this.captureAmount);

        if (!isNaN(amount)) {
            const data = {
                transaction_id: this.captureData.transaction_id,
                order_id: this.captureData.order_id,
                amount: amount,
                id: this.captureData.id
            };
            this.orderS.callCaptured(data).then(
                res => {
                   if (res.result.success) {
                        this.alertS.success(this.alertContainer, res.result.message, true, 3000);
                        this.captureAmount='';
                        this.loadPaymentList.emit({isLoad:true,message: res.result.message});
                    } else {
                        this.alertS.error(this.alertContainer, res.result.message, true, 3000);
                    }
                }
            ).catch(
                err => this.error(err, 'Something wrong!!!')
            );
        } else {
            this.alertS.error(this.alertContainer, "Input is not correct format", true, 5000);
        }

    }


    error(err, message) {
        console.log(err);
        this.alertS.error(this.alertContainer, message, true, 5000);
    }
}
