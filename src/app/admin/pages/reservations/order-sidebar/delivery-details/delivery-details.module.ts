import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DeliveryDetailsComponent } from './delivery-details.component';
import { RouterModule, Routes } from '@angular/router';
import { CurrencyFormatModule } from '../../../../../modules/currency-format/currency-format.pipe';

const routes: Routes = [
  {
      path: '',
      component: DeliveryDetailsComponent
  }
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    CurrencyFormatModule
  ],
  declarations: [DeliveryDetailsComponent]
})
export class DeliveryDetailsModule { }
