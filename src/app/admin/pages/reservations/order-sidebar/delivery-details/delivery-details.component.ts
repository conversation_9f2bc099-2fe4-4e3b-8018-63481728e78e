import { Component, OnInit, OnDestroy, ViewChild, ElementRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { OrderService } from '../../order.service/order.service';
import { Helpers } from './../../../../../helpers';
import { AlertService } from '../../../../../modules/alert/alert.service';

@Component({
  selector: 'app-delivery-details',
  templateUrl: './delivery-details.component.html',
  styleUrls: ['./delivery-details.component.css']
})
export class DeliveryDetailsComponent implements OnInit, OnDestroy {

  sub: Subscription[] = [];
  path: string;
  order_id: number;
  delivery;
  track;
  label_pdf_link = '';
  label_image_link = '';
  currency;

  @ViewChild('hasCusAlert') alertContainer: ElementRef;

  constructor(
    private router: Router,
    private orderS: OrderService,
    private activeRoute: ActivatedRoute,
    private alertS: AlertService,
  ) {
    this.currency = JSON.parse(localStorage.getItem("currency"));
   }

  ngOnInit() {
    Helpers.setLoading(true);
    this.sub[0] = this.activeRoute.data.subscribe(
      val => {
        this.delivery = val.delivery;
        if(this.delivery && this.delivery.response)
        {
          let data = this.delivery.response.label_download;
          this.label_pdf_link = data ? data.pdf : null;
          this.label_image_link = data ? data.png : null;
        }
        Helpers.setLoading(false);
      }
    );

    this.path = this.activeRoute.snapshot['_routerState'].url;
    this.order_id = parseInt(this.orderS.getOrderId(this.activeRoute));
  }

  ngOnDestroy() {
    this.sub.forEach(f => f.unsubscribe());
  }


  getTitle(d: string = '') {
    const arr = ['fedex', 'ups'];
    return arr.includes(d.toLowerCase());
  }

  getDate(d) {
    return new Date(d);
  }

  makeShipment() {
    Helpers.setLoading(true);
    // this.orderS.confirmShipment(this.order_id).then(data => {
    //   if(data.result.data){
    //     Helpers.setLoading(false);
    //     this.delivery['delivery_result'] = data.result.data;
    //   }
    // });

    this.orderS.createShippingLabel(this.order_id, 'ship').then(res => {
      if (res.status == "OK") {
        Helpers.setLoading(false);
        console.log(res)
        const data = res.result.data.label_download;
        if (data) {
          this.label_pdf_link =data.pdf;
          this.label_image_link = data.png;
        }

      }
      else {
        console.log(res)
        this.alertS.error(this.alertContainer, res.result.message, true, 5000);
      }
    });
  }

  makeTrack() {
    Helpers.setLoading(true);
    this.orderS.trackShipment(this.order_id).then(data => {
      Helpers.setLoading(false);
      this.track = data.result.data;
    });

  }
  createShipment(order_id,shipment_id){
    this.router.navigateByUrl(
      `/admin/reservations/shipping/create/${order_id}?shipment_id=${shipment_id}`
    );
  }

}
