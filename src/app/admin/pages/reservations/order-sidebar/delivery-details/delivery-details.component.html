<div class="custom-alert" #hasCusAlert></div>

<div class="animated fadeIn">
  <div class="m-portlet__body" style="padding-bottom: 15px;">
    <div>
      <table class="table" *ngIf="delivery">

        <tbody *ngIf="delivery?.method == 1 || null">
          <tr>
            <th>Type:</th>
            <td>
              <b>Instore Pickup</b>
            </td>
          </tr>
          <tr>
            <th>Pickup from:</th>
            <td>
              <b>{{delivery?.pickup_from}}</b>
            </td>
          </tr>
           <tr>
            <button type="button" class="btn btn-sm btn-brand" (click)="createShipment(order_id,delivery?.id)">Create Shipment</button>
          </tr> 
        </tbody>


        <tbody *ngIf="delivery?.method == 2">
          <tr>
            <th>Type:</th>
            <td>
              <b>Delivery{{delivery.config.charge_by === 'zone' ? ' by Zone' : delivery.config.charge_by === 'location' ? ' by Location' : ''}}</b>
            </td>
          </tr>
          <tr>
            <th>{{delivery.config.charge_by === 'zone' ? 'Zone Name' : 'Pickup from:'}}</th>
            <td>
              <b>{{delivery.config.location?.pickup_from}}</b>
            </td>
          </tr>
          <tr>
            <th>Delivery To:</th>
            <td>
              <b>{{delivery.config.location?.delivery_to}}</b>
            </td>
          </tr>
          <tr>
            <th>Charge:</th>
            <td>
              <b>{{delivery?.charge_amount | currency}}</b>
            </td>
          </tr>
          <!-- <tr>
            <th>Distance:</th>
            <td>
              <b>{{delivery.config.location?.distance}}</b>
            </td>
          </tr> -->
          <!-- <tr>
            <th>Tax:</th>
            <td>
              <b>{{delivery?.delivery_tax}}</b>
            </td>
          </tr> -->
          <tr>
            <button type="button" class="btn btn-sm btn-brand" (click)="createShipment(order_id,delivery?.id)">Create Shipment</button>
          </tr> 
        </tbody>

        <tbody *ngIf="delivery?.method == 4">
          <tr>
            <th>Type:</th>
            <td>
              <b>Shipping carrier</b>
            </td>
          </tr>
          <tr>
            <th>Pickup from:</th>
            <td>
              <b>{{delivery?.pickup_from}}</b>
            </td>
          </tr>
          <tr>
            <th>Delivery To:</th>
            <td>
              <b>{{delivery?.delivery_to}}</b>
            </td>
          </tr>
          <tr>
            <th>Charge:</th>
            <td>
              <b>{{delivery?.charge | currency}}</b>
            </td>
          </tr>
          <tr>
            <th>Service Name:</th>
            <td>
              <b>{{delivery?.service_name}}</b>
            </td>
          </tr>
          <tr>
            <th>Tax:</th>
            <td>
              <b>{{delivery?.delivery_tax}}</b>
            </td>
          </tr>
          <tr *ngIf="delivery?.service_name; else createShipping" >
            <button type="button" class="btn btn-sm btn-brand" [routerLink]="'/admin/reservations/shipping-orders/' + delivery?.id">View Shipment</button>
          </tr>
          <ng-template #createShipping>
            <tr>
              <button type="button" class="btn btn-sm btn-brand"  (click)="createShipment(order_id,delivery?.id)">Create Shipment</button>
            </tr>
          </ng-template>
         
        </tbody>


        <tbody *ngIf="delivery?.method == 5">
          <tr>
            <th>Type:</th>
            <td>
              <b>Free Shipping</b>
            </td>
          </tr>
          <tr>
            <th>Pickup from:</th>
            <td>
              <b>{{delivery?.pickup_from}}</b>
            </td>
          </tr>
          <tr>
            <th>Delivery To:</th>
            <td>
              <b>{{delivery?.delivery_to}}</b>
            </td>
          </tr>
          <tr>
            <button type="button" class="btn btn-sm btn-brand"  (click)="createShipment(order_id,delivery?.id)">Create Shipment</button>
          </tr>
        </tbody>
        
        <tbody *ngIf="delivery?.method == 6">
          <tr>
            <th>Type:</th>
            <td>
              <b>Standard Shipping</b>
            </td>
          </tr>
          <tr>
            <th>Pickup from:</th>
            <td>
              <b>{{delivery?.pickup_from}}</b>
            </td>
          </tr>
          <tr>
            <th>Delivery To:</th>
            <td>
              <b>{{delivery?.delivery_to}}</b>
            </td>
          </tr>
          <tr>
            <th>Charge:</th>
            <td>
              <b>{{delivery?.charge | currency}}</b>
            </td>
          </tr>
          <tr>
            <th>Tax:</th>
            <td>
              <b>{{delivery?.delivery_tax}}</b>
            </td>
          </tr>
          <tr>
            <th>Distance:</th>
            <td>
              <b>{{delivery?.distance}}</b>
            </td>
          </tr>
          <tr>
            <button type="button" class="btn btn-sm btn-brand"  (click)="createShipment(order_id,delivery?.id)">Create Shipment</button>
          </tr>
        </tbody>

        <tbody *ngIf="delivery?.method == 7">
          <tr>
            <th>Type:</th>
            <td>
              <b>Flat Shipping</b>
            </td>
          </tr>

          <tr>
            <th>Charge:</th>
            <td>
              <b>{{delivery?.charge_amount | currency}}</b>
            </td>
          </tr>
          <tr>
            <th>Two way shipping:</th>
            <td>
              <b>{{delivery?.config?.two_way ? 'Yes' : 'No'}}</b>
            </td>
          </tr>
          <tr>
            <th>Handling amount:</th>
            <td>
              <b>{{delivery?.config?.handling_amount}}</b>
            </td>
          </tr>
          <tr>
            <button type="button" class="btn btn-sm btn-brand"  (click)="createShipment(order_id,delivery?.id)">Create Shipment</button>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>