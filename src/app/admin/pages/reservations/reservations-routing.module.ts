import {NgModule} from '@angular/core';
import {Routes, RouterModule} from '@angular/router';
import { AdminService } from '../../admin.service';
import { PagesComponent } from '../pages.component';
import { ReservationsComponent } from './reservations.component';
import { OrderService } from './order.service/order.service';


const routes: Routes = [
  {
    path: '',
    component: PagesComponent,
    children: [{
        path: '',
        component: ReservationsComponent,
        canActivate: [AdminService],
        children: [
            {
                path: 'dashboard',
                loadChildren: () => import('./reservations-dashboard/reservations-dashboard.module').then(m => m.ReservationsDashboardModule),
                data : { title:'Order dashboard'}
            },
            {
                path: ':order_id/details',
                loadChildren: () => import('./order-details/order-details.module').then(m => m.OrderDetailsModule),
                data : { title:'Order details'}
            },
            {
                path: 'rental-orders',
                loadChildren: () => import('./order-list/order-list.module').then(m => m.OrderListModule),
                resolve: {'order': OrderService}
            },
            {
                path: 'shipping-orders',
                loadChildren: () => import('./shipping-order/shipping-order.module').then(m => m.ShippingOrderModule)
            },
            {
                path: 'shipping/create/:order_id',
                loadChildren: () => import('./shipping-order/create-shipping-order/create-shipping-order.module').then(m => m.CreateShippingOrderModule)
            },
            {
                path: 'rental-calendar',
                loadChildren: () => import('./rental-calender/rental-calender.module').then(m => m.RentalCalenderModule),
                data : { title:'Rental Calendar'}
            },
            {
                path: ':status',
                loadChildren: () => import('./order-list/order-list.module').then(m => m.OrderListModule),
                resolve: {'order': OrderService},
                data : { title:'Order list'}
            }
        ]
    }]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ReservationsRoutingModule {}
