<div class="custom-alert" #hasCusAlert></div>

<!-- BEGIN: Subheader -->
<div class="m-subheader">
    <div class="d-flex align-items-center">
        <div class="mr-auto">
            <h3 class="m-subheader__title m-subheader__title--separator">
                Order List
            </h3>
            <ul class="m-subheader__breadcrumbs m-nav m-nav--inline">
                <li class="m-nav__item m-nav__item--home">
                    <a routerLink="/admin" class="m-nav__link m-nav__link--icon">
                        <i class="m-nav__link-icon la la-home"></i>
                    </a>
                </li>
                <li class="m-nav__separator">
                    <i class="fa fa-angle-right"></i>
                </li>
                <li class="m-nav__item">
                    <a class="m-nav__link">
                        <span class="m-nav__link-text">
              Reservations
            </span>
                    </a>
                </li>
                <li class="m-nav__separator" style="padding-left: 10px">
                    <i class="fa fa-angle-right"></i>
                </li>
                <li class="m-nav__item">
                    <a class="m-nav__link">
                        <span class="m-nav__link-text">
              Order List
            </span>
                    </a>
                </li>
            </ul>

        </div>
        <a routerLink="/admin/reservations/rental-calendar" type="button" class="btn btn-dark btn-md">
            <i class="fa fa-calendar edit-icon"></i>
        </a>
        <!-- <div  id="m_daterangepicker_3" >
        <span  class="m-subheader__daterange" id="m_dashboard_daterangepicker">
          <span class="m-subheader__daterange-label">
            <span id="show_dateRaneLabel" class="m-subheader__daterange-title">Select Pickup Date </span>
            <span class="m-subheader__daterange-date m--font-brand"></span>
          </span>
          <a  class="btn btn-sm btn-brand m-btn m-btn--icon m-btn--icon-only m-btn--custom m-btn--pill">
            <i style="color: #fff;" class="la la-angle-down"></i>
          </a>
        </span>
      </div> -->
    </div>
</div>
<!-- END: Subheader -->
<div class="m-content product-list animated fadeIn">

    <order-filter 
        [staustId]="staustId"
        [order_type]="order_type"
        (loadList)="filterList($event)" 
        (initialStatus)="getInitialStatus($event)">
    </order-filter>

    <div class="m-portlet m-portlet--mobile ">
        <div class="m-portlet__head orderlist-head ">
            <div class="m-portlet__head-caption ">
                <div class="m-portlet__head-title ">
                    <h3 class="m-portlet__head-text mb-0 ">
                        {{order_type === 1 ? 'Order' : 'Quote'}} List
                    </h3>
                    <div class="button-midle button-cus text-right " style="display: inline-block; ">
                        <div class="orderlist-datetime-range">
                            <div *ngIf="isRentalDateEditMode;else showText" id="daterangeWrapper">
                                <app-date-time-range [ngClass]="['both']" (dateModel)="filterRentalDate($event)" [placeholderEnd]="currentDate" [placeholderStart]="currentDate">
                                </app-date-time-range>
                                <!-- <button class="button theme-btn cancel-btn" (click)="isRentalDateEditMode = false">Cancel</button> -->
                            </div>

                            <ng-template #showText>
                                <label>
                                    <i style="cursor: pointer;" class="fa fa-calendar edit-icon" (click)="onClickEditDate()"></i>
                                </label>
                            </ng-template>
                        </div>
                        <button #selected type="button " class="btn btn-brand btn-sm " (click)="all() ">
                            All
                        </button>
                        <button type="button " class="btn btn-brand btn-sm " (click)="today() ">
                            Today
                        </button>
                        <button type="button " class="btn btn-brand btn-sm " (click)="thisWeek() ">
                            This week
                        </button>
                        <button type="button " class="btn btn-brand btn-sm " (click)="nextWeek() ">
                            Next week
                        </button>

                    </div>
                </div>
            </div>
        </div>
        <div class="m-portlet__body ">
            <!--begin: Datatable -->
            <!--begin::Section-->
            <div class="m-section ">
                <div class="m-section__content price-table " style="position: relative; ">

                    <div>
                        <div class="table-responsive " style="margin-bottom: 15px; ">
                            <table class="table table-hover ">
                                <thead>
                                    <tr>
                                        <th></th>
                                        <th (click)="sorting( 'id') " style="cursor: pointer; ">
                                            Order ID
                                            <i class="la icon " id="id " style="padding-left: 5px "></i>
                                        </th>
                                        <th (click)="sorting( 'name') " style="cursor: pointer; ">
                                            Customer Name
                                            <i class="la icon " id="name " style="padding-left: 5px "></i>
                                        </th>
                                        <th (click)="startDateOrderBy()" style="cursor: pointer;">
                                            Start Date
                                            <i class="fa" #startDateArrow></i>
                                        </th>
                                        <th (click)="endDateOrderBy()" style="cursor: pointer;">
                                            End Date
                                            <i class="fa" #endDateArrow></i>
                                        </th>
                                        <th>
                                            Total Quantity
                                        </th>
                                        <th>
                                            Total Price
                                        </th>
                                        <th>Payment Status</th>
                                        <th>Status</th>
                                        <th>Pickup/Return</th>
                                        <th style="min-width: 90px!important; ">
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                                <div *ngIf="loader; else table " class="table-load m-loader m-loader--brand "></div>
                                <ng-template #table>
                                    <tbody *ngIf="orderList.length < 1; else hasData ">
                                        <tr *ngIf="!loader ">
                                            <td colspan="10 ">
                                                <h4 class="text-center ">No Order Found</h4>
                                            </td>
                                        </tr>
                                    </tbody>
                                    <ng-template #hasData>
                                        <tbody>
                                            <!-- <ng-container *ngFor="let order of orderList "> -->
                                            <ng-template ngFor let-order let-i='index' let-o='odd' let-e='even' [ngForOf]="orderList " [ngForTrackBy]="trackOrder ">
                                                <tr [ngClass]="{ 'odd-tr':o, 'even-tr':e} ">
                                                    <td style="max-width: 50px; ">
                                                        <span id="product_sidebar_toggle" (click)="openSummary( 'order-id-'+ order.id, 'order-icon-'+ order.id) " class="m-portlet__nav-link btn m-btn m-btn--hover-brand m-btn--icon m-btn--icon-only m-btn--pill" title="View Payment ">
                                                            <i #toggledClass class="fa fa-plus-circle" id="{{ 'order-icon-'+order.id}} "></i>
                                                        </span>
                                                    </td>
                                                    <td>{{order.id}}</td>
                                                    <td (click)="openSidebar( 'edit-order/'+order.id) " style="cursor: pointer;color:#716aca ">{{order.name}}</td>
                                                    <td>{{order.rent_start | customDate: 'rental'}}</td>
                                                    <td>{{order.rent_end | customDate: 'rental'}}</td>
                                                    <td>{{order.total_quantity}}</td>
                                                    <td>{{order.total_price | currency}}</td>
                                                    <td>
                                                        <span (click)="goToPayment(order) " class="m-badge m-badge--wide " [ngClass]="checkStatusPayment(order.payment_status) " style="cursor: pointer; ">
                                {{getPaymentStatus(order.payment_status)}}
                              </span>
                                                    </td>
                                                    <td>
                                                        <span *ngIf="order.type==1 && getStatus(order.status) " class="m-badge m-badge--wide " [ngClass]="checkStatus(order.status) " (click)="changeStatus(order) " style="cursor: pointer; ">{{getStatus(order.status)}}</span>
                                                        <span *ngIf="order.type==2" class="m-badge m-badge--wide m-badge--warning">Quote</span>
                                                    </td>
                                                    <td>
                                                        <span *ngIf="(order.type==1) && order.rent && checkPickUp(order.status) " class="m-badge m-badge--wide m-badge--primary " (click)="changePickup(order, false) " style="cursor: pointer; ">Pickup?</span>
                                                        <span *ngIf="(order.type==1) && order.rent && order.status==5 " class="m-badge m-badge--wide m-badge--danger " (click)="changePickup(order, true) " style="cursor: pointer; ">Return?</span>
                                                        <!--                        <span *ngIf="order.rent && checkPickUp(order.status) " class="m-badge m-badge&#45;&#45;wide m-badge&#45;&#45;primary "-->
                                                        <!--                               style="cursor: pointer; ">Pickup?</span>-->
                                                        <!--                        <span *ngIf="order.rent && order.status==5 " class="m-badge m-badge&#45;&#45;wide m-badge&#45;&#45;danger "-->
                                                        <!--                               style="cursor: pointer; ">Return?</span>-->
                                                    </td>
                                                    <td>
                                                        <a id="product-details-page " href="{{url}}pages/pdf?order_id={{order.id}} " class="m-portlet__nav-link btn m-btn m-btn--hover-brand m-btn--icon m-btn--icon-only m-btn--pill " title="Download PDF ">
                                                            <i class="fa fa-file-pdf-o "></i>
                                                        </a>
                                                        <span id="product-details-page " (click)="gotoDetails(order.id) " class="m-portlet__nav-link btn m-btn m-btn--hover-brand m-btn--icon m-btn--icon-only m-btn--pill " title="View Order ">
                                <i class="fa fa-eye "></i>
                              </span>
                                                        <span id="product_sidebar_toggle " (click)="openSidebar( 'edit-order/'+order.id) " class="m-portlet__nav-link btn m-btn m-btn--hover-brand m-btn--icon m-btn--icon-only m-btn--pill " title="Edit Order ">
                                <i class="fa fa-pencil-square-o "></i>
                              </span>
                                                        <span (click)="copyOrder(order) " class="m-portlet__nav-link btn m-btn m-btn--hover-brand m-btn--icon m-btn--icon-only m-btn--pill " title="Copy Order ">
                                <i class="fa fa-copy "></i>
                              </span>
                                                        <span *ngIf="order.status==1 " id="product_sidebar_toggle " (click)="deleteOrder(order.id, i,order.status) " class="m-portlet__nav-link btn m-btn m-btn--hover-danger m-btn--icon m-btn--icon-only m-btn--pill
                            " title="Delete Order ">
                                <i class="fa fa-trash "></i>
                              </span>
                                                        <span *ngIf="order.status!=1 " id="product_sidebar_toggle " (click)="deleteOrder(order.id, i,order.status) " class="m-portlet__nav-link btn m-btn m-btn--hover-danger m-btn--icon m-btn--icon-only m-btn--pill
                            " title="Archive Order ">
                                <i class="fa fa-trash "></i>
                              </span>
                                                    </td>
                                                </tr>
                                                <tr class="" id="{{ 'order-id-'+order.id}}" *ngIf="('order-id-'+order.id) == orderDetailsOpenId">
                                                    <td colspan="10 ">
                                                        <div class="description-fields ">
                                                            <div class="row ">
                                                                <div class="col-md-6 ">
                                                                    <p><span class="description-field-title ">Order Id: </span> <span>{{order.id}}</span></p>
                                                                    <p><span class="description-field-title ">Order Source: </span> <span>{{order?.event_location}}</span></p>
                                                                    <p><span class="description-field-title ">Total Quantity: </span> <span>{{order.total_quantity}}</span></p>
                                                                    <p><span class="description-field-title ">Pickup Store: </span> <span>{{order.pickup}}</span></p>
                                                                    <p><span class="description-field-title ">Order Date & Time: </span> <span>
                                      {{order.created | customDate}}</span></p>
                                                                    <p><span class="description-field-title ">Delivery Date & Time: </span> <span>
                                      {{order.delivery_date | customDate}}</span></p>
                                                                </div>
                                                                <div class="col-md-6 ">
                                                                    <p><span class="description-field-title ">Sub Total: </span> <span>{{(order.sub_total ?
                                        order.sub_total : 0) | currency}} </span></p>
                                                                    <p><span class="description-field-title ">Delivery Cost: </span> <span>{{order.delivery_charge
                                        | currency}}</span></p>
                                                                    <p><span class="description-field-title ">Discount: </span> <span>{{(order.total_discount
                                        ? order.total_discount : 0) | currency}} {{order.total_discount ? '(' +
                                        order?.coupon?.code + ')' : ''}}</span></p>
                                                                    <p><span class="description-field-title ">Sales Tax: </span> <span>{{(order.tax ?
                                        order.tax : 0) | currency}}</span></p>
                                                                    <p><span class="description-field-title ">Delivery Tax: </span> <span>{{(order.delivery_tax
                                        ? order.delivery_tax : 0) | currency}}</span></p>
                                                                    <p><span class="description-field-title ">Deposit Amount: </span> <span>{{order.total_deposit
                                        | currency}}</span></p>
                                                                    <p><span class="description-field-title ">Grand Total: </span> <span>{{order.total_price |
                                        currency}}</span></p>
                                                                </div>
                                                            </div>

                                                        </div>
                                                    </td>
                                                </tr>
                                            </ng-template>
                                            <!-- </ng-container> -->
                                        </tbody>
                                    </ng-template>
                                </ng-template>

                            </table>
                        </div>
                        <!-- pagination Start-->
                        <boot-pagination [totalSize]="pagi.total " [page]="pagi.page " [pagelimit]="pagi.limit " [listSize]="pagi.limit " (pageChange)="reloadTable($event) "></boot-pagination>
                        <!-- pagination End-->
                    </div>
                </div>
            </div>
            <!--end::Section-->
            <!--end: Datatable -->
        </div>
    </div>
    <!--end::Portlet-->
</div>



<!-- sidebar -->

<div class="native-routing animated ">
    <button class="close-sidebar btn btn-sm btn-brand ">
    <i class="fa fa-chevron-right "></i>
  </button>
    <span class="close-sidebar-upper ">
    <i class="la la-close "></i>
  </span>
    <div class="native-routing-container ">
        <router-outlet></router-outlet>
    </div>
</div>