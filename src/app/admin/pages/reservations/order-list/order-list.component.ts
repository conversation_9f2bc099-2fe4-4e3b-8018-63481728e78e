import { HttpErrorResponse } from '@angular/common/http';
import {
    Component,
    OnInit,
    AfterViewInit,
    HostListener,
    OnDestroy,
    ViewChild,
    ElementRef,
    Renderer2
} from "@angular/core";
import {ActivatedRoute, Router, ActivatedRouteSnapshot} from "@angular/router";
import {SidebarService} from "../../sidebar-service/sidebar.service";
import {OrderService} from "../order.service/order.service";
import {Pagi} from "../../../../modules/pagination/pagi.model";
import {Subscription} from "rxjs";
import {
    SORTING,
    calculatePage,
    FORMATESORT,
    GET_USER,
    isJson,
    Payment_status
} from "../../../../globals/_classes/functions";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {Helpers} from "../../../../helpers";
import {DialogBoxComponent} from "../../../../modules/dialog-box/dialog-box.component";
import {AlertService} from "../../../../modules/alert/alert.service";
import {StatusDialogBoxComponent} from "../dialog-box/dialog-box.component";
import {Order} from "../models/order-models";
import {EndPoint} from "../../../../globals/endPoint/config";
import {map} from "rxjs/operators";
import { HttpService } from "../../../../modules/http-with-injector/http.service";
import { SettingService } from "../../settings/setting-service/setting.service";
import { IStatus } from '../../../../modules/status-option/status-option.component';

declare let $: any;
declare var moment: any;

@Component({
  selector: "app-order-list",
  templateUrl: "./order-list.component.html",
  styleUrls: ["./order-list.component.css"],
})
export class OrderListComponent implements OnInit, AfterViewInit, OnDestroy {
  sidebarOpen: boolean = false;
  pagi: Pagi = new Pagi();
  filter: string = "";
  loader: boolean;
  orderList: Order[] = [];
  sub: Subscription[] = [];
  sort = {};
  status = null;
  routeStatus = "";
  staustId = null;
  currentPath: string;
  statusArray: IStatus[] = [];
  modStatusArray: IStatus[] = [];
  url = EndPoint;
  rentalOrders;
  currentDate = new Date().toDateString();
  isRentalDateEditMode = false;

  filterByDate = [
    { name: "All", value: "all" },
    { name: "Today", value: "today" },
    { name: "This week", value: "thisWeek" },
    { name: "Next week", value: "nextWeek" },
  ];

  @ViewChild("hasCusAlert") alertContainer: ElementRef;
  @ViewChild("selected", {static : true}) el: ElementRef;
  @ViewChild('startDateArrow') startDateArrow: ElementRef;
  @ViewChild('endDateArrow') endDateArrow: ElementRef;
  selectedStatus: number[] = [];
  orderDetailsOpenId: any;
  // toggledClass = 'fa fa-plus-circle';
  @ViewChild('toggledClass') toggledClass: ElementRef;
  order_type = 1;

  @HostListener("window:resize", ["$event"])
  onResize(event) {
    if (this.sidebarOpen) {
      this.sidebarS.openSidebar();
    }
  }

  constructor(
    private sidebarS: SidebarService,
    private router: Router,
    private route: ActivatedRoute,
    private modalService: NgbModal,
    private orderS: OrderService,
    private alertS: AlertService,
    private render: Renderer2,
    public http: HttpService,
    private settingService: SettingService,
    private renderer: Renderer2
  ) {
    this.sub.push(
      this.route.params.subscribe(param => {
        if (param.status && param.status === 'quote') {
          this.order_type = 2;
        } else if (param.status && param.status === 'all') {
          this.order_type = 1;
        }
      })
    );
    this.getStatusData();
    this.checkRoute();
    this.addUpdateOrder();
    this.getOrderListFromRoute();
  }

  ngOnInit() {
    const selected = this.el.nativeElement;
    this.render.addClass(selected, "orderlist-active-btn");
    //window.scrollTo(0, 0);
    // this.orderS.filterDateRangeSub.subscribe(res=>{
    //   if(res.reset){
    //     this._dateRange();
    //   }
    // })
    this.sub.push(this.sidebarS.sidebarOpen.subscribe(val => {
      this.sidebarOpen = val;
      this.sidebarS.closeCartSidebar();
     })
    );
  }

  ngAfterViewInit() {
    this.closeEdit();
  }

  ngOnDestroy() {
    for (let s of this.sub) {
      s.unsubscribe();
    }
    this.orderS.filterDateRangeSub.next({
      startDate: '',
      endDate: '',
      reload: false,
      reset: true,
      status: ''
    });
  }

  private checkRoute() {
    this.sub[0] = this.router.events
      .pipe(
        map(() => {
          const orderEdit = this.router.createUrlTree(["edit-order"], {
            relativeTo: this.route,
          });
          return this.router.isActive(orderEdit, false);
        })
      )
      .subscribe((active) => {
        //  console.log(active);
        if (active) {
          $(".native-routing").css("display", "block");
          this.sidebarS.openSidebar();
          this.sidebarOpen = active;
        } else {
          this.sidebarS.removeSidebar();
          $(".native-routing").css("display", "none");
          this.sidebarOpen = active;
        }
      });
  }

  addUpdateOrder() {
    this.sub[1] = this.orderS.addUpdateOrder.subscribe((val) => {
      //  console.log(val);
      if (val) {
        // console.log(val);
        if (val.add) {
          if (this.pagi.page == 1) {
            const size = this.orderList.length;
            if (size === 10) {
              this.orderList.splice(9, 1);
              this.orderList.splice(0, 0, val.data);
            } else {
              this.orderList.splice(0, 0, val.data);
            }
          } else {
            this.dataRender(this.pagi.page, this.pagi.limit, "");
          }
        } else {
          this.dataRender(this.pagi.page, this.pagi.limit, "");
        }
      }
    });
  }

  getDate(d) {
    return this.orderS.formateListDate(d);
  }

  get showStartTime() {
    const contents = localStorage.getItem("contents")
      ? JSON.parse(localStorage.getItem("contents"))
      : null;
    if (
      contents.site_specific.confg &&
      contents.site_specific.confg.hasOwnProperty("show_start_time")
    ) {
      return contents.site_specific.confg.show_start_time;
    }
    return true;
  }

  getOrderListFromRoute() {
    this.sub[2] = this.route.data.subscribe((val) => {
      this.dataList(val["order"]);
      const path = this.route.snapshot["_routerState"].url;
      if (path.includes("rental-orders")) {
        this.rentalOrders = 1;
      } else {
        this.rentalOrders = 0;
      }
    });
    this.sub[3] = this.route.params.subscribe((val) => {
      // console.log(val);
      if (Object.keys(val).length > 0) {
        this.currentPath = val.status;
        this.staustId = this.orderS.getStatusId(val.status);
        this.routeStatus = this.staustId ? "&status=" + this.staustId : "";
        // console.log(this.staustId, this.routeStatus);
      }
    });
  }

  trackOrder(index, order) {
    return order ? order.id : null;
  }

  reloadTable(e) {
    if (this.order_type === 2 && !this.filter.includes('type')) {
      this.filter = this.filter + '&type=2';
    }
    const query = FORMATESORT(this.sort);
    console.log(query)
    this.getOrderList(e.page, e.limit, this.filter, query);
  }

  getOrderList(p, l, f, s?) {
    this.loader = true;
    this.dataRender(p, l, f, s);
  }

  private dataRender(p?, l?, f?, s?) {
    this.orderS
      .getOrderList(p, l, this.rentalOrders, this.routeStatus, f, s)
      .subscribe(
        (res) => {
          // console.log(res);
          this.dataList(res);
          this.loader = false;
        },
        (err) => console.log(err)
      );
  }

  private dataList(res) {
    var data = res.data;
    this.orderList = data;
    this.pagi.total = res["total"] || 0;
    this.pagi.page = parseInt(res["page_no"]) || 1;
    this.pagi.limit = parseInt(res["limit"]) || 20;
  }

  filterList(e) {
    console.log(e);
    this.filter = e;
    this.sort = SORTING(null, {});
    this.getOrderList(1, 20, this.filter);
  }

  openSidebar(a) {
    this.sidebarS.openSidebar();
    this.router.navigate([a], { relativeTo: this.route });
  }

  openCartSidebar() {
    const store = GET_USER().location_id;
    if (store) {
      this.sidebarS.sidebarOpenChange(true);
      this.sidebarS.openCartSidebar();
    } else {
      this.alertS.info(
        this.alertContainer,
        "Store has not been selected. Please select store.",
        true,
        5000
      );
    }
  }

  gotoDetails(order_id) {
    sessionStorage.setItem(
      "lastUrl",
      this.currentPath ? this.currentPath : "rental-orders"
    );
    this.router.navigate(["/admin/reservations/" + order_id + "/details"]);
  }

  private closeEdit() {
    $(".close-sidebar").click((e) => {
      e.preventDefault();
      this.close();
    });
    $(".close-sidebar-upper").click((e) => {
      e.preventDefault();
      this.close();
    });
  }

  private close() {
    this.sidebarOpen = false;
    this.sidebarS.removeSidebar();
    this.router.navigate([
      "admin/reservations/" +
        (this.currentPath ? this.currentPath : "rental-orders"),
    ]);
    // console.log(this.currentPath);
  }

  openSummary(id, icon) {
    if (this.orderDetailsOpenId === id) {
      this.orderDetailsOpenId = null;
      // this.renderer.addClass(this.toggledClass.nativeElement, 'fa-plus-circle')
      // this.renderer.removeClass(this.toggledClass.nativeElement, 'fa-minus-circle')
    } else {
      this.orderDetailsOpenId = id;
      // this.toggledClass = 'fa fa-minus-circle'
      // this.renderer.addClass(this.toggledClass.nativeElement, 'fa-minus-circle')
      // this.renderer.removeClass(this.toggledClass.nativeElement, 'fa-plus-circle')
    }
    // $("#" + id).toggleClass("dis-block");
    // $("#" + icon).toggleClass("fa-minus-circle");
  }

  // getorderdetailsId(id, icon) {
  //   return id
  // }

  getDateFormate(date) {
    return this.orderS.getDate(date);
  }

  getStatus(s) {
    const status = this.modStatusArray.find(x => x.id == s);
    if (status) {
      return status.label;
    }
    return '';
    // if (this.modStatusArray) {
    //   return this.orderS.getStatus(s, this.modStatusArray);
    // }
  }

  checkStatus(s) {
    return this.orderS.checkStatus(s);
  }

  sorting(id) {
    this.sort = SORTING(id, this.sort);
    const query = FORMATESORT(this.sort);
    this.dataRender(this.pagi.page, this.pagi.limit, this.filter, query);
    // console.log(this.sort);
  }

  deleteOrder(att_id, i, orderStatus) {
    orderStatus = +orderStatus;
    if (orderStatus === 1) {
      const modalRef = this.modalService.open(DialogBoxComponent, {
        centered: true,
        size: "sm",
      });

      modalRef.componentInstance.massage =
        "Are you sure you want to delete #" + att_id + "?";

      modalRef.result.then(
        (result) => {
          if (result) {
            Helpers.setLoading(true);
            this.archiveOrderSet(att_id, i);
          }
        },
        (res) => {
          console.log(res);
        }
      );
    } else {
      Helpers.setLoading(true);
      this.archiveOrder(att_id, 1, i);
    }
  }

  archiveOrderSet(id, i) {
    this.orderS
      .deleteOrder(id)
      .then((res) => {
        this.loadTable();
        Helpers.setLoading(false);
        this.alertS.success(
          this.alertContainer,
          "Order has been deleted",
          true,
          5000
        );
        this.orderList.splice(i, 1);

        this.updateOrderLimit();
      })
      .catch((err) => {
        console.log(err);
        Helpers.setLoading(false);
        this.alertS.error(
          this.alertContainer,
          "Something wrong! Order has been not deleted",
          true,
          5000
        );
      });
  }

  archiveOrder(order_id, archiveId, index) {
    this.orderS
      .changeStatus(order_id, archiveId)
      .then((res) => {
        Helpers.setLoading(false);
        this.alertS.success(
          this.alertContainer,
          `Order #${order_id} has been archived`,
          true,
          5000
        );

        this.orderList.splice(index, 1);
      })
      .catch((err) => {
        console.log(err);
        Helpers.setLoading(false);
        this.alertS.error(
          this.alertContainer,
          `Something wrong! Order #${order_id} has not been archived`,
          true,
          5000
        );
      });
  }

  updateOrderLimit() {
    this.http
      .get("contents")
      .toPromise()
      .then((res) => {
        if (res.status === "OK" && res.result.data.length > 0) {
          const content = {};
          const data = res.result.data.filter((f) => {
            return f["config"].status === 1;
          });

          for (const c of data) {
            const tag = this.formatTag(c.config.tag);
            content[tag] = isJson(c.contents)
              ? JSON.parse(c.contents)
              : c.contents;
          }
          const total =
            content["site_specific"]["confg"]["order"]["total_order"];
          this.settingService.onOrderLimitChange({
            updateOrderLimit: true,
            total_orders: total,
          });

          return content;
        } else {
          return {};
        }
      })
      .catch((err) => console.log(err));
  }

  formatTag(text: String) {
    return text.replace(/(\-[a-z])/g, function ($1) {
      return $1.toUpperCase().replace("-", "");
    });
  }

  loadTable() {
    let d = calculatePage(this.pagi.page, this.pagi.limit, this.pagi.total);
    // console.log(d);
    if (d.change) {
      const query = FORMATESORT(this.sort);
      this.dataRender(d.page, d.limit, this.filter, query);
    } else {
      this.pagi.total--;
    }
  }

  formateAddress(...address) {
    return address
      .filter((a) => {
        return a && a !== "";
      })
      .join(", ");
  }

  getStatusData() {
    this.loader = true;
    this.orderS.getOrderStatus().subscribe(
      (res) => {
        this.statusArray = res;
        this.statusArray.map((status, i) => {
          this.modStatusArray.push(status);
          if (status.child) {
            // let z = i;
            status.child.map((child, j) => {
              const mod_child = {
                id: child.id,
                label: child.label,
                isChild: true
              }
              this.modStatusArray.push(mod_child);
            })
            
          }
        });
        this.loader = false;
      },
      (err) => console.log(err)
    );
  }

  changeStatus(order) {
    const modalStatus = this.modalService.open(StatusDialogBoxComponent, {
      centered: true,
    });
    modalStatus.componentInstance.orderStatus = order.status;
    const status = this.modStatusArray.filter(s => s.label !== 'Paid Other')
    modalStatus.componentInstance.status = status;
    modalStatus.result.then(
      (result) => {
        if (result) {
          Helpers.setLoading(true);
          this.statusChange(order, result);
          console.log(order);
          console.log(result);
        }
      },
      (res) => {
        //     console.log(res);
      }
    );
  }

  statusChange(order, id) {
    this.orderS
      .changeStatus(order.id, id)
      .then((res) => {
        order.status = id;
        Helpers.setLoading(false);
        this.alertS.success(
          this.alertContainer,
          `Status of ${order.id} has been changed`,
          true,
          5000
        );
      })
      .catch((err) => {
        console.log(err);
        Helpers.setLoading(false);
        this.alertS.error(
          this.alertContainer,
          `Something wrong! Status of ${order.id} has been not changed`,
          true,
          5000
        );
      });
  }

  checkPickUp(s) {
    let status = ['3', '4', '4-1', '4-2', '4-3', '6-1', '6-2', '6-3', '7'];
    if (status.includes(s)) {
      return true;
    }
    return false;
  }

  changePickup(order, ret) {
    // console.log(order);
    if (ret) {
      if (order.payment) {
        sessionStorage.setItem("lastUrl", this.currentPath);
        sessionStorage.setItem("pay", JSON.stringify(order.payment));
        if (
          (order.payment.transaction_id != "" ||
            order.payment.transaction_id != "0") &&
          order.payment.payment_method == "Authorized"
        ) {
          this.router.navigate([
            "/admin/reservations/" +
              order.id +
              "/details/edit/payment/capture/" +
              order.payment.id,
          ]);
        } else {
          this.router.navigate([
            "/admin/reservations/" + order.id + "/details",
          ]);
        }
      }
    } else {
      // const modalRef = this.modalService.open(DialogBoxComponent, {
      //   centered: true,
      //   size: 'sm'
      // });
      // modalRef.componentInstance.massage = 'The customer has picked up this order?';
      // modalRef.result
      //   .then((result) => {
      //     if (result) {
      //       this.statusChange(order, 5);
      //     }
      //   }, (res) => {
      //     console.log(res);
      //   });
      this.router.navigate([
        `/admin/reservations/${order.id}/details/assign-asset/list`,
      ]);
    }
  }

  private _dateRange() {
    let callOnce = false;
    // predefined ranges
    let startDate = moment().format("YYYY-MM-DD");
    let endDate = moment().format("YYYY-MM-DD");
    let selectDate = false;
    let selectedDateRange =
      moment().format("MM-DD-YYYY") + " - " + moment().format("MM-DD-YYYY");
    $("#m_daterangepicker_3").daterangepicker(
      {
        opens: "left",
        ranges: {
          Today: [moment(), moment()],
          Yesterday: [
            moment().subtract(1, "days"),
            moment().subtract(1, "days"),
          ],
          "Last 7 Days": [moment().subtract(6, "days"), moment()],
          "Last 30 Days": [moment().subtract(29, "days"), moment()],
          "This Week": [moment().startOf("week"), moment().endOf("week")],
          "This Month": [moment().startOf("month"), moment().endOf("month")],
          "Next Month": [
            moment().add(1, "month").startOf("month"),
            moment().add(1, "month").endOf("month"),
          ],
          "Last Month": [
            moment().subtract(1, "month").startOf("month"),
            moment().subtract(1, "month").endOf("month"),
          ],
        },
        autoUpdateInput: true,
        buttonClasses: "m-btn btn",
        applyClass: "btn-brand",
        cancelClass: "btn-danger",
      },

      (start, end, label) => {
        selectDate = true;
        startDate = start.format("YYYY-MM-DD");
        endDate = end.format("YYYY-MM-DD");
        selectedDateRange =
          start.format("MM-DD-YYYY") + " - " + end.format("MM-DD-YYYY");
        this.callDateRAngeFilter(startDate, endDate, selectedDateRange);
        setTimeout(() => {
          selectDate = false;
        }, 200);
      }
    );

    $(".ranges ul li ").click(() => {
      setTimeout(() => {
        if (!selectDate) {
          this.callDateRAngeFilter(startDate, endDate, selectedDateRange);
        }
      });
    });
    $(".range_inputs .applyBtn  ").click(() => {
      setTimeout(() => {
        if (!selectDate) {
          this.callDateRAngeFilter(startDate, endDate, selectedDateRange);
        }
      });
    });
  }

  today() {
    this.isRentalDateEditMode = false;
    $(".btn-brand").removeClass("orderlist-active-btn");
    this.render.addClass(event.target, "orderlist-active-btn");
    const startDate = moment().format("YYYY-MM-DD");
    const endDate = moment().format("YYYY-MM-DD");
    this.DateRAngeFilter(startDate, endDate, this.filter);
  }

  thisWeek() {
    this.isRentalDateEditMode = false;
    $(".btn-brand").removeClass("orderlist-active-btn");
    this.render.addClass(event.target, "orderlist-active-btn");
    let startDate = moment().startOf("week");
    let endDate = moment().endOf("week");
    startDate = startDate.format("YYYY-MM-DD");
    endDate = endDate.format("YYYY-MM-DD");
    this.DateRAngeFilter(startDate, endDate, this.filter);
  }

  nextWeek() {
    this.isRentalDateEditMode = false;
    $(".btn-brand").removeClass("orderlist-active-btn");
    this.render.addClass(event.target, "orderlist-active-btn");
    let startDate = moment().add(1, "week").startOf("week");
    let endDate = moment().add(1, "week").endOf("week");
    startDate = startDate.format("YYYY-MM-DD");
    endDate = endDate.format("YYYY-MM-DD");
    this.DateRAngeFilter(startDate, endDate, this.filter);
  }

  all() {
    this.isRentalDateEditMode = false;
    const startDate = "";
    const endDate = "";
    $(".btn-brand").removeClass("orderlist-active-btn");
    this.render.addClass(event.target, "orderlist-active-btn");
    // this.getOrderList(1, 20, null );
    // this.filter = "";
    // this.getOrderList(1, 20, this.filter);
    // this.orderS.filterDateRangeSub.next({startDate:"",endDate:"",reload:false,reset:true});
    this.DateRAngeFilter(startDate, endDate, this.filter);
    // this.getOrderListFromRoute();
  }

  DateRAngeFilter(startDate, endDate, status) {
    this.orderS.filterDateRangeSub.next({
      startDate: startDate,
      endDate: endDate,
      reload: true,
      reset: false,
      status: status
    });
  }

  callDateRAngeFilter(startDate, endDate, range) {
    if (startDate && endDate && range) {
      this.showDateRangeLabel(range);
      this.orderS.filterDateRangeSub.next({
        startDate: startDate,
        endDate: endDate,
        reload: true,
        reset: false,
        status: this.filter
      });
    }
  }

  showDateRangeLabel(range) {
    $("#show_dateRaneLabel").html(range);
  }

  copyOrder(order: Order, availableByForce?: boolean): void {
    Helpers.setLoading(true);
    this.orderS
      .copyOrder(order.id, availableByForce)
      .catch((err: HttpErrorResponse) => {
        Helpers.setLoading(false);
        this.alertS.error(
          this.alertContainer,
          err.error.result ? err.error.result.message : err.message,
          true,
          3000
        );
      })
      .then((res) => {
        Helpers.setLoading(false);
        if (res.status === "OK") {
          this.getOrderListFromService();
          this.alertS.success(
            this.alertContainer,
            res.result.message,
            true,
            3000
          );
        } else {
          if (res.result.hasOwnProperty('data')) {
            if (res.result.data.hasOwnProperty('available')) {
              if (!res.result.data.available) {
                this.openModalCopyConfirmation(res.result.message, order);
              }
            }  else {
              this.alertS.error(
                this.alertContainer,
                res.result ? res.result.message : "Something went wrong!!",
                true,
                3000
              );
            }
          } else {
            this.alertS.error(
              this.alertContainer,
              res.result ? res.result.message : "Something went wrong!!",
              true,
              3000
            );
          }
        }
      });
  }

  private openModalCopyConfirmation(message: string, order: Order): void {
    const modalRef = this.modalService.open(DialogBoxComponent, {
      centered: true,
      size: "sm",
    });
    modalRef.componentInstance.massage = message;      
    modalRef.componentInstance.positive = "Proceed";
    modalRef.componentInstance.negative = "Cancel";
    modalRef.result.then(
      (result) => {
        if (result) {
          console.log(result);
          this.copyOrder(order, true);
        }
      },
      (res) => {
        console.log(res);
      }
    );
  }

  private getOrderListFromService(): void {
    this.orderS.getOrderListWithType(this.pagi.page, this.pagi.limit, '').subscribe(
      (res: any) => {
        this.dataList(res);
      },
      (err: HttpErrorResponse) => {
        this.alertS.error(
          this.alertContainer,
          err.error.result ? err.error.result.message : err.message,
          true,
          3000
        );
      }
    );
  }

  getPaymentStatus(statusId: number) {
    let status: any;
    status = Payment_status.find(x => +statusId === x.id);
    if (status) {
      return status.label;
    } else {
      return '';
    }
  }

  checkStatusPayment(statusId: number) {
    switch (+statusId) {
      case 1:
        return "m-badge--success";
      case 2:
        return "m-badge--danger";
      case 3:
        return "m-badge--danger";
    }
  }

  goToPayment(order) {
    this.router.navigate(["/admin/reservations/" + order.id + "/details/edit/payment"]);
  }

  filterRentalDate(e) {
    $(".btn-brand").removeClass("orderlist-active-btn");
    $("#daterangeWrapper").addClass("orderlist-active-btn");
    const startDate = moment(e.startDate).format("YYYY-MM-DD");
    const endDate = moment(e.endDate).format("YYYY-MM-DD");
    this.DateRAngeFilter(startDate, endDate, this.filter);
  }

  onClickEditDate() {
    this.isRentalDateEditMode = !this.isRentalDateEditMode;
  }

  getInitialStatus(e) {
    this.filter = e;
  }

  startDateOrderBy(): void {
    this.render.removeClass(this.endDateArrow.nativeElement, "fa-sort-desc");
    this.render.removeClass(this.endDateArrow.nativeElement, "fa-sort-asc");
    const startDateElem = this.startDateArrow.nativeElement;
    if (startDateElem.classList.contains("fa-sort-desc")) {
      const sort = this.filter + `&sort_by=rent_start&sort_order=asc`;
      this.render.removeClass(startDateElem, "fa-sort-desc");
      this.render.addClass(startDateElem, "fa-sort-asc");
      this.getOrderList(1, 20, sort);
    } else if (startDateElem.classList.contains("fa-sort-asc")) {
      const sort = this.filter + `&sort_by=rent_start&sort_order=desc`;
      this.render.removeClass(startDateElem, "fa-sort-asc");
      this.render.addClass(startDateElem, "fa-sort-desc");
      this.getOrderList(1, 20, sort);
    } else {
      this.render.addClass(startDateElem, "fa-sort-desc");
      const sort = this.filter + `&sort_by=rent_start&sort_order=desc`;
      this.getOrderList(1, 20, sort);
    }
  }

  endDateOrderBy(): void {
    this.render.removeClass(this.startDateArrow.nativeElement, "fa-sort-desc");
    this.render.removeClass(this.startDateArrow.nativeElement, "fa-sort-asc");
    const endDateElem = this.endDateArrow.nativeElement;
    if (endDateElem.classList.contains("fa-sort-desc")) {
      const sort = this.filter + `&sort_by=rent_end&sort_order=asc`;
      this.render.removeClass(endDateElem, "fa-sort-desc");
      this.render.addClass(endDateElem, "fa-sort-asc");
      this.getOrderList(1, 20, sort);
    } else if (endDateElem.classList.contains("fa-sort-asc")) {
      const sort = this.filter + `&sort_by=rent_end&sort_order=desc`;
      this.render.removeClass(endDateElem, "fa-sort-asc");
      this.render.addClass(endDateElem, "fa-sort-desc");
      this.getOrderList(1, 20, sort);
    } else {
      this.render.addClass(endDateElem, "fa-sort-desc");
      const sort = this.filter + `&sort_by=rent_end&sort_order=desc`;
      this.getOrderList(1, 20, sort);
    }
  }
}
