import {
  Component,
  OnInit,
  Output,
  EventEmitter,
  AfterViewInit,
  Input,
  OnDestroy,
  ViewChild,
  ElementRef
} from "@angular/core";
import { OrderSearch } from "../../models/order-models";
import { HttpInspectorService } from "../../../../../modules/http-with-injector/http-inspector.service";
import { FORMAT_SEARCH, GET_USER } from "../../../../../globals/_classes/functions";
import { OrderService } from "../../order.service/order.service";
import { ActivatedRoute } from "@angular/router";
import { Subscription, interval } from "rxjs";
import { IStatus } from '../../../../../modules/status-option/status-option.component';
import { PLATFORM_ID,Inject } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { SidebarService } from "../../../sidebar-service/sidebar.service";
import { AlertService } from "./../../../../../modules/alert/alert.service";

declare let $: any;
declare var moment: any;

interface SalesMan {
  id: number;
  name: string;
}

@Component({
  selector: "order-filter",
  templateUrl: "./order-filter.component.html",
  styleUrls: ["./order-filter.component.css"]
})
export class OrderFilterComponent implements OnInit, AfterViewInit, OnDestroy {
  orderSearch: OrderSearch;
  filter: string;
  salesMan: SalesMan[] = [];
  search: boolean;
  status: any = [];
  location = [];
  sub: Subscription;

  statusArray: IStatus[] = [];
  modStatusArray: IStatus[] = [];

  @Input("staustId") staustId;
  @Input() order_type: number;
  @Output("loadList") loadList: EventEmitter<string> = new EventEmitter();
  @Output("initialStatus") initialStatus: EventEmitter<string> = new EventEmitter();
  selectedStatus: number[] = [];
  orderedStatusArr: IStatus[];
  @ViewChild("hasCusAlert") alertContainer: ElementRef;

  constructor(@Inject(PLATFORM_ID) private platform: Object,private sidebarS:SidebarService,private route: ActivatedRoute, private orderS: OrderService,private alertS: AlertService) {
    this.route.params.subscribe(param => {
      if (param.status && param.status === 'all') {
        this.filterInitialization();
      }
    })
  }

  ngOnInit() {
    // $(function () {
    //   $(".search-title").on("click", function () {
    //       $(".search-content-body").slideToggle();
    //   });
    // })
    if (isPlatformBrowser(this.platform)) {
      $(document).ready(function() {
        $('.search-title').click(function() {
          $('.search-content-body').slideToggle("slow");
        });
      });
    }
    
    this.getStatus();
    this.getSalesmanList();
    this.getLocation();
    this.sub = this.route.paramMap.subscribe(val => {
      this.reset();
    });
    this.orderS.filterDateRangeSub.subscribe(res => {
      if (res.reload) {
        this.orderSearch.start_date = res.startDate;
        this.orderSearch.end_date = res.endDate;
        this.searchUsers();
      }
      // console.log(res);
    });
  }

  ngAfterViewInit() {
    this.datePicker();
    this._dateRange();
    this.filterInitialization();
  }

  private filterInitialization() {
    if (isPlatformBrowser(this.platform)) {
    let intervl = setInterval(
      () => {
        if (this.statusArray.length > 0) {
          this.statusArray.map(s => {
            if (s.label !== 'Archived' && s.label !== 'Paid Other') {
              this.selectedStatus.push(+s.id);
              $(`#${s.domid}`).addClass('orderlist-active');
            }
          });
          const statuses = this.selectedStatus.join(',');
          this.orderSearch.status = statuses;
          this.filter = FORMAT_SEARCH(this.orderSearch);
          if (this.filter) {
            this.initialStatus.emit(this.filter);
          }
          window.clearInterval(intervl);
        }
      }, 100);
    }
  }

  private _dateRange() {
    // predefined ranges
    const set_start_date = moment().startOf("month");
    const set_end_date = moment().endOf("month");
    $("#m_daterangepicker_3").daterangepicker(
      {
        opens: "left",
        startDate: set_start_date,
        endDate: set_end_date,
        ranges: {
          Today: [moment(), moment()],
          Yesterday: [
            moment().subtract(1, "days"),
            moment().subtract(1, "days")
          ],
          "Last 7 Days": [moment().subtract(6, "days"), moment()],
          "Last 30 Days": [moment().subtract(29, "days"), moment()],
          "This Month": [moment().startOf("month"), moment().endOf("month")],
          "Last Month": [
            moment()
              .subtract(1, "month")
              .startOf("month"),
            moment()
              .subtract(1, "month")
              .endOf("month")
          ]
        },
        autoUpdateInput: true,
        buttonClasses: "m-btn btn",
        applyClass: "btn-brand",
        cancelClass: "btn-danger"
        // startDate: start,
        // endDate: end,
      },
      (start, end, label) => {
        this.orderSearch.start_date = start.format("YYYY-MM-DD");
        this.orderSearch.end_date = end.format("YYYY-MM-DD");
        $("#m_daterangepicker_3 .form-control").val(
          "From " +
            start.format("YYYY-MM-DD") +
            " To " +
            end.format("YYYY-MM-DD")
        );
      }
    );

    $("#m_daterangepicker_3 .form-control").val(
      "From " +
        set_start_date.format("YYYY-MM-DD") +
        " To " +
        set_end_date.format("YYYY-MM-DD")
    );
  }

  ngOnDestroy() {
    this.sub.unsubscribe();
  }

  getLocation() {
    this.orderS.getLocations().subscribe(
      res => {
        if (res.status == "OK") {
          this.location = res.result.data.map(m => {
            return { id: m.id, name: m.name };
          });
        } else {
          this.location = [];
        }
      },
      err => {
        this.location = [];
        console.log(err);
      }
    );
  }

  getStatus() {
    this.orderS.getOrderStatus().subscribe(
      res => {
        this.status = res;
        this.statusArray = res;
        this.orderedStatusArr = this.statusArray.sort((a, b) => a.serial_no - b.serial_no);
        this.statusArray.map((status, i) => {
          const domidArr = status.label.split(' ');
          if (domidArr.length > 1) {
            status['domid'] = domidArr.join('-');
          } else {
            status['domid'] = status.label
          }
          this.modStatusArray.push(status);
          if (status.child) {
            status.child.map((child, j) => {
              const mod_child = {
                id: child.id,
                label: child.label,
                isChild: true
              }
              this.modStatusArray.push(mod_child);
            });
          }
          // if (status.label !== 'archived') {
          //   this.selectedStatus.push(+status.id);
          //   $(`#${status.domid}`).addClass('orderlist-active');
          // }
        });
      },
      err => console.log(err)
    );
  }

  getSalesmanList() {
    this.orderS.getAdminList().subscribe(
      res => {
        this.salesMan = res.data;
      },
      err => console.log(err)
    );
  }

  searchUsers() {
    if (this.order_type === 2) {
      this.orderSearch.type = '2';
    }
    this.filter = FORMAT_SEARCH(this.orderSearch);
    if (this.filter) {
      this.loadList.emit(this.filter);
      this.search = true;
    }
  }

  resetSearch() {
    this.reset();
    this.filter = null;
    $("#show_dateRaneLabel").html('Select Pickup Date');
    // this.selectedStatus = [];
    //   this.statusArray.map(s => {
    //     if (s.label !== 'Archived' && s.label !== 'Paid Other') {
    //       this.selectedStatus.push(+s.id);
    //       $(`#${s.domid}`).addClass('orderlist-active');
    //     }
    //   });
      const statuses = this.selectedStatus.join(',');
      this.orderSearch.status = statuses;
      this.searchUsers();
    // this.orderS.filterDateRangeSub.next({startDate:"",endDate:"",reload:false,reset:true, status: statuses})
    // if (this.search) {
    //   this.loadList.emit("");
    //   this.search = false;
    // }
  }

  reset() {
    this.orderSearch = new OrderSearch();
    this.orderSearch.created = null;
  }

  orderDateChange() {
    $("#order-date")
      .datepicker()
      .on("changeDate", e => {
        let date = e.date;
        this.orderSearch.created =
          date.getFullYear() +
          "-" +
          (date.getMonth() + 1) +
          "-" +
          date.getDate();
        // console.log(this.orderSearch.created);
      });
  }

  private datePicker() {
    $("#order-date").datepicker({
      todayHighlight: true,
      orientation: "bottom left",
      format: "yyyy-mm-dd",
      templates: {
        leftArrow: '<i class="la la-angle-left"></i>',
        rightArrow: '<i class="la la-angle-right"></i>'
      }
    });
  }

  getStatusValue($event) {
    this.orderSearch.status = $event;
    // this.status.map(s => {
    //   $(`#${s.domid}`).removeClass('orderlist-active');
    // });
    // this.status.find(s => {
    //   if ($event.startsWith(s.id.toString())) {
    //     $(`#${s.domid}`).addClass('orderlist-active');
    //   }
    // });
  }

  // openAdvanceSearch() {
  //   $('.search-panel#filterSearch').toggleClass('dis-block');
  //   $('#filterSearch').toggleClass('la-angle-down la-angle-up');
  // }

  selectOrderStatus(status: any) {
    if(this.selectedStatus.indexOf(status.id) === -1) {
      $(`#${status.domid}`).addClass('orderlist-active');
      this.selectedStatus.push(status.id);
      const statuses = this.selectedStatus.join(',');
      this.orderSearch.status = statuses;
      this.searchUsers();
    } else {
      $(`#${status.domid}`).removeClass('orderlist-active');
      this.selectedStatus.splice(this.selectedStatus.indexOf(status.id), 1);
      const statuses = this.selectedStatus.join(',');
      this.orderSearch.status = statuses;
      this.searchUsers();
    }
  }

  resetStatusFilter(): void {
    if (this.statusArray.length > 0) {
      this.selectedStatus = [];
      this.statusArray.map(s => {
        if (s.label !== 'Archived' && s.label !== 'Paid Other') {
          this.selectedStatus.push(+s.id);
          $(`#${s.domid}`).addClass('orderlist-active');
        }
      });
      const statuses = this.selectedStatus.join(',');
      this.orderSearch.status = statuses;
      this.searchUsers();
    }
  }

  allStatusFilter(): void {
    if (this.statusArray.length > 0) {
      this.selectedStatus = [];
      this.statusArray.map(s => {
        if (s.label !== 'Paid Other') {
          this.selectedStatus.push(+s.id);
          $(`#${s.domid}`).addClass('orderlist-active');
        }
      });
      const statuses = this.selectedStatus.join(',');
      this.orderSearch.status = statuses;
      this.searchUsers();
    }
  }
  openSidebar() {
    const orderLeft=sessionStorage.getItem('order_left');
    const order_left=parseInt( orderLeft);
    const user = GET_USER();
    const store = GET_USER().location_id;
    if (store) {
      this.sidebarS.sidebarOpenChange(true);
      this.sidebarS.openCartSidebar();
    } else {
      this.alertS.info(
        this.alertContainer,
        "Store has not been selected. Please select store.",
        true,
        5000
      );
    }
  }
  
}
