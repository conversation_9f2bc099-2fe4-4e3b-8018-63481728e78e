.form-group {
    padding-bottom: 1.2rem!important;
}

.add-list-btn {
    display: table-cell;
    vertical-align: middle;
}

.search-panel .row>div {
    padding-right: 0px;
    padding-left: 0px;
    ;
}

.search-panel select option[data-default] {
    color: #888;
}

.search-panel select option[value=""][disabled] {
    display: none;
}

.m-portlet__head.search-title {
    cursor: pointer;
}


/*-- Adition order filter --*/

@media (max-width: 575px) {
    .m-portlet .m-portlet__body {
        padding: 15px 15px;
    }
}

.m-portlet {
    margin-bottom: 15px;
}

.orderlist-selectbox-area {
    padding: 0;
}

.orderlist-selectbox {
    margin: 0;
    padding: 0;
    padding-bottom: 0;
}

.orderlist-selectbox li {
    list-style: none;
    display: inline-block;
    padding: 0px 0;
    padding-right: 10px;
    margin-bottom: 15px;
}

.orderlist-selectbox li a {
    text-decoration: none;
    color: #333;
    background-color: #fff;
    padding: 8px 16px;
    border: 1px solid #f3f3f8;
    cursor: pointer;
    box-shadow: 0px 1px 15px 1px rgba(69, 65, 78, 0.08);
    font-weight: 400;
    position: relative;
    border-radius: 4px;
    display: block;
    font-size: 12px;
}

.orderlist-selectbox li a span {
    display: none;
}

.orderlist-selectbox li a.orderlist-active {
    background-color: #716acad6;
    color: #fff;
}

.orderlist-selectbox li a.orderlist-active span {
    display: block;
    font-size: 10px;
    position: absolute;
    left: 4px;
    top: 12px;
    right: 0;
}

.orderlist-selectbox {
    display: inline-block;
}

.orderlist-allreset-area {
    float: right;
}

@media (max-width: 1340px) {
    .orderlist-allreset-area {
        float: unset;
    }
}

.search-title {
    cursor: pointer;
}

.btn-addproduct {
    color: #fff !important;
    background-color: #35BFA3;
    border-color: #35BFA3;
    padding: 12px 25px;
    float: right;
    font-size: 14px;
}

.btn-addproduct i {
    font-size: 12px;
}

.btn-addproduct:hover,
.btn-addproduct:hover {
    color: #fff !important;
    background-color: #3dd8b8 !important;
    border-color: #3dd8b8 !important;
}

@media (max-width: 991px) {
    .btn-addproduct {
        padding: 12px 25px;
        font-size: 14px;
    }
    .btn-addproduct i {
        font-size: 12px;
    }
}

@media (max-width: 767px) {
    .btn-addproduct {
        padding: 12px 20px;
        font-size: 13px;
    }
    .btn-addproduct i {
        font-size: 10px;
    }
}

@media (max-width: 575px) {
    .btn-addproduct {
        padding: 10px 15px;
        float: right;
        font-size: 12px;
        margin-top: 15px;
    }
    .btn-addproduct i {
        font-size: 10px;
    }
}


/* .orderlist-allreset-btn {
    float: right;
} */