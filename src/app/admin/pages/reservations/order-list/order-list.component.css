.m-portlet__head-title {
    width: 100%;
}

.m-portlet__head-text {
    display: inline-block !important;
    width: 10%;
}

.button-cus {
    display: inline-block !important;
    width: 89%;
}

.view-cart {
    display: inline-block !important;
}

.button-midle {
    display: table-cell;
    vertical-align: middle;
    font-size: 1.3rem;
    font-weight: 500;
    font-family: Roboto;
    /* padding-top: 20px;
    padding-bottom: 20px; */
}

.button-midle .btn {
    margin-left: 10px;
    padding: 24px 20px;
    color: #575962;
    background-color: transparent;
    border: none;
    border-bottom: 1px solid #fff;
    border-radius: 0;
    font-size: 14px;
}

.button-midle .btn:focus,
.button-midle .btn:hover {
    color: #575962 !important;
}

.button-midle .orderlist-active-btn {
    border-bottom: 1px solid #111;
    color: #111;
}

.custom-alert {
    position: fixed;
    top: 2%;
    right: 30px;
    z-index: 5000;
}

.dis-none {
    display: none;
}

.dis-block {
    display: table-row!important;
}

.m-dropdown.m-dropdown--align-center.m-dropdown--large .m-dropdown__wrapper {
    margin-left: -50px;
}

.description-field-title {
    /* color:#716aca!important; */
    font-weight: 600;
    padding-right: 5px;
}

.custom-alert {
    position: fixed;
    top: 90px;
    right: 30px;
    z-index: 5000;
}

.m-badge.m-badge--warning {
    color: #fff!important;
}

.m-portlet .m-portlet__head .m-portlet__head-caption .m-portlet__head-title .m-portlet__head-text {
    width: auto;
}

.orderlist-selectbox-area {
    padding: 0;
}

.orderlist-selectbox {
    padding: 0;
    margin-bottom: 5px;
}

.orderlist-selectbox li {
    list-style: none;
    display: inline-block;
    padding: 12px 0;
    padding-right: 20px;
    margin-bottom: 25px;
}

.orderlist-selectbox li a {
    text-decoration: none;
    color: #333;
    background-color: #fff;
    padding: 12px 25px;
    border: 1px solid #f3f3f8;
    cursor: pointer;
    box-shadow: 0px 1px 15px 1px rgba(69, 65, 78, 0.08);
    font-weight: 400;
    position: relative;
    border-radius: 4px;
}

.orderlist-selectbox li a span {
    display: none;
}

.orderlist-selectbox li a.orderlist-active {
    background-color: #716acad6;
    color: #fff;
}

.orderlist-selectbox li a.orderlist-active span {
    display: block;
    font-size: 14px;
    position: absolute;
    left: 8px;
    top: 15px;
    right: 0;
}

.orderlist-datetime-range {
    position: relative;
    display: inline-block;
    padding-right: 20px;
}

.orderlist-datetime-range .orderlist-active-btn {
    padding: 25px 0px;
}

.fa-sort-down:before,
.fa-sort-desc:before {
    margin-top: -7px;
    vertical-align: middle;
    line-height: 0;
    display: inline-block;
}

.fa-sort-up:before,
.fa-sort-asc:before {
    margin-top: 8px;
    vertical-align: middle;
    line-height: 0;
    display: inline-block;
}

@media (min-width: 768px) and (max-width: 1199px) {
    .orderlist-datetime-range {
        padding-right: 10px;
    }
    .button-midle.button-cus {
        width: 87%;
    }
    .button-midle .btn {
        margin-left: 0;
        padding: 24px 15px;
    }
}

@media (min-width: 300px) and (max-width: 767px) {
    .orderlist-datetime-range {
        padding-right: 10px;
    }
    .button-midle.button-cus {
        width: 86%;
    }
    .button-midle .btn {
        margin-left: 0;
        padding: 24px 5px;
        font-size: 13px;
    }
}

@media (max-width: 575px) {
    .orderlist-datetime-range {
        width: 100%;
        background-color: #fff;
        display: inline-block;
        float: left;
        padding: 20px 25px;
        border-top: 1px solid #f2f3f8;
    }
    .orderlist-datetime-range label {
        margin-bottom: 0;
        line-height: 0;
    }
    .orderlist-head .button-midle .btn {
        font-size: 12px !important;
        margin-left: 12px !important;
        padding: 20px 10px !important;
    }
    .orderlist-datetime-range .orderlist-active-btn {
        padding: 5px 0px;
        border-bottom: none;
    }
}