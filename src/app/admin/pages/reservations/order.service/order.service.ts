import { Injectable, Optional } from "@angular/core";
import { HttpService } from "../../../../modules/http-with-injector/http.service";
import { OrderServiceConfig, Customer, Payment } from "../models/order-models";
import {
  Resolve,
  ActivatedRouteSnapshot,
  RouterStateSnapshot
} from "@angular/router";
import { map, catchError } from "rxjs/operators";
import { BehaviorSubject, of } from "rxjs";
import {
  FormatPrice,
  FormateAttribute,
  isJson,
  GET_USER,
  getCartDate,
  AdminFormatPrice
} from "../../../../globals/_classes/functions";

class Update {
  from: string;
  data: any;
  update: boolean;
}
declare var moment: any;
@Injectable()
export class OrderService implements Resolve<any> {
  config: OrderServiceConfig;
  private orderDateRangeConfig = {
    startDate: "",
    endDate: "",
    reload: false,
    reset: false,
    status: ''
  };
  isPaymentGatewayStripe : boolean = false;
  filterDateRangeSub = new BehaviorSubject(this.orderDateRangeConfig);
  private subject = new BehaviorSubject<any>(null);
  addUpdateOrder = this.subject.asObservable();

  private updateSideBarData = new BehaviorSubject<any>(new Update());
  updateData = this.updateSideBarData.asObservable();

  private updateorderDetailsSubject = new BehaviorSubject<any>(new Update());
  updateOrderDetails = this.updateorderDetailsSubject.asObservable();

  private reloadPaymentSubject = new BehaviorSubject<any>(null);
  reloadSidebarPayment = this.subject.asObservable();

  private showItemSearchboxSubject = new BehaviorSubject<any>(null);
  showItemSearchbox = this.subject.asObservable();

  private noteAddSubject = new BehaviorSubject<any>(null);
  noteAdd = this.noteAddSubject.asObservable();

  onNoteAdd(data) {
    this.noteAddSubject.next(data);
  }

  loadSidebarPayment(data) {
    this.subject.next(data);
  }

  loadOrderDetails(data) {
    this.updateorderDetailsSubject.next(data);
  }

  ItemSearchboxShow(data) {
    this.subject.next(data);
  }

  updateOrder(data) {
    this.subject.next(data);
  }
  update(data: Update) {
    this.updateSideBarData.next(data);
  }

  constructor(
    @Optional() config: OrderServiceConfig,
    private http: HttpService
  ) {
    this.config = config;
  }

  resolve(route?: ActivatedRouteSnapshot) {
    if (route.routeConfig.path == "rental-orders") {
      return this.getOrderList(1, 20, 1, "", "");
    } else if (route.routeConfig.path == "pickups") {
      const id: number = 2;
      let s = "&status=" + id;
      return this.getOrderList(1, 20, 0, s, "");
    } else if (route.routeConfig.path == "returns") {
      const id: number = 5;
      let s = "&status=" + id;
      return this.getOrderList(1, 20, 0, s, "");
    } else {
      const id = this.getStatusId(route.params.status);
      let s = id === 0 || id ? "&status=" + id : "";
      let type = 1;
      if (route.params.status && route.params.status === 'quote') {
        type = 2
      }
      return this.getOrderListWithType(1, 20, type, 0, s, "");
    }
  }

  getStatusId(s) {
    // console.log(s);
    if (!s || s == "all") {
      return null;
    } else {
      let li = s.lastIndexOf("-");
      return parseInt(s.slice(li + 1));
    }
  }

  getStoreCurrencyConfig() {
    return this.http.get("currency-config").toPromise();
  }

  getOrderId(route) {
    let id = route.parent.parent.snapshot.params.order_id;
    id = id ? id : route.parent.parent.parent.parent.snapshot.params.order_id;
    return id;
  }

  getId(route) {
    let id = route.parent.parent.params.order_id;
    id = id ? id : route.parent.parent.parent.parent.params.order_id;
    return id;
  }

  getDate(d) {
    if (d) {
      return new Date(d);
    }
    return "";
  }

  getStatus(s, data) {
    return data[parseInt(s) - 1].label;
  }

  checkStatus(s) {
    if (isNaN(s)) {
      const idArr = s.split('-');
      s = parseInt(idArr[0])
    }
    s = parseInt(s)
    switch (s) {
      case 1:
        return "m-badge--info";
      case 2:
        return "m-badge--warning";
      case 3:
        return "m-badge--success";
      case 4:
        return "m-badge--danger";
      case 5:
        return "m-badge--primary";
      case 6:
        return "m-badge--brand";
      case 7:
        return "m-badge--info";
      default:
        return "m-badge--brand";
    }
  }

  formateCustomer(data, cus: Customer) {
    cus.first_name = data.first_name;
    cus.last_name = data.last_name;
    cus.email = data.email;
    if (data.primary_addres) {
      cus.phone = data.primary_addres.phone;
      cus.mobile = data.primary_addres.mobile;
      cus.address_line1 = data.primary_addres.address_line1;
      cus.city = data.primary_addres.city;
      cus.country_id = data.primary_addres.country_id;
      cus.state_id = data.primary_addres.state_id;
      cus.zipcode = data.primary_addres.zipcode;
    }
    return cus;
  }

  formateNewOrder(data, cus: Customer) {
    let order = {
      id: null,
      created: null,
      name: null,
      address: null,
      country: null,
      state: null,
      city: null,
      zipcode: null,
      status: null,
    };
    order["id"] = data.order_id;
    order["status"] = cus["status"];
    order["created"] = data.order_date;
    order["name"] = cus.first_name + " " + (cus.last_name || "");
    order["address"] = cus.address_line1 || null;
    order["country"] = cus.country_id || null;
    order["state"] = cus.state_id || null;
    order["city"] = cus.city || null;
    order["zipcode"] = cus.zipcode || null;
    // console.log(order);
    return order;
  }

  formatePrice(data) {
    if (data && data.length > 0) {
      return FormatPrice(data);
    }
    return { base: {}, rent: [] };
  }
  adminFormatePrice(data) {
    if (data && data.length > 0) {
      return AdminFormatPrice(data);
    }
    return { base: {}, rent: [] };
  }


  formateAttribute(data) {
    return FormateAttribute(data);
  }

  getCurrentDateTime(date) {
    let obj = { date: "", time: "" };
    obj["date"] =
      date.getFullYear() + "-" + (date.getMonth() + 1) + "-" + date.getDate();
    obj["time"] = date.getHours() + ":" + date.getMinutes();
    return obj;
  }

  formateDate(data) {
    if (data) {
      return data.date + " " + data.time;
    }
    return null;
  }

  formateListDate(d) {
    if (d) {
      return new Date(d);
    }
    return "";
  }

  getPaymentMethod(t, data) {
    if (data && data.length > 0) {
      return data[t - 1];
    }
  }

  getPaidTotal(data) {
    return data
      .map((a) => {
        return a["payment_amount"];
      })
      .reduce((t, n) => {
        return t + n;
      }, 0);
  }

  formatePaymentEdit(data) {
    let payment = new Payment();
    payment.id = data.id;
    payment.order_id = data.order_id;
    payment.type = data.type;
    payment.payment_method = data.payment_method;
    payment.payment_amount = data.payment_amount;
    payment.note = data.note;
    return payment;
  }

  // formateOrderDetails(val, data) {
  //     data.tax = val.tax;
  //     data.total_quantity = val.total_quantity;
  //     data.total_price = val.total_price;
  //     data.delivery_charge = val.delivery_charge;
  //     data.total_discount = val.total_discount;
  //     data.vat = val.vat;
  //     return data;
  // }

  getTotalDeposit(order) {
    return order
      .map((d) => {
        return d.deposit_amount;
      })
      .reduce((total, item) => {
        return total + item;
      }, 0);
  }

  getAvailableQty(pro, sd?, dur?) {
    const qty = pro.products_availabilities
      .filter((d) => {
        return this.checkDate(d.start_date, d.end_date, sd, dur);
      })
      .map((q) => {
        return q.quantity;
      })
      .reduce((t, i) => {
        return t + i;
      }, 0);
    let quant = pro.quantity - qty;
    return quant < 0 ? 0 : quant;
  }

  checkDate(s, e, i, d) {
    let date = new Date();
    if (i) {
      date = new Date(i);
    }
    let cur = new Date(
      date.getFullYear(),
      date.getMonth(),
      date.getDate(),
      12,
      0
    ).getTime();
    let curEnd = cur + 86400000 * (d - 1);
    // console.log(new Date(curEnd), d);
    let st = new Date(
      new Date(s).getFullYear(),
      new Date(s).getMonth(),
      new Date(s).getDate(),
      0,
      0
    ).getTime();
    let end = new Date(
      new Date(e).getFullYear(),
      new Date(e).getMonth(),
      new Date(e).getDate(),
      23,
      59
    ).getTime();
    return (st <= cur && end >= cur) || (end > cur && st < curEnd);
  }

  getTextResponse(text) {
    const arr = [];
    if (text && isJson(text)) {
      const data = JSON.parse(text);
      for (let d in data) {
        let obj = {};
        obj["key"] = d;
        obj["value"] = data[d];
        arr.push(obj);
      }
    }
    return arr;
  }

  /******************** All API for reservations ******************/

  getterminals() {
    return this.http.get(`locations`).pipe(map((res) => res));
  }

  getOrderStatus() {
    return this.http.get("order/status").pipe(map((res) => res.result.data));
  }

  changeStatus(id, s_id) {
    return this.http.get(`orders/${id}/status/${s_id}`).toPromise();
  }

  submitOrder(order_id) {
    return this.http
      .post(`assets/manage/${order_id}/submit-order`, {})
      .toPromise();
  }

  getOrderList(page?, limit?, rent?, status?, filter?, sort?) {
    return this.http
      .get(
        "orders?page_no=" +
          (page ? page : 1) +
          "&limit=" +
          (limit ? limit : 10) +
          "&rental_orders=" +
          rent +
          status +
          filter +
          (sort ? sort : "")
      )
      .pipe(map((res) => res.result));
  }

  getRevisedOrderList(page?, limit?, rent?, status?, filter?, sort?) {
    return this.http
      .get(
        "orders?page_no=" +
          (page ? page : 1) +
          "&limit=" +
          (limit ? limit : 10) +
          "&rental_orders=0")
      .pipe(map((res) => res.result));
  }

  getOrderListWithType(page?, limit?, type?, rent?, status?, filter?, sort?) {
    return this.http
      .get(
        "orders?page_no=" +
          (page ? page : 1) +
          "&limit=" +
          (limit ? limit : 10) +
          "&type=" +
          (type ? type : 1) +
          "&rental_orders=" +
          rent +
          status +
          filter +
          (sort ? sort : "")
      )
      .pipe(map((res) => res.result));
  }

  getShipOrderList(page?, limit?) {
    return this.http
      .get(
        "shipping/orders?page_no=" +
          (page ? page : 1) +
          "&limit=" +
          (limit ? limit : 10)
      )
      .pipe(map((res) => res.result));
  }

  deleteShipment(ship_id) {
    return this.http.delete("shipping/cancel/" + ship_id).toPromise();
  }

  getOrderListWithSearchString(page?, limit?, searchString?) {
    const queryParams = {
      page_no: page ? page : 1,
      limit: limit ? limit : 10,
      rental_orders: 0,
      is_search: true,
      search: searchString,
    };
    return this.http
      .get("orders", { params: queryParams })
      .pipe(map((res) => res.result));
  }

  getAdminList() {
    return this.http.get("users/admin").pipe(map((res) => res.result));
  }

  getCustomer(user_id) {
    return this.http
      .get(`users/customer/${user_id}`)
      .pipe(map((res) => res.result));
  }

  getOrderCustomer(order_id) {
    return this.http
      .get(`orders/customer/${order_id}`)
      .pipe(map((res) => res.result));
  }

  addCustomer(data) {
    return this.http.post(`customer/add`, data).toPromise();
  }

  updateCustomer(order_id, data) {
    return this.http.post(`orders/customer/${order_id}`, data).toPromise();
  }

  deleteOrder(id) {
    return this.http.delete(`orders/${id}`).toPromise();
  }

  getProduct(attr_id, order_id = 0) {
    const loc = GET_USER().location_id;
    let api_url = "";
    const cartDateObj = getCartDate("adminAfterOrder");

    if (cartDateObj != null) {
      api_url =
        "products/view/variant-product/" +
        attr_id +
        "?location=" +
        loc +
        "&start_date=" +
        cartDateObj.startDate +
        "&end_date=" +
        cartDateObj.endDate +
        "&source=admin&type=order&order_id=" +
        order_id;
    } else {
      api_url = `products/view/variant-product/${attr_id}`;
    }

    return this.http.get(api_url).pipe(map((res) => res.result));
  }

  getPosProduct(attr_id, order_id = 0) {
    const loc = GET_USER().location_id;
    let api_url = "";
    const cartDateObj = getCartDate("posAfterOrder");

    if (cartDateObj != null) {
      api_url =
        "products/view/variant-product/" +
        attr_id +
        "?location=" +
        loc +
        "&start_date=" +
        cartDateObj.startDate +
        "&end_date=" +
        cartDateObj.endDate +
        "&source=pos&type=order&order_id=" +
        order_id;
    } else {
      api_url = `products/view/variant-product/${attr_id}`;
    }

    return this.http.get(api_url).pipe(map((res) => res.result));
  }

  getProductList(id) {
    return this.http.get(`orders/${id}`).pipe(map((res) => res.result));
  }

  searchProduct(search) {
    const loc = GET_USER().location_id;
    return this.http.get(`products/search?location=${loc}&${search}`);
  }

  addItem(data, edit) {
    if (edit) {
      return this.http.post(`orders/update-item`, data).toPromise();
    } else {
      return this.http.post(`orders/add-item`, data).toPromise();
    }
  }

  deleteItem(id) {
    return this.http.delete(`orders/item/${id}/delete`).toPromise();
  }

  getPaymentAmount(id) {
    return this.http
      .get(`payments/${id}/amount`)
      .pipe(map((res) => res.result));
  }

  getPaymentAmountSummary(id) {
    return this.http
      .get(`order/${id}/payments?is_paid=true&list=false&log=false&memo=true`)
      .pipe(map((res) => res.result.data));
  }

  getPaymentList(id) {
    return this.http.get(`payments/${id}/all`).pipe(map((res) => res.result));
  }

  addPayment(data) {
    return this.http.post(`payments`, data).toPromise();
  }

  addCardPaymentAfterOrder(data) {
    return this.http.post(`payments/${data.order_id}/1`, data).toPromise();
  }

  updatePayment(data) {
    return this.http.post(`payments/${data.id}`, data).toPromise();
  }

  deletePayment(id) {
    return this.http.delete(`payments/${id}`).toPromise();
  }

  getStripInfo(id) {
    return this.http.get(`customers/${id}`).pipe(map((res) => res.result));
  }

  getStripInfoByEmail(email) {
    return this.http
      .get(`orders/customer/search?search=${email}&from=payment`)
      .pipe(map((res) => res.result));
  }

  getOrderDetails(id) {
    return this.http.get(`orders/${id}/review`).pipe(map((res) => res.result));
  }

  getShipOrderDetails(id) {
    return this.http.get(`shipping/${id}`).pipe(map((res) => res.result));
  }

  getShipOrderDetailsOrder(id) {
    return this.http
      .get(`shipping/${id}?type=order`)
      .pipe(map((res) => res.result));
  }

  getShipRates(id) {
    return this.http.get(`shipping/rate/${id}`).pipe(map((res) => res.result));
  }

  getShipRatesOrder(id, date) {
    let queryParams = {
      type: "order",
    };
    if (date) {
      queryParams["date"] = date;
    }
    return this.http
      .get(`shipping/rate/${id}`, { params: queryParams })
      .pipe(map((res) => res));
  }

  getShipRatesOrderPost(id, data) {
    return this.http.post(`shipping/rate/${id}`, data).pipe(map((res) => res));
  }

  createShipLabel(id, type) {
    return this.http
      .post(`shipping/create-label/${id}/${type}`, {})
      .pipe(map((res) => res));
  }

  updateShipping(data, id) {
    return this.http.post(`shipping/update/${id}`, data).toPromise();
  }

  createShipping(data) {
    return this.http.post(`shipping/create-shipment`, data).toPromise();
  }

  exportInvoice(id) {
    return this.http
      .get(`quickbook/export/invoice/${id}`)
      .pipe(map((res) => res.result));
  }

  getRefundList(id) {
    return this.http
      .get(`orders/${id}/deposits`)
      .pipe(map((res) => res.result));
  }

  getRefunds(id) {
    return this.http
      .get(`orders/refund/list/${id}`)
      .pipe(map((res) => res.result));
  }

  addRefund(data) {
    return this.http.post(`orders/refund`, data).toPromise();
  }

  deleteRefund(id) {
    return this.http.delete(`orders/refund/${id}`).toPromise();
  }

  getLocations() {
    return this.http.get(`locations`).pipe(map((res) => res));
  }

  getCashiers() {
    return this.http
      .get(`users/index?pageNo=1&limit=10&user_type_id=4`)
      .pipe(map((res) => res));
  }

  updateReturnLocation(id, returnLocation) {
    const data = {
      return_to: returnLocation,
    };
    return this.http.post(`orders/${id}/update`, data);
  }

  updatePickupLocation(id, pickupLocation) {
    const data = {
      pickup: pickupLocation,
    };
    return this.http.post(`orders/${id}/update`, data);
  }

  getTransaction(id) {
    return this.http.get(`payment-log/${id}`).pipe(map((res) => res.result));
  }

  callVoid(data) {
    return this.http.post(`orders/void`, data).toPromise();
  }

  callRefund(data) {
    return this.http.post(`orders/refund`, data).toPromise();
  }

  callCaptured(data) {
    return this.http.post(`orders/capture`, data).toPromise();
  }

  getDeliveryDetails(id) {
    return this.http.get(`delivery-details/${id}`).pipe(
      map((res) => res.result.data),
      catchError((e) => of(null))
    );
  }

  // getPDF(id) {
  //     return this.http.getBlob(`pages/pdf?order_id=${id}`);
  // }

  confirmShipment(id) {
    return this.http.get(`confirm-shipment/${id}`).toPromise();
  }

  trackShipment(id) {
    return this.http.get(`track-shipment/${id}`).toPromise();
  }

  createShippingLabel(id, type) {
    return this.http
      .post(`shipping/create-label/${id}`, { type: type })
      .toPromise();
  }

  //**************************Charges API******* */

  getAdditionalChargeList(id) {
    return this.http.get(`orders/view-charges/${id}?type=cart`).toPromise();
  }

  getChargeList(id) {
    return this.http.get(`orders/view-charges/${id}`).toPromise();
  }

  getCharge(order_id, id) {
    return this.http.get(`orders/view-charges/${order_id}/${id}`).toPromise();
  }

  addOrUpdateCharge(data) {
    return this.http.post(`orders/add-charge`, data).toPromise();
  }

  deleteCharge(id) {
    return this.http.delete(`orders/delete-charge/${id}`).toPromise();
  }

  addOrUpdateOrderNote(data, order_id) {
    return this.http.post(`orders/${order_id}/notes`, data).toPromise();
  }

  getOrderNoteList(order_id) {
    return this.http
      .get(`orders/${order_id}/notes?page=1&limit=25&type=note`)
      .toPromise();
  }

  getOrderLogList(order_id, page, limit) {
    const query = {
      page: page ? page : 1,
      limit: limit ? limit : 20,
      type: 'log'
    }
    return this.http
      .get(`orders/${order_id}/notes`, {params: query})
      .toPromise();
  }

  deleteOrderNote(id) {
    return this.http.delete(`notes/${id}`).toPromise();
  }

  quoteToOrder(id) {
    return this.http.post(`orders/accept-quote`, { order_id: id }).toPromise();
  }

  copyOrder(orderId: any, availableByForce?: boolean): Promise<any> {
    let data = {
      duplicate: true
    };
    if (availableByForce) {
      data['available'] = availableByForce;
    }
    return this.http
      .post(`orders/${orderId}/copy`, data)
      .toPromise();
  }
  resendEmail(order_id) {
    return this.http
      .get(`order/${order_id}/email`)
      .toPromise();
  }

  recurringPriceByOrderID(order_id) {
    return this.http
      .get(`orders/recurring/${order_id}`)
      .toPromise();
  }

  modifyRecurringPayment(order_id, data) {
    return this.http
      .post(`orders/recurring/${order_id}`, data)
      .toPromise();
  }
}
