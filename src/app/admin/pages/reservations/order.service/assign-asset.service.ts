import { Injectable } from "@angular/core";
import {
  ActivatedRouteSnapshot,
  Resolve,
  Router,
  RouterStateSnapshot
} from "@angular/router";
import { OrderService } from "./order.service";
import { map, catchError } from "rxjs/operators";
import { HttpService } from "../../../../modules/http-with-injector/http.service";
import { of } from "rxjs";

@Injectable()
export class AssignAssetService implements Resolve<any> {
  constructor(private http: HttpService) {}

  resolve(router: ActivatedRouteSnapshot, state: RouterStateSnapshot) {}

  getAssets(filter?, query?) {
    let url = "";
    if (filter && query) {
    } else if (!filter && query) {
      url = `assets/assign?${query}`;
    } else if (filter && !query) {
      url = `assets/assign?${filter}`;
    } else {
      url = "assets/assign";
    }

    return this.http.get(url).pipe(
      map(res => {
        return res.result;
      }),
      catchError(err => {
        return of([]);
      })
    );
  }
  assignAsset(order_id, asset_id) {
    return this.http
      .post(`assets/manage/${order_id}/assign`, { asset_id: asset_id })
      .toPromise();
  }

  assignAssetWithStatus(order_id, asset_id, change_status) {
    return this.http
      .post(`assets/manage/${order_id}/assign`, { asset_id: asset_id, change_status: change_status })
      .toPromise();
  }

  dischargeAssign(data) {
    return this.http.post("assets/discharge-asset", data).toPromise();
  }
  getAssetsActivity(asset_id) {
    return this.http
      .get(`assets/tracking-history?content_id=${asset_id}&is_tab=1`)
      .pipe(
        map(res => {
          return res.result.data;
        }),
        catchError(err => {
          return of([]);
        })
      );
  }
  searchReturnAsset(serial_no, order_id) {
    return this.http
      .get(`assets/return/search?serial_no=${serial_no}&order_id=${order_id}`)
      .pipe(
        map(res => {
          return res.result.data;
        }),
        catchError(err => {
          return of([]);
        })
      );
  }
  setReturnedAsset(data) {
    return this.http
      .post(`assets/return`, data)
      .toPromise();
  }
  getReturnedAssets(order_id) {
    return this.http.get(`assets/return/${order_id}`).pipe(
      map(res => {
        return res.result.data;
      }),
      catchError(err => {
        return of([]);
      })
    );
  }
  deleteReturnItem(id) {
    return this.http.delete(`assets/return/` + id).toPromise();
  }

  searchAsset(order_id, serial_no) {
    return this.http.get(`assets/manage/${order_id}?serial_no=${serial_no}`);
  }

  addAsset(order_id, order_item_id, serial_no) {
    return this.http
      .post(`assets/manage/${order_id}`, {
        order_item_id: order_item_id,
        serial_no: serial_no
      })
      .toPromise();
  }

  processOrder(order_id) {
    return this.http.get(`assets/manage/${order_id}/check-order`).toPromise();
  }
}
