.edit-btn{
    display: table-cell; vertical-align: middle;
    text-align: right;
}
.edit-btn i{
    color:#041531!important;
}
.edit-btn i:hover{
    color:#ffffff!important;
}

.description-field-title{
    /* color:#716aca!important; */
    font-weight: 600;
    padding-right: 5px;
}

.m-badge.m-badge--warning {
    color: #fff!important;
}

.dis-none{
    display: none;
}
.dis-block{
    display: table-row!important;
}
.m-badge.m-badge--warning {
    color: #fff!important;
}

.custom-alert{
    position: fixed;
    top: 90px;
    right: 30px;
    z-index: 5000;
}
.m-portlet__head-title{
    width: 100%;
}
.m-portlet__head-text{
    width: 90%;
}

.status {
    margin-top: 4px;
    margin-left: 16px;
    color: #fff;
}

.img-resize{
    max-width: 100px!important;
    height: 100px!important;
    object-fit: contain;
}
.table-order-asset th{ border-top: 0px !important;}

.customer-details-title {
    width: 72%;
}
.cancel-btn {
    display: inline-block;
    margin-left: 15px;
    height: 33px;
    border-radius: 3px;
    font-size: 12px;
    padding: 0 15px;
    background-color: #333;
    color: #fff;
    border: none;
}