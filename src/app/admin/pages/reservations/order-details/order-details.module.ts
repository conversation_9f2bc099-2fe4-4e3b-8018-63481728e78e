import { DialogBoxComponent } from './../../../../modules/dialog-box/dialog-box.component';
import { DialogBoxModule } from './../../../../modules/dialog-box/dialog-box.module';
import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { OrderDetailsComponent } from "./order-details.component";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { RouterModule, Routes } from "@angular/router";
import { OrderComponent } from "./order/order.component";
import { CustomerComponent } from "./customer/customer.component";
import { ItemsComponent } from "./items/items.component";
import { PaymentComponent } from "./payment/payment.component";
import { StatusDialogBoxModule } from "../dialog-box/dialog-box.module";
import { StatusDialogBoxComponent } from "../dialog-box/dialog-box.component";
import { CurrencyFormatModule } from "../../../../modules/currency-format/currency-format.pipe";
import { StoreEditComponent } from './order/store-edit/store-edit.component';
import { TotalCostComponent } from './order/total-cost/total-cost.component';
import { AssignAssetService } from "../order.service/assign-asset.service";
import { SettingService } from "../../settings/setting-service/setting.service";
import { OrderNotesModule } from "../order-notes/order-notes.module";
import { ImagePopupComponent } from "../../../../modules/image-popup/image-popup.component";
import { ImagePopupModule } from "../../../../modules/image-popup/image-popup.module";
import { OrderDiscountNoteComponent } from './order-discount-note/order-discount-note.component';
import { LogsComponent } from './logs/logs.component';
import { PaginationModule } from '../../../../modules/pagination/pagination.module';
import { DateFormatModule } from '../../../../modules/date-format/date-format-pipe';
import { TransactionComponent } from './transaction/transaction.component';
import { DateTimeRangeModule } from './../../../../modules/date-time-range/date-time-range.module';
const routes: Routes = [
  {
    path: "",
    component: OrderDetailsComponent,
    children: [
      {
        path: "edit",
        loadChildren:
          () => import('./../order-sidebar/order-sidebar.module').then(m => m.OrderSidebarModule)
      },
      {
        path: "assign-asset",
        loadChildren:
          () => import('./../asset-sidebar/asset-sidebar.module').then(m => m.AssetSidebarModule)
      },
      {
        path: "store-edit",
        component: StoreEditComponent
      },
      {
        path: "total-cost",
        component: TotalCostComponent
      }
    ]
  }
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    StatusDialogBoxModule,
    CurrencyFormatModule,
    FormsModule,
    ReactiveFormsModule,
    OrderNotesModule,
    ImagePopupModule,
    DialogBoxModule,
    PaginationModule,
    DateFormatModule,
    DateTimeRangeModule,
  ],
  entryComponents: [StatusDialogBoxComponent,ImagePopupComponent, DialogBoxComponent],
  exports: [RouterModule],
  declarations: [
    OrderDetailsComponent,
    OrderComponent,
    CustomerComponent,
    ItemsComponent,
    PaymentComponent,
    StoreEditComponent,
    TotalCostComponent,
    OrderDiscountNoteComponent,
    LogsComponent,
    TransactionComponent
  ],
  providers:[AssignAssetService,SettingService]
})
export class OrderDetailsModule {}
