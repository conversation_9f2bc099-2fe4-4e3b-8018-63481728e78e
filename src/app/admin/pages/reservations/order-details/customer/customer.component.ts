import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON>t, <PERSON><PERSON><PERSON><PERSON>, ElementRef ,ViewChild} from '@angular/core';
import { Customer } from '../../models/order-models';
import { Subscription } from 'rxjs';
import { OrderService } from '../../order.service/order.service';
import { ActivatedRoute } from '@angular/router';
import { imgURL, EndPoint } from "../../../../../globals/endPoint/config";
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ImagePopupComponent } from '../../../../../modules/image-popup/image-popup.component';
import { AlertService } from '../../../../../modules/alert/alert.service';

@Component({
  selector: 'order-details-customer',
  templateUrl: './customer.component.html',
  styleUrls: ['../order-details.component.css']
})
export class CustomerComponent implements OnInit, OnDestroy {

  customer: Customer;
  sub: Subscription;
  order_id: number;
  custom_values;
  imageUrl = imgURL;
  url = EndPoint;
  @ViewChild("hasCusAlert") alertContainer: ElementRef;
  constructor(
    private activeRoute: ActivatedRoute,
    private orderS: OrderService,
    private modalService: NgbModal,
    private alertS: AlertService
  ) {
    this.order_id = this.activeRoute.parent.snapshot.params.order_id;
    // console.log(this.order_id);
   }

  ngOnInit() {
    this.getCustomer();
    this.updateCustomer();
  }

  ngOnDestroy() {
    this.sub.unsubscribe();
  }

  getCustomer() {
    this.orderS.getOrderCustomer(this.order_id).subscribe(
      res => {
        this.customer = res.data;
        this.custom_values = res.data;
        sessionStorage.setItem('customer_id',JSON.stringify(res.data.customer_id));
      },
      err => console.log(err)
    );
  }

  updateCustomer() {
    this.sub = this.orderS.updateData.subscribe(
      val => {
        if (val.update && val.from == 'CUSTOMER') {
          // console.log(val);
          this.customer = val.data;
        }
      }
    );
  }


  showBigImage(img) {
    const modalImage = this.modalService.open(ImagePopupComponent, {
      centered: true
    });
    modalImage.componentInstance.image = img;
  }
  resendEmail() {
    console.log(this.order_id);
    console.log(`order/${this.order_id}/email`);
    this.orderS
      .resendEmail(this.order_id)
      .then(res => {
        console.log(res);
        if(res.status=="OK")
        {
          this.alertS.success(
            this.alertContainer,
            res.result.message,
            true,
            5000
          );
        }
        else{
          this.alertS.error(this.alertContainer, res.result ? res.result.message : 'Something went wrong!', true, 3000)
        }
        
      })
      .catch(err => {
        this.alertS.error(this.alertContainer, err.error.result ? err.error.result.message : err.message, true, 3000)
      });
  }

}
