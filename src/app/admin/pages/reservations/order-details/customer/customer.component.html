<div class="custom-alert" #hasCusAlert></div>
<div class="m-portlet m-portlet--full-height">
  <div class="m-portlet__head">
    <div class="m-portlet__head-caption">
      <div class="m-portlet__head-title">
        <h3 class="m-portlet__head-text colorPurpel customer-details-title">
          Customer Details
        </h3>
        <div class="edit-btn">
          <a (click)="resendEmail()"
            class="m-portlet__nav-link btn m-btn m-btn--hover-brand m-btn--icon m-btn--icon-only m-btn--pill"
            title="Resend Order Email">
            <i class="fa fa-envelope"></i>
          </a>
          <a id="product-details-page" href="{{url}}pages/pdf?order_id={{order_id}}"
            class="m-portlet__nav-link btn m-btn m-btn--hover-brand m-btn--icon m-btn--icon-only m-btn--pill"
            title="Download PDF">
            <i class="fa fa-file-pdf-o"></i>
          </a>
          &nbsp;
          <a id="m_product_sidebar_toggle" [routerLink]="'./edit/customer'"
            class="btn btn-secondary m-btn m-btn--icon m-btn--icon-only m-btn--pill m-btn--air" title="Edit Customer">
            <i class="la la-edit"></i>
          </a>
        </div>
      </div>
    </div>
  </div>

  <div class="m-portlet__body">
    <div class="description-fields">
      <div class="row">
        <p class="col-md-6">
          <span class="description-field-title">Name: </span>
          <span>{{ customer?.first_name }} {{ customer?.last_name }}</span>
        </p>
        <p class="col-md-6">
          <span class="description-field-title">Email: </span>
          <span>{{ customer?.email }}</span>
        </p>
      </div>
      <div class="row">
        <p class="col-md-6">
          <span class="description-field-title">Mobile: </span>
          <span>{{ customer?.mobile }}</span>
        </p>

        <p class="col-md-6">
          <span class="description-field-title">Country: </span>
          <span>{{ customer?.country_id }}</span>
        </p>
      </div>
      <div class="row">
        <p class="col-md-6">
          <span class="description-field-title">Address: </span>
          <span>{{ customer?.address_line1 }}</span>
        </p>
        <p class="col-md-6">
          <span class="description-field-title">City: </span>
          <span>{{ customer?.city }}</span>
        </p>
      </div>
      <div class="row">
        <p class="col-md-6">
          <span class="description-field-title">State: </span>
          <span>{{ customer?.state_id }}</span>
        </p>
        <p class="col-md-6">
          <span class="description-field-title">Zip Code: </span>
          <span>{{ customer?.zipcode }}</span>
        </p>
      </div>
      <div class="row">
        <p *ngIf="customer?.company" class="col-md-6">
          <span class="description-field-title">Company: </span>
          <span>{{ customer?.company }}</span>
        </p>
      </div>
      <div class="row" *ngIf="custom_values?.custom_values" style="padding:5px 0px;">
        <h5 class="col-md-12 colorPurpel">Additional Info:</h5>
        <div class="w-100" *ngFor="let custom of custom_values?.custom_values">
          <div class="col-md-12 description-field-title">
            {{ custom.field_label }}
          </div>

          <div [ngSwitch]="custom?.filetype">
            <ng-template [ngSwitchCase]="'application'">
              <div class="col-md-12">
                <a *ngIf="custom?.filepath != ''" [attr.href]="custom?.filepath" title="Download file">Download File</a>
              </div>
            </ng-template>
            <ng-template [ngSwitchCase]="'image'">
              <div class="col-md-12">
                <img *ngIf="custom?.filepath != ''" class="img pointer" style="cursor: pointer" [src]="custom?.filepath"
                  height="100px" (click)="showBigImage(custom?.filepath)" />
              </div>
            </ng-template>
            <ng-template ngSwitchDefault>
              <div class="col-md-12">
                <i>{{ custom.field_values }}</i>
              </div>
            </ng-template>
          </div>
        </div>
      </div>
      <div class="row" *ngIf="customer?.shipping_address1" style="padding:5px 0px;">
        <h5 class="col-12 colorPurpel">Shipping Info:</h5>
        <hr />
        <p class="col-md-6">
          <span class="description-field-title">Name: </span>
          <span>{{ customer?.first_name }} {{ customer?.last_name }}</span>
        </p>
        <p class="col-md-6">
          <span class="description-field-title">Phone: </span>
          <span>{{ customer?.mobile }}</span>
        </p>
        <p class="col-md-6">
          <span class="description-field-title">Address line 1: </span>
          <span>{{ customer?.shipping_address1 }}</span>
        </p>
        <!-- <p class="col-md-6"><span class="description-field-title">Address line 2: </span>
                    <span>{{customer?.shipping_address2}}</span></p> -->
        <p class="col-md-6">
          <span class="description-field-title">City: </span>
          <span>{{ customer?.shipping_city }}</span>
        </p>
        <p class="col-md-6">
          <span class="description-field-title">State: </span>
          <span>{{ customer?.shipping_state }}</span>
        </p>
        <p class="col-md-6">
          <span class="description-field-title">Zip Code: </span>
          <span>{{ customer?.shipping_zipcode }}</span>
        </p>
        <p class="col-md-6">
          <span class="description-field-title">Country: </span>
          <span>{{ customer?.shipping_country }}</span>
        </p>
      </div>
      <div *ngIf="customer?.signature">
        <h5 class="colorPurpel">Signature:</h5>
        <img src="{{ imageUrl + 'orders/' + customer.signature }}" class="img-fluid  img-thumbnail " />
      </div>
    </div>
  </div>
</div>