<div class="custom-alert" #hasCusAlert>
</div>

<div class="m-portlet m-portlet--full-height">
    <div class="m-portlet__head assetitems-head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text colorPurpel" style="width: 140px;">
                    Inventory Items
                </h3>
                <div class="m-portlet__head-text text-right">

                    <a id="m_product_sidebar_toggle" href="{{url}}order/picklist/pdf?order_id={{order_id}}" class="btn btn-outline-brand mr-3 btn-sm m-btn m-btn--custom close-sidebar-asset" data-toggle="tooltip" data-placement="bottom" title="Download pdf">
                        <span>Pick List</span>
                    </a>

                    <a id="m_product_sidebar_toggle" [routerLink]="'./edit/product'" class="btn btn-outline-brand mr-3 btn-sm m-btn m-btn--custom close-sidebar-asset" data-toggle="tooltip" data-placement="bottom" title="Edit Item">
                        <span>Edit Item</span>
                    </a>
                    <a id="m_product_sidebar_toggle" [routerLink]="'./assign-asset/list'" class="btn btn-outline-info  mr-3 btn-sm m-btn m-btn--custom close-sidebar-asset" title="Assign Assets">
                        <span>Pickup</span>
                    </a>
                    <a id="m_product_sidebar_toggle" [routerLink]="'./assign-asset/return'" class="btn btn-outline-success btn-sm m-btn m-btn--custom close-sidebar-asset" title="Return Item">
                        <span>Return</span>
                    </a>

                </div>
            </div>
        </div>
    </div>

    <div class="m-portlet__body">
        <div class="m-portlet__head-text inventory-items-datetime" *ngIf="isDateRange">
            <div *ngIf="isRentalDateEditMode;else showText">
                <app-date-time-range (startDateTime)="selectstartDateTime($event)" (endDateTime)="selectendDateTime($event)" [placeholderEnd]="rent_endDate" [placeholderStart]="rent_startDate" (reload)="onReload($event)" [displayTime]="showStartTime"></app-date-time-range>
                <button class="button theme-btn cancel-btn" (click)="isRentalDateEditMode = false">Cancel</button>
            </div>
        </div>
        <ng-template #showText>
            <label class="">
        <b>Rental Dates:</b> {{rent_startDate | customDate: 'rental'}} - {{rent_endDate | customDate: 'rental'}}
        <i class="fa fa-edit edit-icon" (click)="onClickEdit()"></i>
      </label>
        </ng-template>
        <div class="table-responsive">
            <table class="table">
                <thead>
                    <tr>
                        <th>Image</th>
                        <th>Description</th>
                        <!-- <th>Asset</th> -->
                        <th>Price</th>

                        <th>Quantity</th>
                        <th>Subtotal</th>

                        <!-- <th>Returned</th> -->
                    </tr>
                </thead>
                <tbody *ngIf="productList && productList.order_items.length > 0; else NoItem">
                    <ng-container *ngFor="let order of productList?.order_items; let i = 'index'; trackBy: trackList">
                        <tr>
                            <td>
                                <img *ngIf="order.product.images && order.product.images.length>0; else alterImage" class="img-fluid img-avatar img-thumbnail img-resize" src="{{imageUrl +  order.store_id + '/' + order.product.id + '/' + order.product.images[0].image_small}}" onError="this.src='./assets/img/home/<USER>';"
                                    alt="Product Image" onError="this.src='./assets/img/home/<USER>';" />
                                <ng-template #alterImage>
                                    <img class="img-fluid img-avatar img-thumbnail img-resize" src="./assets/img/home/<USER>" alt="Product Image" />
                                </ng-template>
                            </td>
                            <td>
                                <h5>{{order.product.name}}</h5>
                                <div *ngIf="order.product.variant_chain && order.product.variant_chain !='Unassigned: Unassigned'">
                                    <small>({{order.product.variant_chain}})</small>
                                </div>
                                <p>Item id: {{order?.product?.id}}</p>
                                <!-- <div>
                  <div *ngIf="order.rent_start">
                    <small>From :{{ productList?.rent_start | customDate : 'rental' }}</small>
                  </div>
                  <div *ngIf="order.rent_end">
                    <small>To :{{productList?.rent_end | customDate : 'rental'  }}</small>
                  </div>
                </div> -->
                                <div *ngIf="order.deposit_amount > 0">
                                    Deposit Amount: {{order.deposit_amount | currency}} {{order.deposite_tax == 'true'? '(Including Tax)' : ''}}
                                </div>

                                <ng-container *ngIf="order.product_type===2 && order.children.length">
                                    <h6 class="pt-3 pb-1">Package Includes</h6>
                                    <ng-container *ngFor="let packageProduct of order.children">
                                        <div *ngIf="packageProduct?.product_type !==3" class="mb-2">
                                            <div style="font-size:13px;">{{packageProduct.product.name}}({{ packageProduct.quantity }})</div>
                                            <small *ngIf="packageProduct.product.variant_chain !== 'Unassigned: Unassigned'"> <i>{{packageProduct.product.variant_chain}}</i></small>
                                        </div>
                                    </ng-container>

                                </ng-container>
                            </td>
                            <!-- <td>
                <span *ngIf="order?.order_asset;else noAsset">
                  Serial :# {{order.order_asset.serial_no}} <a (click)="navigate(order.quantity_id, order.id)"><i style="color: #2489d4;
                    cursor: pointer;" class="fa fa-pencil"></i></a>
                </span>
                <ng-template #noAsset>
                  <a (click)="navigate(order.quantity_id, order.id)"
                    class="btn btn-outline-info btn-sm m-btn m-btn--custom close-sidebar-asset"> Add Asset</a>
                </ng-template>

              </td> -->
                            <td>{{order.price | currency}}
                                <!--                {{order.rental_type && order.rental_type != 'buy'? '/' + order.term : ''}}-->
                                <!--                {{getType(order.rental_type, order.term)}}-->
                            </td>
                            <!-- <td>{{order.sales_tax}}% <br> ${{order.sales_tax_price | number: '1.2-2'}}</td> NOT REQUIRED -->
                            <td>{{order.quantity}}</td>
                            <td>
                                {{order.sub_total |currency}}
                            </td>
                            <!-- <td>${{order.total | number: '1.2-2'}}</td> -->
                            <!-- <td>
                <div *ngIf="order?.order_asset && order?.order_asset.return_status">
                  <span>
                    {{order?.order_asset.return_date | date }}
                  </span> <br>
                  <span [ngStyle]="{'background': getReturnStatus(order?.order_asset.return_status).color}"
                    class="status m-badge m-badge--wide">{{
                                        getReturnStatus(order?.order_asset.return_status).text
                                        }}</span>
                </div>
              </td> -->
                        </tr>

                        <ng-container *ngFor="let addon_item of order?.children; let i = 'index';">
                            <tr *ngIf="addon_item.product_type==3">
                                <td>
                                    <img *ngIf="addon_item.product.images && addon_item.product.images.length>0; else alterImage" class="img-fluid img-avatar img-thumbnail img-resize" src="{{imageUrl +  addon_item.store_id + '/' + addon_item.product.id + '/' + addon_item.product.images[0].image_small}}"
                                        onError="this.src='./assets/img/home/<USER>';" alt="Product Image" onError="this.src='./assets/img/home/<USER>';" />
                                    <ng-template #alterImage>
                                        <img class="img-fluid img-avatar img-thumbnail img-resize" src="./assets/img/home/<USER>" alt="Product Image" />
                                    </ng-template>
                                </td>
                                <td>
                                    <h5>{{addon_item.product.name}}</h5>
                                    <small *ngIf="addon_item.product.variant_chain !== 'Unassigned: Unassigned'"> <i>{{addon_item.product.variant_chain}}</i></small>
                                </td>
                                <td></td>
                                <td>{{addon_item.quantity}}</td>
                                <td></td>
                            </tr>
                        </ng-container>

                        <ng-container *ngIf="order.product_type ===1">
                            <tr *ngIf="order.item_assets && order.item_assets.length > 0;">
                                <td></td>
                                <td colspan="4">
                                    <table class="table table-condenced table-order-asset">
                                        <tr>
                                            <th>Asset</th>
                                            <th>Scan Out</th>
                                            <th>Scan In</th>
                                            <th>Return Status</th>
                                            <th>Current Condition</th>
                                            <th>Action</th>
                                        </tr>
                                        <tr *ngFor="let order_asset of order.item_assets; let j = index">
                                            <td><a (click)="navigate_asset_details(order_asset.asset?.id)" target="_blank" style="cursor: pointer;">{{ order_asset.asset?.serial_no}}</a></td>
                                            <td>{{ order_asset.pickup_date | date:'MM/dd/yyyy, hh:mm a'}}</td>
                                            <td>{{ order_asset.return_date | date:'MM/dd/yyyy, hh:mm a' }}</td>
                                            <td>{{ order_asset.return_status }}</td>
                                            <td>{{ order_asset.asset?.status}}</td>
                                            <td *ngIf="order_asset.return_status == null">
                                                <button (click)="removeAsset(order_asset.id, order.id)" class="btn m-btn m-btn--hover-danger m-btn--icon m-btn--icon-only m-btn--pill">
                            <i class="fa fa-trash"></i>
                          </button>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </ng-container>

                        <ng-container *ngIf="order.product_type === 2">
                            <tr *ngIf="order.children.length > 0 && order?.assigned_asset">
                                <td></td>
                                <td colspan="4">
                                    <table class="table table-condenced table-order-asset">
                                        <tr>
                                            <th>Asset</th>
                                            <th>Scan Out</th>
                                            <th>Scan In</th>
                                            <th>Return Status</th>
                                            <th>Current Condition</th>
                                            <th>Action</th>
                                        </tr>
                                        <ng-container *ngFor="let child of order.children; let j = index">
                                            <tr *ngFor="let order_asset of child.item_assets; let x = index">
                                                <td><a (click)="navigate_asset_details(order_asset.asset?.id)" target="_blank" style="cursor: pointer;">{{ order_asset.asset?.serial_no}}</a></td>
                                                <td>{{ order_asset.pickup_date | date:'MM/dd/yyyy, hh:mm a'}}</td>
                                                <td>{{ order_asset.return_date | date:'MM/dd/yyyy, hh:mm a' }}</td>
                                                <td>{{ order_asset.return_status }}</td>
                                                <td>{{ order_asset.asset?.status}}</td>
                                                <td *ngIf="order_asset.return_status == null">
                                                    <button (click)="removeAsset(order_asset.id, order_asset.order_item_id)" class="btn m-btn m-btn--hover-danger m-btn--icon m-btn--icon-only m-btn--pill">
                              <i class="fa fa-trash"></i>
                            </button>
                                                </td>
                                            </tr>
                                        </ng-container>
                                    </table>
                                </td>
                            </tr>
                        </ng-container>

                    </ng-container>
                </tbody>
                <ng-template #NoItem>
                    <tbody>
                        <tr>
                            <td colspan="6">
                                <h5 class="text-center">No Item Found</h5>
                            </td>
                        </tr>
                    </tbody>
                </ng-template>
            </table>
        </div>
    </div>
</div>