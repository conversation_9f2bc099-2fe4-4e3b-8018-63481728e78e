import { <PERSON>mponent, OnInit, OnDestroy, Input, OnChanges, ViewChild, ElementRef } from "@angular/core";
import { Subscription } from "rxjs";
import { OrderService } from "../../order.service/order.service";
import { ActivatedRoute, Route, Router } from "@angular/router";
import { SidebarService } from "../../../sidebar-service/sidebar.service";
import { product_image, EndPoint } from "../../../../../globals/endPoint/config";
import {
  formateRentType,
  ASSETS_RETURN_CONDITION,
  setCartRentalDate
} from "../../../../../globals/_classes/functions";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { DialogBoxComponent } from "../../../../../modules/dialog-box/dialog-box.component";
import { Helpers } from "../../../../../helpers";
import { AssignAssetService } from "../../order.service/assign-asset.service";
import { AlertService } from "../../../../../modules/alert/alert.service";
import { HttpService } from "../../../../../modules/http-with-injector/http.service";

@Component({
  selector: "order-details-items",
  templateUrl: "./items.component.html",
  styleUrls: ["../order-details.component.css"]
})
export class ItemsComponent implements OnInit, OnDestroy, OnChanges {
  productList;
  sub: Subscription[] = [];
  order_id: number;
  imageUrl = product_image;
  url = EndPoint;
  rent_startDate;
  rent_endDate;
  isDateRange: boolean = false;
  isRentalDateEditMode:boolean=false;
  // sidebar
  sideBaropen: boolean;
  @Input() reload: boolean;
  @ViewChild("hasCusAlert") alertContainer: ElementRef;

  constructor(
    private http: HttpService,
    private router: Router,
    private activeRoute: ActivatedRoute,
    private orderS: OrderService,
    private service: AssignAssetService,
    private sidebarS: SidebarService,
    private alertS: AlertService,
    private modalService: NgbModal
  ) {
    this.sub[0] = this.activeRoute.parent.params.subscribe(val => {
      // console.log(val)
      this.order_id = val["order_id"];
      this.getProductList();
    });
  }

  ngOnInit() {
    this.updateList();
  }
  ngOnChanges() {
    // console.log('c', this.reload)
    if (this.reload) {
      this.getProductList();
    }
  }
  openSidebar(route) {
    this.sidebarS.openSidebar();

    $(".native-routing").css("display", "block");
    this.router.navigate([route], {
      relativeTo: this.activeRoute
    });
  }
  ngAfterViewInit() {
    window.scrollTo(0, 0);
  }


  get showStartTime() {
    const contents = localStorage.getItem("contents")
      ? JSON.parse(localStorage.getItem("contents"))
      : null;
    if (
      contents.site_specific.confg &&
      contents.site_specific.confg.hasOwnProperty("show_start_time")
    ) {
      return contents.site_specific.confg.show_start_time;
    }
    return true;
  }

  getReturnStatus(id) {
    const obj = ASSETS_RETURN_CONDITION.find(
      item => Number(item.value) === Number(id)
    );
    return obj;
  }
  ngOnDestroy() {
    for (let s of this.sub) {
      s.unsubscribe();
    }
  }

  getProductList() {
    this.orderS.getProductList(this.order_id).subscribe(
      res => {
        this.productList = res.data;
        this.productList.order_items.map(item => {
          if (item.children.length > 0) {
            item.children.map(child => {
              if (child.item_assets.length > 0) {
                item['assigned_asset'] = true; // atleast one assigned asset
              }
            })
          }
        });
        this.getDateRangeData()
      },
      err => console.log(err)
    );
  }

  updateList() {
    this.sub[1] = this.orderS.updateData.subscribe(val => {
      if (val.update && val.from == "ITEMS") {
        // console.log(val);
        this.productList = val.data;
        this.productList.order_items.map(item => {
          if (item.children.length > 0) {
            item.children.map(child => {
              if (child.item_assets.length > 0) {
                item['assigned_asset'] = true; // atleast one assigned asset
              }
            })
          }
        });
      }
    });
  }
  getDateRangeData() {
    if (this.productList.order_items?.length) {
      let item = this.productList.order_items.find(item => item.rental_type !== 'buy')
      if (item) {
        this.rent_startDate = item.rent_start;
        this.rent_endDate = item.rent_end;
        this.isDateRange = true;
      }else {
        this.isDateRange = false;
      }
    }
  }
  onClickEdit(){
    this.isRentalDateEditMode=true;
  }
  onReload(event){
    Helpers.setLoading(true);

    setTimeout(t=>{
      this.http
      .post("products/availability",
      {
        type:'order',
        order_id:this.order_id,
        start_date:this.rent_startDate,
        end_date:this.rent_endDate,
        source:'admin'
      })
      .toPromise()
      .then(res => {
        Helpers.setLoading(false)
        if (res.status === "NOK") {
        this.isRentalDateEditMode=false; 
        }
        else{
          this.isRentalDateEditMode=false;
          this.productList=res.result.data;
  
          this.rent_startDate=this.productList.rent_start;
          this.rent_endDate=this.productList.rent_end;
  
        setCartRentalDate('adminAfterOrder',this.rent_startDate, this.rent_endDate); 
        }
  
      }).catch(err=>{
        Helpers.setLoading(false)
        this.isRentalDateEditMode=true; 
      });
  
    },1000)
  }
  getDate(d) {
    return this.orderS.formateListDate(d);
  }

  getType(d, t) {
    return d && t ? formateRentType(d, t) : "";
  }

  trackList(index, pro) {
    return pro ? pro.id : null;
  }

  calculatetax(tax, pr) {
    const t = (pr * tax) / 100;
    return t;
  }
  navigate(id, order_items_id) {
    this.router.navigateByUrl(
      `/admin/reservations/${
      this.order_id
      }/details/assign-asset/list?quantity_id=${id}&order_items_id=${order_items_id}`
    );
  }
  navigate_asset_details(id) {
    this.router.navigateByUrl('admin/inventory/product-asset/details/' + id)
  }

  removeAsset(asset_id, order_item_id) {
    const modalRef = this.modalService.open(DialogBoxComponent, {
      centered: true,
      size: "sm"
    });
    modalRef.componentInstance.massage = "Are you sure you want to delete?";
    modalRef.result.then(
      result => {
        if (result) {
          Helpers.setLoading(true);
          this.discharge(asset_id, order_item_id);
        }
      },
      res => {
        console.log(res);
      }
    );
  }

  discharge(asset_id, order_item_id) {
    this.service
      .dischargeAssign({
        id: asset_id,
        order_item_id: Number(order_item_id)
      })
      .then(res => {
        Helpers.setLoading(false);

        if (res.result.error) {
          this.alertS.error(this.alertContainer, res.result.error, true, 5000);
        } else {
          this.getProductList();
          this.alertS.success(
            this.alertContainer,
            res.result.message,
            true,
            5000
          );
        }
      })
      .catch(err => {
        this.alertS.error(
          this.alertContainer,
          "Something wrong Please try again !!!",
          true,
          5000
        );
      });
  }
  selectstartDateTime(e) {
    this.rent_startDate = e;
  }

  selectendDateTime(e) {
    this.rent_endDate = e;
  }


}
