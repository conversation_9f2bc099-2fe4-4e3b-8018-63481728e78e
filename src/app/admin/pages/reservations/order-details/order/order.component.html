<div class="custom-alert" #hasCusAlert></div>

<div class="m-portlet m-portlet--full-height">
  <div class="m-portlet__head">
    <div class="m-portlet__head-caption">
      <div class="m-portlet__head-title row">
        <h3 class="m-portlet__head-text col-12 colorPurpel">
          Order Details
        </h3>
        <div *ngIf="is_showExportInvoice" class="edit-btn">
          <span
            (click)="onClickExportInvoice()"
            class="m-badge m-badge--wide"
            style="margin-right: 10px;cursor: pointer;width: 78px"
          >
            QB Export
          </span>
        </div>
      </div>
    </div>
  </div>

  <div class="m-portlet__body">
    <div class="description-fields row">
      <div class="col-md-6">
        <p>
          <span class="description-field-title">Order Id: </span>
          <span>{{ order.id }}</span>
        </p>
        <p>
          <span class="description-field-title">Quantity: </span>
          <span>{{ order.total_quantity }}</span>
        </p>
        <p *ngIf="!pickupEdit; else edit">
          <span style="cursor: pointer;" class="description-field-title">
            <span class="description-field-title"> Pickup Store: </span>
            <span class="m-badge m-badge--wide" style="margin-right: 10px;">
              {{ order.pickup }}
            </span>
            <i class="la la-edit" (click)="pickupEditOp()"></i>
          </span>
        </p>
        <ng-template #edit>
          <div class="description-field-title">Pickup Store:</div>
          <div class="float-left">
            <select
              class="form-control form-control-sm m-input"
              name="locPicup"
              [(ngModel)]="pickupLocationId"
            >
              <option [value]="location.id" *ngFor="let location of locations"
                >{{ location.name }}
              </option>
            </select>
          </div>
          <div class="float-left ml-2">
            <button
              type="submit"
              (click)="updatePickupLoc()"
              class="btn btn-success m-btn--icon btn-sm m-btn--icon-only"
            >
              <i class="la la-save"></i>
            </button>
            <button
              type="button"
              (click)="pickupEdit = false"
              class="btn btn-danger m-btn--icon btn-sm m-btn--icon-only"
            >
              <i class="la la-close"></i>
            </button>
          </div>
          <div style="clear: both;"></div>
        </ng-template>

        <p>
          <span (click)="goToPayment(order)" style="cursor: pointer;" *ngIf="getPaymentStatus(order.payment_status)">
            <span class="description-field-title">Payment Status: </span>
            <span
              class="m-badge m-badge--wide"
              [ngClass]="checkStatusPayment(order.payment_status)"
              style="margin-right: 10px;"
            >
              {{ getPaymentStatus(order.payment_status) }}
            </span>
          </span>
        </p>

        <p *ngIf="order?.type == 1; else quote">
          <span (click)="changeStatus(order)" style="cursor: pointer;" *ngIf="getStatus(order.status)">
            <span class="description-field-title">Status: </span>
            <span
              class="m-badge m-badge--wide"
              [ngClass]="checkStatus(order.status)"
              style="margin-right: 10px;"
            >
              {{ getStatus(order.status) }}
            </span>
            <i class="la la-edit"></i>
          </span>
        </p>
        <ng-template #quote>
          <p>
            <span (click)="changeQuote(order)" style="cursor: pointer;">
              <span class="description-field-title">Status: </span>
              <span
                class="m-badge m-badge--wide m-badge--brand"
                style="margin-right: 10px;"
              >
                Quote
              </span>
              <span
                class="m-badge m-badge--wide m-badge--warning"
                style="margin-right: 10px;"
              >
                Accept?
              </span>
            </span>
          </p>
        </ng-template>
        <p>
          <span class="description-field-title">Order Date & Time: </span>
          <span>{{order?.created | customDate}}</span>
        </p>
        <p>
          <span class="description-field-title">Order Source: </span>
          <span>{{ order?.event_location }}</span>
        </p>
        <p>
          <span class="description-field-title">Delivery Date & Time: </span>
          <span>{{order?.delivery_date | date:'MM/dd/yyyy, hh:mm a'}}</span>
        </p>
      </div>
      <div class="col-md-6">
        <p>
          <span class="description-field-title">Sub Total: </span>
          <span>{{ (order.sub_total ? order.sub_total : 0) | currency }} </span>
        </p>
        <p>
          <span class="description-field-title">Delivery Charge: </span>
          <span>{{
            (order.delivery_charge ? order.delivery_charge : 0) | currency
          }}</span>
        </p>
        <p>
          <span class="description-field-title">Discount: </span>
          <span
            >{{ (order.total_discount ? order.total_discount : 0) | currency }}
            {{
              order.total_discount ? "(" + order?.coupon?.code + ")" : ""
            }}</span
          >
        </p>
        <p *ngIf="!returnEdit; else dropdown">
          <span class="description-field-title">Return location:</span>
          <span class="m-badge m-badge--wide" style="margin-right: 10px;">{{
            order.return_to
          }}</span>
          <i class="la la-edit" (click)="editReturnLoc()"></i>
        </p>
        <ng-template #dropdown>
          <div>
            <span class="description-field-title">Return location:</span>
          </div>
          <div class="float-left">
            <select
              class="form-control m-input "
              name="loc"
              [(ngModel)]="returnLocationId"
            >
              <option [value]="location.id" *ngFor="let location of locations"
                >{{ location.name }}
              </option>
            </select>
          </div>
          <div class="float-left">
            <button
              type="submit"
              (click)="updateReturnLoc()"
              class="btn btn-success m-btn--icon btn-sm m-btn--icon-only"
            >
              <i class="la la-save"></i>
            </button>
            <button
              type="button"
              (click)="cancelEdit()"
              class="btn btn-danger m-btn--icon btn-sm m-btn--icon-only"
            >
              <i class="la la-close"></i>
            </button>
          </div>
          <div style="clear: both;"></div>
        </ng-template>
        <p>
          <span class="description-field-title">Sales Tax: </span>
          <span>{{ (order.tax ? order.tax : 0) | currency }}</span>
        </p>
        <p>
          <span class="description-field-title">Delivery Tax: </span>
          <span>{{
            (order.delivery_tax ? order.delivery_tax : 0) | currency
          }}</span>
        </p>
        <p>
          <span class="description-field-title">Deposit Amount: </span>
          <span>{{ order.total_deposit | currency }}</span>
        </p>
        <p>
          <span class="description-field-title">Additional Charge: </span>
          <span>{{
            order.additional_charge + order.return_charge | currency
          }}</span>
        </p>
        <p>
          <span class="description-field-title">Grand Total: </span>
          <span>{{ order.total | currency }}</span>
        </p>
        <!-- <p (click)="navigateCost()" ><span class="description-field-title">Total Cost: </span> <span>$500.00 ST</span><i style="cursor: pointer;" class="la la-edit"></i></p> -->
      </div>
    </div>
  </div>
</div>
