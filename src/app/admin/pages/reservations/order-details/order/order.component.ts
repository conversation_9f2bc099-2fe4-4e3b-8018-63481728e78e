import { DialogBoxComponent } from './../../../../../modules/dialog-box/dialog-box.component';
import {
  Component,
  OnInit,
  OnDestroy,
  ViewChild,
  ElementRef
} from "@angular/core";
import { Subscription } from "rxjs";
import { OrderService } from "../../order.service/order.service";
import { ActivatedRoute, Router, NavigationEnd } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { StatusDialogBoxComponent } from "../../dialog-box/dialog-box.component";
import { Helpers } from "../../../../../helpers";
import { AlertService } from "../../../../../modules/alert/alert.service";
import { SidebarService } from "./../../../sidebar-service/sidebar.service";
import { Order } from "../../models/order-models";
import { SettingService } from "../../../settings/setting-service/setting.service";
import { IStatus } from '../../../../../modules/status-option/status-option.component';
import { Payment_status } from './../../../../../globals/_classes/functions';

@Component({
  selector: "order-details-order",
  templateUrl: "./order.component.html",
  styleUrls: ["../order-details.component.css"]
})
export class OrderComponent implements OnInit, OnDestroy {
  order: Order = new Order();
  sub: Subscription[] = [];
  order_id: number;
  statusArray: IStatus[] = [];
  returnEdit = false;
  pickupEdit = false;
  locations = [];
  returnLocationId = null;
  pickupLocationId = null;
  is_showExportInvoice;

  @ViewChild("hasCusAlert") alertContainer: ElementRef;
  modStatusArray: IStatus[] = [];
  private child: any;

  constructor(
    private activeRoute: ActivatedRoute,
    private router: Router,
    private modalService: NgbModal,
    private orderS: OrderService,
    private alertS: AlertService,
    private sidebarS: SidebarService,
    private settingS: SettingService
  ) {
    this.sub[0] = this.activeRoute.parent.params.subscribe(val => {
      this.order_id = val["order_id"];
      this.getOrderList();
    });

    this.is_showExportInvoice = false;
  }

  ngOnInit() {
    this.sub.push(
      this.router.events.subscribe(e => {
        if (e instanceof NavigationEnd && this.child !== this.activeRoute.firstChild) {
          if (e.urlAfterRedirects === `/admin/reservations/${this.order_id}/details`) {
            this.getOrderList();
          }
        }
      })
    );
    this.updateList();
    this.getStatusData();
    this.orderS.getLocations().subscribe(res => {
      this.locations = res.result.data;
      const selectedLocation = this.locations.filter(
        location => location.name === this.order.return_to
      );
      const selectedLocationP = this.locations.filter(
        location => location.name === this.order.pickup
      );
      selectedLocation.length > 0
        ? (this.returnLocationId = selectedLocation[0].id)
        : (this.returnLocationId = this.locations[0].id);
      selectedLocationP.length > 0
        ? (this.pickupLocationId = selectedLocationP[0].id)
        : (this.pickupLocationId = this.locations[0].id);
    });

    this.orderS.updateOrderDetails.subscribe(res => {
      if (res) {
        this.getOrderList();
      }
    });

    this.settingS.getLeadLagTime().subscribe(
      res => {
        if (res.data !== null) {
          if (res.data.hasOwnProperty("quickbook")) {
            if (res.data.quickbook.hasOwnProperty("code")) {
              if (res.data.quickbook.code !== "") {
                this.is_showExportInvoice = true;
                sessionStorage.setItem(
                  "quickbook",
                  JSON.stringify(res.data.quickbook)
                );
              } else {
                sessionStorage.setItem("quickbook", null);
              }
            } else {
              sessionStorage.setItem("quickbook", null);
            }
          } else {
            sessionStorage.setItem("quickbook", null);
          }
        } else {
          sessionStorage.setItem("quickbook", null);
        }
      },
      err => console.error(err)
    );
  }

  openSidebar(route) {
    this.sidebarS.openSidebar();
    $(".native-routing").css("display", "block");
    this.router.navigate([route], {
      relativeTo: this.activeRoute
    });
  }

  ngOnDestroy() {
    for (let s of this.sub) {
      s.unsubscribe();
    }
  }

  getStatusData() {
    this.orderS.getOrderStatus().subscribe(
      res => {
        this.statusArray = res;
        this.statusArray.map((status, i) => {
          this.modStatusArray.push(status);
          if (status.child) {
            // let z = i;
            status.child.map((child, j) => {
              const mod_child = {
                id: child.id,
                label: child.label,
                isChild: true
              }
              this.modStatusArray.push(mod_child);
            })
            
          }
        });
      },
      err => console.log(err)
    );
  }

  getOrderList() {
    this.orderS.getOrderDetails(this.order_id).subscribe(
      res => {
        this.order = res.data;
        console.log(this.order);
      },
      err => console.log(err)
    );
  }

  updateList() {
    this.sub[1] = this.orderS.updateData.subscribe(val => {
      if (val.update && (val.from == "ITEMS" || val.from == "PAYMENT")) {
        this.getOrderList();
      }
    });
  }

  getDateFormate(date) {
    return this.orderS.getDate(date);
  }

  getStatus(s) {
    const status = this.modStatusArray.find(x => x.id == s);
    if (status) {
      return status.label;
    }
    return '';
    // return this.orderS.getStatus(s, this.statusArray);
  }

  checkStatus(s) {
    return this.orderS.checkStatus(s);
  }

  changeQuote(order): void {
    const modalStatus = this.modalService.open(DialogBoxComponent, {
      centered: true
    });
    modalStatus.componentInstance.massage =
      "Do you want to accept this quote as order?";
    modalStatus.result.then(result => {
      if (result) {
        Helpers.setLoading(true);
        this.quoteToOrder(order);
      }
    });
  }

  private quoteToOrder(quote: any): void {
    this.orderS
      .quoteToOrder(quote.id)
      .catch(err => {
        Helpers.setLoading(false);
        this.alertS.error(
          this.alertContainer,
          err.error.result ? err.error.result.message : err.message,
          true,
          3000
        );
      })
      .then(res => {
        Helpers.setLoading(false);
        if (res.status === "OK") {
          this.alertS.success(
            this.alertContainer,
            res.result.message,
            true,
            3000
          );
          this.getOrderList();
        } else {
          this.alertS.error(
            this.alertContainer,
            res.result.message,
            true,
            3000
          );
        }
      });
  }

  changeStatus(order) {
    // console.log(order.status);
    const modalStatus = this.modalService.open(StatusDialogBoxComponent, {
      centered: true
    });
    modalStatus.componentInstance.orderStatus = order.status;
    const status = this.modStatusArray.filter(s => s.label !== 'Paid Other')
    modalStatus.componentInstance.status = status;
    modalStatus.result.then(
      result => {
        if (result) {
          Helpers.setLoading(true);
          this.statusChange(order, result);
        }
      },
      res => {
        console.log(res);
      }
    );
  }

  statusChange(order, id) {
    this.orderS
      .changeStatus(order.id, id)
      .then(res => {
        order.status = id;
        Helpers.setLoading(false);
        this.alertS.success(
          this.alertContainer,
          `Status of ${order.id} has been changed`,
          true,
          5000
        );
      })
      .catch(err => {
        console.log(err);
        Helpers.setLoading(false);
        this.alertS.error(
          this.alertContainer,
          `Something wrong! Status of ${order.id} has been not changed`,
          true,
          5000
        );
      });
  }

  navigate() {
    this.router.navigateByUrl(
      `/admin/reservations/${this.order_id}/details/store-edit`
    );
  }

  navigateCost() {
    this.router.navigateByUrl(
      `/admin/reservations/${this.order_id}/details/total-cost`
    );
  }

  editReturnLoc() {
    this.returnEdit = true;
  }

  pickupEditOp() {
    this.pickupEdit = true;
  }

  updateReturnLoc() {
    this.orderS
      .updateReturnLocation(this.order_id, this.returnLocationId)
      .subscribe(
        res => {
          if (res.status === "OK") {
            this.order.return_to = res.result.data;
            this.returnEdit = false;
          }
        },
        err => console.log(err)
      );
    this.returnEdit = false;
  }

  updatePickupLoc() {
    this.orderS
      .updatePickupLocation(this.order_id, this.pickupLocationId)
      .subscribe(
        res => {
          if (res.status === "OK") {
            this.order.return_to = res.result.data;
            this.returnEdit = false;
          }
        },
        err => console.log(err)
      );
    this.pickupEdit = false;
  }

  cancelEdit() {
    this.returnEdit = false;
  }

  onClickExportInvoice() {
    Helpers.setLoading(true);
    const quickbookData = JSON.parse(sessionStorage.getItem("quickbook"));
    if (
      quickbookData !== null &&
      quickbookData.hasOwnProperty("code") &&
      quickbookData.code != ""
    ) {
      this.orderS.exportInvoice(this.order_id).subscribe(res => {
        console.log(res);
        let data = res.data.hasOwnProperty("success");
        if (res.data.hasOwnProperty("success")) {
          Helpers.setLoading(false);
          this.alertS.success(
            this.alertContainer,
            `Invoice exported`,
            true,
            5000
          );
        } else {
          this.alertS.error(
            this.alertContainer,
            `Something wrong!`,
            true,
            5000
          );
        }
      });
    } else {
    }
  }

  getPaymentStatus(statusId: number) {
    let status: any;
    status = Payment_status.find(x => +statusId === x.id);
    if (status) {
      return status.label;
    } else {
      return '';
    }
  }

  checkStatusPayment(statusId: number) {
    switch (+statusId) {
      case 1:
        return "m-badge--success";
      case 2:
        return "m-badge--danger";
      case 3:
        return "m-badge--danger";
    }
  }

  goToPayment(order) {
    this.router.navigate(["/admin/reservations/" + order.id + "/details/edit/payment"]);
  }
}
