import { Component, OnInit, AfterViewInit, ViewChild, ElementRef } from '@angular/core';
import { AlertService } from "./../../../../../../modules/alert/alert.service";
import { TransferLocation } from './StoreLocation.models';
declare let $: any;

@Component({
  selector: 'app-store-edit',
  templateUrl: './store-edit.component.html',
  styleUrls: ['./store-edit.component.css']
})

export class StoreEditComponent implements OnInit, AfterViewInit {

  transferLocation: TransferLocation = new TransferLocation();
  location = [
    { value: null, name: 'Select location'},
    { value: 1, name: 'New york'},
    { value: 2, name: 'Dhaka'}
  ];
  loader: boolean;
  @ViewChild("hasCusAlert") alertContainer: ElementRef;

  constructor(
    private alertS: AlertService
  ) {
    this.location = [
      { value: null, name: 'Select location'},
      { value: 1, name: 'New york'},
      { value: 2, name: 'Dhaka'}
    ];
    this.transferLocation.from_location = this.location[0].value;
    this.transferLocation.to_location = this.location[0].value;
  }

  ngOnInit() {

    this.loader = false;
  }

  ngAfterViewInit() {
    this.datePicker();
  }

  startDateChange() {
    $('#start-date').datepicker().on('changeDate', (e) => {
      let date = e.date;
      this.transferLocation.start_date = (date.getMonth() + 1) + '-' + date.getDate() + '-' + date.getFullYear();
    });
  }

  endDateChange() {
    $('#end-date').datepicker().on('changeDate', (e) => {
      let date = e.date;
      this.transferLocation.end_date = (date.getMonth() + 1) + '-' + date.getDate() + '-' + date.getFullYear();
    });
  }

  private datePicker() {
    $('#start-date').datepicker({
      todayHighlight: true,
      orientation: "bottom left",
      format: 'yyyy-mm-dd',
      templates: {
          leftArrow: '<i class="la la-angle-left"></i>',
          rightArrow: '<i class="la la-angle-right"></i>'
      }
    });
    $('#end-date').datepicker({
      todayHighlight: true,
      orientation: "bottom right",
      format: 'yyyy-mm-dd',
      templates: {
          leftArrow: '<i class="la la-angle-left"></i>',
          rightArrow: '<i class="la la-angle-right"></i>'
      }
    });
  }

  updateLocation(){
    console.log(this.transferLocation);
  }

}
