import { Component, OnInit, OnDestroy, ViewChild, ElementRef } from '@angular/core';
import { Payment, Methods } from '../../models/order-models';
import { ActivatedRoute, Router } from '@angular/router';
import { OrderService } from '../../order.service/order.service';
import { Subscription } from 'rxjs';
import { AlertService } from '../../../../../modules/alert/alert.service';

@Component({
  selector: 'order-details-payment',
  templateUrl: './payment.component.html',
  styleUrls: ['../order-details.component.css']
})
export class PaymentComponent implements OnInit, OnDestroy {

  paymentList: Payment[] = [];
  sub: Subscription[] = [];
  order_id: number;
  methodsAmount: Methods = new Methods();
  pId: number;
  total_order_amount;
  due_amount;
  paid_amount;
  released_amount;

  @ViewChild('hasCusAlert') alertContainer: ElementRef;
  is_arb: boolean;
  is_recurring_order: boolean;

  constructor(
    private activeRoute: ActivatedRoute,
    private alertS: AlertService,
    private router: Router,
    private orderS: OrderService
  ) {
    this.sub[0] = this.activeRoute.parent.params.subscribe(
      val => {
        this.order_id = val['order_id'];
        this.getAmount();
        this.getPaymentList();
      }
    );
    this.is_recurring();
  }

  ngOnInit() {
    sessionStorage.removeItem('pay');
    this.updateList();
    this.loadExternalScript();
  }

  private loadExternalScript() {
    return new Promise((resolve, reject) => {
      const scriptElement = document.createElement("script");
      scriptElement.src = "https://js.stripe.com/v3/";
      scriptElement.onload = resolve;
      document.body.appendChild(scriptElement);
    });
  }

  private is_recurring(): void {
    const contents = localStorage.getItem('contents')
      ? JSON.parse(localStorage.getItem('contents'))
      : null;
    if (contents) {
      const arb_sup_admin = contents.site_specific.confg.hasOwnProperty('arb')
        ? contents.site_specific.confg.arb.hasOwnProperty('active')
          ? contents.site_specific.confg.arb.active
          : false
        : false;
      if (arb_sup_admin) {
        this.is_arb = contents.site_specific.confg.arb.hasOwnProperty('store_active')
          ? contents.site_specific.confg.arb.store_active === 'before_rental' || contents.site_specific.confg.arb.store_active === 'after_rental'
            ? true
            : false
          : false;
        if (this.is_arb) {
          this.checkRecurringPricing();
        }
      }
    }
  }

  checkRecurringPricing() {
    this.orderS.recurringPriceByOrderID(this.order_id)
      .catch(err => console.log(err))
      .then(res => {
        if (res.result.data) {
          res.result.data.amount ? this.is_recurring_order = true : this.is_recurring_order = false;
          this.orderS.isPaymentGatewayStripe = res.result.data.payment_gateway === 'Stripe';
        }
      });
  }

  ngOnDestroy() {
    for (let s of this.sub) {
      s.unsubscribe();
    }
  }

  getPaymentList() {
    this.orderS.getPaymentAmountSummary(this.order_id).subscribe(
      res => {
        if(res.hasOwnProperty('list'))
        {
         this.paymentList=res.list;
        }
        
      },
      err => console.log(err)
    );
  }

  updateList() {
    this.sub[1] = this.orderS.updateData.subscribe(
      val => {
        if (val.update && val.from == 'PAYMENT') {
          // console.log(val);
          if (val.data) {
            this.paymentList = val.data;
          } else {
            this.getPaymentList();
          }
          this.getAmount();
        } else if (val.update && val.from == 'ITEMS') {
          this.getAmount();
        }
      }
    );
  }

  getAmount() {
    this.orderS.getPaymentAmountSummary(this.order_id).subscribe(
      res => {
        if(res.hasOwnProperty('memo'))
        {
          this.paid_amount = res.memo.paid || 0;
          this.total_order_amount = res.memo.total || 0;
          this.due_amount = res.memo.due || 0;
          this.released_amount = res.memo.released || 0;
        }
      },
      err => console.log(err)
    );
  }

  get status () {
    const data = this.paymentList.find( f => f.payment_method === 'Capture');
    return data ? true : false;
  }

  trackPayment(index, pay) {
    return pay ? pay.id : null;
  }

  getTotalPaid(data) {
    return this.orderS.getPaidTotal(data);
  }

  openSummary(id, icon) {
    $('#' + id).toggleClass('dis-block');
    $('#' + icon).toggleClass('fa-minus-circle');
  }

  getTextResponse(text) {
    return this.orderS.getTextResponse(text);
  }

  trackbyText(index, data) {
    return data ? data.id : null;
  }

  voidBtn(pay) {
    this.pId = pay.id;
    console.log(pay);
    const data = {
      transaction_id: pay.transaction_id,
      order_id: this.order_id,
      amount: pay.payment_amount,
      id: pay.id
    }
    this.orderS.callVoid(data).then(
      res => {
        if (res.result.transaction_id) {
          this.alertS.success(this.alertContainer, res.result.message, true, 3000);
          this.orderS.update({ data: null, from: 'PAYMENT', update: true });
        } else {
          this.alertS.error(this.alertContainer, res.result.message, true, 3000);
        }
        this.pId = null;
      }
    ).catch(
      err => this.error(err, 'Something wrong!!!')
    );
  }

  refund(pay) {
    if (!sessionStorage.getItem('pay')) {
      if(this.status) {
        pay['payment_method'] = 'Capture'
      }
      sessionStorage.setItem('pay', JSON.stringify(pay));
    }
    this.router.navigate([`edit/refund`], { relativeTo: this.activeRoute });
  }

  captured(pay) {
    this.router.navigate(['edit/payment/capture/'+pay.id], { relativeTo: this.activeRoute });
    // this.pId = pay.id;
    // console.log(pay);
    // const data = {
    //   transaction_id: pay.transaction_id,
    //   order_id: this.order_id,
    //   amount: pay.payment_amount,
    //   id: pay.id
    // }
    // this.orderS.callCaptured(data).then(
    //   res => {
    //     if (res.result.data.success) {
    //       this.alertS.success(this.alertContainer, res.result.data.response_text, true, 3000);
    //       this.orderS.update({ data: null, from: 'PAYMENT', update: true });
    //     } else {
    //       this.alertS.error(this.alertContainer, res.result.data.response_text, true, 3000);
    //     }
    //     this.pId = null;
    //   }
    // ).catch(
    //   err => this.error(err, 'Something wrong!!!')
    // );
  }

  error(err, message) {
    this.pId = null;
    console.log(err);
    this.alertS.error(this.alertContainer, message, true, 5000);
  }

}
