import { Component, OnInit, Input, OnChanges } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { OrderService } from '../../order.service/order.service';



@Component({
  selector: 'app-order-discount-note',
  templateUrl: './order-discount-note.component.html',
  styleUrls: ['./order-discount-note.component.css']
})
export class OrderDiscountNoteComponent implements OnInit,OnChanges {

  loader:boolean=false;
  order_id;
  orderItemList=[];
  is_show_order_discount:true;

  @Input() reload: boolean;

  constructor(
    private activeRoute: ActivatedRoute,
    private orderS: OrderService
  ) { 
  
  }

  ngOnInit() {
    this.activeRoute.parent.params.subscribe(val => {
      console.log(val)
      this.order_id = val["order_id"];
      this.getOrderItems();
    });
  }

  ngOnChanges(){
    if (this.reload) {
      this.getOrderItems();
    }
  }

  getOrderItems(){
    if (this.order_id) {
      this.orderS.getProductList(this.order_id).subscribe(
        res => {
          this.orderItemList = res.data.order_items;
          this.orderItemList=this.orderItemList.filter(x=>(x.additional && x.additional.subtaintial_note !==''));
        },
        err => console.log(err)
      );
    }
  }


}
