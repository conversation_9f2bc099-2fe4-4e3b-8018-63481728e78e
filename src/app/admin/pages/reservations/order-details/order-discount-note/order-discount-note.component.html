

<div *ngIf="orderItemList.length>0" class="m-portlet m-portlet--full-height">
  <div class="m-portlet__head assetitems-head">
    <div class="m-portlet__head-caption">
      <div class="m-portlet__head-title">
        <h3 class="m-portlet__head-text colorPurpel">
         Discount Notes
        </h3>
      </div>
    </div>
  </div>

  <div class="m-portlet__body">
    <div class="table-responsive">
      <table class="table">
        <thead>
          <tr>
            <th>Item</th>
            <th>Discount(%)</th>
            <th>Note</th>
          </tr>
        </thead>
        <tbody *ngIf="orderItemList && orderItemList.length > 0; else NoItem">
          <ng-container *ngFor="let item of orderItemList; let i = 'index';">
            <tr>
              
              <td class="w-30">
                <h5>{{item?.product?.name}}</h5>
              </td>
             
              <td class="w-10 text-center">{{item?.off_amount}}</td>
            
              <td class="w-60">{{item?.additional?.subtaintial_note}}</td>
            </tr>
         
          </ng-container>
        </tbody>
        <ng-template #NoItem>
          <tbody>
            <tr>
              <td colspan="6">
                <h5 class="text-center">No Item Found</h5>
              </td>
            </tr>
          </tbody>
        </ng-template>
      </table>
    </div>
  </div>
</div>
