import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { AssetSidebarComponent } from "./asset-sidebar.component";
import { Routes, RouterModule } from "@angular/router";


const routes: Routes = [
  {
    path: "",
    component: AssetSidebarComponent,
    children: [
      {
        path: "return",
        loadChildren: () => import('./asset-return/asset-return.module').then(m => m.AssetReturnModule)
      },
      // {
      //   path: "list",
      //   loadChildren: "./assign-asset/assign-asset.module#AssignAssetModule"
      // },
      {
        path: "list",
        loadChildren: () => import('./order-assign-asset/order-assign-asset.module').then(m => m.OrderAssignAssetModule)
      },
   
      {
        path: "**",
        redirectTo: "list"
      }
    ]
  }
];
@NgModule({
  imports: [CommonModule, RouterModule.forChild(routes)],
  declarations: [AssetSidebarComponent]
})
export class AssetSidebarModule {}
