<div *ngIf="data.length>0">
  <div class="table-responsive" style="margin-bottom: 10px;">
    <h5>Ordered Items<small>/</small>Assets with customer</h5>
    <table class="table">
      <thead>
        <tr>
          <th>Product</th>
          <th>Asset SL No</th>
          <th>Scan Out</th>
          <th>Condition</th>
          <th>Actions </th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let asset of data ">
          <td>
            <div><b>{{asset?.product_name}}</b></div>
            <small *ngIf="asset?.variant_chain !='Unassigned: Unassigned'">{{asset?.variant_chain}}</small>
          </td>
          <td>{{ asset?.serial_no ? '#' + asset?.serial_no : asset?.serial_no}}</td>
          <td>
            {{ asset.pickup_date ? asset.pickup_date : ''}}
            <div *ngIf="asset.rent_start">Start: {{asset.rent_start | date }} {{asset.rent_start | date: "shortTime" }}
            </div>
            <div *ngIf="asset.rent_end">End : {{asset.rent_end | date }} {{asset.rent_end | date: "shortTime" }}</div>
            <div *ngIf="asset.rent_start || asset.rent_end ">Duration : {{asset.rental_duration}}</div>
          </td>
          <td> <span [ngStyle]="{'background': getCurrentCondition(asset.current_condition)?.color}"
              class="status m-badge m-badge--wide">{{
                          getCurrentCondition(asset.current_condition)?.text
                          }}</span></td>
          <td>
            <a  style="color:#fff;" (click)="showReturned(asset)"
              class="btn btn-info btn-sm btn-return-asset" title="Return">Return</a>
          </td>
        </tr>


      </tbody>

    </table>
  </div>
</div>