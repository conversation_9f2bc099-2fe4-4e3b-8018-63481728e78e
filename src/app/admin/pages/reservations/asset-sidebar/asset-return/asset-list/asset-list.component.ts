import { Component, OnInit, Input, OnChanges, Output, EventEmitter } from "@angular/core";
import { ASSETS_CONDITION } from "../../../../../../globals/_classes/functions";

@Component({
  selector: "app-asset-list",
  templateUrl: "./asset-list.component.html",
  styleUrls: ["./asset-list.component.css"]
})
export class AssetListComponent implements OnInit, OnChanges {
  @Input() searchResult = [];
  @Output() onSelected= new EventEmitter();
  constructor() {}
  data = [];

  ngOnInit() {}

  ngOnChanges() {
    this.data = this.searchResult.length ? this.searchResult : [];
  }

  showReturned(data){
    this.onSelected.emit(data);
  }

  getCurrentCondition(id) {
    const obj = ASSETS_CONDITION.find(
      item => Number(item.value) === Number(id)
    );
    return obj;
  }
}
