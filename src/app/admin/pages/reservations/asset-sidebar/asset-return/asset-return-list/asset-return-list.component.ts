import {
  Component,
  OnInit,
  Input,
  OnChanges,
  Output,
  EventEmitter
} from "@angular/core";

import { AssignAssetService } from "../../../order.service/assign-asset.service";
import { ASSETS_RETURN_CONDITION } from "../../../../../../globals/_classes/functions";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { DialogBoxComponent } from "../../../../../../modules/dialog-box/dialog-box.component";

@Component({
  selector: "asset-return-list",
  templateUrl: "./asset-return-list.component.html",
  styleUrls: ["./asset-return-list.component.css"]
})
export class AssetReturnListComponent implements OnInit, OnChanges {
  data = [];
  @Input() returnList = [];
  @Output() onDelete = new EventEmitter();
  constructor(
    private modalService: NgbModal,
    private service: AssignAssetService
  ) {}

  ngOnInit() {}
  ngOnChanges() {
    this.data = this.returnList;
  }

  getReturnStatus(id) {
    const obj = ASSETS_RETURN_CONDITION.find(
      item => Number(item.value) === Number(id)
    );
    return obj;
  }

  deletItem(id) {
    this.deleteDialog("Are you sure you want to  delete?").then(
      result => {
        if (result) {
          this.onDelete.emit(id);
        }
      },
      res => {
        // console.log(res);
      }
    );
  }
  deleteDialog(message) {
    const modalRef = this.modalService.open(DialogBoxComponent, {
      centered: true,
      size: "sm",
      windowClass: "animated fadeIn"
    });
    modalRef.componentInstance.massage = message;
    return modalRef.result;
  }
}
