<div class="table-responsive" style="margin-bottom: 10px;" *ngIf="data.length>0">
  <h5>Returned Items<small>/</small>Assets </h5>
  <table class="table table-hover">
    <thead>
      <tr>
        <th>Product Name </th>
        <th>Asset SL No </th>
        <th>Return Status </th>
        <th>Scan In</th>
        <th>Additional Charge</th>
        <th> Actions</th>
      </tr>
    </thead>
    <tbody>
      <tr class="even-tr" *ngFor="let item of data ">
        <td>
          {{item.product_name}}
        </td>
        <td>
          {{item.serial_no ? '#' + item.serial_no :''}}
        </td>
        <td>
          <span [ngStyle]="{'background': getReturnStatus(item.return_status)?.color}"
          class="status m-badge m-badge--wide">{{
                          getReturnStatus(item.return_status)?.text
                          }}</span>
        </td>
        <td>
          {{item.return_date }}
        </td>
        <td>
          {{item.charge | currency }}
        </td>
        <td>
          <a (click)="deletItem(item.id)"
            class="m-portlet__nav-link btn m-btn m-btn--hover-danger m-btn--icon m-btn--icon-only m-btn--pill"
            title="Archive Product"><i class="fa fa-trash"></i></a>
        </td>
      </tr>
    </tbody>
  </table>
</div>
