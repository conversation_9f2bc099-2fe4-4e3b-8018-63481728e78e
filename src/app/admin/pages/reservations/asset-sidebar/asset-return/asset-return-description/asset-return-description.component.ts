import {
  Component,
  OnInit,
  AfterViewInit,
  Input,
  OnChanges,
  Output,
  EventEmitter,
  ViewChild,
  ElementRef
} from "@angular/core";
import { FormGroup, FormBuilder, Validators } from "@angular/forms";
import { ASSETS_RETURN_CONDITION } from "../../../../../../globals/_classes/functions";
import { AssignAssetService } from "../../../order.service/assign-asset.service";
import { AlertService } from "../../../../../../modules/alert/alert.service";
declare let $: any;
@Component({
  selector: "asset-return-description",
  templateUrl: "./asset-return-description.component.html",
  styleUrls: ["./asset-return-description.component.css"]
})
export class AssetReturnDescriptionComponent
  implements OnInit, AfterViewInit, OnChanges {
  form: FormGroup;
  submitting: boolean;
  return_conditions = ASSETS_RETURN_CONDITION;
  current_date;
  current_date_show;
  currency;
  @Input() selectedAsset;
  @Output() onHide = new EventEmitter();
  @ViewChild("hasCusAlert") alertContainer: ElementRef;
  showForm = true;
  constructor(
    private alertS: AlertService,
    private service: AssignAssetService,
    private fb: FormBuilder
  ) {
    const current = new Date();
    const return_date_show =
      (current.getFullYear() < 10 ? '0' : '') + current.getFullYear() +
      "-" +
      ((current.getMonth() + 1) < 10 ? '0' : '') + (current.getMonth() + 1) +
      "-" +
      (current.getDate() < 10 ? '0' : '') + current.getDate();
    this.current_date_show = return_date_show;
    const return_date =
      (current.getFullYear() < 10 ? '0' : '') + current.getFullYear() +
      "-" +
      ((current.getMonth() + 1) < 10 ? '0' : '') + (current.getMonth() + 1) +
      "-" +
      (current.getDate() < 10 ? '0' : '') + current.getDate() +
      " " +
      (current.getHours() < 10 ? '0' : '') + current.getHours() +
      ":" +
      (current.getMinutes() < 10 ? '0' : '') + current.getMinutes() +
      ":" +
      (current.getSeconds() < 10 ? '0' : '') + current.getSeconds();
    this.current_date = return_date;
    this.form = this.fb.group({
      return_date: [this.current_date],
      return_status: [this.return_conditions[0].value],
      comments: [""],
      additional_charge: [""]
    });
    this.currency = JSON.parse(localStorage.getItem('currency'));
  }

  ngOnInit() {

  }
  ngAfterViewInit() {
    this.datePicker();
  }
  ngOnChanges() {
    // console.log(this.selectedAsset)
    this.showForm = this.selectedAsset ? true : false;
  }
  orderDateChange() {
    $("#order-date")
      .datepicker()
      .on("changeDate", e => {
        let date = e.date;
        let current = new Date();
        const return_date =
          (date.getFullYear() < 10 ? '0' : '') + date.getFullYear() +
          "-" +
          ((date.getMonth() + 1) < 10 ? '0' : '') + (date.getMonth() + 1) +
          "-" +
          (date.getDate() < 10 ? '0' : '') + date.getDate() +
          " " +
          (current.getHours() < 10 ? '0' : '') + current.getHours() +
          ":" +
          (current.getMinutes() < 10 ? '0' : '') + current.getMinutes() +
          ":" +
          (current.getSeconds() < 10 ? '0' : '') + current.getSeconds();
        this.form.get("return_date").setValue(return_date);
      });
  }
  private datePicker() {
    $("#order-date").datepicker({
      todayHighlight: true,
      orientation: "bottom left",
      format: "yyyy-mm-dd",
      templates: {
        leftArrow: '<i class="la la-angle-left"></i>',
        rightArrow: '<i class="la la-angle-right"></i>'
      }
    });
  }

  submit() {
    this.submitting = true;

    this.service
      .setReturnedAsset({
        asset_id: this.selectedAsset.id,
        order_item_id : this.selectedAsset.order_item_id,
        ...this.form.getRawValue()
      })
      .then(res => {
        this.submitting = false;
        if (res.result.error) {
          this.alertS.error(this.alertContainer, res.result.error, true, 5000);
        } else {
          this.alertS.success(
            this.alertContainer,
            res.result.message,
            true,
            5000
          );
          this.onHide.emit(true);
        }
      })
      .catch(err => {
        this.submitting = false;
        this.alertS.error(
          this.alertContainer,
          "Something wrong Please try again !!!",
          true,
          5000
        );
      });
  }

  cancel() {
    this.showForm = false;
     this.onHide.emit(true);
  }
}
