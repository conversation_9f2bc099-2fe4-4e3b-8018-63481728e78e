<div class="custom-alert" #hasCusAlert></div>
<form class="exapand-asset-return mt-5" [formGroup]="form" (submit)="submit()" *ngIf="selectedAsset && showForm">
  <h3>{{selectedAsset.serial_no ? selectedAsset?.product_name+'(#' + selectedAsset.serial_no +')' : selectedAsset?.product_name }} </h3>
  <div class="row">
    <div class="form-group col-md-6">
      <label for="title">Return Date</label>
      <div class="input-group date">
        <input type="text" class="form-control m-input" [value]="current_date_show" (click)="orderDateChange()"
          id="order-date" readonly />
        <div class="input-group-append">
          <span class="input-group-text">
            <i class="la la-calendar-check-o"></i>
          </span>
        </div>
      </div>
    </div>
    <div class="form-group col-md-6">
      <label for="title">Return Status</label>
      <select formControlName="return_status" class="form-control m-input ng-untouched ng-pristine ng-valid"
        name="status">
        <option [value]="item.value" *ngFor="let item of return_conditions ">{{item.text}}</option>

      </select>
    </div>
  </div>
  <div class="row">
    <div class="col-md-12">
      <div class="form-group m-form__group">
        <label for="">Additional Charge</label>

        <div class="input-group m-input-group">
          <div class="input-group-prepend">
                  <span class="input-group-text">
                    {{ currency.symbol? currency.symbol : '$'}}
                  </span>
          </div>
          <input numberOnly class="form-control m-input" name="additional_charge" formControlName="additional_charge"
                 type="text" placeholder="0.00" autocomplete="off">
          <div class="input-group-append">
            <small class="input-group-text" >
              {{ currency.code? currency.code : 'USD'}}
            </small>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="form-group col-md-12">
      <label for="comments">Comments</label>
      <input formControlName="comments" class="form-control m-input ng-untouched" placeholder="Comment" type="text"
        value="">
    </div>
  </div>
  <div class="row">
    <div class="col-md-4">
      <button type="submit" [disabled]="!form.valid" class="btn btn-primary">Submit <i *ngIf="submitting"
          class="fa fa-spinner fa-spin"></i></button>
      <button (click)="cancel()" type="button" class=" ml-2  btn  btn-brand">Cancel</button>
    </div>
  </div>
</form>
