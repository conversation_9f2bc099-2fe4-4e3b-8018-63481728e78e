import { Component, OnInit, ElementRef, ViewChild } from "@angular/core";
import {
  FormControl,
  FormGroup,
  FormBuilder,
  Validators
} from "@angular/forms";
import { AssignAssetService } from "../../order.service/assign-asset.service";
import { ActivatedRoute } from "@angular/router";
import { AlertService } from "../../../../../modules/alert/alert.service";

@Component({
  selector: "app-asset-return",
  templateUrl: "./asset-return.component.html",
  styleUrls: ["./asset-return.component.css"]
})
export class AssetReturnComponent implements OnInit {
  @ViewChild("hasCusAlert") alertContainer: ElementRef;
  form: FormGroup;
  order_id;
  isShow;
  search_result = [];
  selectedAsset;
  loader: boolean;
  returnList = [];
  loaded: boolean;
  constructor(
    private alertS: AlertService,
    private route: ActivatedRoute,
    private service: AssignAssetService,
    private fb: FormBuilder
  ) {
    this.form = this.fb.group({
      search: ["", Validators.required]
    });

    this.order_id = this.route.parent.parent.parent.parent.snapshot.params.order_id;
    this.getReturnList();
  }

  ngOnInit() {
    this.getFullList();
  }

  submit() {
    this.getFullList();
  }

  getFullList() {
    this.loader = true;
    let search = this.form.get("search").value;

    if (search == null) {
      search = "";
    }
    this.service.searchReturnAsset(search, this.order_id).subscribe(res => {
      //  console.log(res);
      this.loader = false;
      this.search_result = res;
      this.isShow = true;
      this.loaded = true;
    });
  }
  showDescription(data) {
    // console.log(data)
    this.selectedAsset = data;
  }
  onHide(e) {
    if (e) {
      this.isShow = false;
      this.selectedAsset=null;
      this.form.reset();
      this.getReturnList();
      this.getFullList();
      this.loaded = false;
    }
  }

  getReturnList() {
    this.service.getReturnedAssets(this.order_id).subscribe(res => {
      //   console.log(res);
      this.returnList = res;
    });
  }
  reset() {
    this.form.reset();
    this.isShow = false;
    this.search_result = [];
    this.loaded = false;
  }
  deletItem(id) {
    this.service
      .deleteReturnItem(id)
      .then(res => {
        if (res.result.error) {
          this.alertS.error(this.alertContainer, res.result.error, true, 5000);
        } else {
          this.isShow = false;
          this.selectedAsset = null;
          this.getReturnList();
          this.getFullList();
          this.alertS.success(
            this.alertContainer,
            res.result.message,
            true,
            5000
          );
        }
      })
      .catch(err => {
        this.loader = false;
        this.alertS.error(
          this.alertContainer,
          "Something wrong Please try again !!!",
          true,
          5000
        );
      });
  }
}
