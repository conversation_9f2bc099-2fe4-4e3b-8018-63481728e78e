import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { AssetReturnComponent } from "./asset-return.component";
import { AssetReturnListComponent } from "./asset-return-list/asset-return-list.component";
import { AssetReturnDescriptionComponent } from "./asset-return-description/asset-return-description.component";
import { Routes, RouterModule } from "@angular/router";
import { ReactiveFormsModule } from "@angular/forms";
import { AssetListComponent } from "./asset-list/asset-list.component";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { DialogBoxComponent } from "../../../../../modules/dialog-box/dialog-box.component";
import { DialogBoxModule } from "../../../../../modules/dialog-box/dialog-box.module";
const routes: Routes = [
  {
    path: "",
    component: AssetReturnComponent
  }
];
@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    ReactiveFormsModule,
    NgbModule,
    DialogBoxModule
  ],
  declarations: [
    AssetReturnComponent,
    AssetReturnListComponent,
    AssetReturnDescriptionComponent,
    AssetListComponent
  ],
  entryComponents: []
})
export class AssetReturnModule {}
