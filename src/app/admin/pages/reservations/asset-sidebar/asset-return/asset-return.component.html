<div class="custom-alert" #hasCusAlert></div>
<div class="m-quick-sidebar--skin-light inventory-sidebar">
  <div class="m-content product-sidebar-description" style="padding:30px 0px;">
    <h4 style="padding: 10px 0;">Items<small>/</small>Asset Return</h4>
    <form [formGroup]="form" (submit)="submit()"
      class="m-form m-form--fit m-form--label-align-right ng-untouched ng-pristine ng-valid" novalidate="">
      <div class="row">
        <div class="form-group col-md-12">
          <input style="position:relative" formControlName="search"
            class="form-control m-input ng-untouched ng-pristine ng-valid" id="title" name="name"
            placeholder="Type serial # " type="search">
          <div *ngIf="loader" class="m-loader" style="right: 45px;
            position: absolute;
            z-index: 999;
            top: 18px;"></div>
          <div *ngIf="loaded" (click)="reset()" style="right: 80px;
          position: absolute;
          z-index: 999;
          top: 20px;cursor: pointer;">
            <i class="fa fa-times"></i>
          </div>
          <button [disabled]="!form.valid" class="btn btn-sm btn-brand search-btn" type="submit"><i class="fa fa-search"></i></button>
        </div>


        <!-- <div class="col-md-6">
          <button [disabled]="!form.valid" class="btn btn-sm btn-brand" type="submit">Search</button>
        </div> -->
      </div>
    </form>
    <div class="col-md-12 p-0">
      <asset-return-list (onDelete)="deletItem($event)" [returnList]="returnList"></asset-return-list>
    </div>
    <div class="col-md-12 p-0">
      <app-asset-list (onSelected)="showDescription($event)" [searchResult]="search_result"></app-asset-list>
<!--      <div class="not_found" *ngIf="!search_result.length">-->
<!--        No product Found-->
<!--      </div>-->
    </div>

    <div class="col-md-12 p-0">
      <asset-return-description *ngIf="isShow && selectedAsset" [selectedAsset]="selectedAsset"
        (onHide)="onHide($event)">
      </asset-return-description>
    </div>

  </div>
</div>
