<div class="custom-alert" #hasCusAlert></div>

<div class=" animated fadeInRight">
  <div class="m-quick-sidebar--skin-light inventory-sidebar">
    <div class="m-content product-sidebar-description">
      <h4 style="padding: 10px 0;">Pickup<small>/</small>Assign Asset</h4>
      <div *ngIf="loader" class="m-loader m-loader--brand"></div>

      <div>
        <assign-asset-search 
          *ngIf="is_showAddAssetSearch" 
          (onAssignList)="getAssets($event)"
          [productList]="productList">
        </assign-asset-search>
      </div>


      <div class="table-responsive mt-3">
        <table class="table">
          <thead class="item-head">
            <tr>
              <th class="text-left">Image</th>
              <th class="text-left">Name</th>
              <th>Quantity</th>
            </tr>
          </thead>
          <tbody *ngIf="orderItemList.length > 0; else NoItem">
            <ng-container *ngFor="
                let order of orderItemList;
                let i = index;
                trackBy: trackList
              ">
              <tr [ngClass]="getBgColor(order.item_assets.length,order.quantity,order.product.is_tracked)">
                <td class="text-left w-10">
                  <img *ngIf="
                      order.product.images && order.product.images.length > 0;
                      else alterImage
                    " class="img-fluid img-avatar img-thumbnail img-resize asset-img" src="{{
                      imageUrl +
                        order.store_id +
                        '/' +
                        order.product.id +
                        '/' +
                        order.product.images[0].image_small
                    }}" onError="this.src='./assets/img/home/<USER>';" alt="Product Image"
                    onError="this.src='./assets/img/home/<USER>';" />
                  <ng-template #alterImage>
                    <img class="img-fluid img-avatar img-thumbnail img-resize asset-img"
                      src="./assets/img/home/<USER>" alt="Product Image" />
                  </ng-template>
                </td>
                <td class="w-80 text-left">
                  {{ order.product.name }}
                </td>
                <td class="w-10">{{ order.quantity }}</td>
              </tr>

              <tr *ngIf="order.item_assets && order.item_assets.length > 0;">
                <td></td>
                <td colspan="4">
                  <table class="table table-condenced table-order-asset">
                    <tr>
                      <th>Asset</th>
                      <th>Scan Out</th>
                      <th>Scan In</th>
                      <th>Return Status</th>
                      <th>Action</th>
                    </tr>

                    <ng-container *ngFor="let order_asset of order.item_assets; let j = index">
                      <tr [ngClass]="[
                    order_asset?.recent_assign_asset ? 'bg-current-asset' : ''
                    ]">
                        <td><a (click)="navigate_asset_details(order_asset.asset?.id)" target="_blank"
                            style="cursor: pointer;">{{ order_asset.asset?.serial_no}}</a></td>
                        <td>{{ order_asset.pickup_date | date:'MM/dd/yyyy, hh:mm a'}}</td>
                        <td>{{ order_asset.return_date | date:'MM/dd/yyyy, hh:mm a' }}</td>
                        <td>{{ order_asset.return_status }}</td>
                        <td *ngIf="order_asset.return_status == null">
                          <button (click)="removeAsset(order_asset.id, order.id)"
                            class="btn m-btn m-btn--hover-danger m-btn--icon m-btn--icon-only m-btn--pill">
                            <i class="fa fa-trash"></i>
                          </button>
                        </td>
                      </tr>
                    </ng-container>

                  </table>
                </td>
              </tr>

            </ng-container>
          </tbody>
          <ng-template #NoItem>
            <tbody>
              <tr>
                <td colspan="6">
                  <h5 class="text-center">No Item Found</h5>
                </td>
              </tr>
            </tbody>
          </ng-template>
        </table>
      </div>


      <!-- <div *ngIf="show_proceed_btn && isAssigned && order_status !==5;else NoMoreAssets" class=" row mt-5"> -->
      <div *ngIf="show_proceed_btn" class=" row mt-5"> 
        <div class="col-md-12 text-center">
          <button class="btn btn-sm btn-danger mr-2" (click)="cancel()" type="submit">
            Cancel
          </button>
          <button class="btn btn-sm btn-brand" (click)="proccedOrder()" type="submit">
            Process Order
          </button>
        </div>
      </div>

      <!-- <ng-template #NoMoreAssets>
        <div *ngIf="is_showAddAssetSearch==false && order_status !==5" class=" row mt-5">
          <div class="col-md-12 text-center">
            <h3>
              No more assets need assignment.
            </h3>
            <button class="btn btn-sm btn-danger mr-2" (click)="cancel()" type="submit">
              Cancel
            </button>
            <button class="btn btn-sm btn-brand" (click)="proccedOrder()" type="submit">
              Process Order
            </button>
          </div>
        </div>
      </ng-template> -->




      <div class="row">
        <div class="col-md-12 mt-5">
          <asset-history [assetId]="asset_id"></asset-history>
        </div>
      </div>
    </div>
  </div>
</div>