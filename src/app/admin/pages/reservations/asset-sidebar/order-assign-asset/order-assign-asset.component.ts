import { Component, OnInit, ViewChild, ElementRef } from "@angular/core";
import { Router, ActivatedRoute } from "@angular/router";
import { AssignAssetService } from "../../order.service/assign-asset.service";
import { AlertService } from "../../../../../modules/alert/alert.service";
import { OrderService } from "../../order.service/order.service";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { DialogBoxComponent } from "../../../../../modules/dialog-box/dialog-box.component";
import { Helpers } from "../../../../../helpers";
import { product_image } from "../../../../../globals/endPoint/config";


@Component({
  selector: "app-order-assign-asset",
  templateUrl: "./order-assign-asset.component.html",
  styleUrls: ["./order-assign-asset.component.css"]
})
export class OrderAssignAssetComponent implements OnInit {
  productList = [];
  order_id;
  orderItemList = [];
  imageUrl = product_image;
  show_proceed_btn: boolean;
  loader: boolean;
  asset_id;
  isAssigned=false;
  @ViewChild("hasCusAlert") alertContainer: ElementRef;

  is_showAddAssetSearch;
  order_status;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private service: AssignAssetService,
    private alertS: AlertService,
    private orderService: OrderService,
    private modalService: NgbModal
  ) {
    this.order_id = this.route.parent.parent.parent.parent.snapshot.params.order_id;
    this.getProducts();
  }

  ngOnInit() { }

  getProducts(current_assign_asset_id?) {
    this.loader = true;
    this.isAssigned =false;
    let order_items = [];
    this.orderItemList = [];
    this.orderService.getProductList(this.order_id).subscribe(
      res => {
        this.checkPickup(res);
        if (res.data && res.data.order_items.length) {
          this.order_status = res.data.status;

          if(current_assign_asset_id) {
            res.data.order_items.map(i=>{
              if (i.product_type === 1) {
                if(i.item_assets.length > 0) {
                  this.isAssigned =true;
                  //find current added asset
                  i.item_assets.map(asset=> {
                    if(asset.id==current_assign_asset_id) {
                      asset['recent_assign_asset']=true;
                    } else{
                      asset['recent_assign_asset']=false;
                    }
                    return asset;
                  })
                }
                this.orderItemList.push(i);
              } else if (i.product_type === 2) {
                i.children.map(child => {
                  if (child.item_assets.length > 0) {
                    this.isAssigned =true;
                    child.item_assets.map(asset=> {
                      if(asset.id==current_assign_asset_id) {
                        asset['recent_assign_asset']=true;
                      } else{
                        asset['recent_assign_asset']=false;
                      }
                      return asset;
                    })
                  }
                  this.orderItemList.push(child);
                })
              }
            })
          } else {
            res.data.order_items.map(item => {
              if (item.product_type === 1) {
                this.orderItemList.push(item);
              } else if (item.product_type === 2) {
                item.children.map(child => {
                  this.orderItemList.push(child);
                })
              }
            });
            //is assign asset exist
            this.orderItemList.map(i=>{
              if(i.item_assets.length > 0)
                {
                  this.isAssigned =true;
                }
            })
          }
          
          
          order_items = res.data.order_items;
          this.productList = order_items.map(item => {
            let children = [];
            if (item.hasOwnProperty("children")) {
              children = item.children;
            }
            return {
              order_item_id: item.id,
              product: item.product,
              product_type: item.product_type,
              package_children: children ? children : undefined
            };
          });

          this.checkTrackedOrderItemList(order_items);
        }
        this.loader = false;
      },
      err => console.log(err)
    );
  }

  checkTrackedOrderItemList(orderItemList) {
    this.is_showAddAssetSearch = false;
    let data = [];
    let packageData = [];

    data = orderItemList;
    data.forEach(x => {
      if (x.product_type == "2") {
        packageData = x.children;
        packageData.forEach(x => {
          if (x.product.is_tracked == 1) {
            this.is_showAddAssetSearch = true;
          }
        });
      } else {
        if (x.product.is_tracked == 1) {
          this.is_showAddAssetSearch = true;
        }
      }
    });
  }

  checkPickup(res) { // when status is only 'order prep, delviery prep, pickup prep, shipping prep - show the button
    this.show_proceed_btn =
      res.data.status == '4' || res.data.status == '4-1'  || res.data.status == '4-2' || res.data.status == '4-3' ? true : false;
  }


  proccedOrder() {
    this.service.processOrder(this.order_id).then(res => {
      // console.log(res);
      if (res.status) {
        if (res.result.data.status) {
          this.statusChange(5);
          this.router.navigateByUrl(`/admin/reservations/${this.order_id}/details`);
        } else {
          this.pickup(res.result.data.message);

        }
      }
    });
  }

  cancel() {
    this.router.navigateByUrl(`/admin/reservations/${this.order_id}/details`);
  }


  pickup(message) {
    const modalRef = this.modalService.open(DialogBoxComponent, {
      centered: true,
      size: "sm"
    });
    modalRef.componentInstance.massage = message;
      
    modalRef.result.then(
      result => {
        if (result) {
          this.statusChange(5);
          this.router.navigateByUrl(`/admin/reservations/${this.order_id}/details`);
        }
      },
      res => {
        console.log(res);
      }
    );
  }


  statusChange(status) {
    this.orderService
      .submitOrder(this.order_id)
      .then(res => {
        this.show_proceed_btn = false;
        Helpers.setLoading(false);
        this.alertS.success(
          this.alertContainer,
          `Status of ${this.order_id} has been changed`,
          true,
          5000
        );

        this.orderService.loadOrderDetails(true);
      })
      .catch(err => {
        console.log(err);
        Helpers.setLoading(false);
        this.alertS.error(
          this.alertContainer,
          `Something wrong! Status of ${this.order_id} has been not changed`,
          true,
          5000
        );
      });
  }


  getAssets(e) {
    this.getProducts(e.id);
  }



  getBgColor(asset_length, quantity, is_tracked) {
    let bgColor = '';
    if (is_tracked) {
      if (asset_length == quantity) {
        bgColor = 'bg-green'
      }
      else {
        bgColor = 'bg-red'
      }
    }

    return bgColor;
  }


  removeAsset(asset_id, order_item_id) {
    const modalRef = this.modalService.open(DialogBoxComponent, {
      centered: true,
      size: "sm"
    });
    modalRef.componentInstance.massage = "Are you sure you want to delete?";
    modalRef.result.then(
      result => {
        if (result) {
          Helpers.setLoading(true);
          this.discharge(asset_id, order_item_id);
        }
      },
      res => {
        console.log(res);
      }
    );
  }

  discharge(asset_id, order_item_id) {
    this.service
      .dischargeAssign({
        id: asset_id,
        order_item_id: Number(order_item_id)
      })
      .then(res => {
        Helpers.setLoading(false);

        if (res.result.error) {
          this.alertS.error(this.alertContainer, res.result.error, true, 5000);
        } else {
          this.getProducts();
          this.alertS.success(
            this.alertContainer,
            res.result.message,
            true,
            5000
          );
        }
      })
      .catch(err => {
        this.alertS.error(
          this.alertContainer,
          "Something wrong Please try again !!!",
          true,
          5000
        );
      });
  }
  navigate_asset_details(id){}
  trackList(index, pro) {
    return pro ? pro.id : null;
  }
}
