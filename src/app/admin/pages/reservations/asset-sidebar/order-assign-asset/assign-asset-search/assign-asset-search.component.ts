import {
  Component,
  OnInit,
  Input,
  Output,
  EventEmitter,
  ViewChild,
  ElementRef,
  OnChanges
} from "@angular/core";
import { FormControl } from "@angular/forms";
import { OrderService } from "../../../order.service/order.service";
import { ActivatedRoute, Router } from "@angular/router";
import { AlertService } from "../../../../../../modules/alert/alert.service";
import { AssignAssetService } from "../../../order.service/assign-asset.service";
@Component({
  selector: "assign-asset-search",
  templateUrl: "./assign-asset-search.component.html",
  styleUrls: ["./assign-asset-search.component.css"]
})
export class AssignAssetSearchComponent implements OnInit, OnChanges {
  loader;
  order_id;
  notFound: boolean;
  assignManually: boolean;
  @Input() productList = [];
  newProductList = [];
  @Output() onAssignList = new EventEmitter();
  order_item_id;
  search = new FormControl();
  serial_no;
  params;
  @ViewChild("hasCusAlert") alertContainer: ElementRef;
  notAvailable: boolean;
  notAvailableAssetData: any;

  constructor(
    private router: Router,
    private service: AssignAssetService,
    private alertS: AlertService,
    private route: ActivatedRoute,
    private orderService: OrderService
  ) {
    this.order_id = this.route.parent.parent.parent.parent.snapshot.params.order_id;
  }

  ngOnInit() {}
  ngOnChanges() {
   // console.log(this.productList);

    if (this.productList.length) {
      this.order_item_id = this.productList[0].order_item_id;
    }
    this.productList.forEach(
      item => {
        if (item.product_type === 1) {
          item.product['parent_id'] = item.product.id;
        } else {
          const parent_id = item.product.id;
          item.package_children.forEach(
            i => {
              i.product['parent_id'] = parent_id;
            }
          );
        }
      }
    );
    // console.log(this.productList);
    const newPackageList = this.productList.filter(
      product => product.product_type === 2
      ).map(
        item => item.package_children
      );

    const newProductList = this.productList.filter(
      product => product.product_type === 1
      ).map(
        item => item.product
      );
    newPackageList.forEach(
      item => {
        item.forEach(
          i => {
            // console.log(i);
            newProductList.push(i);
          }
        );
      }
    );
    // console.log(newProductList);
    this.newProductList = newProductList.map(
      product => product.product_type === 1 ? product.product : product
      );
    // console.log(this.newProductList);

    // Array.prototype.push.apply(newPackageList, newProductList);
    // console.log(newPackageList);
  }
  onShow(e) {
    this.assignManually = false;
    this.notFound = e.status;
    if (e.status && e.params) {
      this.serial_no = e.params;
    }
  }

  onShowUnavailable(e) {
    this.notAvailable = e.status;
    this.notAvailableAssetData = e.data;
    console.log(this.notAvailableAssetData);
  }

  changeAssetStatus() {
    const change_status = true;
    this.service
    .assignAssetWithStatus(this.order_id, this.notAvailableAssetData.asset_id, change_status)
    .then(res => {
      if (res.status === "OK") {
        this.notAvailable = false;
        this.onAssignList.emit(res.result.data);
      } else {
        this.alertS.error(this.alertContainer, res.result.error, true, 5000);
      }
    })
    .catch(err => {
      this.alertS.error(
        this.alertContainer,
        "Something went wrong !! Please try again !! ",
        true,
        5000
      );
    });
  }

  close() {
    this.router.navigateByUrl(`/admin/reservations/${this.order_id}/details`)
  }

  getAssignList(e) {
    // console.log(e);
    this.onAssignList.emit(e);
  }

  setManually() {
    this.assignManually = true;
  }
  setItem(item_id) {
    console.log(item_id);
    const orderItemid = this.productList.find(
      product => product.product.id == item_id
    );
    console.log(orderItemid.order_item_id);
    this.order_item_id = orderItemid.order_item_id;
  }

  cancel() {
    this.params = "";
    this.notFound = false;
  }
  addAsset() {
    console.log(this.order_item_id, this.order_id, this.serial_no);
    this.service
      .addAsset(this.order_id, this.order_item_id, this.serial_no)
      .then(res => {
        if (res.status === "OK") {
          this.onAssignList.emit(res.result.data);
          this.params = "";
          this.notFound = false;
          this.assignManually = false;
        } else {
          this.alertS.error(this.alertContainer, res.result.error, true, 5000);
        }
      })
      .catch(err => {
        this.alertS.error(
          this.alertContainer,
          "Something wrong ! Please try again !! ",
          true,
          5000
        );
      });
  }
}
