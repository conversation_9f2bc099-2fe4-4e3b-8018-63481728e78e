<div class="custom-alert" #hasCusAlert></div>
<div class="col-md-12">
  <div class="row mt-4 justify-content-center">
    <div class="col-md-12 p-0">
      <app-asset-search [params]="params" (onAssignList)="getAssignList($event)" (onShow)="onShow($event)" (onShowUnavailable)="onShowUnavailable($event)"></app-asset-search>
    </div>

  </div>
</div>

<div class="row box pb-4 m-0 mt-5" *ngIf="notFound">
  <div class="col-md-12">
    <div class="row">
      <div class="col-md-12 pt-4 pb-4 text-center">
        <p>
          Can't find any associated asset.
        </p>
      </div>
    </div>
    <div class="row">
      <div class="col-md-12 text-center">
        <button class="btn btn-sm btn-primary mr-2" (click)="cancel()" type="button">Okay</button>
        <button class="btn btn-sm btn-success" (click)="setManually()" type="button">Assign Manually </button>
      </div>

    </div>
  </div>
  <div class="row m-0" *ngIf="assignManually" style="width: 100%;">
    <div class="col-md-11">
      <label for="title"> Select Product</label>
      <select (change)="setItem($event.target.value)" class="form-control m-input" name="attribute">
        <option [value]="item.parent_id" *ngFor="let item of newProductList">{{item.name}}</option>
      </select>
    </div>
    <div class="col-md-1 pt-3 p-0">
      <button class="btn btn-sm btn-brand btn-ok" type="button" (click)="addAsset()">Ok</button>
    </div>
  </div>
</div>

<div class="row box pb-4 m-0 mt-5" *ngIf="notAvailable">
  <div class="col-md-12">
    <div class="row">
      <div class="col-md-12 pt-4 pb-4 text-center">
        <p>
          This asset is not available. Do you wan to change the asset status?
        </p>
      </div>
    </div>
    <div class="row">
      <div class="col-md-12 text-center">
        <button class="btn btn-sm btn-primary mr-2" (click)="changeAssetStatus()" type="button">Okay</button>
        <button class="btn btn-sm btn-success" (click)="close()" type="button">Cancel </button>
      </div>

    </div>
  </div>
</div>
