import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { OrderAssignAssetComponent } from "./order-assign-asset.component";
import { AssignAssetSearchComponent } from "./assign-asset-search/assign-asset-search.component";
import { Routes, RouterModule } from "@angular/router";
import { ReactiveFormsModule, FormsModule } from "@angular/forms";
import { DialogBoxModule } from "../../../../../modules/dialog-box/dialog-box.module";
import { AssetSearchComponent } from "./asset-search/asset-search.component";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { AssetHistoryComponent } from "./asset-history/asset-history.component";
import { DialogBoxComponent } from "../../../../../modules/dialog-box/dialog-box.component";

const routes: Routes = [
  {
    path: "",
    component: OrderAssignAssetComponent
  }
];
@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    ReactiveFormsModule,
    DialogBoxModule,
    FormsModule,
    NgbModule
  ],
  declarations: [
    OrderAssignAssetComponent,
    AssignAssetSearchComponent,
    AssetSearchComponent,
    AssetHistoryComponent
  ],
  entryComponents:[DialogBoxComponent]
})
export class OrderAssignAssetModule {}
