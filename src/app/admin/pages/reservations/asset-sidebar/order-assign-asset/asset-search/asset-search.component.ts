import {
  Component,
  OnInit,
  Output,
  EventEmitter,
  ElementRef,
  ViewChild,
  Input
} from "@angular/core";
import { Observable } from "rxjs/internal/Observable";
import {
  debounceTime,
  distinctUntilChanged,
  tap,
  switchMap,
  map,
  retry,
  catchError
} from "rxjs/operators";
import { AssignAssetService } from "../../../order.service/assign-asset.service";
import { ActivatedRoute } from "@angular/router";
import { AlertService } from "../../../../../../modules/alert/alert.service";

@Component({
  selector: "app-asset-search",
  templateUrl: "./asset-search.component.html",
  styleUrls: ["./asset-search.component.css"]
})
export class AssetSearchComponent implements OnInit {
  productList: any[];
  selectedValue: string;
  locationList: any[];
  showSellPriceInput = false;
  loader = false;
  inValid: boolean;
  // autocomplete search
  fromInventory = true;
  searchData: any[] = [];
  selectedProduct: string;
  searchText: string;
  order_id;
  @Input() params;
  @ViewChild("hasCusAlert") alertContainer: ElementRef;
  @Output() onAssignList = new EventEmitter();
  @Output() onShow = new EventEmitter();
  @Output() onShowUnavailable = new EventEmitter();
  constructor(
    private alertS: AlertService,
    private route: ActivatedRoute,
    private service: AssignAssetService
  ) {
    this.order_id = this.route.parent.parent.parent.parent.snapshot.params.order_id;
  }

  ngOnInit() {}

  search = (text$: Observable<string>) => {
    return text$.pipe(
      debounceTime(700),
      distinctUntilChanged(),
      tap(() => {
        $(".admin-asset-search").click();
        this.searchData = [];
        this.loader = true;
      }),
      switchMap(term => {
        if (!term) {
          this.onShow.emit({ status: false, params: "" });
        }
        return this.getProduct(term);
      }),
      tap(() => {
        this.loader = false;
        setTimeout(() => {
          $(".admin-cart .dropdown-menu.show .dropdown-item").unbind();
          const perId = $(".admin-cart .dropdown-menu.show").attr("id");
          var c =
            document.getElementById(perId) &&
            document.getElementById(perId).children
              ? document.getElementById(perId).children
              : [];
          // console.log(c)
          for (let j = 0; j < c.length; j++) {
            var id = c[j].getAttribute("id");
            var original = document.getElementById(id);
            var replacement = document.createElement("div");
            for (var i = 0, l = original.attributes.length; i < l; ++i) {
              var nodeName = original.attributes[i].name;
              var nodeValue = original.attributes[i].value;
              if (nodeName != "type") {
                replacement.setAttribute(nodeName, nodeValue);
              }
            }
            replacement.innerHTML = `<div class='inner-serch' data-item='${
              this.searchData[j].id
            }'>${this.searchData[j].name}</div>`;
            original.parentNode.replaceChild(replacement, original);
          }
          $(".inner-serch").click(e => {
            e.preventDefault();
            let id = $(e.target).data("item");
            const data = this.searchData.find(f => f.id === parseInt(id));
            this.selectedProduct = data.name;
            (<HTMLInputElement>(
              document.getElementById("typeahead-http")
            )).value = "hello";
          });
        }, 100);
      })
    );
  };

  setProduct(data) {
    console.log(data);
    // this.onAssignList.emit({
    //   asset_id: 1,
    //   product_name: "new",
    //   varient_chain: "45",
    //   current_condition: 1,
    //   current_status: 1
    // });
    if (data.available) {
      this.service
      .assignAsset(this.order_id, data.asset_id)
      .then(res => {
        if (res.status === "OK") {
          this.onAssignList.emit(res.result.data);
        } else {
          this.alertS.error(this.alertContainer, res.result.error, true, 5000);
        }
      })
      .catch(err => {
        this.alertS.error(
          this.alertContainer,
          "Something went wrong !! Please try again !! ",
          true,
          5000
        );
      });
    } else {
      this.onShowUnavailable.emit({ status: true, data });
    }
  }
  getProduct(params): any {
    this.loader = true;
    if (!params && params === "") {
      this.loader = false;
      return [];
    }
    return this.service.searchAsset(this.order_id, params).pipe(
      map(res => {
        if (res.status === "NOK") {
          this.alertS.error(this.alertContainer, res.result.error, true, 5000);
          return;
        }
        if (res.status === "OK" && res.result.asset_found === "one") {
          this.onAssignList.emit(res.result.data);
          return;
        }
        if (res.status === "OK" && res.result.asset_found === "none") {
          this.onShow.emit({ status: true, params: params });
          return;
        }
        //console.log(res);
        this.searchData = res.result.data;
        this.loader = false;
        setTimeout(() => {
          $(".home .dropdown-menu.show button").hover(() => {}, () => {});
        }, 100);
        return res.result.data.map(r => r);
      }),
      retry(3),
      catchError(() => {
        this.loader = false;
        return [];
      })
    );
  }

  formatter = x => x.name;
}
