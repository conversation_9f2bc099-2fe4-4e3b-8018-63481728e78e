<div class="custom-alert" #hasCusAlert></div>

<div class="search-product">
    <div class="header-part">
      <div class="input-group search-area">
        {{selectedProduct}}
        <div class="admin-asset-search admin-asset-search-2">
          <ng-template #rt let-r="result" let-t="term">
            <div (click)="setProduct(r)">
              <div> {{r.product_name}} (#{{r.serial_no}})</div>
              <span *ngIf="!r.available" class="status m-badge m-badge--wide">Not available</span>
              <div class="colorPurpel" *ngIf="r.variant_chain && r.variant_chain!=='Unassigned: Unassigned'">
                <small style="font-style: italic">{{ r.variant_chain }}</small>
              </div>
            </div>
          </ng-template>
          <input id="typeahead-http" name="search" type="text" class="form-control" [resultTemplate]="rt"
            [(ngModel)]="params" [ngbTypeahead]="search" [inputFormatter]="formatter" [focusFirst]="false"
            placeholder="Search.." />
        </div>
        <div *ngIf="loader" class="m-loader" style="        right: 68px;
        position: absolute;
        z-index: 999;
        top: 18px;"></div>

        <div class="input-group-btn">
          <button type="button" class="btn btn-search btn-default search-btn">
            <i class="fa fa-search"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
