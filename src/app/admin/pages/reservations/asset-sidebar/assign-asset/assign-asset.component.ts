import { Component, OnInit, ElementRef, ViewChild } from "@angular/core";
import {
  ASSETS_STATUS,
  ASSETS_CONDITION
} from "../../../../../globals/_classes/functions";
import { Router, ActivatedRoute } from "@angular/router";
import { InventoryService } from "../../../inventory/inventory-serveice/inventory.service";
import { AlertService } from "../../../../../modules/alert/alert.service";
import { Asset } from "../../../inventory/product-asset/asset-models/asset.models";
import { AssignAssetService } from "../../order.service/assign-asset.service";
import { OrderService } from "../../order.service/order.service";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { DialogBoxComponent } from "../../../../../modules/dialog-box/dialog-box.component";
import { Helpers } from "../../../../../helpers";

@Component({
  selector: "app-assign-asset",
  templateUrl: "./assign-asset.component.html",
  styleUrls: ["./assign-asset.component.css"]
})
export class AssignAssetComponent implements OnInit {
  assets: Asset[] = [];
  loader: boolean;
  order_items_id;
  asset_id;
  order_id;
  @ViewChild("hasCusAlert", {static: false}) alertContainer: ElementRef;
  quantity_id;
  productList = [];
  showPickup = false;
  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private service: AssignAssetService,
    private alertS: AlertService,
    private orderService: OrderService,
    private modalService: NgbModal
  ) {
    // console.log(this.route);
    //  this.getQueryParams();
    this.order_id = this.route.parent.parent.parent.parent.snapshot.params.order_id;
    this.getProducts();
  }

  getQueryParams() {
    const query = this.route.snapshot.queryParams;
    for (let c in query) {
      if (c === "quantity_id") {
        this.quantity_id = query[c];
      }
      if (c === "order_items_id") {
        this.order_items_id = query[c];
        this.getAssets();
      }
    }
  }
  ngOnChanges() {}

  ngOnInit() {
    // const obj = this.route.snapshot.queryParams;
    // if (!Object.keys(obj).length) {
    //   this.getAssets();
    // }
  }
  getAssets(filter?) {
    this.loader = true;
    let query = "";
    if (filter) {
      query = `${filter}`;
    }
    console.log(query);
    this.service.getAssets(false, query).subscribe(res => {
      this.assets = res.data ? res.data : [];
      this.loader = false;
    });
  }

  getCurrentCondition(id) {
    const obj = ASSETS_CONDITION.find(
      item => Number(item.value) === Number(id)
    );
    return obj;
  }
  getCurrentStatus(id) {
    const obj = ASSETS_STATUS.find(item => Number(item.value) === Number(id));
    return obj;
  }
  // assign(asset_id) {
  //   this.service
  //     .assignAsset({
  //       asset_id: asset_id,
  //       order_item_id: Number(this.order_items_id)
  //     })
  //     .then(res => {
  //       if (res.result.error) {
  //         this.alertS.error(this.alertContainer, res.result.error, true, 5000);
  //       } else {
  //         this.getAssets();
  //         this.alertS.success(
  //           this.alertContainer,
  //           res.result.message,
  //           true,
  //           5000
  //         );
  //       }
  //     })
  //     .catch(err => {
  //       this.alertS.error(
  //         this.alertContainer,
  //         "Something wrong Please try again !!!",
  //         true,
  //         5000
  //       );
  //     });
  // }
  discharge(asset_id) {
    this.service
      .dischargeAssign({
        asset_id: asset_id,
        order_item_id: Number(this.order_items_id)
      })
      .then(res => {
        if (res.result.error) {
          this.alertS.error(this.alertContainer, res.result.error, true, 5000);
        } else {
          this.getAssets();
          this.alertS.success(
            this.alertContainer,
            res.result.message,
            true,
            5000
          );
        }
      })
      .catch(err => {
        this.alertS.error(
          this.alertContainer,
          "Something wrong Please try again !!!",
          true,
          5000
        );
      });
  }

  getLocation(id) {
    const obj = JSON.parse(localStorage.getItem("locations")).find(
      item => Number(item.id) === Number(id)
    );
    return obj.name;
  }
  reloadTable(e) {
    this.order_items_id = e.item_id;
    this.getAssets(e.query);
  }
  navigate(id) {
    this.router.navigateByUrl("admin/inventory/product-asset/details/" + id);
  }

  getProducts() {
    this.loader = true;
    let order_items = [];
    this.orderService.getProductList(this.order_id).subscribe(
      res => {
        if (res.data && res.data.order_items.length) {
          this.checkPickup(res);
          order_items = res.data.order_items;
          this.productList = order_items.map(item => {
            return { order_item_id: item.id, product: item.product };
          });
          const query = `item_id=${
            this.productList[0].order_item_id
          }&is_search=true`;
          this.order_items_id = this.productList[0].order_item_id;
          this.getAssets(query);
        }
      },
      err => console.log(err)
    );
  }
  checkPickup(res) {
    this.showPickup =
      res.data.status == 3 || res.data.status == 2 ? true : false;
  }
  pickup() {
    const modalRef = this.modalService.open(DialogBoxComponent, {
      centered: true,
      size: "sm"
    });
    modalRef.componentInstance.massage =
      "The customer has picked up this order?";
    modalRef.result.then(
      result => {
        if (result) {
          this.statusChange(5);
        }
      },
      res => {
        console.log(res);
      }
    );
  }

  statusChange(status) {
    this.orderService
      .changeStatus(this.order_id, status)
      .then(res => {
        Helpers.setLoading(false);
        this.alertS.success(
          this.alertContainer,
          `Status of ${this.order_id} has been changed`,
          true,
          5000
        );
      })
      .catch(err => {
        console.log(err);
        Helpers.setLoading(false);
        this.alertS.error(
          this.alertContainer,
          `Something wrong! Status of ${this.order_id} has been not changed`,
          true,
          5000
        );
      });
  }
}
