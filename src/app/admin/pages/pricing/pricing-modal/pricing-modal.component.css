.m-pricing-table-2 .m-pricing-table-2__head h1 {
    color: #334960;
}
.m-pricing-table-2 .m-pricing-table-2__head .btn-group .btn.active {
    background: #334960;
}
.m-pricing-table-2__head {
    padding-bottom: 50px;
}
.tab-pane {
    padding: 0 50px; 
}
.table-bordered th, 
.table-bordered td {
    border: none ;
}
table tr th,
table tr td {
    text-align: center;
    font-weight: 400;
    color: #798898;
    border-left: 1px solid #f4f5f8;
}
table tr th:nth-child(1),
table tr th:nth-child(4),
table tr td:nth-child(1),
table tr td:nth-child(4) {
    border: 1px solid #f4f5f8;
    border-right: none;
}

table tr th:nth-child(2),
table tr td:nth-child(2) {
    border-left: 1px solid #f4f5f8;
    border-right: none;
    border-top: 1px solid #f4f5f8;
    border-bottom: 1px solid #f4f5f8;
}
.table-bordered tr.plan-name th {
    border: none;
}
.plan-name th {
    color: #58626b !important;
    font-size: 23px;
    height: 150px;
    font-weight: 500 !important;
    border: none;
}
.plan-name th:first-child {
    border-right: 1px solid #eee !important;
    border-top: none;
    border-left: none;
}
.plan-price {
    background-color: transparent;
}
.plan-price th {
    border: none;
    border-left: 1px solid #eee;
    border-top: 1px solid #eee;
    background-color:#fff;
}
.plan-price th:first-child {
    border-top: none;
}
.plan-price h3 {
    font-size: 40px;
    color: #334960;
}
.plan-price h3 small {
    font-size: 12px;
    margin: 0 5px;
    font-weight: 500;
}
.table-w-150 {
    width: 350px;
}
.plan-price h5 {
    color: #334960;
    font-size: 23px;
}
.plan-bottom-price th {
    padding: 25px 0;
}
.bg-white {
    background-color: #fff !important;
}
.tab-pane p {
    font-weight: 400;
    color: #798898;
    padding-bottom: 0;
    margin-bottom: 0;
    padding-top: 15px;
}
.table-bordered th:nth-child(3), 
.table-bordered td:nth-child(3) {
    border-left: none !important;
}
table th.tc-active,
table td.tc-active {
    border-top: 1px solid #f4f5f8 !important;
    border-bottom: 1px solid #f4f5f8 !important;
    border-left: 1px solid #334960 !important;
    border-right: 1px solid #334960 !important
}
table tr:first-child th.tc-active{
    border-top: 1px solid #334960 !important
}
table tr:last-child td.tc-active{
    border-bottom: 1px solid #334960 !important
}

.m-pricing-table-2 .m-pricing-table-2__head {
  padding-bottom: 0 !important;
}


/* pricing new css */
*, ::after, ::before {
    box-sizing: border-box;
  }
  body {
    /* font-family: 'Roboto', sans-serif; */
    line-height: 1.5;
    padding: 0;
    margin: 0;
  }
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    padding: 0;
    margin: 0;
    line-height: 100%;
    /* font-family: 'Roboto', sans-serif; */
    font-weight: 500;
    letter-spacing: 0.2px;
  }
  p {
    padding: 0;
    margin: 0;
    line-height: 100%;
    /* font-family: 'Roboto', sans-serif; */
    letter-spacing: 0.2px;
  }
  span {
    /* font-family: 'Roboto', sans-serif; */
    line-height: 100%;
    letter-spacing: 0.2px;
  }
  sub, sup {
    position: relative;
    font-size: 75%;
    line-height: 0;
    vertical-align: baseline;
    /* font-family: 'Roboto', sans-serif; */
  }
  ul {
    list-style: none;
    margin: 0;
    padding: 0;
  }
  a {
    text-decoration: none;
  }
  a:hover {
    text-decoration: none;
  }
  
  .rentmypricing-container {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
  }
  
  @media (min-width: 576px){
    .rentmypricing-container {
      max-width: 540px;
    }
  }
  @media (min-width: 768px){
    .rentmypricing-container {
      max-width: 720px;
    }
  }
  @media (min-width: 992px){
    .rentmypricing-container {
      max-width: 960px;
    }
  }
  
  @media (min-width: 1200px){
    .rentmypricing-container {
        max-width: 1140px;
    }
  }
  .renmty-pricing-row {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
  }
  .renmty-pricing-collum-12,
  .renmty-pricing-collum-6,
  .renmty-pricing-collum-4,
  .renmty-pricing-collum-3,
  .renmty-pricing-collum-2 {
    position: relative;
    width: 100%;
    min-height: 1px;
    padding-right: 15px;
    padding-left: 15px;
  }
  .renmty-pricing-collum-12 {
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }
  .renmty-pricing-collum-6 {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }
  .renmty-pricing-collum-4 {
    -ms-flex: 0 0 33.333333%;
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }
  .renmty-pricing-collum-3 {
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
  }
  
  .rentmypricing-wraper{
      padding: 0px;
      margin: 0px;
      width: 100%;
  }
  
  /*-- pricing plan --*/
  .rentmy-pricing-plan {
    background-color: #fff;
    padding: 50px 0;
  }
  .section-title-area {
    padding-bottom: 50px;
    text-align: center;
  }
  .section-title-area h1 {
    /* font-size: 45px; */
    font-size: 2.5rem;
    padding-bottom: 0;
    color: #f25f48;
    /* color: #474c54; */
  }
  .section-title-area h2 {
    font-size: 20px;
    color: #93a0b3;
    font-weight: 400;
  }
  .pricingtable-content {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
  }
  .pricingtable-top-content-tab {
    display: flex;
  }
  .pricing-table-list {
    display: block;
    -moz-appearance: none;
    -webkit-appearance: none;
    appearance: none;
    background: transparent;
    border: 0;
    border-color: #e6e8ec;
    border-style: solid;
    border-width: 1px 0;
    color: #414042;
    cursor: pointer;
    font-family: inherit;
    font-size: inherit;
    font-size: 16px;
    font-style: inherit;
    font-weight: inherit;
    font-weight: 500;
    line-height: inherit;
    margin: 0;
    max-width: 40vw;
    padding: 15px;
    vertical-align: baseline;
    width: 250px;
    border: 1px solid #f2f3f8;
    text-align: center;
  }
  .tab-pane.active  {
    transition: all .5s;
  }
  #rentmy-billed-annually {
    display: block;
  }
  #rentmy-billed-monthly {
    display: none;
  }
  /* pricing table 2  */
  .rentmytable-mtb-15 {
    margin: 15px 0;
  }
  .rentmytable-ptb-40 {
    padding: 40px 0;
  }
  .rentmytable-mb-30 {
    margin-bottom: 30px;
  }
  .rentmytable-pricing-5 {
    padding: 30px 0;
    text-align: center;
    z-index: 1;
    position: relative;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    border-radius: 10px;
    margin-top: 0;
    border: 1px solid #f2f2f2;
    transition: all 0.5s ease-in-out;
    transition: all 0.5s ease-in-out;
    -moz-transition: all 0.5s ease-in-out;
    -ms-transition: all 0.5s ease-in-out;
    -o-transition: all 0.5s ease-in-out;
    -webkit-transition: all 0.5s ease-in-out;
  }
  .white-bg {
    background: #ffffff;
  }
  .rentmytable-pricing-5:hover, .rentmytable-pricing-5.active {
    margin-top: -5px;
    box-shadow: 0px 0px 50px 0px rgba(0, 0, 0, 0.1);
    border: 1px solid #334960;
  }
  .pricing-header {
    color: #7b7b7b;
    font-weight: 600;
    letter-spacing: 1px;
    min-height: 235px;
  }
  .rentmytable-tw-6 {
    font-weight: 500;
  }
  
  .rentmytable-mb-10 {
    margin-bottom: 10px;
  }
  .rentmytable-tw-6 {
    font-weight: 600;
  }
  .rentmytable-mtb-30 {
    margin: 30px 0;
  }
  .rentmytable-mtb-20 {
    margin: 20px 0;
  }
  .rentmytable-mr-10 {
    margin-right: 10px;
  }
  .rentmytable-mr-0 {
    margin-right: 0px;
  }
  .rentmytable-main-plan {
    border: none;
  }
  .rentmytable-main-plan:hover, 
  .rentmytable-main-plan.active {
    margin-top: 0;
    box-shadow: none;
    border: none;
  }
  .rentmytable-mtb-30 h6 {
    color: #7b7b7b;
    font-weight: 500;
    letter-spacing: 1px;
    margin-bottom: 0;
    line-height: 40px;
    text-transform: uppercase;
  }
  .rentmytable-mtb-20 {
    margin: 0;
    height: 40px;
    line-height: 40px;
  }
  .rentmytable-main-plan .rentmytable-mtb-20 {
    margin: 0;
  }
  .rentmy-pricing-plan .button {
    color: #fff !important;
    cursor: pointer;
    padding: 10px 36px;
    font-weight: 500;
    font-size: 15px;
    border: none;
    position: relative;
    background: #334960;
    display: inline-block;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    text-transform: unset;
    border-radius: 50px;
    text-transform: uppercase;
  }
  .rentmy-pricing-plan a, 
  .rentmy-pricing-plan .button, 
  .rentmy-pricing-plan input {
    outline: medium none !important;
    color: #334960;
  }
  .rentmy-pricing-plan a, 
  .rentmy-pricing-plan .button {
    transition: all 0.5s ease-in-out;
    transition: all 0.5s ease-in-out;
    -moz-transition: all 0.5s ease-in-out;
    -ms-transition: all 0.5s ease-in-out;
    -o-transition: all 0.5s ease-in-out;
    -webkit-transition: all 0.5s ease-in-out;
    color: #333333;
  }
  .pricintable-tabtwo .nav.nav-pills {
    background-color: #334960;
    border-radius: 50px;
    padding: 5px;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    list-style: none;
    margin-bottom: 15px;
  }
  .pricintable-tabtwo .rentmypricing-tab-menu li .pricing-table-list {
    color: #fff;
    border: none;
  }
  .pricintable-tabtwo .rentmypricing-tab-menu li.active .pricing-table-list {
    background-color: #fff;
    border-color: #fff;
    color: #334960;
    border-radius: 50px;
  }
  
  /*-- new css --*/
  .rentmytable-collum {
    padding: 0 7px;
  }
  .icon-image {
    width: 100px;
    margin: 20px 0;
  }
  .main-plan-title {
   text-transform: uppercase;
   font-size: 22px;
   color: #7b7b7b;
  }
  .pricing-header span {
    margin-top: -10px;
    width: 100%;
    text-align: center;
    float: left;
    font-weight: 500;
    font-size: 14px;
    color: #888;
  }
  .price-title {
    font-size: 42px;
    color: #333;
  }
  .price-title sup {
    font-size: 14px;
    top: -19px;
    color: #888;
    font-weight: 500;
  } 
  .price-title small {
    font-size: 14px;
    color: #888;
    font-weight: 500;
  }
  .rentmytable-collum ul li {
    border-top: 1px solid #f2f3f8;
    font-weight: 400;
    color: #888;
  } 
  .rentmytable-collum ul li:last-child {
    border-bottom: 1px solid #f2f3f8;
  } 
  .rentmytable-collum ul li i {
    font-size: 12px;
    color: #888;
  }
  
  /* extra plan area  */
  .extra-plan-area {
     padding: 30px 0;
  }
  .label-container {
    display: block;
    position: relative;
    padding-left: 35px;
    margin-bottom: 12px;
    cursor: pointer;
    font-size: 20px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-color: #f2f3f8;
    padding: 8px 0 8px 50px;
    border-radius: 50px;
    font-weight: 400;
    letter-spacing: 0.2px;
    color: #333;
    text-align: left;
  }
  .label-container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
  }
  .checkmark {
    position: absolute;
    top: 8px;
    left: 10px;
    height: 30px;
    width: 30px;
    background-color: #fff;
    border-radius: 50%;
  }
  .label-container:hover input ~ .checkmark {
    background-color: #f25f48;
  }
  .label-container input:checked ~ .checkmark {
    background-color: #f25f48;
  }
  .checkmark:after {
    content: "";
    position: absolute;
    display: none;
  }
  .label-container input:checked ~ .checkmark:after {
    display: block;
  }
  .label-container .checkmark:after {
    top: 10px;
    left: 10px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: white;
  }
  .extra-list ul {
    list-style: inside;
    padding-left: 10px;
  }
  .extra-list ul li {
    font-size: 15px;
    font-weight: 400;
    color: #555;
    padding: 2px 0;
  }
  
  /*-- core feature css --*/
  .rentmy-core-features {
    padding: 50px 0;
    background-color: #f2f3f8;
  }
  .rentmy-corefeature-box {
    background-color: #fff;
    text-align: center;
    padding: 30px 20px 20px;
    margin: 10px 0 20px;
    transition: all .3s;
  }
  .rentmy-corefeature-box:hover {
    box-shadow: 0px 0px 10px 1px rgba(190, 189, 189, 0.4)
  }
  .core-icon-image {
    width: 70px;
  }
  .rentmy-corefeature-box h4 {
    font-size: 18px;
    font-weight: 400;
    padding: 20px 0px 0;
    min-height: 94px;
    color: #6b7c94;
    line-height: 25px;
  }
  
  /*-- Extra feature css --*/
.rentmy-extra-feature {
    background-color: #fff;
    padding: 50px 0;
    padding-bottom: 15px;
    border-top: 1px solid #f2f3f8;
}
.rentmy-extra-feature .extra-list {
    margin: 10px 0;
}
.rentmy-extra-feature .rentmy-corefeature-box {
    box-shadow: 0px 0px 8px -1px rgba(190, 189, 189, 0.4);
    border: 5px solid transparent;
    cursor: pointer;
    border-radius: 4px;
}
.rentmy-extra-feature .rentmy-corefeature-box:hover {
    box-shadow: 0px 0px 10px 1px rgba(190, 189, 189, 0.4);
    border: 5px solid transparent;
}
/* .extra-pricing-active {
  box-shadow: 0px 0px 10px 1px rgba(190, 189, 189, 0.4);
  border: 5px solid #65af65 !important;
}
.rentmy-corefeature-box span {
  position: absolute;
  top: 28px;
  right: 33px;
  color: #fff;
  width: 30px;
  height: 30px;
  background-color: #65af65;
  box-shadow: 0px 0px 8px -1px rgba(190, 189, 189, 0.4);
  line-height: 34px;
  font-size: 10px;
  border-radius: 50px;
  display: none;
}
.rentmy-corefeature-box.extra-pricing-active span {
  display: block;
}
.extra-pricing-active span i {
  font-size: 14px;
} */
.rentmy-extra-feature .rentmy-corefeature-box h4 {
    height: auto;
    min-height: 80px;
}
.btn-info {
  color: #fff;
  background-color: #36a3f7;
  border-color: #36a3f7;
  padding: 11px 35px;
  font-size: 15px;
}  
  
@media (min-width:1200px) and (max-width: 1350px) {
  .rentmy-corefeature-box h4 {
    font-size: 16px;
  }
}
@media (max-width: 1199px) {
    .section-title-area h1 {
      font-size: 42px;
    }
    .pricing-table-list {
      padding: 12px;
      width: 225px;
    }
    .pricing-header {
      min-height: 217px;
    }
    .main-plan-title {
      font-size: 20px;
    }
    .icon-image {
      width: 90px;
    }
    .price-title {
      font-size: 40px;
    }
    .price-title sup {
      top: -17px;
    }
    .pricing-header span {
      margin-top: -8px;
    }
    .rentmy-corefeature-box h4 {
      font-size: 16px;
      line-height: 20px;
    }
    .label-container {
      font-size: 16px;
      padding: 11px 0 11px 50px;
    }
  }
  @media (max-width: 991px) {
    .rentmy-extra-feature .renmty-pricing-collum-4 {
      -ms-flex: 0 0 50%;
      flex: 0 0 50%;
      max-width: 50%;
    }
    .price-title {
      font-size: 35px;
    }
    .price-title sup {
      top: -13px;
    }
    .pricing-header span {
      margin-top: -5px;
    }
    .rentmy-corefeature-box h4 {
      font-size: 16px;
      line-height: 20px;
    }
  }
  @media (max-width: 767px) {
    .rentmy-core-features .renmty-pricing-collum-4 {
      -ms-flex: 0 0 50%;
      flex: 0 0 50%;
      max-width: 50%;
    }
    .section-title-area h1 {
      font-size: 40px;
    }
    .pricing-header {
      min-height: 160px;
    }
    .pricintable-tabtwo .nav.nav-pills {
      padding: 4px;
    }
    .pricing-table-list {
      padding: 10px;
      width: 220px;
    }
    .main-plan-title {
      font-size: 18px;
    }
    .icon-image {
      width: 70px;
      margin: 15px 0;
    }
    .price-title {
      font-size: 25px;
    }
    .price-title sup {
      top: -8px;
    }
    .pricing-header span {
      margin-top: 0;
    }
    .rentmy-pricing-plan .button {
      padding: 8px 25px;
      font-size: 15px;
    }
    .label-container {
      font-size: 12px;
      padding: 14px 0 14px 50px;
    }
    
  }
  @media (max-width: 575px) {
    .renmty-pricing-collum-4 {
      -ms-flex: 0 0 100%;
      flex: 0 0 100%;
      max-width: 100%;
    }
    .rentmytable-collum {
      padding: 0 15px;
    }
    .rentmy-core-features .renmty-pricing-collum-4 {
      -ms-flex: 0 0 100%;
      flex: 0 0 100%;
      max-width: 100%;
    }
    .rentmy-extra-feature .renmty-pricing-collum-4 {
      -ms-flex: 0 0 100%;
      flex: 0 0 100%;
      max-width: 100%;
    }
    .section-title-area h1 {
      font-size: 35px;
    }
    .section-title-area h2 {
      font-size: 16px;
    }
    .pricintable-tabtwo .nav.nav-pills {
      padding: 3px;
    }
    .pricing-table-list {
      padding: 8px;
    }
    .pricing-header {
      min-height: 160px;
    }
    .main-plan-title {
      font-size: 18px;
    }
    .icon-image {
      width: 70px;
      margin: 15px 0;
    }
    .price-title {
      font-size: 25px;
    }
    .price-title sup {
      top: -8px;
    }
    .pricing-header span {
      margin-top: 0;
    }
    .rentmy-pricing-plan .button {
      padding: 8px 25px;
      font-size: 15px;
    }
    .rentmy-corefeature-box h4 {
      font-size: 18px;
      line-height: 22px;
    }
    .label-container {
      font-size: 15px;
      padding: 12px 0 12px 50px;
    }
    .pricingtable-top-content-tab {
      width: 100%;
    }
    .pricintable-tabtwo .nav.nav-pills {
      width: 100%;
    }
    .pricintable-tabtwo .nav.nav-pills li {
      width: 50%;
    }
    .pricing-table-list {
      padding: 0;
      width: 100%;
      max-width: 100%;
      height: 40px;
      text-align: center;
      line-height: 40px !important;
    }
  }