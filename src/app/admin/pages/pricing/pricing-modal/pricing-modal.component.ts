import { SidebarService } from './../../sidebar-service/sidebar.service';
import { HttpService } from './../../../../modules/http-with-injector/http.service';
import { AsideNavComponent } from './../../../layouts/aside-nav/aside-nav.component';
import {
  Component,
  OnInit,
  HostListener,
  ElementRef,
  ViewChild,
  ViewEncapsulation
} from "@angular/core";
import {
  PlanList,
  FeatureList,
  SubscriptionPlan,
  newPlanList
} from "../models/pricing-models";
import { AlertService } from "../../../../modules/alert/alert.service";
import { ActivatedRoute, Router } from "@angular/router";
import { PricingService } from "../pricing.service";
import { Helpers } from "../../../../helpers";
import { SettingService } from "../../settings/setting-service/setting.service";
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

declare let Tawk_API: any;
declare let $: any;

@Component({
  selector: "pricing-modal",
  templateUrl: "./pricing-modal.component.html",
  styleUrls: ["./pricing-modal.component.css"],
  encapsulation: ViewEncapsulation.None
})
export class PricingModalComponent implements OnInit {
  isMonthly: boolean = true;
  isNew: boolean;
  current_plan;
  activePlanType;
  currentPlanPrice;

  planPeriodIsMonthly: boolean;
  featurelist = FeatureList;
  Loader: boolean;
  subsPlans;
  planList=[
    {
      id: 1, 
      name: 'LITE',
      price: {semiAnnual:'79',monthly:'0'},
      active:false,
      img: './assets/img/admin/pricing/kite.png'
  },
    {
    id: 2, 
    name: 'STANDARD',
    price: {semiAnnual:'109',monthly:'55'},
    active:false,
    img: './assets/img/admin/pricing/paper-plane-solid.png'
},
{
    id: 3, 
    name: 'PRO',
    price: {semiAnnual:'169',monthly:'150'},
    active:false,
    img: './assets/img/admin/pricing/rocket-solid2.png'
}
// {
//     id: 3, 
//     name: 'SILVER',
//     price: {semiAnnual:'135',monthly:'144'},
//     active:true
// },
// {
//     id: 4, 
//     name: 'PLATINUM',
//     price: {semiAnnual:'209',monthly:'209'},
//     active:false 
// }
];

featureList=[
  {name:'Mobile-friendly online store & back-office management',plan1:'fa fa-check',plan2:'fa fa-check',plan3:'fa fa-check',plan4:'fa fa-check',},
  {name:'Personalized webstore',plan1:'fa fa-check',plan2:'fa fa-check',plan3:'fa fa-check',plan4:'fa fa-check',},
  {name:'Rent and sell products',plan1:'fa fa-check',plan2:'fa fa-check',plan3:'fa fa-check',plan4:'fa fa-check',},
  {name:'No per-user fees',plan1:'fa fa-check',plan2:'fa fa-check',plan3:'fa fa-check',plan4:'fa fa-check',},
  {name:'Unlimited orders',plan1:'fa fa-check',plan2:'fa fa-check',plan3:'fa fa-check',plan4:'fa fa-check',},
  {name:'Currency option',plan1:'fa fa-check',plan2:'fa fa-check',plan3:'fa fa-check',plan4:'fa fa-check',},
  {name:'Custom labels for all fields',plan1:'fa fa-check',plan2:'fa fa-check',plan3:'fa fa-check',plan4:'fa fa-check',},
  {name:'Delivery & Pickup controls',plan1:'fa fa-check',plan2:'fa fa-check',plan3:'fa fa-check',plan4:'fa fa-check',},
  {name:'Rental Packages',plan1:'fa fa-check',plan2:'fa fa-check',plan3:'fa fa-check',plan4:'fa fa-check',},
  {name:'Customer database',plan1:'fa fa-check',plan2:'fa fa-check',plan3:'fa fa-check',plan4:'fa fa-check',},
  {name:'Digital Coupons',plan1:'fa fa-check',plan2:'fa fa-check',plan3:'fa fa-check',plan4:'fa fa-check',},
  {name:'Barcode tracking & asset tracking',plan1:'fa fa-check',plan2:'fa fa-check',plan3:'fa fa-check',plan4:'fa fa-check',},
  {name:'User accounts & permissions management',plan1:'fa fa-check',plan2:'fa fa-check',plan3:'fa fa-check',plan4:'fa fa-check',},
  {name:'SMS & email customer receipts, alerts and reminders',plan1:'fa fa-check',plan2:'fa fa-check',plan3:'fa fa-check',plan4:'fa fa-check',},
  {name:'Accept Online Payments using RentMy Payments*',plan1:'fa fa-check',plan2:'fa fa-check',plan3:'fa fa-check',plan4:'fa fa-check',},
  {name:'Category management (unlimited)',plan1:'fa fa-check',plan2:'fa fa-check',plan3:'fa fa-check',plan4:'fa fa-check',},
  {name:'Detailed Reports',plan1:'fa fa-check',plan2:'fa fa-check',plan3:'fa fa-check',plan4:'fa fa-check',},
  {name:'Capture & save digital signatures',plan1:'fa fa-check',plan2:'fa fa-check',plan3:'fa fa-check',plan4:'fa fa-check',},
  {name:'Indefinite Free Trial',plan1:'fa fa-check',plan2:'fa fa-check',plan3:'fa fa-check',plan4:'fa fa-check',},
  {name:'Custom checkout fields',plan1:'fa fa-check',plan2:'fa fa-check',plan3:'fa fa-check',plan4:'fa fa-check',},
  {name:'Custom receipts',plan1:'fa fa-check',plan2:'fa fa-check',plan3:'fa fa-check',plan4:'fa fa-check',},
  {name:'Edit orders to add products, charges and more',plan1:'fa fa-check',plan2:'fa fa-check',plan3:'fa fa-check',plan4:'fa fa-check',},
  {name:'WordPress Plugin',plan1:'fa fa-check',plan2:'fa fa-check',plan3:'fa fa-check',plan4:'fa fa-check',},
  {name:'Add & manage additional locations (store, warehouses)',plan1:'',plan2:'fa fa-check',plan3:'fa fa-check',plan4:'fa fa-check',},
  {name:'Manage shipments with ShipEngine	',plan1:'',plan2:'fa fa-check',plan3:'fa fa-check',plan4:'fa fa-check',},
  {name:'Connect Your Merchant Processor ( Stripe, authorize.net, PayPal & more)',plan1:'',plan2:'fa fa-check',plan3:'fa fa-check',plan4:'fa fa-check',},
  {name:'Optional Customer registration & account login',plan1:'',plan2:'',plan3:'fa fa-check',plan4:'fa fa-check',},
  {name:'Checkout on your domain	',plan1:'',plan2:'',plan3:'fa fa-check',plan4:'fa fa-check',},
  {name:'Dedicated Account Manager	',plan1:'',plan2:'',plan3:'fa fa-check',plan4:'fa fa-check',},
  {name:'FreeTier 2 technical support	',plan1:'',plan2:'',plan3:'fa fa-check',plan4:'fa fa-check',},
  {name:'QuickBooks integration	',plan1:'',plan2:'',plan3:'fa fa-check',plan4:'fa fa-check',},
  {name:'MailChimp integration',plan1:'',plan2:'',plan3:'fa fa-check',plan4:'fa fa-check',},
  {name:'Checkout on your domain (and hosting of your current website)',plan1:'',plan2:'',plan3:'',plan4:'fa fa-check',},
  {name:'Secure hosting of your domain	',plan1:'',plan2:'',plan3:'',plan4:'fa fa-check',},
  {name:'Prioritized layout & workflow consultation & design',plan1:'',plan2:'',plan3:'',plan4:'fa fa-check',},
  {name:'Optional private vendor/partner access (one-time activation fee applies)',plan1:'',plan2:'',plan3:'',plan4:'fa fa-check',}
]


  @ViewChild("hasCusAlert", {static: true}) alertContainer: ElementRef;
  constructor(
    public service: PricingService,
    public route: ActivatedRoute,
    public alert: AlertService,
    public settingService: SettingService
  ) {
    // super(router, sidebarS, alert, http, priceS, ngbModal, settingService);
    this.subsPlans = this.route.snapshot.data.subs_plans;

    sessionStorage.setItem('PlanList',JSON.stringify( this.subsPlans));
    
    const user = JSON.parse(localStorage.getItem("currentUser"));
    this.getCurrent_plan();

    if (user.subscription && user.subscription.account_type) {
      this.isMonthly = user.subscription.isMonthly;

      const plan_type = user.subscription.isActive ? user.subscription.account_type : 'FREE';
      if (plan_type == "FREE") {
        this.changeSubsPeriod("yearly");
      } else{
        if (this.isMonthly) {
          this.changeSubsPeriod("monthly");
        } else {
          this.changeSubsPeriod("yearly");
        }
      }

    } else {
      this.isMonthly = false;
      this.current_plan = "FREE";
      this.changeSubsPeriod("yearly");
    }
  }

  ngOnInit() {
    $(function () {
      $(".chat-help-btn2, #gethelp-closebar2").on("click", function () {
        console.log('clicked');
        $("#get-help2").toggleClass("toggle-show");
      });
    });

    this.service.upgrade_plan.subscribe(res => {
      if (res != null) {
        if (res == "error") {
          this.alert.error(
            this.alertContainer,
            "Something wrong!!",
            true,
            5000
          );
        } else {
          console.log(res)
          this.current_plan = res.data.account_type;
          this.settingService.onSubscriptionChange({account_type:this.current_plan});
          this.isMonthly = res.data.isMonthly;
          const user_data = JSON.parse(localStorage.getItem("currentUser"));
          res.data["card"] = true;
          user_data.subscription = res.data;
          user_data.subscription['isActive'] = true;
          localStorage.setItem("currentUser", JSON.stringify(user_data));

          if(res.data.hasOwnProperty('newInventory') && res.data.newInventory)
          {
            $('#SubsAlart').hide();
            sessionStorage.setItem('showSubscriptionAlert',"no")
          }  
         

          this.getCurrent_plan();
          if (this.isMonthly) {
            this.changeSubsPeriod("monthly");
          } else {
            this.changeSubsPeriod("yearly");
          }

          this.alert.success(this.alertContainer, res.message, true, 5000);
        }
      }
    });

  //  this.getSettings();

    $(function() {
      $('.rentmypricing-tab-menu a').click(function() {
    
        $('.rentmypricing-tab-menu li').removeClass('active');
        $(this).parent().addClass('active');

        var currentTab = $(this).attr('href');
        $('.rentmypricing-tab-content .rentmypricing-tab-panel').hide();
        $(currentTab).show();
    
        return false;
      });
    });
  }

  showAlert(e) {
    if (e.res) {
      this.alert.success(this.alertContainer, e.message, false, 5000);
    } else {
      this.alert.error(this.alertContainer, e.message, false, 5000);
    }
  }
  onLoad(e) {
    if (e) {
      Helpers.setLoading(true);
    } else {
      Helpers.setLoading(false);
    }
  }
  getCurrent_plan() {
    const user = JSON.parse(localStorage.getItem("currentUser"));
    if (user.subscription && user.subscription.account_type) {
      this.current_plan = String(user.subscription.isActive ? user.subscription.account_type : 'FREE').toUpperCase();
      this.isMonthly = user.subscription.isMonthly;

      if(this.current_plan !="FREE")
      {
        this.currentPlanPrice = this.isMonthly
        ? Number(
            this.planList.find(x => x.name == this.current_plan).price.monthly
          )
        : Number(
            this.planList.find(x => x.name == this.current_plan).price
              .semiAnnual
          );
      }
      else{
        this.current_plan = "FREE";
        this.isMonthly = true;
      }
     
    } else {
      this.current_plan = "FREE";
      this.isMonthly = true;
    }
  }

  getPlanByName(name){
   return this.planList.find(x=>x.name==name);
  }

  getPlanBtnName(planPrice) {
   return this.getBtnName(this.currentPlanPrice,planPrice);
  }

  getBtnName(currentPrice, planPrice) {
    let name = "";

    if (this.current_plan == "FREE" || this.current_plan == "Free") {
      return (name = "UPGRADE");
    } else {
      if (Number(currentPrice) === Number(planPrice)) {
        name = "CURRENT PLAN";
      } else if (Number(planPrice) > Number(currentPrice)) {
        name = "UPGRADE";
      } else {
        name = "DOWNGRADE";
      }
      return name;
    }
  }

  changeSubsPeriod(type) {
    this.isMonthly = type === "monthly" ? true : false;
    if (type === "monthly") {
      this.planList.map(item => {
        item["plan_id"] = this.service.getPlanId(
          item.name,
          this.subsPlans.month
        );
        item.id = this.service.getNewId(item.name, this.subsPlans.month);
        item.active =
          this.getBtnName(this.currentPlanPrice, item.price.monthly) ==
          "CURRENT PLAN"
            ? true
            : false;
        item["btnText"] = this.getBtnName(
          this.currentPlanPrice,
          item.price.monthly
        );
      });
    } else {
      this.planList.map(item => {
        item["plan_id"] = this.service.getPlanId(
          item.name,
          this.subsPlans.semi
        );
        item.id = this.service.getNewId(item.name, this.subsPlans.semi);
        item.active =
          this.getBtnName(this.currentPlanPrice, item.price.semiAnnual) ==
          "CURRENT PLAN"
            ? true
            : false;
        item["btnText"] = this.getBtnName(
          this.currentPlanPrice,
          item.price.semiAnnual
        );
      });
    }

    console.log(this.planList);
  }

  getSubsCription() {
    const user = JSON.parse(localStorage.getItem("currentUser"));
    const subscription = {
      account_type: this.current_plan,
      card: user.subscription.card ? user.subscription.card : null,
      isMonthly: this.isMonthly
    };
    return subscription;
  }

  getSettings() {
    this.getCurrent_plan();
    const user = JSON.parse(localStorage.getItem("currentUser"));
    if (user.subscription) {
      this.isMonthly = user.subscription.isMonthly;
      this.isNew = user.subscription.card ? false : true;
    } else {
      this.isMonthly = false;
      this.isNew = false;
    }
  }

  onClickChatExpert() {
    Tawk_API.toggle();
  }
}
