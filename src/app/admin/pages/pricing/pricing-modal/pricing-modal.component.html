<div class="custom-alert" #hasCusAlert></div>
<!-- <div class="custom-alert" #hasCusAlert></div>
<div class="m-subheader">
  <div class="d-flex align-items-center">
    <div class="row" style="width: 100%;">
      <div class="col-10">
        <h3 class="welcome-title">PLANS</h3>
      </div>
      <div class="col-2 text-right"></div>
    </div>
  </div>
</div>
<div class="m-content">
  <div class="m-portlet">
    <div class="m-portlet__body m-portlet__body--no-padding">
      <div class="m-pricing-table-4 pb-0">
        <div class="m-pricing-table-4__top">
          <div
            class="m-pricing-table-4__top-container m-pricing-table-4__top-container--fixed pb-5"
          >
            <div class="m-pricing-table-2">
              <div class="m-pricing-table-2__head pb-5">
                <div class="m-pricing-table-2__title m--font-light">
                  <h1>Transparent &amp; Simple Pricing</h1>
                </div>
                <div
                  class="btn-group nav m-btn-group m-btn-group--pill m-btn-group--air"
                  role="group"
                >
                  <button
                    type="button"
                    (click)="changeSubsPeriod('yearly')"
                    [ngClass]="{ 'active show ': !isMonthly }"
                    class="btn m-btn--pill m-btn--wide m-btn--uppercase m-btn--bolder "
                  >
                    Semi Annual
                  </button>
                  <button
                    type="button"
                    (click)="changeSubsPeriod('monthly')"
                    [ngClass]="{ 'active show': isMonthly }"
                    class="btn m-btn--pill m-btn--wide m-btn--uppercase m-btn--bolder"
                  >
                    Monthly
                  </button>
                </div>
              </div>
            </div>

            <div class="m-pricing-table-4__top-body">
              <div class="m-pricing-table-4__top-items">
                <div class="plans-pricing-body">
                  <div
                    *ngFor="let plan of planList"
                    class="plans-pricing-item"
                    [ngClass]="plan?.active ? 'active-plans-pricing' : ''"
                  >
                    <div class="plans-pricing-title">
                      <h3>{{ plan?.name }}</h3>
                      <h4 *ngIf="isMonthly && plan?.price.monthly != '0'">
                        ${{ plan?.price.monthly }}/mo
                      </h4>
                      <h4 *ngIf="!isMonthly && plan?.price.semiAnnual != '0'">
                        ${{ plan?.price.semiAnnual }}/mo
                      </h4>

                    </div>
                    <div class="plans-pricing-info">
                      <div
                        *ngIf="isMonthly && plan.period?.monthly != ''"
                        class="plan-pricing-duration"
                      >
                        <h5>{{ plan.period?.monthly }}</h5>
                      </div>
                      <div
                        *ngIf="isMonthly == false && plan.period?.semiAnnual != ''"
                        class="plan-pricing-duration"
                      >
                        <h5>{{ plan.period?.semiAnnual }}</h5>
                      </div>
                      <div *ngIf="plan?.popular" class="plans-pricing-popular">
                        <h5>Popular</h5>
                      </div>
                      <div class="plan-pricing-info-table">
                        <table class="table">
                          <tr *ngFor="let feature of plan?.featureList">
                            <td>{{ feature?.name }}</td>
                          </tr>
                        </table>
                      </div>
                    </div>
                    <div class="plans-pricing-bottom">

                      <upgrade-subscription
                        *ngIf="btnText !== 'CURRENT PLAN';
                          else currentPlan
                        "
                        (onLoad)="onLoad($event)"
                        (showAlert)="showAlert($event)"
                        [btnText]="plan?.btnText"
                        [plan]="plan"
                        [accountType]="plan.name"
                      ></upgrade-subscription>
                      <ng-template #currentPlan>
                        <button
                          type="button"
                          style="cursor: text"
                          class="btn m-btn--pill  btn-info m-btn--wide m-btn--uppercase m-btn--bolder m-btn--lg"
                        >
                          Current Plan
                        </button>
                      </ng-template>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="preload" *ngIf="Loader">
  <app-preloader></app-preloader>
</div> -->



<div class="m-subheader product-section-list">
  <div class="d-flex align-items-center">
    <div class="mr-auto">
      <h3 class="m-subheader__title m-subheader__title--separator">
        Plan
      </h3>
      <ul class="m-subheader__breadcrumbs m-nav m-nav--inline">
        <li class="m-nav__item m-nav__item--home">
          <a routerLink="/admin" class="m-nav__link m-nav__link--icon">
            <i class="m-nav__link-icon la la-home"></i>
          </a>
        </li>
        <li class="m-nav__separator">
          <i class="fa fa-angle-right"></i>
        </li>
        <li class="m-nav__item">
          <a class="m-nav__link">
            <span class="m-nav__link-text">
              Plan
            </span>
          </a>
        </li>
      </ul>
    </div>
  </div>
</div>

<div class="m-content">
  <div class="m-portlet">
    <div class="m-portlet__body m-portlet__body--no-padding">
      <div class="m-pricing-table-2">
        <div class="m-pricing-table-2__head">
          <div class="m-pricing-table-2__title m--font-light">
            <h1>
              Never Any Transaction Or Per-Contract Fees
            </h1>
          </div>

          <div class="rentmypricing-wraper">
            <section class="rentmy-pricing-plan">
                <div class="rentmypricing-container">
                    <div class="renmty-pricing-row">

                        <div class="renmty-pricing-collum-12">
        
                            <div class="rentmypricing-tab-block" id="rentmypricing-tab-block">
                                <div class="pricingtable-content pricintable-tabtwo">
                                    <div class="pricingtable-top-content-tab">
                                        <!-- <ul class="nav nav-pills rentmypricing-tab-menu">
                                            <li [ngClass]="{ 'active show': !isMonthly }">
                                                <a class="pricing-table-list" (click)="changeSubsPeriod('yearly')" href="#rentmy-billed-annually">
                                                    Billed Semi-annually
                                                </a>
                                            </li>
                                            <li [ngClass]="{ 'active show': isMonthly }">
                                                <a class="pricing-table-list" (click)="changeSubsPeriod('monthly')" href="#rentmy-billed-monthly">
                                                    Billed Monthly
                                                </a>
                                            </li>
                                        </ul> -->
                                    </div>
                                </div>


                                <div class="renmty-pricing-row">
                                  <div *ngFor="let plan of planList" class="renmty-pricing-collum-4 rentmytable-collum rentmytable-mtb-15">
                                      <div class="rentmytable-pricing-5 rentmytable-ptb-40 white-bg">
                                          <div class="pricing-header rentmytable-mb-30">
                                              <h6 class="rentmytable-tw-6 rentmytable-mb-10 main-plan-title">{{plan.name}}</h6>
                                              <img [src]="plan.img" class="icon-image" />
                                              <h3 class="rentmytable-tw-6 price-title"><sup>$</sup>{{isMonthly ? plan.price.monthly : plan.price.semiAnnual}}<small>/Per Month</small></h3>
                                              <!-- <span style="text-decoration: line-through;" *ngIf="plan.name!='FREE'">+${{plan.name == "START UP" ? '99' : '199'}} activation fee</span> -->
                                              <!-- <span style='margin-top:10px;'>{{plan.name == "FREE" ? '' : '(limited time offer!)'}} </span> -->
                                          </div>
                                          <ul class="rentmytable-mtb-30">
                                              <li class="rentmytable-mtb-20">
                                                {{plan.name == "LITE" ? '100 Products' : plan.name == "STANDARD" ? '500 Products + Core features': '1,500 Products + Core & Extras'}}
                                              </li>
                                          </ul>

                                          <upgrade-subscription *ngIf="btnText !== 'CURRENT PLAN';
                                              else currentPlan
                                            " (onLoad)="onLoad($event)" (showAlert)="showAlert($event)"
                                            [btnText]="getPlanBtnName(isMonthly ? plan.price.monthly : plan.price.semiAnnual)"
                                            [plan]="getPlanByName(plan.name)" [accountType]="plan.name"></upgrade-subscription>
                                          <ng-template #currentPlan>
                                            <button type="button" style="cursor: text"
                                              class="btn m-btn--pill  btn-info m-btn--wide m-btn--uppercase m-btn--bolder m-btn--lg">
                                              Current Plan 
                                            </button>
                                          </ng-template>
                                      </div>
                                  </div>

                                  <!-- <div class="renmty-pricing-collum-4 rentmytable-collum rentmytable-mtb-15">
                                      <div class="rentmytable-pricing-5 rentmytable-ptb-40 white-bg">
                                          <div class="pricing-header rentmytable-mb-30">
                                              <h6 class="rentmytable-tw-6 rentmytable-mb-10 main-plan-title">PRO</h6>
                                              <img src="./assets/img/admin/pricing/diamond-solid.png" class="icon-image" />
                                              <h3 class="rentmytable-tw-6 price-title">Let's Talk!</h3>
                                          </div>
                                          <ul class="rentmytable-mtb-30">
                                              <li class="rentmytable-mtb-20">Dream It & We Can Do It!</li>
                                          </ul>
                                          <a (click)="onClickChatExpert()" class="button rentmytable-mr-0">Upgrade</a>

                                      </div>
                                  </div> -->
                              </div>
                            


                                <!-- <div class="rentmypricing-tab-content">
        
                                    <div class="rentmypricing-tab-panel" id="rentmy-billed-annually">
                                        <div class="renmty-pricing-row">
                                            <div class="renmty-pricing-collum-4 rentmytable-collum rentmytable-mtb-15">
                                                <div class="rentmytable-pricing-5 rentmytable-ptb-40 white-bg">
                                                    <div class="pricing-header rentmytable-mb-30">
                                                        <h6 class="rentmytable-tw-6 rentmytable-mb-10 main-plan-title">Start up</h6>
                                                        <img src="./assets/img/admin/pricing/paper-plane-solid.png" class="icon-image" />
                                                        <h3 class="rentmytable-tw-6 price-title"><sup>$</sup>40<small>/Per Month</small></h3>
                                                        <span>+$99 Setup fee</span>
                                                    </div>
                                                    <ul class="rentmytable-mtb-30">
                                                        <li class="rentmytable-mtb-20">100 Products</li>
                                                    </ul>

                                                    <upgrade-subscription *ngIf="btnText !== 'CURRENT PLAN';
                                                        else currentPlan
                                                      " (onLoad)="onLoad($event)" (showAlert)="showAlert($event)"
                                                      [btnText]="getPlanBtnName(isMonthly ? planList[0].price.monthly : planList[0].price.semiAnnual)"
                                                      [plan]="getPlanByName(planList[0].name)" [accountType]="planList[0].name"></upgrade-subscription>
                                                    <ng-template #currentPlan>
                                                      <button type="button" style="cursor: text"
                                                        class="btn m-btn--pill  btn-info m-btn--wide m-btn--uppercase m-btn--bolder m-btn--lg">
                                                        Current Plan
                                                      </button>
                                                    </ng-template>
                                                </div>
                                            </div>
                                            <div class="renmty-pricing-collum-4 rentmytable-collum rentmytable-mtb-15">
                                                <div class="rentmytable-pricing-5 rentmytable-ptb-40 white-bg active">
                                                    <div class="pricing-header rentmytable-mb-30">
                                                        <h6 class="rentmytable-tw-6 rentmytable-mb-10 main-plan-title">SMB</h6>
                                                        <img src="./assets/img/admin/pricing/rocket-solid2.png" class="icon-image" />
                                                        <h3 class="rentmytable-tw-6 price-title"><sup>$</sup>130<small>/Per Month</small></h3>
                                                        <span>+$199 Setup fee</span>
                                                    </div>
                                                    <ul class="rentmytable-mtb-30">
                                                        <li class="rentmytable-mtb-20">1,000 Products</li>
                                                    </ul>
                                                    <upgrade-subscription *ngIf="btnText !== 'CURRENT PLAN';
                                                        else currentPlan2
                                                      " (onLoad)="onLoad($event)" (showAlert)="showAlert($event)"
                                                      [btnText]="getPlanBtnName(isMonthly ? planList[1].price.monthly : planList[1].price.semiAnnual)"
                                                      [plan]="getPlanByName(planList[1].name)" [accountType]="planList[1].name"></upgrade-subscription>
                                                    <ng-template #currentPlan2>
                                                      <button type="button" style="cursor: text"
                                                        class="btn m-btn--pill  btn-info m-btn--wide m-btn--uppercase m-btn--bolder m-btn--lg">
                                                        Current Plan
                                                      </button>
                                                    </ng-template>
                                                </div>
                                            </div>
                                            <div class="renmty-pricing-collum-4 rentmytable-collum rentmytable-mtb-15">
                                                <div class="rentmytable-pricing-5 rentmytable-ptb-40 white-bg">
                                                    <div class="pricing-header rentmytable-mb-30">
                                                        <h6 class="rentmytable-tw-6 rentmytable-mb-10 main-plan-title">PRO</h6>
                                                        <img src="./assets/img/admin/pricing/diamond-solid.png" class="icon-image" />
                                                        <h3 class="rentmytable-tw-6 price-title">Let's Talk!</h3>
                                                    </div>
                                                    <ul class="rentmytable-mtb-30">
                                                        <li class="rentmytable-mtb-20">Dream It & We Can Do It!</li>
                                                    </ul>
                                                    <a class="button rentmytable-mr-0" href="https://client.rentmy.co/auth/sign-up?plan=pro">Start for free</a>

                                                </div>
                                            </div>
                                        </div>
                                    </div>
        
                                    <div class="rentmypricing-tab-panel" id="rentmy-billed-monthly">
                                        <div class="renmty-pricing-row">
                                            <div class="renmty-pricing-collum-4 rentmytable-collum rentmytable-mtb-15">
                                                <div class="rentmytable-pricing-5 rentmytable-ptb-40 white-bg">
                                                    <div class="pricing-header rentmytable-mb-30">
                                                        <h6 class="rentmytable-tw-6 rentmytable-mb-10 main-plan-title">Start up</h6>
                                                        <img src="./assets/img/admin/pricing/paper-plane-solid.png" class="icon-image" />
                                                        <h3 class="rentmytable-tw-6 price-title"><sup>$</sup>55<small>/Per Month</small></h3>
                                                        <span>+$99 Setup fee</span>
                                                    </div>
                                                    <ul class="rentmytable-mtb-30">
                                                        <li class="rentmytable-mtb-20">100 Products</li>
                                                    </ul>
                                                    <a class="button rentmytable-mr-0" href="#">Start for free</a>
                                                </div>
                                            </div>
                                            <div class="renmty-pricing-collum-4 rentmytable-collum rentmytable-mtb-15">
                                                <div class="rentmytable-pricing-5 rentmytable-ptb-40 white-bg active">
                                                    <div class="pricing-header rentmytable-mb-30">
                                                        <h6 class="rentmytable-tw-6 rentmytable-mb-10 main-plan-title">SMB</h6>
                                                        <img src="./assets/img/admin/pricing/rocket-solid2.png" class="icon-image" />
                                                        <h3 class="rentmytable-tw-6 price-title"><sup>$</sup>150<small>/Per Month</small></h3>
                                                        <span>+$199 Setup fee</span>
                                                    </div>
                                                    <ul class="rentmytable-mtb-30">
                                                        <li class="rentmytable-mtb-20">1,000 Products</li>
                                                    </ul>
                                                    <a class="button rentmytable-mr-0" href="#">Start for free</a>
                                                </div>
                                            </div>
                                            <div class="renmty-pricing-collum-4 rentmytable-collum rentmytable-mtb-15">
                                                <div class="rentmytable-pricing-5 rentmytable-ptb-40 white-bg">
                                                    <div class="pricing-header rentmytable-mb-30">
                                                        <h6 class="rentmytable-tw-6 rentmytable-mb-10 main-plan-title">PRO</h6>
                                                        <img src="./assets/img/admin/pricing/diamond-solid.png" class="icon-image" />
                                                        <h3 class="rentmytable-tw-6 price-title">Let's Talk!</h3>
                                                    </div>
                                                    <ul class="rentmytable-mtb-30">
                                                        <li class="rentmytable-mtb-20">Dream It & We Can Do It!</li>
                                                    </ul>
                                                    <a class="button rentmytable-mr-0" href="#">Start for free</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                
                                </div> -->
                            </div>
                        </div>
                    </div>
                </div>
                <div style="margin-top: 20px;">
                  <h3>Need more? Let's talk about a custom solution for your requirements</h3>
                  <button class="btn m-btn--pill  btn-info m-btn--uppercase m-btn--bolder m-btn--md" type="button" (click)="onClickChatExpert()"  style="margin-top:20px;"> SHARE YOUR VISION</button>
                </div>
            </section>

            <section class="rentmy-core-features">
              <div class="rentmypricing-container">
                  <div class="renmty-pricing-row">
                      <div class="renmty-pricing-collum-12">
                          <div class="section-title-area">
                              <h1>Core Features</h1>
                              <h2></h2>
                          </div>
                      </div>
                  </div>
                  <div class="renmty-pricing-row">
                      <div class="renmty-pricing-collum-4">
                          <div class="rentmy-corefeature-box">
                              <img src="./assets/img/admin/pricing/mobile.png" class="core-icon-image" />
                              <h4>Mobile-friendly ordering & management</h4>
                          </div>
                      </div>
                      <div class="renmty-pricing-collum-4">
                          <div class="rentmy-corefeature-box">
                              <img src="./assets/img/admin/pricing/eccommerce.png" class="core-icon-image" />
                              <h4>eCommerce rental website Take payments using any supported processor</h4>
                          </div>
                      </div>
                      <div class="renmty-pricing-collum-4">
                          <div class="rentmy-corefeature-box">
                              <img src="./assets/img/admin/pricing/list.png" class="core-icon-image" />
                              <h4>Pick Lists, load sheets, reports & management tools </h4>
                          </div>
                      </div>
                      <div class="renmty-pricing-collum-4">
                          <div class="rentmy-corefeature-box">
                              <img src="./assets/img/admin/pricing/wp.png" class="core-icon-image" />
                              <h4>WordPress plug-in</h4>
                          </div>
                      </div>
                      <div class="renmty-pricing-collum-4">
                          <div class="rentmy-corefeature-box">
                              <img src="./assets/img/admin/pricing/free.png" class="core-icon-image" />
                              <h4>Take your time free trial</h4>
                          </div>
                      </div>
                      <div class="renmty-pricing-collum-4">
                          <div class="rentmy-corefeature-box">
                              <img src="./assets/img/admin/pricing/localization.png" class="core-icon-image" />
                              <h4>Localization options</h4>
                          </div>
                      </div>
                  </div>
      
              </div>
          </section>
    
            <section class="rentmy-core-features rentmy-extra-feature">
                <div class="rentmypricing-container">
                    <div class="renmty-pricing-row">
                        <div class="renmty-pricing-collum-12">
                            <div class="section-title-area">
                                <h1>All The Extras You Need!</h1>
                            </div>
                        </div>
                    </div>
                    <div class="renmty-pricing-row">
                        <div class="renmty-pricing-collum-4">
                            <div class="rentmy-corefeature-box">
                                <img src="./assets/img/admin/pricing/pos.png" class="core-icon-image" />
                                <h4>POS & Cash Drawer Powerful counter support</h4>
                            </div>
                        </div>
                        <div class="renmty-pricing-collum-4">
                            <div class="rentmy-corefeature-box extra-pricing-active">
                                <img src="./assets/img/admin/pricing/quickbooks.png" class="core-icon-image" />
                                <h4>QuickBooks Online Sync orders & payments</h4>
                            </div>
                        </div>
                        <div class="renmty-pricing-collum-4">
                            <div class="rentmy-corefeature-box">
                                <img src="./assets/img/admin/pricing/mailchimp.png" class="core-icon-image" />
                                <h4>MailChimp Easily Import & export customer info.</h4>
                            </div>
                        </div>
                        <div class="renmty-pricing-collum-4">
                            <div class="rentmy-corefeature-box">
                                <img src="./assets/img/admin/pricing/chat.png" class="core-icon-image" />
                                <h4>SMS Reminders & Customizable Receipts</h4>
                            </div>
                        </div>
                        <div class="renmty-pricing-collum-4">
                            <div class="rentmy-corefeature-box">
                                <img src="./assets/img/admin/pricing/customeraccount.png" class="core-icon-image" />
                                <h4>Customer Accounts View & clone orders</h4>
                            </div>
                        </div>
                        <div class="renmty-pricing-collum-4">
                            <div class="rentmy-corefeature-box">
                                <img src="./assets/img/admin/pricing/api.png" class="core-icon-image" />
                                <h4>RentMy APIs Power your website or app </h4>
                            </div>
                        </div>
                        <div class="renmty-pricing-collum-4">
                            <div class="rentmy-corefeature-box">
                                <img src="./assets/img/admin/pricing/shipping.png" class="core-icon-image" />
                                <h4>Shipping Options Dropship anywhere</h4>
                            </div>
                        </div>
                        <div class="renmty-pricing-collum-4">
                            <div class="rentmy-corefeature-box">
                                <img src="./assets/img/admin/pricing/signature.png" class="core-icon-image" />
                                <h4>Digital Signature Capture & save signatures</h4>
                            </div>
                        </div>
                        <div class="renmty-pricing-collum-4">
                            <div class="rentmy-corefeature-box">
                                <img src="./assets/img/admin/pricing/account.png" class="core-icon-image" />
                                <h4>Unlimited Employee Accounts</h4>
                            </div>
                        </div>
                    </div>
        
                </div>
            </section>
        
        </div>


          <!-- <div class="btn-group nav m-btn-group m-btn-group--pill m-btn-group--air" role="group">
            <button type="button" class="btn m-btn--pill  active m-btn--wide m-btn--uppercase m-btn--bolder"
              data-toggle="tab" (click)="changeSubsPeriod('yearly')" [ngClass]="{ 'active show ': !isMonthly }"
              role="tab">
              Semi Annual
            </button>
            <button type="button" class="btn m-btn--pill  m-btn--wide m-btn--uppercase m-btn--bolder" data-toggle="tab"
              (click)="changeSubsPeriod('monthly')" [ngClass]="{ 'active show': isMonthly }" role="tab">
              Monthly
            </button>


          </div> -->
        </div>
        <!-- <div class="tab-content">
          <div class="tab-pane active">
            <table class="table table-condensed table-hover">
              <thead>
                <tr class="plan-name">
                  <th class="bg-white"> </th>
                  <th *ngFor="let plan of planList">{{plan.name}}</th>
                </tr>
                <tr class="plan-price">
                  <th class="bg-white"></th>
                  <th *ngFor="let plan of planList">
                    <h3><small>$</small>{{isMonthly ? plan.price.monthly : plan.price.semiAnnual}}<small>/mo</small>
                    </h3>
                  </th>

                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let feature of featureList">
                  <td class="text-left table-w-150">{{feature?.name}}</td>
                  <td><i [ngClass]="feature?.plan1"></i></td>
                  <td><i [ngClass]="feature?.plan2"></i></td>
                  <td><i [ngClass]="feature?.plan3"></i></td>
                  <td><i [ngClass]="feature?.plan4"></i></td>
                </tr>
                <tr>
                <tr>
                  <td class="text-left table-w-150"># of Product</td>
                  <td>10</td>
                  <td>50</td>
                  <td>1500</td>
                  <td>Unlimited</td>
                </tr>
                <tr>
                  <td></td>
                  <td *ngFor="let plan of planList">
                    <upgrade-subscription *ngIf="btnText !== 'CURRENT PLAN';
                        else currentPlan
                      " (onLoad)="onLoad($event)" (showAlert)="showAlert($event)"
                      [btnText]="getPlanBtnName(isMonthly ? plan.price.monthly : plan.price.semiAnnual)"
                      [plan]="getPlanByName(plan.name)" [accountType]="plan.name"></upgrade-subscription>
                    <ng-template #currentPlan>
                      <button type="button" style="cursor: text"
                        class="btn m-btn--pill  btn-info m-btn--wide m-btn--uppercase m-btn--bolder m-btn--lg">
                        Current Plan
                      </button>
                    </ng-template>
                  </td>

                </tr>
              </tbody>
            </table>
            <p class="text-left">
              ** Additional locations billed at 50% of monthly subscription of primary location
            </p>
          </div>

        </div> -->
      </div>
    </div>
  </div>
</div>


<div class="get-help-body" id="get-help2">
  <a id="gethelp-closebar2" class="right-close">
    <i class="la la-close"></i>
  </a>
  <iframe
    src="https://tawk.to/chat/5c069b35fd65052a5c93b942/1d6bdmblc"
  ></iframe>
</div>