<div class="custom-alert" #hasCusAlert></div>

<div class="modal-body">
  <!-- <div class="modal-header">
    
  </div> -->
  <button (click)="onClickClose()" class="close" type="button">
    <i class="fa fa-times"></i>
  </button>
  <div class="row m-0">
    <div class="col-sm-6 popup-left p-5">
      <p class="mb-0">
        RentMy {{ planDetails?.plan?.product_name }} Subscription plan
      </p>
      <small>
        ${{
          planDetails?.price?.plan / planDetails?.plan?.interval_count
        }}/month (Billed {{ planDetails?.plan?.name }})
      </small>
      <small class="price">${{ totalPlanPrice | number: ".2-2" }}</small>
      <br />
      <hr />
      <p class="mb-1">
        Billed Location = {{ currentLocationCount }} &nbsp;&nbsp;&nbsp;&nbsp;(1
        x ${{ location_individual }}/month)
      </p>
      <small class="price">${{ locationTotal | number: ".2-2" }}</small>
      <span class="change-location mt-1" (click)="onClickChangeLocation()"
        >(Change Location)</span
      >
      <select
        *ngIf="is_show_changeLocation"
        (change)="OnselectLocation($event.target.value)"
        class="form-control"
      >
        <option value="-1">--select--</option>
        <option value="1">1</option>
        <option value="2">2</option>
        <option value="3">3</option>
        <option value="4">4</option>
        <option value="5">5</option>
        <option value="6">6</option>
        <option value="7">7</option>
        <option value="8">8</option>
        <option value="9">9</option>
        <option value="10">10</option>
      </select>
      <br /><br />
      <hr class="mt-5" />
      <p class="total">Total</p>
      <small class="total-price"
        >${{ totalPlanPrice + locationTotal | number: ".2-2" }}</small
      >

      <br /><br />
      <label class="m-checkbox">
        <input
          type="checkbox"
          [(ngModel)]="is_termsAndConditionChecked"
          (click)="onClickTerms()"
        />
        I hereby certify that i have read and understand the information
        provided to me in the Terms of Service regarding the companies standard
        operating procedures<span></span>
      </label>
    </div>
    <div class="col-sm-6 popup-right pt-3 pb-3">
      <!-- <div class="row mt-3">
        <div class="col-md-8">
          <div class="m-form__group form-group">
            <label class="m-radio mr-5">
              <input
                checked
                type="radio"
                name="stripeCard"
                value="stripe"
                (click)="onChangeCardType('newCard')"
              />
              Pay using new credit card
              <span></span>
            </label>
            <label
              *ngFor="let card of stripeOldCardList; let i = index"
              class="m-radio mr-5"
            >
              <input
                type="radio"
                name="stripeCard"
                value="stripe"
                (click)="onChangeCardType('oldCard', card.id)"
              />
              Pay using ****{{ stripeOldNumber(card) }}
              <span></span>
            </label>
            <br />
            <button
              *ngIf="is_ShowOldCardSubmitbtn"
              class="btn btn-info"
              (click)="onClickOldCardNumberSubmit()"
            >
              Subscribe
            </button>
          </div>
        </div>
      </div> -->
      <ng-template #stripe></ng-template>
    </div>
  </div>
</div>
