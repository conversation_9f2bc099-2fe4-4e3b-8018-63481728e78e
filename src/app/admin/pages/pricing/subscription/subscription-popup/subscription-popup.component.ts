import {
  Component,
  OnInit,
  ViewChild,
  ViewContainerRef,
  Type,
  ComponentFactoryResolver,
  ComponentRef,
  ElementRef
} from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { StripeComponent } from "../gateway/stripe/stripe.component";
import { PricingService } from "../../pricing.service";
import { Helpers } from "../../../../../helpers";
import { HttpService } from "../../../../../modules/http-with-injector/http.service";
import { map } from "rxjs/operators";
import { AlertService } from "../../../../../modules/alert/alert.service";
import { GET_USER } from "../../../../../globals/_classes/functions";

@Component({
  selector: "app-subscription-popup",
  templateUrl: "./subscription-popup.component.html",
  styleUrls: ["./subscription-popup.component.css"]
})
export class SubscriptionPopupComponent implements OnInit {
  upgradePlan;
  gateImage: string = "";
  allGateways = [];
  stripeOldCardList = [];
  currentLocationCount;
  is_ShowOldCardSubmitbtn = false;
  oldStripeCardToken;
  planDetails;
  totalPlanPrice;
  locationTotal;
  location_individual;
  customer_id;
  is_show_changeLocation = false;
  is_termsAndConditionChecked=false;

  componentRef: ComponentRef<any>;
  paymentInfo = new Object();
  @ViewChild("stripe", { read: ViewContainerRef })
  stripe: ViewContainerRef;

  @ViewChild("hasCusAlert") alertContainer: ElementRef;

  constructor(
    public activeModal: NgbActiveModal,
    private resolver: ComponentFactoryResolver,
    private service: PricingService,
    private http: HttpService,
    private alert: AlertService
  ) {}

  ngOnInit() {
    this.upgradePlan = JSON.parse(sessionStorage.getItem("upGradePlan"));

    this.getPaymentGateway();
    this.getPlanDetails(this.upgradePlan.id);
  }

  getPlanDetails(planId) {
    Helpers.setLoading(true);
    return this.http
      .get("subscription/details/" + planId)
      .toPromise()
      .then(res => {
        const data = res.result.data;
        this.planDetails = data;
        this.currentLocationCount = this.planDetails.locations;
        this.totalPlanPrice = this.planDetails.price.plan;
        this.location_individual = this.planDetails.price.location_individual;
        this.locationTotal = this.planDetails.price.location_total;
        this.stripeOldCardList = data.customer.data.sources.data;
        this.customer_id = data.customer.data.id;

        Helpers.setLoading(false);
      })
      .catch(err => {
        console.log(err);
      });
  }

  getPaymentGateway() {
    Helpers.setLoading(true);
    this.service.getGateways().subscribe(
      res => {
        this.allGateways = res;
        this.loadComponent("stripe");
        Helpers.setLoading(false);
      },
      err => {
        console.log(err);
      }
    );
  }

  onClickClose() {
    this.activeModal.close("Close click");
  }

  private loadComponent(name) {
    this.createComponent(StripeComponent, name);
    this.gateImage = "./assets/img/home/<USER>";
  }

  private createComponent(com: Type<any>, name?: string) {
    if (this.stripe != undefined) {
      const factory = this.resolver.resolveComponentFactory(com);
      this.stripe.clear();
      this.componentRef = this.stripe.createComponent(factory);
      this.componentRef.instance.onSelectPayment.subscribe(e => this.submit(e));
      if (name) {
        const data = this.allGateways.find(
          f => f.name.toLowerCase() === name.toLowerCase()
        );
      }
    }
  }

  submit(e) {
    this.paymentInfo = Object.assign(this.paymentInfo, e);
    this.submitPayment();
  }

  submitPayment() {
    this.paymentInfo["plan_id"] = this.planDetails.plan.id;
    this.paymentInfo["location"] = this.currentLocationCount;

    this.http
      .post("subscription/create", this.paymentInfo)
      .toPromise()
      .then(res => {
        if (res.status === "OK") {
          this.service.onPlanChange(res.result);
          this.activeModal.close("Close click");
          Helpers.setLoading(false);
        } else {
          this.service.onPlanChange("error");
          Helpers.setLoading(false);
        }
      })
      .catch(err => {
        this.service.onPlanChange("error");
        Helpers.setLoading(false);
      });
  }

  OnselectLocation(value) {
    const LocationCount = Number(value);
    if (!isNaN(LocationCount) && LocationCount > 0) {
      this.currentLocationCount = Number(value);
      this.locationTotal = this.location_individual * LocationCount;

      this.is_show_changeLocation = false;
    }
  }

  onChangeCardType(cardType, oldCardToken?) {
    if (cardType === "newCard") {
      this.is_ShowOldCardSubmitbtn = false;
      this.loadComponent("Stripe");
    } else {
      this.oldStripeCardToken = oldCardToken;
      this.is_ShowOldCardSubmitbtn = true;
      this.stripe.clear();
    }
  }

  onClickOldCardNumberSubmit() {
    Helpers.setLoading(true);
    this.paymentInfo["account"] = this.oldStripeCardToken;
    this.paymentInfo["plan_id"] = this.planDetails.plan.id;
    this.paymentInfo["paytype"] = "stripe_token";
    this.paymentInfo["location"] = this.currentLocationCount;

    this.http
      .post("subscription/create", this.paymentInfo)
      .toPromise()
      .then(res => {
        if (res.status === "OK") {
          Helpers.setLoading(false);

          this.activeModal.close("Close click");
        } else {
          Helpers.setLoading(false);
        }
      })
      .catch(err => {
        Helpers.setLoading(false);
      });
  }

  stripeOldNumber(oldCard) {
    return (
      oldCard.last4 +
      "(Exp - " +
      oldCard.exp_month +
      "/" +
      oldCard.exp_year +
      ")"
    );
  }

  onClickChangeLocation() {
    this.is_show_changeLocation = true;
  }

  onClickTerms(){
   this.is_termsAndConditionChecked=!this.is_termsAndConditionChecked;
   this.service.getSubscriptionTermsCheckValue(this.is_termsAndConditionChecked);
  }
}
