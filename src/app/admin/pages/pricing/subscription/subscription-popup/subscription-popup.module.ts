import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { SubscriptionPopupComponent } from './subscription-popup.component';
import { StripeSubsModule } from '../gateway/stripe/stripe.module';
import { StripeComponent } from '../gateway/stripe/stripe.component';

@NgModule({
  imports: [
    ReactiveFormsModule,
    FormsModule,
    CommonModule,
    StripeSubsModule
  ],
  providers:[],
  declarations: [SubscriptionPopupComponent],
  exports: [SubscriptionPopupComponent],
  entryComponents:[StripeComponent]
})
export class SubscriptionPopupModule { }
