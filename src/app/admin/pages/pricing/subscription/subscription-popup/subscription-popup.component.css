
.custom-alert {
    position: absolute;
    width: 100%;
    z-index: 9;
}
.popup-left{
    background-color:royalblue;
    color: #fff;
}
.m-checkbox {
    color: #fff !important;
}
.modal-body {
    padding: 0 !important;
}
hr {
    border-top: 1px solid #bbb;
}

.change-location {
    float: left;
    cursor: pointer;
    text-decoration: underline;
}
select {
    float: left;
    width: 115px;
    display: inline-block;
    margin-left: 15px;
    height: calc(2rem + 2px) !important;
    padding: 0.1rem 1rem;
}
.m-checkbox > span {
    border: 1px solid #fff !important;
    background-color: #fff !important;
}
.m-checkbox > input:checked ~ span {
    border: 1px solid #fff !important;
    background-color: #fff !important;
}
p {
    font-size: 18px;
}
span,
small {
    font-size: 15px;
    color: #eee;
}
.price {
    float: right;
    display: inline-block;
    font-size: 18px;
    color: #fff;
    margin-top: -8px;
}
.total-price {
    float: right;
    display: inline-block;
    font-size: 18px;
    color: #fff;
}
.total{
    display: inline-block;
}
button.close {
    padding: 0;
    background-color: transparent;
    border: 0;
    -webkit-appearance: none;
    background-color: #ffff;
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 9;
    width: 30px;
    height: 30px;
    box-shadow: 0px 0px 10px #ddd;
    border-radius: 100%;
    color: #555;
    line-height: 0px;
    opacity: unset;
}