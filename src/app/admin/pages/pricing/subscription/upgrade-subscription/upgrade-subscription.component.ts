import {
  Component,
  OnInit,
  Input,
  Output,
  AfterViewInit,
  HostListener,
  EventEmitter
} from "@angular/core";
import { HttpService } from "../../../../../modules/http-with-injector/http.service";
import {
  NgbModal,
  NgbModalOptions,
  NgbActiveModal
} from "@ng-bootstrap/ng-bootstrap";
import { DialogBoxComponent } from "../../../../../modules/dialog-box/dialog-box.component";
import { SubscriptionPopupComponent } from "../subscription-popup/subscription-popup.component";
import { map } from "rxjs-compat/operator/map";
import { Helpers } from "../../../../../helpers";
import { PricingService } from "../../pricing.service";
import { ActivatedRoute } from "@angular/router";

@Component({
  selector: "upgrade-subscription",
  templateUrl: "./upgrade-subscription.component.html",
  styleUrls: ["./upgrade-subscription.component.css"]
})
export class UpgradeSubscriptionComponent implements OnInit, AfterViewInit {
  stripeCheckoutHandler: any;
  @Input() plan;
  @Input() accountType: string;
  @Input() btnText = "Upgrade";
  @Input() addCardMode = false;
  @Input() isNew: boolean;
  @Output() showAlert = new EventEmitter();
  // tslint:disable-next-line:no-output-on-prefix
  // tslint:disable-next-line:no-output-on-prefix
  @Output() onLoad = new EventEmitter();
  constructor(
    private modalService: NgbModal,
    private http: HttpService,
    private service: PricingService,
    private route:ActivatedRoute
  ) {}

  ngOnInit() {
   console.log(this.btnText)

   let planList=[];
   let plan_name='';
   let type='';

   this.route.queryParamMap.subscribe(params => {
    plan_name = params.get("plan_name");
    type = params.get("type");
  });


  const planObj=JSON.parse(sessionStorage.getItem('PlanList'));
  let plan;
   if(plan_name==this.plan.name){
      if (type === "semi") {
        plan=planObj.semi[plan_name]
      }
      else{
        plan=planObj.month[plan_name]
      }

      this.openPopup(plan);
   }
   
  }

  ngAfterViewInit() {
    // this.stripeCheckoutLoader.createHandler({
    //   // key: environment.stripeKey,
    //   key: 'pk_live_OwX9QH4rBtTPhypoT5DLoJKh',
    //   image: 'https://rentmy.co/wp-content/uploads/2017/12/icon.png',
    //   locale: 'auto',
    //   panelLabel: this.btnText,
    //   allowRememberMe: false,
    //   email: this.getEmail(),
    //   token: (token) => {
    //   }
    // }).then((handler: StripeCheckoutHandler) => {
    //   this.stripeCheckoutHandler = handler;
    // });
  }

  // upgrade() {
  //   if ((this.planId && this.accountType) || this.addCardMode) {
  //     this.stripeCheckoutHandler.open({
  //       name: this.addCardMode ? 'Add New Card' : `${this.btnText} YOUR  PLAN`,
  //     }).then((token) => {
  //       if (this.addCardMode) {
  //         this.addCard(token.id);
  //       } else {
  //         this.upgradeMyPlan(token.id);
  //       }

  //     }).catch((err) => {
  //       // Payment failed or was canceled by user...
  //     });
  //   }
  // }

  // addCard(token) {
  //   this.http.post('subscription/add-card', { source: token }).toPromise()
  //     .then(res => {
  //       if (res.status === 'OK') {
  //         this.showAlert.emit({ res: true, message: res.result.message });
  //       } else {
  //         this.showAlert.emit({ res: false, message: res.result.error });
  //       }
  //       this.onLoad.emit(false);
  //     }).catch(err => {
  //       this.onLoad.emit(false);
  //       this.showAlert.emit({ res: false, message: 'Something Wrong Please try again !! ' });
  //     });
  // }
  // @HostListener('window:popstate')
  // onPopstate() {
  //   this.stripeCheckoutHandler.close();
  // }
  // upgradeMyPlan(token) {
  //   this.onLoad.emit(true);
  //   this.http.post('subscription/create', { plan_id: this.planId, source: token }).toPromise()
  //     .then(res => {
  //       if (res.status === 'OK') {
  //         this.showAlert.emit({ res: true, message: res.result.message });
  //         this.saveUserData();
  //         this.onUpgrade.emit(true);
  //       } else {
  //         this.showAlert.emit({ res: false, message: res.result.error });
  //       }
  //       this.onLoad.emit(false);
  //     }).catch(err => {
  //       this.onLoad.emit(false);
  //       this.showAlert.emit({ res: false, message: 'Something Wrong Please try again !! ' });
  //     });
  // }

  openPopup(plan) {
    sessionStorage.setItem("upGradePlan", JSON.stringify(plan));
    this.showSubscriptionPopup();

    // if (this.isNew) {
    //   this.upgrade();
    // } else {
    //   this.changePlan();
    // }
  }

  showSubscriptionPopup() {
    const user_data = localStorage.getItem("currentUser")
      ? JSON.parse(localStorage.getItem("currentUser"))
      : {};
     let accountType="FREE";

    if (
      user_data != null &&
      user_data.subscription.hasOwnProperty("account_type") &&
      user_data.subscription.account_type != "" &&
      user_data.subscription.account_type != null
    ) {
      accountType = String(user_data.subscription.account_type).toUpperCase();
    } else {
      accountType = "FREE";
    }


    if (accountType == "FREE") {
      let ngbModalOptions: NgbModalOptions = {
        backdrop: "static",
        keyboard: false,
        size: "lg",
        centered: true
      };

      const modalRef = this.modalService.open(
        SubscriptionPopupComponent,
        ngbModalOptions
      );
      modalRef.componentInstance.name = "SubscriptionPopup";
    } else {
      this.changePlan();
    }
  }

  changePlan() {
    const modalRef = this.modalService.open(DialogBoxComponent, {
      centered: true,
      size: "sm",
      windowClass: "animated fadeIn"
    });
    modalRef.componentInstance.massage = `Are you sure you want to ${
      this.btnText
    }?`;
    modalRef.result.then(resp => {
      if (resp) {
        this.onLoad.emit(true);
        this.http
          .post("subscription/change-plan ", { plan_id: this.plan.id })
          .toPromise()
          .then(res => {
            if (res.status === "OK") {
              this.service.onPlanChange(res.result);
              Helpers.setLoading(false);
            } else {
              Helpers.setLoading(false);
            }
          })
          .catch(err => {
            this.service.onPlanChange("error");
            Helpers.setLoading(false);
          });
      } else {
        Helpers.setLoading(false);
      }
    });
  }

  // getEmail() {
  //   return JSON.parse(localStorage.getItem('currentUser')).email;
  // }
}
