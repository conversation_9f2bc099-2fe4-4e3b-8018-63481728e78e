
.m-accordion.m-accordion--bordered .m-accordion__item, .m-accordion.m-accordion--default .m-accordion__item {
    border: none
}

.m-portlet {
    box-shadow: none;
    background: transparent;
}
form {
    padding: 15px 20px;
}
.card-header {
    padding: 0;
}

.accordion_btn {
    width: 100%;
    text-align: left;
    padding: 2.5% 5%;
}

.card {
    margin: 3% 0;
}




 .partial_payment {
    height: 105px;
 }

 #cardnumber {
    position: relative;
}
.credicardImg {
    width: 50px;
    position: absolute;
    top: 29px;
    right: 1px;

}
.order-error p {
    text-align: center;
    color: #fff;
}
.payment_container {
    position: relative;
}

 img {
    width: 110px;
 }
 label {
    display: inline-block;
    margin-bottom: .5rem;
    color: #888;
    font-weight: 400;
}
select.form-control:not([size]):not([multiple]) {
    color: #999 !important;
}
.form-control.focus, .form-control:focus {
    border-color: #4169e1;
    color: #575962;
}

.btn-brand.disabled, .btn-brand:disabled {
    color: #fff;
    background-color: #4169e1;
    border-color: #4169e1;
    opacity: 0.8;
}

.btn-brand {
    color: #fff;
    background-color: #4169e1;
    border-color: #4169e1;
    opacity: 0.8;
}