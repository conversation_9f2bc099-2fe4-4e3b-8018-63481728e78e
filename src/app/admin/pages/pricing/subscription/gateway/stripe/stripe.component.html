<div class="custom-alert alert-area" #hasCusAlert></div>

<div id="accordion">
  <div class="stripe-card">
    <div id="collapseOne" class="collapse show">
      <form [formGroup]="form">
        <div class="form-group mb-3" id="cardnumber">
          <label for="card-number">Card number</label>
          <input
            id="cardNo"
            type="text"
            numberOnly
            (keyup)="keyUp.next($event.target.value)"
            formControlName="cardNo"
            class="form-control cardnumber"
          />
          <img [src]="cardTypeimg" class="credicardImg" />
        </div>
        <div class="form-group mb-3">
          <label for="name-on-number">Cardholder Name</label>
          <input
            id="cardName"
            type="text"
            formControlName="cardHolderName"
            placeholder="Name on Card "
            class="form-control"
          />
        </div>
        <div class="row">
          <div class="form-group col-md-6 ">
              <label for="" class="col-md-12 p-0">Exp Month</label>
            <select (change)="selectExpireMonth($event)" class="form-control">
              <option [value]="month.value" *ngFor="let month of months">{{
                month.text
              }}</option>
            </select>
          </div>
         
          <div class="form-group col-md-6 ">
            <label for="" class="col-md-12 p-0">Exp Year</label>
            <select
              id="EventXCardYear"
              (change)="selectExpireYear($event)"
              class="form-control"
            >
              <option [value]="year.value" *ngFor="let year of years">{{
                year.text
              }}</option>
            </select>
          </div>
        </div>
        <div class="form-group">
          <label for="cvv_number">Cvv</label>
          <input
            maxlength="4"
            numberOnly
            formControlName="cvcNo"
            type="text"
            placeholder="CVV Number "
            class="form-control"
          />
        </div>

        <div
          class="row justify-content-center mt-4"
          style="position: relative;"
        >
          <div
            *ngIf="loader; else buttonShow"
            class="m-loader"
            style="width: 30px; display: inline-block;"
          ></div>
          <ng-template #buttonShow>
            <button
              [disabled]="form.invalid || !validCard || !key || !isEnableBtn"
              class="btn col-md-4 theme-btn btn-brand"
              (click)="submitStripe()"
            >
              Subscribe
            </button>
          </ng-template>
        </div>
      </form>
    </div>
  </div>
</div>
