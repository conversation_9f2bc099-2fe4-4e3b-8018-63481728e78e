import { Component, OnInit, ElementRef, ViewChild } from '@angular/core';
import { AdminService } from './../../../../admin/admin.service';
import { Pagi } from './../../../../modules/pagination/pagi.model';
import { AlertService } from './../../../../modules/alert/alert.service';

@Component({
  selector: 'app-notifications',
  templateUrl: './notifications.component.html',
  styleUrls: ['./notifications.component.css']
})
export class NotificationsComponent implements OnInit {
  notificationsList: any[] = [];
  pagi: Pagi = new Pagi();

  @ViewChild('hasCusAlert', {static: true}) alertContainer: ElementRef;

  constructor(
    private adminS: AdminService,
    private alertS: AlertService
  ) { }

  ngOnInit() {
    this.getNotifications(1, 100);
  }

  reloadTable(e) {
    this.getNotifications(e.page, e.limit);
  }

  getNotifications(page_no, limit): void {
    this.adminS.getNotifications(page_no, limit)
      .then( res => {
        if (res.status === 'OK') {
          this.notificationsList = res.result.data;
          this.pagi.total = res.result["total"] || 0;
          this.pagi.page = parseInt(res.result["page_no"]) || 1;
          this.pagi.limit = parseInt(res.result["limit"]) || 100;
        } else {
          this.notificationsList = [];
          this.pagi.total = 0;
          this.pagi.page = 1;
          this.pagi.limit = 100;
          this.alertS.error(this.alertContainer, 'Something went wrong!', true, 3000);
        }
      }).catch( err => {
        this.notificationsList = [];
        this.pagi.total = 0;
        this.pagi.page = 1;
        this.pagi.limit = 100;
        this.alertS.error(this.alertContainer, 'Something went wrong!', true, 3000);
      })
  }

}
