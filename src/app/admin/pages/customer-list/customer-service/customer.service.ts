import { Injectable } from "@angular/core";

import { map, catchError } from "rxjs/operators";
import { BehaviorSubject, throwError, of } from "rxjs";
import { HttpService } from "../../../../modules/http-with-injector/http.service";
import { GET_USER } from "../../../../globals/_classes/functions";

declare let $: any;

export class Reload {
  reload?: boolean;
  id?: number;
  from?: string;
  data?: any;
}

interface RentalPriceSetting {
  reload?: boolean;
  price_type?: number;
}

@Injectable()
export class CustomerService {
  private subject = new BehaviorSubject<any>(new Reload());
  reloadDetails = this.subject.asObservable();
  reload(load: Reload) {
    this.subject.next(load);
  }

  private rentSub: RentalPriceSetting = { reload: false };
  rentalPriceSetting = new BehaviorSubject(this.rentSub);
  private subjectRese = new BehaviorSubject<any>(null);
  reserve = this.subjectRese.asObservable();
  reserveReload(load) {
    this.subjectRese.next(load);
  }

  constructor(
    private http: HttpService,
  ) {

  }

  getAllCustomer(p, l, query?) {
    const params = query ? query : "";
    const loc = GET_USER().location_id;
    return this.http
      .get(
        `customers?location=${loc ? loc : ""}&page_no=${p ? p : 1}&limit=${
          l ? l : 20
        }${params}`
      )
      .pipe(map(res => res));
  }


  
  deleteCustomer(customer_id) {
    return this.http.delete(`customers/${customer_id}`).toPromise();
  }

  exportCustomer(data) {
    return this.http.getBlob(`mailchimp/customer/export${data}`);
  }


  exportCustomerByMailchimp(data) {
    return this.http.post(`mailchimp/customer/export`,data).toPromise();
  }


  getMailchimpList() {
    return this.http.get(`mailchimp/list`).toPromise();
  }

  gettagListByMailchimpId(id) {
    return this.http.get(`mailchimp/segments/${id}`).toPromise();
  }
 
  deleteUser(id) {
    return this.http.delete(`customers/${id}`).toPromise();
  }

  register(data) {
    return this.http.post("customers?source=admin", data).toPromise();
  }
  
}
