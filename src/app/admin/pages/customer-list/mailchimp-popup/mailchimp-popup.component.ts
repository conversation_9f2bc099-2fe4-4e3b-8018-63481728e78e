import { Component, OnInit } from '@angular/core';
import { CustomerService } from '../customer-service/customer.service';
import { Helpers } from '../../../../helpers';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-mailchimp-popup',
  templateUrl: './mailchimp-popup.component.html',
  styleUrls: ['./mailchimp-popup.component.css']
})
export class MailchimpPopupComponent implements OnInit {


  mailChimpList = [];
  tagList = [];
  selectedMailChimpId = '';
  selectedTagIds = '';


  constructor(
    private customerService: CustomerService,
    public activeModal: NgbActiveModal
  ) { }

  ngOnInit() {
    this.loadMailChimpList();
  }


  loadMailChimpList() {
    Helpers.setLoading(true);

    this.customerService.getMailchimpList().then(res => {
      if (res.status == "OK") {
        this.mailChimpList = res.result.data
      }
      else {
        this.mailChimpList = [];
      }

      Helpers.setLoading(false);

    }).catch(err => {
      Helpers.setLoading(false);
      console.log(err)
    })
  }

  onClickCancel(){
    this.activeModal.close("");
  }


  onClickRadio(id, i) {
    this.selectedMailChimpId = id;
    this.loadTagList(this.selectedMailChimpId)
    console.log(this.selectedMailChimpId)
  }


  loadTagList(mailchimpId) {
    Helpers.setLoading(true);
    this.customerService.gettagListByMailchimpId(mailchimpId).then(res => {
      if (res.status == "OK") {
        this.tagList = res.result.data;

        this.tagList.map(res => {
          res["check"] = false;
          return res;
        })
      }
      else {
        this.tagList = [];
      }

      Helpers.setLoading(false);

    }).catch(err => {
      Helpers.setLoading(false);
      console.log(err)
    })
  }

  checkOne(e, i) {
    this.tagList[i]["check"] = e.target.checked;
    this.selectedTagIds = this.tagList
      .filter(f => {
        return f["check"];
      })
      .map(m => {
        return m.id;
      })
      .join(",");
  }



  onClickExport() {
    this.activeModal.close({ close: true, result: { mailchimpId: this.selectedMailChimpId, tagIds: this.selectedTagIds } })
  }

}
