import { Component, OnInit, Output, EventEmitter, OnDestroy, Input, AfterViewInit, OnChanges } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { CustomerSearchService } from '../customer-service/customer-search.service';
import { CustomerSearch } from '../../inventory/product-models/inventory.models';
import { FORMAT_SEARCH } from '../../../../globals/_classes/functions';


@Component({
  selector: 'customer-search',
  templateUrl: './customer-search.component.html',
  styleUrls: ['../customer-list.component.css']
})
export class CustomerSearchComponent implements OnChanges, OnInit, AfterViewInit, OnDestroy {

  search: CustomerSearch = new CustomerSearch();
  filter:string;
  reset: boolean;
  sub: Subscription[] = [];
  loader: boolean;

  @Input('checkFilter') checkFilter: boolean;
  @Input () abc;
  @Output('search') searching:EventEmitter<string> = new EventEmitter();


  constructor(
    private activeRoute:ActivatedRoute,
    private customerSearchService: CustomerSearchService
  ) { 
   
  }

  ngOnChanges() {
    if(this.checkFilter) {
      this.checkFilterData();
    }
  }

  ngOnInit() {
   
  }

  ngAfterViewInit() {
    $('.search-panel#advanceSearch').toggleClass('dis-block');
    $('#advanceSearch').toggleClass('la-angle-down la-angle-up');
    
    this.checkFilterData();
  }

  ngOnDestroy() {
    for(let s of this.sub) {
      s.unsubscribe();
    }
  }

  checkFilterData() {
    const param = this.activeRoute.snapshot.queryParamMap.get('param');
    const data = param ? param : null;
    if(data && (!data.includes('search'))) {
      if(!$('.search-panel#advanceSearch').hasClass('dis-block')) {
        this.openAdvanceSearch();
      }
      setTimeout(() => {
        this.search = this.customerSearchService.formatFilter(data);
        this.reset = true;
        this.checkFilter = false;
      }, 100);
    }
  }


  searchCustomer(value){
    value.avd_search=true;
    this.filter = FORMAT_SEARCH(value);
    console.log(this.filter);
    if(this.filter) {
      this.loader = true;
      this.searching.emit(this.filter);
      this.reset = true;
    }
  }

  resetSearch(){
    this.filter = null;
    if(this.reset) {
      this.searching.emit('');
      this.reset = false;
    }
    this.openAdvanceSearch();
  }

  closeFilter() {
    this.search = new CustomerSearch();
    if(this.reset) {
      this.openAdvanceSearch();
      this.reset = false;
    }
  }

  openAdvanceSearch() {
    $('.search-panel#advanceSearch').toggleClass('dis-block');
    $('#advanceSearch').toggleClass('la-angle-down la-angle-up');
  }

}
