<!--begin::Portlet-->
<div class="m-portlet m-portlet--mobile">
  <div class="m-portlet__head" (click)="openAdvanceSearch()" style="cursor: pointer;">
    <div class="m-portlet__head-caption">
      <div class="m-portlet__head-title">
        <h3 class="m-portlet__head-text text-left" style="width:92%;">
          <i class="la la-search"></i>
          Search
        </h3>
        <h3 class="text-right" style="margin: auto;padding: 20px 5px;">
          <i class="la la-angle-up" id="advanceSearch" style="font-size: 1.8rem;"></i>
        </h3>
      </div>
    </div>
  </div>
  <div class="m-portlet__body search-panel dis-none" id="advanceSearch">
    <!--begin::Form-->
    <form class="m-form m-form--fit m-form--label-align-right" #form="ngForm" (ngSubmit)="searchCustomer(form.value)">
      <div class="row">
        <div class="col-md-4 col-sm-6">
          <div class="form-group m-form__group">
            <input type="text" class="form-control" name="first_name" [(ngModel)]="search.first_name"
              placeholder="First Name" aria-describedby="First Name" autocomplete="off">
          </div>
        </div>

        <div class="col-md-4 col-sm-6">
          <div class="form-group m-form__group">
            <input type="text" class="form-control m-input" name="last_name" [(ngModel)]="search.last_name"
              placeholder="Last Name" aria-describedby="Last Name" autocomplete="off">
          </div>
        </div>

        <div class="col-md-4 col-sm-6">
          <div class="form-group m-form__group">
            <input type="text" class="form-control m-input" name="mobile" [(ngModel)]="search.mobile"
              placeholder="Mobile" aria-describedby="Mobile" autocomplete="off">
          </div>
        </div>

        <div class="col-md-4 col-sm-6">
          <div class="form-group m-form__group">
            <input type="text" class="form-control m-input" name="email" [(ngModel)]="search.email"
              placeholder="Email" aria-describedby="Email" autocomplete="off">
          </div>
        </div>

      </div>
      <div class="form-group m-form__group col-12 search-panal-action">
          <div *ngIf="loader; else button" class="m-loader m-loader--brand" 
          style="width: 30px; display: inline-block;"></div>
        <ng-template #button>
          <input type="hidden" value="1" name="avd_search" [(ngModel)]= "search.avd_search" />
          <button type="submit" class="btn m-btn--pill m-btn--air btn-brand btn-sm">
            <i class="fa fa-calendar-check-o"></i>
              Search
          </button>

          <button type="reset" id="resetFilter" class="btn m-btn--pill m-btn--air btn-danger btn-sm" (click)="resetSearch()">
            <i class="fa fa-history"></i>
              Cancel
          </button>
        </ng-template>
        
      </div>
    </form>
    <!--end::Form-->
  </div>
</div>
<!--end::Portlet-->