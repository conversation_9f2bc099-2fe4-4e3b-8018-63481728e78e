import { SidebarService } from '../sidebar-service/sidebar.service';
import {
  Component,
  OnInit,
  ViewEncapsulation,
  AfterViewInit,
  ViewChild,
  ElementRef,
  OnDestroy,
  HostListener
} from "@angular/core";
import { Router, ActivatedRoute } from "@angular/router";
import { NgbModal, NgbModalOptions } from "@ng-bootstrap/ng-bootstrap";
import { Subscription } from "rxjs";
import { CustomerSearchService } from "./customer-service/customer-search.service";
import { CustomerService } from "./customer-service/customer.service";
import { EndPoint, product_image } from "../../../globals/endPoint/config";
import { Pagi } from "../../../modules/pagination/pagi.model";
import { GET_STORE_ID, singleOrNot, downloadFile, GET_USER } from "../../../globals/_classes/functions";
import { AlertService } from "../../../modules/alert/alert.service";
import { DialogBoxComponent } from "../../../modules/dialog-box/dialog-box.component";
import { Helpers } from "../../../helpers";
import { MailchimpPopupComponent } from "./mailchimp-popup/mailchimp-popup.component";
import { SettingService } from "../settings/setting-service/setting.service";
import { ICustomer } from "./customer-service/customers.models";
import { UserSignUp } from "../user-management/models/user.models";
import { PLATFORM_ID,Inject } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
declare let $: any;

@Component({
  selector: "admin-customer-list",
  templateUrl: "./customer-list.component.html",
  styleUrls: ["./customer-list.component.css"],
  encapsulation: ViewEncapsulation.None
})
export class CustomerListComponent implements OnInit, AfterViewInit, OnDestroy {
  customerList: ICustomer[] = [];
  sidebarOpen: boolean;
  filter: string;
  customerIds = null;
  search: string;
  load: boolean;
  loader: boolean;
  end = EndPoint + "products/export";
  pagi: Pagi = new Pagi();
  image = product_image + GET_STORE_ID();
  sub: Subscription[] = [];
  goTop: boolean;
  reloadFilter: boolean;
  pro_id: any;
  copyDone: boolean;
  subs_plan: any;
  abc = true;
  totalItems = 0;
  productStatue;
  is_show_mailchimp_export = false;
  check_uncheck = false;

  signUp: UserSignUp;

  @HostListener('window:resize', ['$event'])
  onResize(event) {
    if (this.sideBaropen) {
      $('.native-routing').css('display', 'block');
      this.sidebarS.openSidebar();
    }
  }

  @ViewChild("hasCusAlert") alertContainer: ElementRef;
  @ViewChild("searchPro", {static: true}) searchPro;
  sideBaropen: boolean;

  constructor(
    @Inject(PLATFORM_ID) private platform: Object,
    private router: Router,
    private alertS: AlertService,
    private route: ActivatedRoute,
    private customerSearchService: CustomerSearchService,
    private customerService: CustomerService,
    private settingS: SettingService,
    private modalService: NgbModal,
    private sidebarS: SidebarService
  ) {
    this.signUp = new UserSignUp();
  }

  ngOnInit() {
    // if (isPlatformBrowser(this.platform)) {
    // window.scrollTo(0, 0);
    // }
    this.sub[0] = this.route.queryParamMap.subscribe(val => {
      const path = this.route.snapshot["_routerState"].url;
      if (!path.includes("edit")) {
        this.pagi.page = val.get("page")
          ? parseInt(val.get("page"))
          : this.pagi.page
          ? this.pagi.page
          : 1;
        this.pagi.limit = val.get("limit")
          ? parseInt(val.get("limit"))
          : this.pagi.limit
          ? this.pagi.limit
          : 20;
        this.filter = val.get("param")
          ? val.get("param")
          : this.filter
          ? this.filter
          : "";
        this.search = this.customerSearchService.formatSearch(this.filter);
        this.getCustomerList(this.pagi.page, this.pagi.limit, this.filter);
      } else {
        if (!this.pagi.page) {
          this.formatPage();
          this.search = this.customerSearchService.formatSearch(this.filter);
          this.getCustomerList(this.pagi.page, this.pagi.limit, this.filter);
        }
      }

      if (this.search != null && this.search.includes("avd_search=true")) {
        this.search = null;
      }
    });

    this.getMailchimpConfig();
  }

  ngAfterViewInit() {
    // if (isPlatformBrowser(this.platform)) {
    // window.scrollTo(0,0);
    // }
    this.closeSidebar();
    this.executeAction();
  }

  ngOnDestroy() {
    for (let s of this.sub) {
      s.unsubscribe();
    }
  }

  getMailchimpConfig() {
    this.settingS.getLeadLagTime().subscribe(
      res => {
        if (res.data !== null) {
          if (res.data.hasOwnProperty("mailchimp")) {
            this.is_show_mailchimp_export = true;
          } else {
            this.is_show_mailchimp_export = false;
          }
        }
      },
      err => console.error(err)
    );
  }

  copyUrl(copyText) {
    copyText.select();
    document.execCommand("copy");
    this.copyDone = true;
    setTimeout(() => {
      this.copyDone = false;
    }, 2000);
  }

  makeQuryparam(param) {
    this.router.navigate(["./"], {
      relativeTo: this.route,
      queryParams: param
    });
  }

  getCustomerList(p, l, q) {
    this.loader = true;
    q = this.pro_id ? q + "&product_id=" + this.pro_id : q;
    this.customerService.getAllCustomer(p, l, q).subscribe(
      res => {
        if (this.goTop) {
          // if (isPlatformBrowser(this.platform)) {
          // window.scrollTo(0, 0);
          // }
        }
        this.loader = false;
        this.searchPro.loader = false;
        if (res.status == "OK") {
          if (this.pro_id) {
            const index = this.customerList.findIndex(
              f => f.id === this.pro_id
            );
            if (index > -1 && res.result.data[0]) {
              const editIndex = res.result.data.findIndex(
                f => f.id === this.pro_id
              );
              this.customerList[index] = res.result.data[editIndex];
            }
            this.pro_id = null;
          } else {
            this.customerList = res.result.data;
            this.totalItems = res.result.total_inventory;
            this.pagi.page = res.result.page_no
              ? JSON.parse(res.result.page_no)
              : 1;
            this.pagi.limit = res.result.limit
              ? JSON.parse(res.result.limit)
              : 20;
            this.pagi.total = res.result.total
              ? JSON.parse(res.result.total)
              : 0;
          }
        } else {
          this.error();
        }
        this.pro_id = null;
      },
      err => this.error()
    );
  }

  error() {
    this.alertS.error(
      this.alertContainer,
      "Something wrong!!! Please try again.",
      true,
      3000
    );
    this.customerList = [];
    this.loader = false;
    this.searchPro.loader = false;
    this.pro_id = null;
  }

  tarckCustomer(index, cus) {
    return cus ? cus.id : null;
  }

  checkAll(e) {
    if (e.target.checked) {
      this.checkUncheck(true);
      this.customerIds = this.customerList
        .map(m => {
          return m.id;
        })
        .join(",");
    } else {
      this.checkUncheck(false);
      this.customerIds = null;
    }
  }

  checkUncheck(d) {
    this.customerList = this.customerList.map(m => {
      m["check"] = d;
      return m;
    });
  }

  checkOne(e, i) {
    this.customerList[i]["check"] = e.target.checked;
    this.customerIds = this.customerList
      .filter(f => {
        return f["check"];
      })
      .map(m => {
        return m.id;
      })
      .join(",");
  }

  reloadTable(e) {
    this.pagi.page = e.page;
    const obj = {
      page: this.pagi.page,
      limit: this.pagi.limit,
      param: this.filter
    };

    if (this.pagi.limit != e.limit) {
      this.pagi.page =
        Math.ceil(this.pagi.total / e.limit) <= this.pagi.page
          ? Math.ceil(this.pagi.total / e.limit)
          : this.pagi.page;
      this.pagi.limit = e.limit;
      obj.limit = e.limit;
      obj.page = this.pagi.page;
    } else {
      delete obj.limit;
    }

    this.goTop = true;
    if (this.filter) {
      this.makeQuryparam(obj);
    } else {
      delete obj.param;
      this.makeQuryparam(obj);
    }
  }

  loadSearchData(filter) {
    this.filter = filter;
    this.customerIds = null;
    this.pagi.page = 1;
    this.goTop = true;
    if (this.filter) {
      this.makeQuryparam({ page: this.pagi.page, param: this.filter });
    } else {
      this.makeQuryparam({ page: this.pagi.page });
    }
    this.search = null;
  }

  searchList(value) {
    this.searchPro.closeFilter();
    this.customerIds = null;
    this.goTop = true;
    if (value.search) {
      const search = "&search=" + value.search.trim();
      this.filter = search;
    }
    this.pagi.page = 1;
    this.makeQuryparam({ page: this.pagi.page, param: this.filter });
  }

  reset(f) {
    if (this.search) {
      this.filter = "";
      this.pagi.page = 1;
      this.goTop = true;
      this.makeQuryparam({ page: this.pagi.page });
    }
    f.form.reset();
  }

  checkStatus(s) {
    return this.customerSearchService.checkStatus(s);
  }

  getStatus(s) {
    return this.customerSearchService.getStatus(s);
  }

  private formatPage() {
    const data = JSON.parse(sessionStorage.getItem("proInfo"));
    this.pagi.limit = this.pagi.limit
      ? this.pagi.limit
      : data
      ? data.limit
      : 20;
    this.filter = this.filter ? this.filter : data ? data.filter : "";
    this.pagi.page = this.pagi.page ? this.pagi.page : data ? data.page : 1;
  }

  deleteDialog(message): Promise<any> {
    const modalRef = this.modalService.open(DialogBoxComponent, {
      centered: true,
      size: "sm",
      windowClass: "animated fadeIn"
    });
    modalRef.componentInstance.massage = message;
    return modalRef.result;
  }

  export() {
    if (this.filter || this.customerIds || this.search) {
      this.exportCustomer(true);
    } else {
      this.dilogBox();
    }
  }

  exportMailchimp() {
    let ngbModalOptions: NgbModalOptions = {
      centered: true,
      backdrop: "static",
      size: "lg",
      windowClass: "animated fadeIn"
    };

    const modalRef = this.modalService.open(
      MailchimpPopupComponent,
      ngbModalOptions
    );
    modalRef.componentInstance.name = "Mailchimp-popup";

    modalRef.result.then(res => {
      if (res.close) {
        let selectedMailChipId = res.result.mailchimpId;
        let selectedTagIds = res.result.tagIds;

        this.exportCustomerByMailChimp(selectedMailChipId, selectedTagIds);
      }
    });
  }

  dilogBox(message?: string, customer?: ICustomer) {
    let params = message
      ? message
      : 'Do you want to export "' + this.pagi.total + '" number of products?';
    this.deleteDialog(params).then(result => {
      if (result) {
        if (message) {
          Helpers.setLoading(true);
          this.customerService.deleteUser(customer.id).then(res => {
            Helpers.setLoading(false);
            this.getCustomerList(this.pagi.page, this.pagi.limit, this.filter);
            // console.log(res);
            this.alertS.success(
              this.alertContainer,
              res.result.message,
              true,
              5000
            );
          });
        } else {
          this.exportCustomer(false);
        }
      }
    });
  }

  exportCustomer(has?) {
    let params = "?customer_id=";
    if (has) {
      params += this.customerIds ? this.customerIds : "all";
      if (this.filter) {
        params += this.filter;
      } else if (this.search) {
        params += this.search;
      }
    }

    Helpers.setLoading(true);
    this.customerService.exportCustomer(params).then(
      res => {
        Helpers.setLoading(false);
        downloadFile(res, this.getDownLoadFileName());
      },
      err => {
        Helpers.setLoading(false);
        console.log(err);
        this.alertS.error(
          this.alertContainer,
          "Something wrong!!! Customer have been not exported",
          true,
          3000
        );
      }
    );
  }

  exportCustomerByMailChimp(mailchimpId, tagIds) {
    Helpers.setLoading(true);
    let sendData = {
      customer_id: this.customerIds ? this.customerIds : "all",
      mailchimp_list_id: mailchimpId ? mailchimpId : "",
      mailchimp_segments: tagIds ? tagIds : ""
    };
    this.customerService.exportCustomerByMailchimp(sendData).then(
      res => {
        if (res.status == "OK") {
          this.alertS.success(
            this.alertContainer,
            res.result.message,
            true,
            3000
          );

          this.customerList = this.customerList.map(m => {
            m["check"] = false;
            return m;
          });

          this.customerIds = null;
          this.check_uncheck = false;
        } else {
          this.alertS.error(
            this.alertContainer,
            res.result.message,
            true,
            3000
          );
        }
        Helpers.setLoading(false);
      },
      err => {
        Helpers.setLoading(false);
        console.log(err);
        this.alertS.error(
          this.alertContainer,
          "Something wrong!!! Customer have been not exported",
          true,
          3000
        );
      }
    );
  }

  getDownLoadFileName() {
    let storeName = GET_USER().store_name;
    let today = new Date();
    let dd = String(today.getDate()).padStart(2, "0");
    let mm = String(today.getMonth() + 1).padStart(2, "0"); //January is 0!
    let yyyy = today
      .getFullYear()
      .toString()
      .substr(-2);

    return `${storeName}_${yyyy}${mm}${dd}.xls`;
  }

  // goToCustomerDetails(customer: ICustomer): void {
  //   this.router.navigate([`/admin/customer/${customer.id}/profile`], {
  //     queryParams: {
  //       source: "customer-list"
  //     }
  //   });
  // }

  goToCustomerEdit(customer: ICustomer): void {
    this.router.navigate(
      [`/admin/customer/${customer.id}/account-settings/personal-info`],
      {
        queryParams: {
          source: "customer-list"
        }
      }
    );
  }

  onClickExport() {
    this.router.navigateByUrl("/admin/inventory/advanced-product-search");
  }

  closeSidebar() {
    $('.close-sidebar').click((e) => {
      e.preventDefault();
      this.executeAction();
    });
    $('.close-sidebar-upper').click((e) => {
      e.preventDefault();
      this.executeAction();
    });
  }

  executeAction() {
    this.sideBaropen = null;
    this.sidebarS.removeSidebar();
    $('.native-routing').css('display', 'none');
  }

  creatnewUser() {
    $('.native-routing').css('display', 'block');
    this.sidebarS.openSidebar();
    this.signUp = new UserSignUp();
    this.sideBaropen = true;
  }

  submitUser(e) {
    console.log(e);
    if(e.off) {
      if(e.success) {
        this.getCustomerList(this.pagi.page, this.pagi.limit, this.filter);
        this.alertS.success(this.alertContainer, e.msg, true, 5000);
        this.executeAction();
      } else {
        this.alertS.error(this.alertContainer, e.msg, true, 5000);
      }
    }
  }



}
