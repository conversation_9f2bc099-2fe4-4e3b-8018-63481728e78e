import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Routes, RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { CustomerListComponent } from './customer-list.component';
import { CustomerSearchComponent } from './customer-search/customer-search.component';
import { CustomerSearchService } from './customer-service/customer-search.service';
import { CustomerService } from './customer-service/customer.service';
import { DialogBoxModule } from '../../../modules/dialog-box/dialog-box.module';
import { PaginationModule } from '../../../modules/pagination/pagination.module';
import { CurrencyFormatModule } from '../../../modules/currency-format/currency-format.pipe';
import { DialogBoxComponent } from '../../../modules/dialog-box/dialog-box.component';
import { LayoutModule } from '../../layouts/layout.module';
import { PagesComponent } from '../pages.component';
import { AdminService } from '../../admin.service';
import { MailchimpPopupComponent } from './mailchimp-popup/mailchimp-popup.component';
import { AddCustomerComponent } from './add-customer/add-customer.component';
import { SidebarService } from '../sidebar-service/sidebar.service';


const routes: Routes = [
 {
    path: '',
    component: PagesComponent,
    children:[
        {path: '',component: CustomerListComponent,canActivate: [AdminService]}
      ]
}

];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    DialogBoxModule,
    LayoutModule,
    FormsModule,
    PaginationModule,
    CurrencyFormatModule
  ],
  exports: [
    RouterModule
  ],
  declarations: [
    CustomerListComponent,
    CustomerSearchComponent,
    MailchimpPopupComponent,
    AddCustomerComponent
  ],
  entryComponents: [
    DialogBoxComponent,
    MailchimpPopupComponent
  ],
  providers:[CustomerService,CustomerSearchService, SidebarService]
})
export class CustomerListModule {
}
