.btn-text{
    margin: 0 10px;
}
.decoration-none:hover{
    text-decoration: none!important;
}
.product-list-right-btn>.first-child-btn{
    margin-right: 30px;
}
.customer-list .m-portlet .m-portlet__head{
    /* border-bottom: none; */
}
.customer-list .search-panel select option[data-default] {
    color: #888;
}
.customer-list .search-panel select option[value=""][disabled] {
    display: none;
}
.customer-list .search-panel input{
    text-align: left;
}
.customer-list .search-panel select{
    text-align-last: left;
    padding-right: 29px;
}

.customer-list .search-panel .form1-search-btn{
    padding-left: 2.6rem!important;
}

.customer-list .search-panel .form-group,.customer-list .search-panel .m-form__group {
    padding:1rem;
}
.customer-list .add-list-btn{
    display: table-cell; vertical-align: middle;
}

/* @media only screen and (max-width: 1000px){
    .customer-list .search-panel .form1-search-btn{
        padding-top: 15px;
        padding-bottom: 15px;
    }
}
@media only screen and (max-width: 768px){
    .customer-list .search-panel .form1-search-btn{
        padding-top: 0px;
        padding-bottom: 0px;
    } 
} */

.copy-msg {
    position: absolute;
    top: 10px;
    left: 45%;
}

.search-panal-action button{
    margin-right: 10px; 
}

.custom-alert{
    position: fixed;
    top: 90px;
    right: 30px;
    z-index: 5000;
}

.dis-none{
    display: none;
}
.dis-block{
    display: block!important;
}

.disabled-cart{
    opacity: 0.5;
    pointer-events: none;
}

.m-portlet__head-title{
    width: 100%;
}

.img-resize-tum{
    max-width: 90px!important;
    height: 90px!important;
    object-fit: contain;
}
.stocklist-body .m-portlet__head-text a {
    padding: 25px 18px;
    color: #575962;
}
.stocklist-active {
    border-bottom: 1px solid #575962;
    color: #575962;
}


.m-portlet .m-portlet__head .m-portlet__head-caption .m-portlet__head-title .m-portlet__head-text {
    width: 80%;
}
.list-search form {
    width: 450px;
    /* float: right; */
}
.list-search button i {
    font-size: 11px !important;
    margin-right: 5px;
}

.addcutomer-btn {
    color: #fff !important;
}
@media (min-width:1400px) and (max-width: 1700px) {
    .m-portlet .m-portlet__head .m-portlet__head-caption .m-portlet__head-title .m-portlet__head-text {
        width: 74%;
    }
}
@media (max-width: 1399px) {
    .m-portlet .m-portlet__head .m-portlet__head-caption .m-portlet__head-title .m-portlet__head-text {
        width: 69%;
    }
    .list-search .form-control {
        font-size: 13px;
    }
    .list-search .col-xl-10 {
        flex: 0 0 74.33333%;
    }
}

@media (min-width: 992px) and (max-width: 1199px) {
    .stocklist-body .m-portlet__head-text a {
        padding: 15px 10px;
        font-size: 16px;
    }
    .m-portlet .m-portlet__head {
        height: auto;
    }
    .m-portlet .m-portlet__head .m-portlet__head-caption .m-portlet__head-title .m-portlet__head-text {
        width: 75%;
    }
    .list-search {
        padding-left: 0;
    }
    .list-search form {
        float: unset;
        margin-bottom: 10px !important;
        margin-top: 15px !important;
    }
    .add-list-btn .btn {
        margin-bottom: 15px;
    }
    .add-list-btn .btn:last-child {
        margin-bottom: 0;
    }
    
}
@media (min-width: 768px) and (max-width: 991px) {
    .m-portlet .m-portlet__head .m-portlet__head-caption .m-portlet__head-title .m-portlet__head-text {
        width: 82%;
    }
    .stocklist-body .m-portlet__head-text a {
        padding: 25px 10px;
        color: #575962;
        font-size: 12px;
    }
    .m-portlet .m-portlet__head .m-portlet__head-caption .m-portlet__head-title .m-portlet__head-text {
        width: 69%;
    }
    .list-search form {
        width: 395px;
    }
    .list-search form input {
        font-size: 11px !important;
    }
    .stocklist-body .m-portlet__head-text a {
        padding: 25px 0px;
    }
    .list-search button {
        font-size: 10px !important;
    }
    .add-list-btn .btn {
        font-size: 10px;
    }
    
}
@media (min-width: 576px) and (max-width: 767px) {
    .stocklist-body .m-portlet__head-text a {
        padding: 15px 10px;
        font-size: 16px;
    }
    .m-portlet .m-portlet__head {
        height: auto;
    }
    .stocklist-body {
        display: block !important;
        padding-bottom: 15px;
    }
    .m-portlet .m-portlet__head .m-portlet__head-caption .m-portlet__head-title .m-portlet__head-text {
        width: 82%;
        display: unset;
    }
    .list-search {
        padding-left: 0;
    }
    .list-search form {
        float: unset;
        margin-bottom: 10px !important;
        margin-top: 15px !important;
        width: 100%;
    }
    .stocklist-packagelist .add-list-btn .btn-brand {
        margin-top: -60px;
    }
    .customer-list .add-list-btn {
        display: unset;
    }
    .add-list-btn .btn:last-child {
        margin-bottom: 0;
    }
    .stocklist-packagelist .add-list-btn .btn-brand {
        margin-top: 0;
    }
    .list-link {
        padding: 13px 15px;
    }
    
}

@media (max-width: 575px) {
    .stocklist-body .m-portlet__head-text a {
        padding: 10px 10px 20px !important;
        width: unset;
    }
    .list-search form {
        width: 90%;
        float: unset;
    }
    .list-search form input {
        font-size: 10px;
    }
    .stocklist-packagelist .add-list-btn .btn-brand {
        margin-top: 20px;
    }
}

@media (max-width: 575px) {
    .mr-auto {
        width: 80%
    }
    .first-child-btn {
        margin-bottom: 10px;
    }
    .m-portlet .m-portlet__body {
        padding: 15px 15px;
    }

    .m-portlet .m-portlet__head 
    .m-portlet__head-caption 
    .m-portlet__head-title.m-product-title {
        display: flex;
        padding-bottom: 15px;
    }
    .customer-list .add-list-btn{
        padding-top: 10px;
    }
}


