<!-- <plan-upgrade-alert
[message]="'You must be a silver or Platinum tier client to add additional inventory.'"
*ngIf="subs_plan==='FREE' && totalItems>10"></plan-upgrade-alert>
<plan-upgrade-alert [message]="'You must be a Platinum tier client to add additional inventory. '" *ngIf="subs_plan==='SILVER' && totalItems>50" ></plan-upgrade-alert> -->

<div class="custom-alert" #hasCusAlert></div>

<!-- BEGIN: Subheader -->
<div class="m-subheader product-section-list">
  <div class="d-flex align-items-center">
    <div class="mr-auto">
      <h3 class="m-subheader__title m-subheader__title--separator">
        Customer List
      </h3>
      <ul class="m-subheader__breadcrumbs m-nav m-nav--inline">
        <li class="m-nav__item m-nav__item--home">
          <a routerLink="/admin" class="m-nav__link m-nav__link--icon">
            <i class="m-nav__link-icon la la-home"></i>
          </a>
        </li>
        <li class="m-nav__separator">
          <i class="fa fa-angle-right"></i>
        </li>
        <li class="m-nav__item">
          <a class="m-nav__link">
            <span class="m-nav__link-text">
              Settings
            </span>
          </a>
        </li>
        <li class="m-nav__separator">
          <i class="fa fa-angle-right"></i>
        </li>
        <li class="m-nav__item">
          <a class="m-nav__link">
            <span class="m-nav__link-text">
              Customer List
            </span>
          </a>
        </li>
      </ul>
    </div>
    <div class="-table-right-btn">
    
      <div class="m-dropdown m-dropdown--inline m-dropdown--arrow m-dropdown--align-right m-dropdown--align-push"
        data-dropdown-toggle="hover" aria-expanded="true">
        <button class="m-portlet__nav-link m-dropdown__toggle decoration-none btn m-btn--pill m-btn--air btn-light btn-sm">
          <span class="btn-text">Tools</span>
          <span class="btn btn-brand btn-sm  m-btn m-btn--outline-2x m-btn--air m-btn--icon m-btn--icon-only m-btn--pill">
            <i class="la la-plus m--hide"></i>
            <i class="la la-angle-down"></i>
          </span>
        </button>
        <div class="m-dropdown__wrapper">
          <span class="m-dropdown__arrow m-dropdown__arrow--right m-dropdown__arrow--adjust"></span>
          <div class="m-dropdown__inner">
            <div class="m-dropdown__body">
              <div class="m-dropdown__content">
                <ul class="m-nav">
                  <!-- <li class="m-nav__item">
                    <a class="m-nav__link" style="cursor: pointer;" (click)="export()">
                      <i class="m-nav__link-icon la la-cloud-download"></i>
                      <span class="m-nav__link-text">
                        Export CSV
                      </span>
                    </a>
                  </li> -->

                  <li *ngIf="is_show_mailchimp_export" class="m-nav__item">
                    <a class="m-nav__link" style="cursor: pointer;" (click)="exportMailchimp()">
                      <i class="m-nav__link-icon la la-cloud-download"></i>
                      <span class="m-nav__link-text">
                        Export Mailchimp
                      </span>
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- END: Subheader -->
<div class="m-content customer-list animated fadeIn">

  <customer-search #searchPro [checkFilter]="reloadFilter" [abc]="abc" (search)="loadSearchData($event)"></customer-search>
  <!--begin::Portlet-->
  <div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__head">
      <div class="m-portlet__head-caption">
        <div class="m-portlet__head-title m-product-title stocklist-body stocklist-packagelist">
          <h3 class="m-portlet__head-text">
            Customer List
          </h3> 
          <div class="add-list-btn text-right assetlist-btn-area">
            <a class="btn btn-brand btn-md addcutomer-btn" (click)="creatnewUser()"><i class="fa fa-plus"></i> Add Customer </a>
          </div>
         
        </div>
      </div>
    </div>
    <div class="m-portlet__body">
      <!--begin: Datatable -->
      <div class="m-section">
        <div class="m-section__content price-table" style="position: relative;">

          <div *ngIf="loader" class="table-load m-loader m-loader--brand"></div>
          <div class="table-responsive" style="margin-bottom: 10px;">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th class="text-center">
                    <label class="m-checkbox m-checkbox--check-bold m-checkbox--state-brand">
                      <input type="checkbox" name="check_uncheck" [(ngModel)]="check_uncheck" (click)="checkAll($event)">
                      <span></span>
                    </label>
                  </th>
                  <th>Id</th>
                  <th>
                    First Name
                  </th>
                  <th>
                   Last Name
                  </th>
                  <th>
                    Mobile
                  </th>
                  <th>
                   Email
                  </th>

                  <th>
                    Actions
                  </th>
                
                </tr>
              </thead>
              <tbody *ngIf="customerList.length<1; else table">
                <tr *ngIf="!loader">
                  <td colspan="11">
                    <h4 class="text-center">No Customer Found</h4>
                  </td>
                </tr>
              </tbody>
              <ng-template #table>
                <tbody>
                  <tr *ngFor="let pro of customerList; let i='index'; trackBy: tarckCustomer; let o='odd'; let e='even'"
                    [ngClass]="{'odd-tr':o, 'even-tr':e}">
                    <td class="text-center">
                      <label class="m-checkbox m-checkbox--check-bold m-checkbox--state-primary">
                        <input type="checkbox" [attr.name]="'pro_'+pro.id" [(ngModel)]="pro.check" (click)="checkOne($event, i)">
                        <span></span>
                      </label>
                    </td>
                    <th>{{pro.id}}</th>
                    <th>
                      {{pro.first_name}}
                    </th>
                    <td>
                      {{pro.last_name}}
                    </td>
                    <td>
                      {{pro.mobile}}
                    </td>
                    <td>
                      {{pro.email}}
                    </td>

                    <td>
                      <!-- <a (click)="goToCustomerDetails(pro)" title="Customer Details" class="m-portlet__nav-link btn m-btn m-btn--hover-primary m-btn--icon m-btn--icon-only m-btn--pill">
                        <i class="fa fa-eye"></i>
                      </a> -->
                      <a (click)="goToCustomerEdit(pro)" title="Edit Customer" class="m-portlet__nav-link btn m-btn m-btn--hover-accent m-btn--icon m-btn--icon-only m-btn--pill">
                        <i class="fa fa-edit"></i>
                      </a>
                      <a title="Delete Customer" class="m-portlet__nav-link btn m-btn m-btn--hover-danger m-btn--icon m-btn--icon-only m-btn--pill"
                        (click)="dilogBox('Do you want to delete this customer?', pro)">
                        <i class="fa fa-trash"></i>
                      </a>
                    </td>

                    <!-- <td>
                      <a *ngIf="pro?.type==1" (click)="copyUrl(copyText)" title="Copy" class="m-portlet__nav-link btn m-btn m-btn--hover-brand m-btn--icon m-btn--icon-only m-btn--pill">
                        <i class="fa fa-copy"></i>
                      </a>
                      <a *ngIf="pro?.type==1" routerLink="/admin/inventory/{{pro.id}}/details" title="Product Details" class="m-portlet__nav-link btn m-btn m-btn--hover-primary m-btn--icon m-btn--icon-only m-btn--pill">
                        <i class="fa fa-eye"></i>
                      </a>
                      <a *ngIf="pro?.type==2" routerLink="/admin/inventory/package/{{pro.id}}/details" title="Product Details" class="m-portlet__nav-link btn m-btn m-btn--hover-primary m-btn--icon m-btn--icon-only m-btn--pill">
                        <i class="fa fa-eye"></i>
                      </a>
                      <a (click)="openSidebar(pro, 'description')" title="Edit Product" class="m-portlet__nav-link btn m-btn m-btn--hover-accent m-btn--icon m-btn--icon-only m-btn--pill">
                        <i class="fa fa-edit"></i>
                      </a>
                      <a *ngIf="pro?.type==1" (click)="openSidebar(pro, 'calendar')" title="Calender" class="m-portlet__nav-link btn m-btn m-btn--hover-dark m-btn--icon m-btn--icon-only m-btn--pill">
                        <i class="fa fa-calendar"></i>
                      </a>
                      <a title="Archive Product" class="m-portlet__nav-link btn m-btn m-btn--hover-danger m-btn--icon m-btn--icon-only m-btn--pill"
                        (click)="deleteProductList(pro)">
                        <i class="fa fa-trash"></i>
                      </a>
                    </td> -->

                  </tr>
                </tbody>
              </ng-template>
            </table>
          </div>
          <!-- pagination Start-->
          <boot-pagination [totalSize]="pagi.total" [page]="pagi.page" [listSize]="pagi.limit" [pagelimit]="pagi.limit"
            (pageChange)="reloadTable($event)"></boot-pagination>
          <!-- pagination End-->
        </div>
      </div>
      <!--end: Datatable -->
    </div>
  </div>
  <!--end::Portlet-->
</div>



<div class="native-routing animated">
  <button class="close-sidebar btn btn-sm btn-brand">
    <i class="fa fa-chevron-right"></i>
  </button>
  <span class="close-sidebar-upper">
    <i class="la la-close"></i>
  </span>
  <div class="native-routing-container">
    <app-add-customer [signUp]="signUp" (submit)="submitUser($event)"></app-add-customer>
  </div>
</div>