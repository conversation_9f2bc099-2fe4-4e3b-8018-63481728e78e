<button type="button" class="close" aria-label="Close" (click)="activeModal.dismiss('')">
    <span aria-hidden="true">&times;</span>
</button> 
<div class="modal-body" style="padding: 40px;">
<h4>Registers</h4>
 <div class="m-portlet__body">
    <div *ngIf="loader" class="table-load m-loader m-loader--brand"></div>
    <div class="table-responsive" style="margin-bottom: 10px;">
        <table class="table table-hover">
          <thead>
            <tr>
              <th>
                Store
              </th>
              <th>
                Terminal
              </th>
              <th>
                Opening Date
              </th>
              <th>
                Closing Date
              </th>
              <th>
                Opening Amount
              </th>
              <th>
                Closing Amount
              </th>
              <th>
                Net
              </th>
            </tr>
          </thead>
          <tbody *ngIf="log.length<1; else table">
            <tr *ngIf="!loader"><td colspan="7"><h4 class="text-center">No Data Found</h4></td></tr>
          </tbody>
          <ng-template #table>
            <tbody>
              <tr *ngFor="let r of log; let o='odd'; let e='even'; trackBy: trackTransaction" [ngClass]="{'odd-tr':o, 'even-tr':e}">
                <td >{{getStore(r.store_id)}}</td>
                <td>{{getTerminal(r.terminal_id)}}</td>
                <td>{{getDate(r.date_opened) | date:'MM/dd/yyyy, hh:mm a'}}</td>
                <td>{{getDate(r.date_closed) | date:'MM/dd/yyyy, hh:mm a'}}</td>
                <td>{{(r?.amount_open ? r.amount_open : 0) | currency}}</td>
                <td>{{(r?.amount_close ? r.amount_close : 0) | currency}}</td>
                <td>{{(r?.amount_net ? r.amount_net : 0) | currency}}</td>
              </tr>
            </tbody>
          </ng-template>
        </table>
      </div>
</div>
<div style="padding-top: 20px;">
    <button type="button" class="btn btn-dark btn-sm" (click)="activeModal.close(false)">
       Close
    </button>
</div>
</div>

