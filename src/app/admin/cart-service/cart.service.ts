import { Injectable, Optional, EventEmitter } from "@angular/core";
import { CartServiceConfig, CartItem } from "./cart.models";
import { BehaviorSubject, Observer, of } from "rxjs";
import { HttpService } from "../../modules/http-with-injector/http.service";
import { map, catchError } from "rxjs/operators";
import { AuthService } from "../../modules/auth/auth.service";
import {
  FormatPrice,
  FormateAttribute,
  GET_USER,
  isJson,
  GETTIME,
  singleOrNot,
  getCartDate
} from "../../globals/_classes/functions";
export interface CartDiscountConfig {
  reload: boolean;
  cart?: any;
}

declare let $: any;

@Injectable()
export class CartService {
  config: CartServiceConfig;
  private discountSubject: CartDiscountConfig = { reload: false };
  cartDiscount = new BehaviorSubject(this.discountSubject);
  private CARTCOUNT = new BehaviorSubject<any>(null);
  cartNo = this.CARTCOUNT.asObservable();

  reloadInventory = new BehaviorSubject(null);

  ProductId = new EventEmitter();
  getId = this.ProductId.asObservable();

  private CHECKOUT = new BehaviorSubject<any>(false);
  checkout = this.CHECKOUT.asObservable();

  private PAYMENT = new BehaviorSubject<any>(false);
  payment = this.PAYMENT.asObservable();

  private LOC = new BehaviorSubject<any>(null);
  location = this.LOC.asObservable();

  private BOLT = new BehaviorSubject<any>(null);
  boltOn = this.BOLT.asObservable();

  reloadCalander: EventEmitter<boolean> = new EventEmitter();

  cartNoChange(data) {
    this.CARTCOUNT.next(data);
  }

  getProductId(data) {

  }
  getDeliveryCharge(data) {
    return this.http.post("delivery-charge-list", data).pipe(
      map(res => {
        return res;
      }),
      catchError(e => of(null))
    );
  }


  goToCheckOut(data) {
    this.CHECKOUT.next(data);
  }

  goToPayment(data) {
    this.PAYMENT.next(data);
  }

  changeLoc(data) {
    this.LOC.next(data);
  }

  cancelBoltTerminal(data) {
    this.BOLT.next(data);
  }

  constructor(
    @Optional() config: CartServiceConfig,
    private http: HttpService,
    private authS: AuthService
  ) {
    this.config = config;
  }

 

  datePicker(date) {
    $("#Rental-time-cart").timepicker({
      defaultTime: GETTIME(date),
      minuteStep: 1,
      showSeconds: false,
      showMeridian: true,
      snapToStep: true
    });
    $("#Rental-end-time-cart").timepicker({
      defaultTime: GETTIME(date),
      minuteStep: 1,
      showSeconds: false,
      showMeridian: true,
      snapToStep: true
    });
    $("#Renterl-date-cart").datepicker({
      todayHighlight: true,
      orientation: "bottom right",
      format: "yyyy-mm-dd",
      templates: {
        leftArrow: '<i class="la la-angle-left"></i>',
        rightArrow: '<i class="la la-angle-right"></i>'
      },
      startDate: new Date()
    });
    $("#Renterl-end-date-cart").datepicker({
      todayHighlight: true,
      orientation: "bottom right",
      format: "yyyy-mm-dd",
      templates: {
        leftArrow: '<i class="la la-angle-left"></i>',
        rightArrow: '<i class="la la-angle-right"></i>'
      },
      startDate: new Date()
    });
    $("#Renterl-date-cart").datepicker("setDate", date);
    $("#Renterl-end-date-cart").datepicker("setDate", date);
  }

  private formateData(data, prop) {
    for (let d of data) {
      d["text"] = d[prop];
    }
    return data;
  }

  formatePrice(data) {
    if (data && data.length > 0) {
      return FormatPrice(data);
    }
    return { base: {}, rent: [] };
  }

  formateAttribute(data) {
    return FormateAttribute(data);
  }

  getCurrentDateTime(date) {
    let obj = { date: "", time: "" };
    obj["date"] =
      date.getFullYear() + "-" + (date.getMonth() + 1) + "-" + date.getDate();
    obj["time"] = date.getHours() + ":" + date.getMinutes();
    return obj;
  }

  formateDate(data) {
    if (data) {
      return data.date + " " + data.time;
    }
    return null;
  }

  formateListDate(d) {
    if (d) {
      return new Date(d);
    }
    return "";
  }

  formatUpdateCart(item) {
    const data = new CartItem();
    const prop = [
      "id",
      "token",
      "product_id",
      "price",
      "quantity",
      "rent_start",
      "rental_duration",
      "rental_type",
      "term",
      "sales_tax",
      "deposit_amount",
      "deposite_tax",
      "driving_license_required",
      "variants_products_id",
      "location"
    ];
    for (const key of prop) {
      data[key] = item[key] ? item[key] : "";
    }
    return data;
  }

  getSessionData(name) {
    const data = sessionStorage.getItem(name);
    return data && isJson(data) ? JSON.parse(data) : null;
  }

  setSessionData(name, data) {
    sessionStorage.setItem(name, JSON.stringify(data));
  }

  removeSessionData(name) {
    sessionStorage.removeItem(name);
  }

  getAvailableQty(
    pro,
    attr,
    proQty,
    list,
    sd?,
    dur?,
    edit?: boolean,
    i?: number
  ) {
    const qty = pro.products_availabilities
      .filter(d => {
        return this.checkDate(d.start_date, d.end_date, sd, dur);
      })
      .map(q => {
        return q.quantity;
      })
      .reduce((t, i) => {
        return t + i;
      }, 0);
    let quant = proQty - qty;

    let prod = list.filter(p => {
      return attr.attributes_products_id == p.attributes_products_id;
    });

    for (let q of prod) {
      quant = quant - q.quantity;
    }

    if (edit) {
      quant = quant + list[i].quantity;
    }

    return quant < 0 ? 0 : quant;
  }

  checkDate(s, e, i, d) {
    let date = new Date();
    if (i) {
      date = new Date(i);
    }
    let cur = new Date(
      date.getFullYear(),
      date.getMonth(),
      date.getDate(),
      12,
      0
    ).getTime();
    let curEnd = cur + 86400000 * (d - 1);
    // console.log(new Date(curEnd), d);
    let st = new Date(
      new Date(s).getFullYear(),
      new Date(s).getMonth(),
      new Date(s).getDate(),
      0,
      0
    ).getTime();
    let end = new Date(
      new Date(e).getFullYear(),
      new Date(e).getMonth(),
      new Date(e).getDate(),
      23,
      59
    ).getTime();
    return (st <= cur && end >= cur) || (end > cur && st < curEnd);
  }

  formateSearchList(data) {
    $(".admin-cart .dropdown-menu.show .dropdown-item").unbind();
    const perId = $(".admin-cart .dropdown-menu.show").attr("id");
    var c = document.getElementById(perId).children;
    //   console.log(c.length, data.length);
    for (let j = 0; j < data.length; j++) {
      var id = c[j].getAttribute("id");
      var original = document.getElementById(id);
      // Create a replacement tag of the desired type
      var replacement = document.createElement("div");

      // Grab all of the original's attributes, and pass them to the replacement
      for (var i = 0, l = original.attributes.length; i < l; ++i) {
        var nodeName = original.attributes[i].name;
        var nodeValue = original.attributes[i].value;
        if (nodeName != "type") {
          replacement.setAttribute(nodeName, nodeValue);
        }
      }

      // Persist contents
      const r = data[j];
      // console.log(r);
      const chain = `<div class="colorPurpel"><small style="font-style: italic">${
        r.chain
      }</small></div>`;
      const buy = `<button class="btn btn-sm btn-xsm btn-outline-dark buy-search" data-attr="${
        r.variants_products_id
      }" style="margin-right: 10px;">Buy</button>`;
      const rent = `<button class="btn btn-sm btn-xsm btn-outline-danger rent-search" data-attr="${
        r.variants_products_id
      }">Rent</button>`;
      const notAdded = `<small>(Price not added)</small>`;
      const buyRentShow = `<div>${r.buy ? buy : ""} ${r.rent ? rent : ""} ${
        r.rent || r.buy ? "" : notAdded
      }</div>`;
      const packageBtnShow = `<button class="btn btn-sm btn-xsm btn-outline-dark package-search" data-attr="${
        r.uuid
      }" style="margin-right: 10px;">View</button>`;
      replacement.innerHTML = `<div>${r.name}</div>${r.chain ? chain : ""} ${
        r.type === 1 ? buyRentShow : packageBtnShow
      }`;

      original.parentNode.replaceChild(replacement, original);
    }
  }

  findAttrProId(data, attr) {
    return data.find(f => f.variants_products_id == attr);
  }

  getMonth() {
    return [
      { text: "-Select Month-", value: null },
      { text: "01 January", value: "01" },
      { text: "02 February", value: "02" },
      { text: "03 March", value: "03" },
      { text: "04 April", value: "04" },
      { text: "05 May", value: "05" },
      { text: "06 June", value: "06" },
      { text: "07 July", value: "07" },
      { text: "08 August ", value: "08" },
      { text: "09 September ", value: "09" },
      { text: "10 October ", value: "10" },
      { text: "11 November", value: "11" },
      { text: "12 December", value: "12" }
    ];
  }

  getYears() {
    let y = new Date().getFullYear();
    let arr = [];
    arr.push({ text: "-Select Year-", value: null });
    for (let i = 0; i < 15; i++) {
      let obj = {};
      obj["text"] = y + i;
      obj["value"] = (y + i).toLocaleString().slice(-2);
      arr.push(obj);
    }
    return arr;
  }

  checkBuy() {
    const cart = this.getSessionData("cartList");
    for (let c of cart) {
      if (c.rental_type != "buy") {
        return false;
      }
    }
    return true;
  }

  // Api integration

  getShipping() {
    return this.http
      .get(`stores/delivery-settings`)
      .pipe(
        map(res => res.result),
        catchError(err => of(null))
      )
      .toPromise();
  }


  checkFreeShipping(cartToken: string) {
    return this.http.get('free-shipping/' + cartToken).toPromise();
  }


  getCustomFieldList(product_id) {
    return this.http.get(`products/custom-fields/values/${product_id}`).toPromise();
  }

  getAddonProductListById(id) {
    const loc= GET_USER().location_id;
    return this.http.get("products/" + id + "/addons?required=true&location=" + loc).toPromise();
  }

  searchProduct(search) {
    const loc = GET_USER().location_id;
    return this.http.get(`products/search?location=${loc}&${search}`);
  }

  getProduct(attr_id, type = 1, token='') {
    const location_id   = GET_USER().location_id;
    const timestamp = new Date().getTimezoneOffset();
    const timezone_offset_minutes = timestamp === 0 ? 0 : -timestamp;
    let url =
      type === 1
        ? `products/view/variant-product/${attr_id}`
        : `package/${attr_id}/details/${timezone_offset_minutes}`;

    const cartDateObj=getCartDate('adminOrder');
    if(cartDateObj !=null)
    {
      url=url+"?start_date="+cartDateObj.startDate+"&end_date="+cartDateObj.endDate+"&location="+location_id+"&source=admin&type=cart&token="+token;
    }
       
    return this.http.get(url).pipe(map(res => res.result));
  }


  getPosProduct(attr_id, type = 1, token='') {
    const location_id   = GET_USER().location_id;
    const timestamp = new Date().getTimezoneOffset();
    const timezone_offset_minutes = timestamp === 0 ? 0 : -timestamp;
    let url =
      type === 1
        ? `products/view/variant-product/${attr_id}`
        : `package/${attr_id}/details/${timezone_offset_minutes}`;

    const cartDateObj=getCartDate('posOrder');
    if(cartDateObj !=null)
    {
      url=url+"?start_date="+cartDateObj.startDate+"&end_date="+cartDateObj.endDate+"&location="+location_id+"&source=pos&type=cart&token="+token;
    }
       
    return this.http.get(url).pipe(map(res => res.result));
  }

  addCart(data) {
    return this.http.post("carts/add-to-cart", data).toPromise();
  }

  getCartList(tokenId) {
    return this.http.get("carts/"+tokenId).pipe(map(res => res.result));
  }

  deleteCart(cart, token) {
    return this.http
      .post("carts/cart-remove-item", {
        token: token,
        cart_id: cart.cart_id,
        cart_item_id: cart.id,
        product_id: cart.product_id
      })
      .toPromise();
  }

  addPayment(paymnet) {
    return this.http.post("orders", paymnet).toPromise();
  }

  addPaymentFromOrderDetails(order_id,paymnet) {
    return this.http.post("payments/"+order_id+"/"+paymnet["type"], paymnet).toPromise();
  }

  updateCart(data) {
    return this.http.post("carts/update", data).toPromise();
  }

  applyCoupon(code) {
    return this.http.post("carts/apply-coupon", code).pipe(map(res => res));
  }

  getCreditCardSwipe(data) {
    return this.http
      .post("orders/bolt-card-connect", data)
      .pipe(map(res => res.result));
  }

  deleteAllCart(id) {
    return this.http.delete(`carts/${id}`).pipe(map(res => res.result));
  }

  printReceipt(data) {
    return this.http
      .post(`orders/print-receipt`, data)
      .pipe(map(res => res.result));
  }

  getterminals() {
    return this.http.get(`locations`).pipe(map(res => res));
  }

  sendStoreData(data) {
    return this.http.post("locations/choose", data).toPromise();
  }

  sendterminData(data) {
    return this.http.post("locations/choose", data).toPromise();
  }

  cancelBolt(data) {
    return this.http.post("orders/cancel", data).toPromise();
  }

  addShipping(data) {
    return this.http.post("orders/delivery-cost", data).toPromise();
  }

  getGateways() {
    return this.http.get(`payments/gateway?checkout=true&is_admin=1`).pipe(
      map(m => m.result.data),
      catchError(err => of([]))
    );
  }

  getShippingList() {
    return this.http.get(`shipping-carrier-list`).pipe(
      map(res => res.result.data),
      catchError(e => of([]))
    );
  }

  getAllShipping(data) {
    return this.http.post("delivery-charge-menu", data).toPromise();
    
  }

  getShippingOptions(data) {
    return this.http.post(`shipping/rate`, data).pipe(
      map(res => res),
      catchError(e => of([]))
    );
  }

  addDeliveryCharge(data) {
    return this.http.post("carts/delivery", data).toPromise();
  }

  getCountryState(id) {
    return this.http.get("state-by-country/" + id).pipe(
      map(res => {
        return res.result.data;
      }),
      catchError(e => of(null))
    );
  }
}
