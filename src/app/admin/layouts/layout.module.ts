import { NgModule } from "@angular/core";
import { HeaderNavComponent } from "./header-nav/header-nav.component";
import { AsideNavComponent } from "./aside-nav/aside-nav.component";
import { FooterComponent } from "./footer/footer.component";
import { CommonModule } from "@angular/common";
import { PagesComponent } from "../pages/pages.component";
import { RouterModule } from "@angular/router";
import { CartViewModule } from "../pages/cart-view/cart-view.module";
import { HrefDirectiveModule } from "../../_directives/directive.module";
import { AdminCheckoutModule } from "../pages/admin-checkout/admin-checkout.module";
import { PlanUpgradeAlertModule } from "../../modules/plan-upgrade-alert/plan-upgrade-alert.module";
import { PricingService } from "../pages/pricing/pricing.service";
import { DialogBoxSubscriptionComponent } from "../../modules/dialog-box-subscription/dialog-box-subscription.component";
import { DialogBoxSubscriptionModule } from "../../modules/dialog-box-subscription/dialog-box-subscription.module";
import { OnboardingWizardModule } from "../pages/onboarding-wizard/onboarding-wizard.module";
import { OnboardingWizardComponent } from "../pages/onboarding-wizard/onboarding-wizard.component";
import { DateFormatModule } from "../../modules/date-format/date-format-pipe";
import { DialogBoxModule } from "../../modules/dialog-box/dialog-box.module";


@NgModule({
  declarations: [
    HeaderNavComponent,
    AsideNavComponent,
    PagesComponent,
    FooterComponent
  ],
  exports: [
    HeaderNavComponent,
    AsideNavComponent,
    PagesComponent,
    FooterComponent,
    HrefDirectiveModule
  ],
  imports: [
    CommonModule,
    CartViewModule,
    AdminCheckoutModule,
    RouterModule,
    HrefDirectiveModule,
    OnboardingWizardModule,
    PlanUpgradeAlertModule,
    DialogBoxSubscriptionModule,
    DialogBoxModule,
    DateFormatModule
  ],
  entryComponents:[OnboardingWizardComponent,DialogBoxSubscriptionComponent],
  providers:[PricingService]
})
export class LayoutModule {}
