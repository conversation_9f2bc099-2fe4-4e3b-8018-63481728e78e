import {
  Component,
  OnInit,
  ViewEncapsulation,
  AfterViewInit,
  ViewChild,
  ElementRef
} from "@angular/core";
import { HttpService } from "../../../modules/http-with-injector/http.service";
import { map, catchError } from "rxjs/operators";
import {
  GET_USER,
  getSubscriptionPlan,
  eLogin,
  encrypt
} from "../../../globals/_classes/functions";
import { SidebarService } from "../../pages/sidebar-service/sidebar.service";
import { AlertService } from "../../../modules/alert/alert.service";
import { NgbModal, NgbModalOptions } from "@ng-bootstrap/ng-bootstrap";
import { DialogBoxSubscriptionComponent } from "../../../modules/dialog-box-subscription/dialog-box-subscription.component";
import { Router, ActivatedRoute } from "@angular/router";
import { PricingService } from "../../pages/pricing/pricing.service";
import { of } from "rxjs";
import { SettingService } from "../../pages/settings/setting-service/setting.service";
import { OnboardingWizardComponent } from "../../pages/onboarding-wizard/onboarding-wizard.component";
import { AdminService } from "../../admin.service";
import { domainName } from "../../../globals/endPoint/config";
import { DialogBoxComponent } from "../../../modules/dialog-box/dialog-box.component";
import { PLATFORM_ID,Inject } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
declare let mLayout: any;
declare let Tawk_API: any;
declare var $: any;

@Component({
  selector: "app-aside-nav",
  templateUrl: "./aside-nav.component.html",
  encapsulation: ViewEncapsulation.None
})
export class AsideNavComponent implements OnInit, AfterViewInit {
  status: string[] = [];
  subs_plan: any;
  is_showVideoWall = false;
  
  order_limit=10;
  order_left = 0;
  order_limit_bg_color_class = 'orderlimit-count-red';
  is_accountType_free = true;

  is_show_mailchimp = false;
  is_show_quickbook = false;
  customer: any;
  client: any;


  @ViewChild("hasCusAlert") alertContainer: ElementRef;
  store_id: any;
  is_show_order_stat: boolean;

  constructor(
    @Inject(PLATFORM_ID) private platform: Object,
    private router: Router,
    private route: ActivatedRoute,
    private sidebarS: SidebarService,
    private alertS: AlertService,
    private http: HttpService,
    private priceS: PricingService,
    private modalService: NgbModal,
    private settingService: SettingService,
    private adminS: AdminService,
  ) {
    this.subs_plan = getSubscriptionPlan();
    this.getTimezoneData();
  }
  ngOnInit() {
    $(function () {
      $(".chat-help-btn, #gethelp-closebar").on("click", function () {
        $("#get-help").toggleClass("toggle-show");
      });
    });

   

    this.getVideoWallConfig().subscribe(res => {
      //show or hide video wall side nav
      this.is_showVideoWall =
        res.timezone.hasOwnProperty("show_video_wall") &&
          res.timezone.show_video_wall
          ? true
          : false;
    });


    //check plan type
    const user = localStorage.getItem("currentUser")
      ? JSON.parse(localStorage.getItem("currentUser"))
      : {};

    if (user != null && user.subscription.hasOwnProperty("account_type")) {
      this.is_accountType_free = user.subscription.account_type == 'FREE' ? true : false;
    }

    if (user != null && user.store_id) {
      this.store_id = user.store_id;
    }


    this.settingService.subscriptionChange.subscribe(res => {
      if (res != null) {
        this.is_accountType_free = res.account_type == 'FREE' ? true : false;
      }
    })


      //calculate order left
      const order_left = JSON.parse(sessionStorage.getItem('order_left'));
      if (!isNaN(parseInt(order_left))) {
        this.order_left =parseInt(order_left);
        this.getOrderLimitBgColor(this.order_left)
      }
    

    this.settingService.orderLimit.subscribe(res => {
      if (res != null) {
        const total_orders = res.total_orders;
        if (!isNaN(parseInt(total_orders))) {
          this.order_left = (this.order_limit - parseInt(total_orders)) < 0 ? 0 : (this.order_limit - parseInt(total_orders));
        }
        else {
          this.order_left = this.order_limit;
        }

        this.getOrderLimitBgColor(this.order_left)
        sessionStorage.setItem('order_left', this.order_left.toString());
      }
    })


    this.settingService.storeContentChange.subscribe(res => {
      if (res != null) {
        const storeContent = res.store_content_settings;
        this.customer = storeContent.customer ? storeContent.customer : null;
        this.client = storeContent.client ? storeContent.client : null;
        this.is_show_mailchimp = storeContent.hasOwnProperty('mailchimp') ? storeContent.mailchimp.active : false;
        this.is_show_quickbook = storeContent.hasOwnProperty('quickbook') ? storeContent.quickbook.active : false;
        this.is_show_order_stat = storeContent.hasOwnProperty('subscription') ?
          (storeContent.subscription.hasOwnProperty('create_order') ?
            storeContent.subscription.create_order :
            true) :
          true;
      }
    })



  }

  private getTimezoneData() {
    this.adminS.getTimeZone().subscribe(res => {
      const timezone = res.timezone;
      if (timezone != null) {
        const storeContent = timezone;
        this.customer = storeContent.customer ? storeContent.customer : null;
        this.client = storeContent.client ? storeContent.client : null;
        this.is_show_mailchimp = storeContent.hasOwnProperty('mailchimp') ? storeContent.mailchimp.active : false;
        this.is_show_quickbook = storeContent.hasOwnProperty('quickbook') ? storeContent.quickbook.active : false;
        this.is_show_order_stat = storeContent.hasOwnProperty('subscription') ?
          (storeContent.subscription.hasOwnProperty('create_order') ?
            storeContent.subscription.create_order :
            true) :
          true;
      }
    });
}


  ngAfterViewInit() {
    setTimeout(() => {
      mLayout.initAside();
    }, 500);
    // $("#chatExpert").attr("href", "javascript:void(Tawk_API.toggle())");
  }


  getOrderLimitBgColor(order_left) {
    if (order_left >= 5 && order_left <= this.order_limit) {
      this.order_limit_bg_color_class = 'orderlimit-count-green'
    }
    else if (order_left >= 3 && order_left < this.order_limit) {
      this.order_limit_bg_color_class = 'orderlimit-count-yellow'
    }
    else if (order_left >= 1 && order_left < 2) {
      this.order_limit_bg_color_class = 'orderlimit-count-red'
    }
    else {
      this.order_limit_bg_color_class = 'orderlimit-count-default'
    }
  }

  getVideoWallConfig() {
    return this.http.get("timezones").pipe(
      map(res => res.result),
      catchError(e => of({ timezone: "" }))
    );
  }

  openSidebar() {
    const store = GET_USER().location_id;
    if (store) {
      this.sidebarS.sidebarOpenChange(true);
      this.sidebarS.openCartSidebar();
    } else {
      this.alertS.info(
        this.alertContainer,
        "Store has not been selected. Please select store.",
        true,
        5000
      );
    }
  }

  get is_manager() {
    return GET_USER().user_type_id == 4;
  }

  onClickChatExpert() {
    Tawk_API.toggle();
  }


  onAddInventory(url) {
    this.subs_plan = getSubscriptionPlan();
    if (this.subs_plan == false) {
      this.router.navigateByUrl(url);
    } else {
      this.priceS.getSubscriptionInfo().subscribe(res => {
        console.log(res);
        if (res.hasOwnProperty("newInventory") && res.newInventory) {
          this.router.navigateByUrl(url);
        } else {
          const modalRef = this.modalService.open(
            DialogBoxSubscriptionComponent,
            {
              centered: true,
              windowClass: "animated fadeIn"
            }
          );
          modalRef.componentInstance.massage =
            "Adding new inventory will put you in a higher tier, do you want to upgrade your plan now or enter trial mode?";
          modalRef.result.then(
            result => {
              if (result) {
                this.router.navigateByUrl("/admin/plans");
              } else {
                sessionStorage.setItem("showSubscriptionAlert", "yes");
                sessionStorage.setItem("subscription", JSON.stringify(res));
                this.router.navigateByUrl(url);
              }
            },
            res => {
              console.log(res);
            }
          );
        }
      });
    }
  }

  onClickSubscribeNow() {
    this.router.navigateByUrl("/admin/plans");
  }

  clickOnQuote() {
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams : {type: 2}
    });
  }

  swithToPosScreen(): void {
    this.adminS.getUserDataForPos()
      .then(
        res => {
          if (res.status === 'OK') {
            const returnUrl = "/cash-register";
            const user = res.result.data;
            const a = eLogin(
              encrypt(encrypt(encrypt(JSON.stringify(user), "upper"), "lower"))
            );
            const href = domainName(user.store_name, "store") + `${returnUrl}?user=${a}`;
            if (isPlatformBrowser(this.platform)) {
            window.open(href, "_self");
            }
          } else {
            this.alertS.error(this.alertContainer, 'Something went wrong!', true, 5000);
          }
        })
      .catch(
        err => {
          this.alertS.error(this.alertContainer, 'Something went wrong!', true, 5000);
        });
  }

  onClickMailchimp(source: string) {
    const compareTo = source === 'mailchimp' ? this.is_show_mailchimp : this.is_show_quickbook;
    if (compareTo) {
      this.router.navigate([`/admin/settings/${source}`]);
    } else {
      const modalRef = this.modalService.open(DialogBoxComponent, {
        centered: true,
        windowClass: "animated fadeIn"
      });
      modalRef.componentInstance.massage = "Not included in your current plan. Review your plan?";
      modalRef.result.then(
        result => {
          if (result) {
            this.router.navigateByUrl("/admin/plans");
          }
        }
      );
    }
  }
}
