<!-- BEGIN: Header -->

<header class="m-grid__item    m-header" data-minimize-offset="200" data-minimize-mobile-offset="200" appunwraptag="">
    <div class="m-container m-container--fluid m-container--full-height">
        <div class="m-stack m-stack--ver m-stack--desktop">
            <!-- BEGIN: Brand -->
            <div class="m-stack__item m-brand  m-brand--skin-dark">
                <div class="m-stack m-stack--ver m-stack--general">
                    <div class="m-stack__item m-stack__item--middle m-brand__logo">
                        <a href="/" target="_blank" class="m-brand__logo-wrapper">
                            <img *ngIf="user.logo; else logoAlter" [src]="user.logo" onError="this.src='./assets/img/admin/logo/store_logo.png';">
                            <ng-template #logoAlter>
                                <img src="./assets/img/admin/logo/store_logo.png">
                            </ng-template>
                        </a>
                    </div>
                    <div class="m-stack__item m-stack__item--middle m-brand__tools">
                        <!-- BEGIN: Left Aside Minimize Toggle -->
                        <a href="javascript:;" id="m_aside_left_minimize_toggle" class="m-brand__icon m-brand__toggler m-brand__toggler--left m--visible-desktop-inline-block">
                            <span></span>
                        </a>
                        <!-- END -->
                        <!-- BEGIN: Responsive Aside Left Menu Toggler -->
                        <a href="javascript:;" id="m_aside_left_offcanvas_toggle" class="m-brand__icon m-brand__toggler m-brand__toggler--left m--visible-tablet-and-mobile-inline-block">
                            <span></span>
                        </a>
                        <!-- END -->
                        <!-- BEGIN: Responsive Header Menu Toggler -->
                        <a id="m_aside_header_menu_mobile_toggle" href="javascript:;" class="m-brand__icon m-brand__toggler m--visible-tablet-and-mobile-inline-block">
                            <span></span>
                        </a>
                        <!-- END -->
                        <!-- BEGIN: Topbar Toggler -->
                        <a id="m_aside_header_topbar_mobile_toggle" href="javascript:;" class="m-brand__icon m--visible-tablet-and-mobile-inline-block">
                            <i class="flaticon-more"></i>
                        </a>
                        <!-- BEGIN: Topbar Toggler -->
                    </div>
                </div>
            </div>
            <!-- END: Brand -->
            <div class="m-stack__item m-stack__item--fluid m-header-head" id="m_header_nav">
                <!-- BEGIN: Horizontal Menu -->
                <button class="m-aside-header-menu-mobile-close  m-aside-header-menu-mobile-close--skin-dark" id="m_aside_header_menu_mobile_close_btn">
					<i class="la la-close"></i>
				</button>
                <div id="m_header_menu" class="m-header-menu m-aside-header-menu-mobile m-aside-header-menu-mobile--offcanvas  m-header-menu--skin-light m-header-menu--submenu-skin-light m-aside-header-menu-mobile--skin-dark m-aside-header-menu-mobile--submenu-skin-dark">
                    <ul class="m-menu__nav  m-menu__nav--submenu-arrow">
                        <li class="m-menu__item  m-menu__item--submenu m-menu__item--rel" data-menu-submenu-toggle="click" data-redirect="true" aria-haspopup="true">
                            <a href="#" class="m-menu__link m-menu__toggle">
                                <i class="m-menu__link-icon flaticon-add"></i>
                                <span class="m-menu__link-text">
									Actions
								</span>
                                <i class="m-menu__hor-arrow la la-angle-down"></i>
                                <i class="m-menu__ver-arrow la la-angle-right"></i>
                            </a>
                            <div class="m-menu__submenu m-menu__submenu--classic m-menu__submenu--left">
                                <span class="m-menu__arrow m-menu__arrow--adjust"></span>
                                <ul class="m-menu__subnav">
                                    <li class="m-menu__item" routerLinkActive="m-menu__item--active" routerLinkActiveOptions="{ exact: true }" aria-haspopup="true">
                                        <a routerLink="/admin/inventory/create" class="m-menu__link">
                                            <i class="m-menu__link-icon flaticon-file"></i>
                                            <span class="m-menu__link-text">
												Create New Inventory
											</span>
                                        </a>
                                    </li>
                                    <li class="m-menu__item" routerLinkActive="m-menu__item--active" routerLinkActiveOptions="{ exact: true }" aria-haspopup="true">
                                        <a class="m-menu__link" (click)="openSidebar()">
                                            <i class="m-menu__link-icon flaticon-cart"></i>
                                            <span class="m-menu__link-text">
												Create New Order
											</span>
                                        </a>
                                    </li>
                                    <li *ngIf="cartNo===0" class="m-menu__item" routerLinkActive="m-menu__item--active" routerLinkActiveOptions="{ exact: true }" aria-haspopup="true">
                                        <a class="m-menu__link" (click)="swithToPosScreen()">
                                            <i class="m-menu__link-icon flaticon-app"></i>
                                            <span class="m-menu__link-text">
													Switch to POS Screen
												</span>
                                        </a>
                                    </li>
                                    <li class="m-menu__item" routerLinkActive="m-menu__item--active" routerLinkActiveOptions="{ exact: true }" aria-haspopup="true">
                                        <a href="/" target="_blank" class="m-menu__link">
                                            <i class="m-menu__link-icon la la-globe"></i>
                                            <span class="m-menu__link-text">
												See Store
											</span>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                        <li class="m-menu__item  m-menu__item--submenu m-menu__item--rel terminal-bolt" data-menu-submenu-toggle="click" data-redirect="true" aria-haspopup="true">
                            <a href="#" class="m-menu__link m-menu__toggle">
                                <i class="m-menu__link-icon flaticon-open-box"></i>
                                <span *ngIf="store; else selectOne" class="m-menu__link-text">
									{{store?.name}}
								</span>
                                <ng-template #selectOne>
                                    <span class="m-menu__link-text">
										Select Location
									</span>
                                </ng-template>
                                <i class="m-menu__hor-arrow la la-angle-down"></i>
                                <i class="m-menu__ver-arrow la la-angle-right"></i>
                            </a>
                            <div class="m-menu__submenu m-menu__submenu--classic m-menu__submenu--left">
                                <span class="m-menu__arrow m-menu__arrow--adjust"></span>
                                <ul class="m-menu__subnav">
                                    <li class="m-menu__item" *ngFor="let ter of storelList">
                                        <a class="m-menu__link" style="cursor: pointer;" (click)="selectStore(ter)">
                                            <i class="m-menu__link-bullet m-menu__link-bullet--dot">
												<span></span>
											</i>
                                            <span class="m-menu__link-text">
												{{ter.name}}
											</span>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                        <li *ngIf="store?.stores_terminals && store.stores_terminals.length > 0" class="m-menu__item  m-menu__item--submenu m-menu__item--rel terminal-bolt" data-menu-submenu-toggle="click" data-redirect="true" aria-haspopup="true">
                            <a href="#" class="m-menu__link m-menu__toggle">
                                <i class="m-menu__link-icon flaticon-network"></i>
                                <span *ngIf="terminal; else selectTer" class="m-menu__link-text">
									{{terminal?.name}}
								</span>
                                <ng-template #selectTer>
                                    <span class="m-menu__link-text">
										Select Terminal
									</span>
                                </ng-template>
                                <i class="m-menu__hor-arrow la la-angle-down"></i>
                                <i class="m-menu__ver-arrow la la-angle-right"></i>
                            </a>
                            <div *ngIf="store?.stores_terminals" class="m-menu__submenu m-menu__submenu--classic m-menu__submenu--left">
                                <span class="m-menu__arrow m-menu__arrow--adjust"></span>
                                <ul class="m-menu__subnav">
                                    <li class="m-menu__item" *ngFor="let ter of store?.stores_terminals">
                                        <a class="m-menu__link" style="cursor: pointer;" (click)="selectTerminal(ter)">
                                            <i class="m-menu__link-bullet m-menu__link-bullet--dot">
												<span></span>
											</i>
                                            <span class="m-menu__link-text">
												{{ter.name}}
											</span>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                        <li style="margin: 7px;">
                            <div class="custom-alert" #hasCusAlert></div>
                        </li>
                    </ul>
                </div>
                <!-- END: Horizontal Menu -->
                <!-- BEGIN: Topbar -->
                <div id="m_header_topbar" class="m-topbar  m-stack m-stack--ver m-stack--general">
                    <div class="m-stack__item m-topbar__nav-wrapper">
                        <ul class="m-topbar__nav m-nav m-nav--inline">
                            <!-- Add Cart -->
                            <li class="m-nav__item m-topbar__user-profile m-topbar__user-profile--img  m-dropdown m-dropdown--medium m-dropdown--arrow m-dropdown--header-bg-fill m-dropdown--align-right m-dropdown--mobile-full-width m-dropdown--skin-light" data-dropdown-toggle="click">
                                <a href="/" target="_blank" class="m-nav__link header-seestore-button">
                                    <span class="m-menu__link-text">
                                        See Store
                                    </span>
                                    <span class="m-nav__link-icon">
                                        <i class="la la-globe"></i>
                                    </span>
                                </a>
                            </li>
                            <li class="m-nav__item m-topbar__user-profile m-topbar__user-profile--img  m-dropdown m-dropdown--medium m-dropdown--arrow m-dropdown--header-bg-fill m-dropdown--align-right m-dropdown--mobile-full-width m-dropdown--skin-light" data-dropdown-toggle="click">
                                <a href="#" class="m-nav__link m-dropdown__toggle">
                                    <span class="m-nav__link-icon">
										<i class="la la-bell"></i>
									</span>
                                    <span #pulse class=""></span>
                                </a>
                                <div class="m-dropdown__wrapper">
                                    <span class="m-dropdown__arrow m-dropdown__arrow--right m-dropdown__arrow--adjust notification-arrow"></span>
                                    <div class="m-dropdown__inner">
                                        <div class="m-dropdown__header m--align-center" style="background: url(./assets/img/admin/notification.jpg); background-size: cover;">
                                            <div class="m-card-user m-card-user--skin-dark">
                                                <h4 class="d-flex flex-center rounded-top">
                                                    <span class="text-white">Alerts</span>
                                                    <!-- <span class="btn btn-text btn-success btn-sm font-weight-bold btn-font-md ml-2">23 new</span> -->
                                                </h4>
                                            </div>
                                        </div>
                                        <div class="m-dropdown__body adminnotification-body">
                                            <div class="m-dropdown__content">
                                                <ul class="m-nav m-nav--skin-light notification-list">
                                                    <li class="m-nav__item" *ngFor="let not of notificationsList" [style.background-color]="not.is_read ? 'null' : '#eee'">
                                                        <a routerLink="{{'/admin/reservations/' + not.order_id + '/details'}}" class="m-nav__link">
                                                            <span class="notification-text">{{not.note}}</span>
                                                            <span class="notification-datetime"><i class="la la-calendar"></i>{{not.created | customDate}}</span>
                                                        </a>
                                                    </li>

                                                </ul>
                                            </div>
                                        </div>
                                        <div class="m-dropdown__body adminnotification-footer" *ngIf="notificationsList?.length>0;else no_alert">
                                            <div class="m-dropdown__content">
                                                <a routerLink="/admin/notifications">View all alerts</a>
                                            </div>
                                        </div>
                                        <ng-template #no_alert>
                                            <div style="padding: 10px;text-align: center;">No alerts</div>
                                        </ng-template>
                                    </div>
                                </div>
                            </li>
                            <li class="m-nav__item m-topbar__quick-actions m-topbar__quick-actions--img">
                                <a class="m-nav__link" (click)="openSidebar()" style="cursor: pointer;">
                                    <span class="m-nav__link-badge m-badge" style="background: #2c2e3e; color: #fff">{{cartNo}}</span>
                                    <span class="m-nav__link-icon">
										<i class="flaticon-cart"></i>
									</span>
                                </a>
                            </li>
                            <li class="m-nav__item m-topbar__user-profile m-topbar__user-profile--img  m-dropdown m-dropdown--medium m-dropdown--arrow m-dropdown--header-bg-fill m-dropdown--align-right m-dropdown--mobile-full-width m-dropdown--skin-light" data-dropdown-toggle="click">
                                <a href="#" class="m-nav__link m-dropdown__toggle">
                                    <span class="m-topbar__userpic">
										<img *ngIf="checkImage(); else alter" [src]="image_url" onError="this.src='./assets/img/admin/logo/user.png';" class="m--img-rounded m--marginless m--img-centered" alt="Profile"/>
										<ng-template #alter>
											<img src="./assets/img/admin/logo/user.png" class="m--img-rounded m--marginless m--img-centered" alt=""/>
										</ng-template>
									</span>
                                    <span class="m-topbar__username m--hide">
										Nick
									</span>
                                </a>
                                <div class="m-dropdown__wrapper">
                                    <span class="m-dropdown__arrow m-dropdown__arrow--right m-dropdown__arrow--adjust"></span>
                                    <div class="m-dropdown__inner">
                                        <div class="m-dropdown__header m--align-center" style="background: url(./assets/img/admin/misc/user_profile_bg.jpg); background-size: cover;">
                                            <div class="m-card-user m-card-user--skin-dark">
                                                <div class="m-card-user__pic">
                                                    <img *ngIf="checkImage(); else alter" onError="this.src='./assets/img/admin/logo/user.png';" [src]="image_url" class="m--img-rounded m--marginless" alt="Profile" />
                                                    <ng-template #alter>
                                                        <img src="./assets/img/admin/logo/user.png" class="m--img-rounded m--marginless" alt="" />
                                                    </ng-template>
                                                </div>
                                                <div class="m-card-user__details">
                                                    <span class="m-card-user__name m--font-weight-500">
														{{user?.name}}
													</span>
                                                    <a href="" class="m-card-user__email m--font-weight-300 m-link">
														{{user?.email}}
													</a>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="m-dropdown__body">
                                            <div class="m-dropdown__content">
                                                <ul class="m-nav m-nav--skin-light">
                                                    <li class="m-nav__section m--hide">
                                                        <span class="m-nav__section-text">
															Section
														</span>
                                                    </li>
                                                    <li class="m-nav__item">
                                                        <a routerLink="{{'/admin/user/' + user.user_id +'/profile'}}" class="m-nav__link">
                                                            <i class="m-nav__link-icon flaticon-profile-1"></i>
                                                            <span class="m-nav__link-text">
																My Profile
															</span>
                                                        </a>
                                                    </li>
                                                    <li class="m-nav__separator m-nav__separator--fit"></li>
                                                    <li class="m-nav__item">
                                                        <a class="btn m-btn--pill btn-secondary m-btn m-btn--custom m-btn--bolder" routerLink="/logout">
															Logout
														</a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
                <!-- END: Topbar -->

            </div>
            <!-- <div class="m-stack__item m-stack__item--fluid m-header-head"> -->
            <plan-upgrade-alert id="SubsAlart" class="dashboard-plan-alert" [message]="
					'Free Trial'
				"></plan-upgrade-alert>
            <!-- </div> -->
        </div>
    </div>


</header>
<!-- END: Header -->