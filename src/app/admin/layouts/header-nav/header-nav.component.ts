import { DashboarService } from './../../pages/dashboard/dashboard.service/dashboard.service';
import {
  Component,
  OnInit,
  ViewEncapsulation,
  AfterViewInit,
  ViewChild,
  ElementRef,
  Renderer2
} from "@angular/core";
import { Helpers } from "../../../helpers";
import { User_login } from "../../pages/user-management/models/user.models";
import { user_image, domainName } from "../../../globals/endPoint/config";
import { AdminService } from "../../admin.service";
import { GET_USER, changeUser, getSubscriptionPlan, eLogin, encrypt } from "../../../globals/_classes/functions";
import { SidebarService } from "../../pages/sidebar-service/sidebar.service";
import { CartService } from "../../cart-service/cart.service";
import { AlertService } from "../../../modules/alert/alert.service";
import { Terminal, Stores } from "../../pages/settings/models/settings.models";
import { PricingService } from "../../pages/pricing/pricing.service";
import { ActivatedRoute, Router, NavigationStart } from '@angular/router';
import { filter } from 'rxjs/operators';
import { PLATFORM_ID,Inject } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
declare let mLayout: any;

@Component({
  selector: "app-header-nav",
  templateUrl: "./header-nav.component.html",
  encapsulation: ViewEncapsulation.None
})
export class HeaderNavComponent implements OnInit, AfterViewInit {
  image_url: string;
  user: User_login;
  cartNo: number;
  store: Stores;
  storelList: Stores[] = [];
  terminal: Terminal;
  subs_plan;

  @ViewChild("hasCusAlert") alertContainer: ElementRef;
  @ViewChild("pulse") pulse: ElementRef;
  notificationsList: any;

  constructor(
    @Inject(PLATFORM_ID) private platform: Object,
    private alertS: AlertService,
    private adminS: AdminService,
    private sidebarS: SidebarService,
    private cartS: CartService,
    private priceS: PricingService,
    private route: Router,
    private renderer: Renderer2
  ) {
    this.getNotifications();
    this.route.events
    .pipe(
      filter(event => event instanceof NavigationStart)
    ).subscribe(res => {
      this.getNotifications();
    });
    this.cartS.cartNo.subscribe(
      val => (this.cartNo = val && val.cart_items ? val.cart_items.length : 0)
    );
    this.adminS.stores.subscribe(val => {
      if (val) {
        this.formatStores(val);
        if (this.store && this.store.stores_terminals.length < 1) {
          changeUser("terminal_id", null);
        }
        this.adminS.changeUser(GET_USER());
      }
    });
  }

  ngOnInit() {
    this.subs_plan = getSubscriptionPlan();
    if(this.subs_plan==false)
    {
      $('#SubsAlart').show();
    }
    else{
      $('#SubsAlart').hide();
    }
    
    this.userChange();
    this.getImageUrl();
    this.getTerminal();

    this.adminS.user.subscribe(res=>{
      if(res.user_id){
        console.log(res);
        this.user=res;
      }
     
    })
  }
  ngAfterViewInit() {
    mLayout.initHeader();
  }
  getImageUrl() {
    this.user = GET_USER();
    this.image_url = user_image + this.user.image;
  }

  getTerminal() {
    this.cartS.getterminals().subscribe(
      res => {
        if (res.status === "OK") {
          // console.log(res.result.data);
          this.formatStores(res.result.data);
        } else {
          this.storelList = [];
          this.store = null;
        }
      },
      err => {
        console.log(err);
        this.storelList = [];
      }
    );
  }

  formatStores(data) {
    this.cartS.changeLoc(data);
    this.storelList = data.filter(d => d.status === 1);
    localStorage.setItem("locations", JSON.stringify(this.storelList));
    if (this.user.location_id) {
      this.store = this.storelList.find(d => d.id === this.user.location_id);
      if (this.user["terminal_id"] && this.store) {
        this.terminal = this.store.stores_terminals.find(
          d => d.id === this.user["terminal_id"]
        );
      } else {
        this.terminal = null;
        changeUser("terminal_id", null);
      }
    } else {
      this.store = null;
      this.terminal = null;
    }
  }

  selectStore(data) {
    // console.log(data);
    const cho = {
      //  user_id: this.user.user_id,
      location_id: data.id,
      terminal_id:
        data.stores_terminals.length && data.stores_terminals[0].id
          ? data.stores_terminals[0].id
          : null
    };
    this.chooseStore(cho, data);
  }

  chooseStore(data, select) {
    this.cartS
      .sendStoreData(data)
      .then(res => {
        if (res.status === "OK") {
          this.store = select;
          this.terminal = this.store.stores_terminals[0]
            ? this.store.stores_terminals[0]
            : null;
          changeUser("location_id", this.store.id);
          this.cartS.reloadInventory.next(true);
          changeUser("terminal_id", this.terminal ? this.terminal.id : null);
          this.alertS.success(
            this.alertContainer,
            "Location has been successfully selected",
            true,
            3000
          );
          this.adminS.changeUser(GET_USER());
        } else {
          this.error("Location has not been selected");
        }
      })
      .catch(err => {
        console.log(err);
        this.error("Location has not been selected");
      });
  }

  selectTerminal(data) {
    const cho = {
      // user_id: this.user.user_id,
      location_id: this.store.id,
      terminal_id: data.id
    };
    if (data.status === 0) {
      this.alertS.info(
        this.alertContainer,
        "Terminal is not active. Please active the terminal.",
        true,
        3000
      );
    } else {
      this.chooseTreminal(cho);
    }
  }

  chooseTreminal(data) {
    this.cartS
      .sendterminData(data)
      .then(res => {
        // console.log(res);
        if (res.status === "OK") {
          const obj = this.store.stores_terminals.find(
            item => item.id === res.result.data.terminal_id
          );
          this.terminal = obj;
          changeUser("terminal_id", this.terminal.id);
          this.alertS.success(
            this.alertContainer,
            "Terminal has been successfully selected",
            true,
            3000
          );
          this.adminS.changeUser(GET_USER());
        } else {
          this.terminal = null;
        }
      })
      .catch(err => {
        console.log(err);
        this.error("Terminal has not been selected");
      });
  }

  error(massage) {
    this.alertS.error(this.alertContainer, massage, true, 3000);
    this.store = null;
  }

  userChange() {
    this.adminS.user.subscribe(user => {
      this.user = user;
      this.image_url = user_image + this.user.image;
    });
  }

  checkImage() {
    if (this.user && this.user.image) {
      return true;
    }
    return false;
  }

  openSidebar() {
    const orderLeft=sessionStorage.getItem('order_left');
    const order_left=parseInt( orderLeft);
    const user = GET_USER();
    const store = GET_USER().location_id;
    if (store) {
      this.sidebarS.sidebarOpenChange(true);
      this.sidebarS.openCartSidebar();
    } else {
      this.alertS.info(
        this.alertContainer,
        "Store has not been selected. Please select store.",
        true,
        5000
      );
    }

    // if(order_left==0 &&  user.subscription.account_type=='FREE'){
    //   this.alertS.error(
    //     this.alertContainer,
    //     "You must subscribe to continue placing orders.",
    //     true,
    //     5000
    //   );
    // }
    // else{

    // }
  }

  getNotifications(): void {
    this.adminS.getNotifications(1, 5)
      .then( res => {
        if (res.status === 'OK') {
          this.notificationsList = res.result.data;
          // for (let i = 0; 1 <= res.result.data; i++) {
          //   if (!res.result.data[i].is_read) {
          //     this.renderer.addClass(this.pulse.nativeElement, 'pulse-ring');
          //     break;
          //   } else {
          //     this.renderer.removeClass(this.pulse.nativeElement, 'pulse-ring');
          //   }
          // }
          if (res.result.unread > 0) {
            this.renderer.addClass(this.pulse.nativeElement, 'pulse-ring');
          } else {
            this.renderer.removeClass(this.pulse.nativeElement, 'pulse-ring');
          }
        } else {
          this.notificationsList = [];
        }
      }).catch( err => {
        this.notificationsList = [];
      })
  }

  swithToPosScreen(): void {
    this.adminS.getUserDataForPos()
      .then(
        res => {
          if (res.status === 'OK') {
            const returnUrl = "/cash-register";
            const user = res.result.data;
            const a = eLogin(
              encrypt(encrypt(encrypt(JSON.stringify(user), "upper"), "lower"))
            );
            const href = domainName(user.store_name, "store") + `${returnUrl}?user=${a}`;
            if (isPlatformBrowser(this.platform)) {
            window.open(href, "_self");
            }
          } else {
            this.alertS.error(this.alertContainer, 'Something went wrong!', true, 5000);
          }
        })
      .catch(
        err => {
          this.alertS.error(this.alertContainer, 'Something went wrong!', true, 5000);
        });
  }
}
