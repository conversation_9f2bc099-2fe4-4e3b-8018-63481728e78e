import { OnlineAuthService } from './../modules/online-store-auth/online-auth.service';
import { Injectable } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  CanActivate,
  CanActivateChild,
  CanLoad,
  Route,
  Router,
  RouterStateSnapshot,
  Resolve
} from '@angular/router';
import { Observable, BehaviorSubject, of } from 'rxjs';
import { AuthService } from '../modules/auth/auth.service';
import { User_login } from './pages/user-management/models/user.models';
import { HttpService } from '../modules/http-with-injector/http.service';
import { Stores } from './pages/settings/models/settings.models';
import { isJson, dcrypt, dLogin } from '../globals/_classes/functions';
import { tap, map, catchError } from 'rxjs/operators';
import { PLATFORM_ID,Inject } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';

@Injectable()
export class AdminService
  implements CanLoad, CanActivate, CanActivateChild, Resolve<any> {
  private USER = new BehaviorSubject<User_login>(new User_login());
  user = this.USER.asObservable();

  private STORE = new BehaviorSubject<Stores[]>(null);
  stores = this.STORE.asObservable();

  private logo = new BehaviorSubject<string>('assets/img/company/logo.png');
  public logoUrl = this.logo.asObservable();

  sendQ(data) {
    return this.http.post('questionaries', data).pipe(
      tap((res) => {
        if (res.result.data.logo) {
          this.logo.next(res.result.data.logo);
          this.logoUrl.subscribe(res => console.log(res))
        }
      }
      )
    )
  }

  changeUser(user) {
    this.USER.next(user);
  }

  changeStore(s) {
    this.STORE.next(s);
  }

  constructor(
    @Inject(PLATFORM_ID) private platform: Object,
    private auth: AuthService,
    private router: Router,
    private http: HttpService,
    private onlineAuth: OnlineAuthService
  ) { }

  resolve(router: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
    this.http
      .get(`clear-cache`)
      .toPromise()
      .then(res => {
        if (res.status === 'OK') {
          this.router.navigate(['/admin/dashboard']);
        }
      });
  }

  canLoad(route: Route): Observable<boolean> | Promise<boolean> | boolean {
    if (
      !(this.auth.authenticated && (this.auth.is_admin || this.auth.is_manager))
    ) {
      this.router.navigate(['/auth/login']).catch(err => {
        console.log(err);
      });
    }
    return this.auth.authenticated;
  }

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
if(isPlatformBrowser(this.platform)){


    localStorage.removeItem('online_store');
    // localStorage.removeItem('onlineAuthUser');
    this.onlineAuth.removeUserFormAdmin('onlineAuthUser');
    localStorage.setItem('clearOnlineAuth', 'true');
    localStorage.setItem('reload-home', 'yes');

    if ((state.url.includes('/admin/dashboard') || state.url.includes('/admin/newusertour')) && route.queryParamMap.get('user')) {
      const d = dcrypt(dcrypt(dcrypt(dLogin(route.queryParamMap.get('user')), 'upper'), 'lower'));
      const user = isJson(d) ? JSON.parse(d) : null;
      if (user) {
        this.setUserInfoForSuper(user);

        if (user.hasOwnProperty('store_slug')) {
          localStorage.setItem('storeName', JSON.stringify(user.store_slug));
        }


        //redirect url
        if (route.queryParamMap.get('redirect_url') != undefined) {

          if (route.queryParamMap.get('plan_name') != undefined && route.queryParamMap.get('type') != undefined) {
            this.router.navigate([route.queryParamMap.get('redirect_url')], { queryParams: { plan_name: route.queryParamMap.get('plan_name'), type: route.queryParamMap.get('type') } });
          }
          else if (route.queryParamMap.get('plan_name') != undefined) {
            this.router.navigate([route.queryParamMap.get('redirect_url')], { queryParams: { plan_name: route.queryParamMap.get('plan_name') } });
          }
          else {
            this.router.navigate([route.queryParamMap.get('redirect_url')]);
          }
        }


      }
    }

    if (
      this.auth.authenticated && this.auth.storeUser
    ) {
      return true;
    }

    //get redirect url and params
    let url = state.url.includes('?') ? state.url.split('?') : state.url;
    if (state.url.includes('?')) {
      this.router.navigateByUrl('/auth/login?redirect_url=' + url[0] + '&' + url[1]);
    }
    else {
      this.router.navigateByUrl('/auth/login?redirect_url=' + url);
    }

    return false;
  }
  }

  canActivateChild(
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    return this.canActivate(childRoute, state);
  }

  private setUserInfoForSuper(value) {
    this.auth.setUser(value, true);

  }

  getUserDataForPos() {
    return this.http.get('users/token?source=pos').toPromise();
  }

  getNotifications(page_no, limit) {
    return this.http.get(`notifications?page_no=${page_no?page_no:1}&limit=${limit?limit:20}`).toPromise();
  }

  getTimeZone() {
    return this.http.get("timezones").pipe(
      map(res => res.result),
      catchError(e => of({ timezone: "" }))
    );
  }

  deleteLogo() {
    return this.http.delete('stores/logo').toPromise();
  }

}
