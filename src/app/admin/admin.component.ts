import { HttpService } from './../modules/http-with-injector/http.service';
import { Component, OnInit, ViewEncapsulation, HostListener } from '@angular/core';
import { Router, NavigationStart, NavigationEnd, ActivatedRoute } from '@angular/router';
import { Helpers } from '../helpers';
import { GET_USER, isJson } from '../globals/_classes/functions';
import { MetaService } from '../_services/meta.service';
import { Subscription } from 'rxjs';
import { SidebarService } from './pages/sidebar-service/sidebar.service';
import { AuthService } from '../modules/auth/auth.service';
import { CartService } from './cart-service/cart.service';
import { logOutStore } from '../globals/endPoint/config';
import { DashboarService } from './pages/dashboard/dashboard.service/dashboard.service';
import { ScriptLoaderService } from '../_services/script-loader.service';
import { SettingService } from './pages/settings/setting-service/setting.service';
import { PLATFORM_ID,Inject } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
declare let mApp: any;
declare let mUtil: any;
declare let mLayout: any;

@Component({
    selector: ".m-grid.m-grid--hor.m-grid--root.m-page",
    templateUrl: "./admin.component.html",
    styleUrls: ['./admin.component.css'],
    encapsulation: ViewEncapsulation.None
})
export class AdminComponent implements OnInit {

    globalBodyClass = 'm-page--loading-non-block m-page--fluid m--skin- m-content--skin-light2 m-header--fixed m-header--fixed-mobile m-aside-left--enabled m-aside-left--skin-dark m-aside-left--offcanvas m-footer--push m-aside--offcanvas-default';

    sidebarOpen: boolean;
    sub: Subscription[] = [];
    loader: boolean;

    @HostListener('window:resize', ['$event'])
    onResize(event) {
        if (this.sidebarOpen) {
            if (isPlatformBrowser(this.platform)) {
                const store = GET_USER().location_id;
                if (store) {
                    this.sidebarS.openCartSidebar();
                }
            }
        }
    }

    constructor(@Inject(PLATFORM_ID) private platform: Object,private _router: Router,
        private sidebarS: SidebarService,
        private cartS: CartService,
        private auth: AuthService,
        private metaS: MetaService,
        private _script: ScriptLoaderService,
        public dashS: DashboarService,
        public http: HttpService,
        private settingS: SettingService) { }

    ngOnInit() {
        const currentuser = JSON.parse(localStorage.getItem('currentUser'));
        this._script.loadScript('body', 'assets/js/admin/tawk.js');
        // this.getCurrency();
        this.getCurrencyConfig();
        this.setContent();

        Helpers.removeScript('assets/js/home/<USER>');
        Helpers.removeScript('assets/js/home/<USER>');
        Helpers.removeCSS('assets/css/home/<USER>');
        Helpers.removeCSS('assets/css/home/<USER>');
        Helpers.removeCustomCSS('home-custom');

        this.addTages();
        this.sub[0] = this.sidebarS.sidebarOpen.subscribe(
            val => {
                this.sidebarOpen = val;
                this.sidebarS.closeCartSidebar();
            }
        );

        this.sub[1] = this._router.events.subscribe((route) => {
            if (route instanceof NavigationEnd) {
                this.sidebarOpen = false;
            }
        });

        this.loadJsPromise().then(
            res => {
                Helpers.setLoading(false);
            }
        );
        Helpers.bodyClass(this.globalBodyClass);

        this._router.events.subscribe((route) => {
            if (route instanceof NavigationStart) {
                (<any>mLayout).closeMobileAsideMenuOffcanvas();
                (<any>mLayout).closeMobileHorMenuOffcanvas();
                Helpers.setLoading(true);
                (<any>$('[data-toggle="m-popover"]')).popover('hide');
                (<any>$('[data-toggle="popover"]')).popover('hide');
                (<any>$('.popover')).popover('hide');
            }
            if (route instanceof NavigationEnd) {
                (<any>mApp).init();
                (<any>mUtil).init();
                Helpers.setLoading(false);
            }

        });

        this.sub[2] = this.auth.checkLogin().subscribe(val => {
            if (!val) {
                localStorage.removeItem('currentUser');
                sessionStorage.removeItem('currentUser');
                if (isPlatformBrowser(this.platform)) {
                window.open(logOutStore + '/auth/login', "_self");
                }
            }
        });
    }

    // private getCurrency() {
    //     this.dashS.getTimeZone().subscribe(res => {
    //       const currency = res.timezone.currency_format;
    //       console.log('loaded')
    //       if (currency) {
    //         localStorage.setItem("currency", JSON.stringify(currency));
    //       }
    //       this.settingS.onStoreContentChange({updateSidebar:true,store_content_settings:res.timezone});
    //     });
    // }

    getCurrencyConfig() {
      this.dashS.getCurrencyConfig().subscribe(
        res => {
          const currency = res;
          if (currency) {
            localStorage.setItem("currency", JSON.stringify(currency));
          }
        }
      )
    }

    setContent(){
      this.http.get('contents').toPromise()
          .then(res => {
              if (res.status === 'OK' && res.result.data.length > 0) {
                  const content = {};
                  const data = res.result.data.filter(f => {
                      return f['config'].status === 1;
                  });

                  for (const c of data) {
                      const tag = this.formatTag(c.config.tag);
                      content[tag] = isJson(c.contents) ? JSON.parse(c.contents) : c.contents;
                  }
                  localStorage.setItem('contents', JSON.stringify(content));
                  return content;
              } else {
                  return {};
              }
          }).catch(err => console.log(err));
    }

    formatTag(text: String) {
      return text.replace(/(\-[a-z])/g, function ($1) { return $1.toUpperCase().replace('-', ''); });
    }

    ngAfterViewInit() {
        this.closeSidebar();
    }

    ngOnDestroy() {
        this.sub.forEach(s => {
            s.unsubscribe();
        });
        if ($('.bacdrop-payment').hasClass('dis-block')) {
            this.cancelBolt();
        }
    }

    closeSidebar() {
        this.sidebarS.closeCartSidebar();
    }


    addTages() {
        // this.metaS.setTitle('RentMy – The complete rental management solution');
        // const metaTag = [
        //     { name: 'twitter:title', content: 'RentMy – The complete rental management solution' },
        //     { property: 'og:title', content: 'RentMy – The complete rental management solution' },
        //     { property: 'og:url', content: 'https://rentmy.co/' }
        // ];
        // this.metaS.updateTags(metaTag);
    }

    cancelBolt() {
        if (isPlatformBrowser(this.platform)) {
        this.loader = true;
        const data = {
            terminal_id: GET_USER().terminal_id
        };
        this.cartS.cancelBoltTerminal(true);
        this.cartS.cancelBolt(data).then(
            res => {
                this.cancel();
            }
        ).catch(
            err => {
                console.log(err);
                this.cancel();
            }
        );
        }
    }

    cancel() {
        $('.bacdrop-payment').removeClass('dis-block');
        $('.bacdrop-payment').addClass('dis-none');
        this.loader = false;
    }

    private loadJsPromise() {
        return new Promise((resolve, reject) => {
            Helpers.setLoading(true);
            resolve();
        });
    }

}
