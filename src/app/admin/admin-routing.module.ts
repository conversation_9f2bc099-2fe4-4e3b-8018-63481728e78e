import { NgModule } from "@angular/core";
import { AdminComponent } from "./admin.component";
import { Routes, RouterModule } from "@angular/router";
import { AdminService } from "./admin.service";
import { AdminRoleGaurdService } from "./admin-role-gaurd.service";

const routes: Routes = [
  {
    path: "",
    component: AdminComponent,
    canActivate: [AdminService],
    children: [
      {
        path: "newusertour",
        loadChildren:
          () => import('./pages/new-user-tour/new-user-tour.module').then(m => m.NewUserTourModule),
        canActivate: [AdminService]
      },
      {
        path: "bulk-demo-import/:id",
        loadChildren:
          () => import('./pages/product-wizard/product-wizard.module').then(m => m.ProductWizardModule),
        canActivate: [AdminService]
      },
      {
        path: "dashboard",
        loadChildren:
          () => import('./pages/dashboard/dashboard.module').then(m => m.DashboardkModule),
        canActivate: [AdminService],
        data : { title:'Dashboard'}
      },
      {
        path: "clear",
        loadChildren:
          () => import('./pages/clearCache/dashboard.module').then(m => m.DashboardClearModule),
        canActivate: [AdminService],
        resolve: [AdminService]
      },
      {
        path: "inventory",
        loadChildren:
          () => import('./pages/inventory/inventory.module').then(m => m.InventoryModule),
        canActivate: [AdminService]
      },
      {
        path: "settings",
        loadChildren: () => import('./pages/settings/settings.module').then(m => m.SettingsModule),
        canActivate: [AdminService]
      },
      {
        path: "website-settings",
        loadChildren: () => import('./pages/website-settings/website-settings.module').then(m => m.WebsiteSettingsModule),
        canActivate: [AdminService]
      },
      {
        path: "reservations",
        loadChildren:
          () => import('./pages/reservations/reservations.module').then(m => m.ReservationsModule),
        canActivate: [AdminService]
      },
      {
        path: "report",
        loadChildren: () => import('./pages/report/report.module').then(m => m.ReportModule),
        canActivate: [AdminService]
      },
      {
        path: "register",
        loadChildren: () => import('./pages/register/register.module').then(m => m.RegisterModule),
        canActivate: [AdminService]
      },
      {
        path: "user",
        loadChildren:
          () => import('./pages/user-management/user-management.module').then(m => m.UserManagementModule),
        canActivate: [AdminService, AdminRoleGaurdService]
      },
      {
        path: "client",
        loadChildren:
          () => import('./pages/client-management/client-management.module').then(m => m.ClientManagementModule),
        canActivate: [AdminService, AdminRoleGaurdService]
      },
      {
        path: "customer",
        loadChildren:
          () => import('./pages/customer-management/customer-management.module').then(m => m.CustomerManagementModule),
        canActivate: [AdminService, AdminRoleGaurdService]
      },
      {
        path: "customers",
        loadChildren:
          () => import('./pages/customer-list/customer-list.module').then(m => m.CustomerListModule),
        canActivate: [AdminService]
      },
      {
        path: "plans",
        loadChildren: () => import('./pages/pricing/pricing.module').then(m => m.PricingModule),
        canActivate: [AdminService, AdminRoleGaurdService]
      },
      {
        path: "notifications",
        loadChildren: () => import('./pages/notifications/notifications.module').then(m => m.NotificationsModule)
      },
      {
        path: "**",
        redirectTo: "dashboard",
        pathMatch: "full"
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AdminRoutingModule {}
