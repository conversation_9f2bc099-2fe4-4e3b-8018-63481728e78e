import { NgModule, NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CardConnectCheckoutComponent } from './card-connect-checkout.component';
import { FormsModule } from '@angular/forms';
import { NumberOnlyDirectiveModule } from '../../../modules/directive/directive.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    NumberOnlyDirectiveModule
  ],
  declarations: [CardConnectCheckoutComponent],
  exports: [CardConnectCheckoutComponent]
})
export class CardConnectCheckoutAdminModule { }
