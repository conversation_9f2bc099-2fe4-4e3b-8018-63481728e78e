import {
  Component,
  OnInit,
  Output,
  EventEmitter,
  OnDestroy,
  Input,
  AfterContentInit,
  HostListener
} from "@angular/core";
import { Subscription, Subject, of } from "rxjs";
import { NgForm, FormControl } from "@angular/forms";
import {
  map,
  debounceTime,
  distinctUntilChanged,
  flatMap,
  delay
} from "rxjs/operators";
import { Route, Router } from "@angular/router";
import { monthsArray, createYearArray, iframe, validateCard } from "../../../globals/_classes/functions";

@Component({
  selector: "app-card-connect-checkout",
  templateUrl: "./card-connect-checkout.component.html",
  styleUrls: ["./card-connect-checkout.component.css"]
})
export class CardConnectCheckoutComponent
  implements OnInit, AfterContentInit, OnDestroy {
  @Input("inStore") inStore;
  @Input("btnColor") btnColor: string;
  @Input("loader") loader: boolean;
  @Input("loaderColor") loaderColor: string;
  @Input("contents") contents: any;
  @Input() backBtnPath: any;
  @Input() source:any;
  @Input() is_show_customer_registration: boolean;
  @Input() isAuthenticated: boolean;
  @Input() src: string;
  @Output("onSelectPayment") onSelectPayment = new EventEmitter();

  validCard = false;
  cardTypeimg = "./assets/img/home/<USER>/generic.png";
  cardNo: string;
  cvcNo = "";
  cardName: string;
  expireMonth: any;
  expireYear: any;
  sub: Subscription;
  token;
  paymentInfo = new Object();
  months = monthsArray;
  years = createYearArray();
  public keyUp = new Subject<string>();
  @HostListener("window:message", ["$event"])
  onMassage(event) {
    if (event && event.origin === "https://fts.cardconnect.com:8443") {
      //  this.token = JSON.parse(event.data).message;
    }
  }
  @HostListener("window:resize", ["$event"])
  onResize(event) {
    iframe("cardName", ".iFram");
  }
  constructor(private router: Router) {}

  ngOnInit() {
    if (this.inStore) {
      const obj = {
        site_specific: {
          checkout_payment: {
            lbl_card_number: "Card Number",
            lbl_name: "Name on Card",
            lbl_expiration_data: "Expiration Date",
            lbl_cvv: "CVV Number",
            btn_continue: "Submit"
          }
        }
      };
      this.contents = obj;
    }


    this.btnColor = this.btnColor ? this.btnColor : "btn-orange";
    this.loaderColor = this.loaderColor ? this.loaderColor : "m-loader--orange";

    const subscription = this.keyUp
      .pipe(
        map(event => event),
        debounceTime(1000),
        distinctUntilChanged(),
        flatMap(search => of(search).pipe(delay(500)))
      )
      .subscribe(data => {
        const res = validateCard(data.replace(/[^\d]+/g, ""));
        if (res.status) {
          this.token = data;//Number(data); // assign card no in token
          this.cardNo = data;
          this.validCard = res.status;
          this.cardTypeimg = res.cardImg;
          this.cardName = res.cardName;
        } else {
          this.cardTypeimg = res.cardImg;
          this.cardNo = "";
          this.validCard = res.status;
          this.cardTypeimg = "./assets/img/home/<USER>/generic.png";
          this.cardName = res.cardName;
          this.token = null;
        }
      });
  }
  checkCVC() {
    if (this.cvcNo) {
      this.cvcNo = this.cvcNo.replace(/[^\d]+/g, "");
    }
  }

  submitCardConnect(form: NgForm,capture) {
    this.loader = true;
    this.paymentInfo["payment_gateway_name"] = "CardConnect";
    this.expireMonth = this.expireMonth
      ? this.expireMonth
      : this.months[0].value;
    this.expireYear = this.expireYear ? this.expireYear : this.years[0].value;
    this.paymentInfo["accttyppe"] = this.cardName;
    this.paymentInfo["account"] = this.token; // assign card no in token
    this.paymentInfo["expiry"] = this.expireMonth + this.expireYear;
    this.paymentInfo["cvv2"] = form.value.cvcNo;
    this.paymentInfo["name_on_card"] = form.value.cardHolderName;
    // this.paymentInfo["capture"] = capture;
    this.onSelectPayment.emit(this.paymentInfo);
  }

  ngAfterContentInit() {
    iframe("cardName", ".iFram");
  }

  ngOnDestroy() {}

  selectExpireMonth(e) {
    this.expireMonth = e.target.value;
  }
  selectExpireYear(e) {
    this.expireYear = e.target.value;
  }

  back() {
    this.router.navigateByUrl(this.backBtnPath);
  }
}
