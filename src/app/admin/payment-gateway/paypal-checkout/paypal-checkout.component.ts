import { Component, OnInit, Input, Output, EventEmitter } from "@angular/core";
import { FormGroup, FormBuilder, Validators } from "@angular/forms";
import { debounceTime } from "rxjs/operators";
import { Router } from "@angular/router";
import { monthsArray, createYearArray, getBillingInfo, validateCard } from "../../../globals/_classes/functions";

@Component({
  selector: "app-paypal-checkout",
  templateUrl: "./paypal-checkout.component.html",
  styleUrls: ["./paypal-checkout.component.css"]
})
export class PaypalCheckoutComponent implements OnInit {
  @Input("inStore") inStore;
  @Input("btnColor") btnColor;
  @Input("loader") loader;
  @Input("loaderColor") loaderColor;
  @Input() contents: any;
  @Input() backBtnPath: any;

  // tslint:disable-next-line:no-output-on-prefix
  @Output("onSelectPayment") onSelectPayment = new EventEmitter();

  paymentInfo = new Object();
  form: FormGroup;
  cardTypeimg = "./assets/img/home/<USER>/generic.png";
  cardNo: string;
  cvcNo = "";
  cardName: string;
  expireMonth: any;
  expireYear: any;
  validCard: boolean;
  months = monthsArray;
  years = createYearArray();

  constructor(private route: Router, private fb: FormBuilder) {
    const data = getBillingInfo(route.url);

    this.form = this.fb.group({
      card_number: ["", Validators.required],
      cvv: ["", Validators.required],
      name_on_card: ['', Validators.required],
      // first_name: [data ? data.first_name : "", Validators.required],
      // last_name: [data ? data.last_name : "", Validators.required],
      // billing_address: [data ? data.address_line1 : "", Validators.required],
      // billing_country: [""],
      // billing_city: [data ? data.city : ""],
      // billing_post_code: [data ? data.zipcode : ""],
      // billing_state: [data ? data.state_id : ""]
    });
    this.checkCVC();
    this.checkCard();
  }

  ngOnInit() {

    if (this.inStore) {
      const obj = {
        site_specific: {
          checkout_payment: {
            lbl_card_number: "Card Number",
            lbl_name: "Name on Card",
            lbl_first_name: "First Name",
            lbl_lastname: "Last Name",
            lbl_address: "Address",
            lbl_city: "City",
            lbl_state: "State",
            lbl_zipcode: "Zip Code ",
            lbl_country: "Country ",
            lbl_expiration_data: "Expiration Date",
            lbl_cvv: "CVV Number",
            btn_continue: "Submit"
          }
        }
      };
      this.contents = obj;
    }
    this.btnColor = this.btnColor ? this.btnColor : "btn-orange";
    this.loaderColor = this.loaderColor ? this.loaderColor : "m-loader--orange";
  }
  private checkCVC() {
    this.form
      .get("cvv")
      .valueChanges.pipe(debounceTime(500))
      .subscribe(res => {
        this.cvcNo = this.form.get("cvv").value;
        this.cvcNo = this.cvcNo.replace(/[^\d]+/g, "");
      });
  }

  checkCard() {
    this.form
      .get("card_number")
      .valueChanges.pipe(debounceTime(500))
      .subscribe(data => {
        const cardNo = data;
        const res = validateCard(cardNo.replace(/[^\d]+/g, ""));
        if (res.status) {
          this.cardNo = this.form.get("card_number").value;
          this.validCard = res.status;
          this.cardTypeimg = res.cardImg;
          this.cardName = res.cardName;
        }
      });
  }

  submitPaypal() {

    // this.loader = true;
    // Object.assign(this.paymentInfo, this.form.value);
    // this.paymentInfo["expiry_month"] = this.expireMonth
    //   ? this.expireMonth
    //   : this.months[0].value;
    // this.paymentInfo["expiry_year"] = this.expireYear
    //   ? this.expireYear
    //   : this.years[0].value;
    // this.onSelectPayment.emit({
    //   card: this.paymentInfo,
    //   payment_gateway_name: "PayPal"
    // });

    this.loader = true;
    this.paymentInfo["payment_gateway_name"] = "PayPal";
    this.expireMonth = this.expireMonth
      ? this.expireMonth
      : this.months[0].value;
    this.expireYear = this.expireYear ? this.expireYear : this.years[0].value;
    this.paymentInfo["accttyppe"] = this.cardName;
    this.paymentInfo["account"] = this.form.get('card_number').value; // assign card no in token
    this.paymentInfo["expiry"] = this.expireMonth + this.expireYear;
    this.paymentInfo["cvv2"] = this.form.get('cvv').value;
    this.paymentInfo["name_on_card"] = this.form.get('name_on_card').value;
    this.onSelectPayment.emit(this.paymentInfo);
  }

  selectExpireMonth(e) {
    this.expireMonth = e.target.value;
  }
  selectExpireYear(e) {
    this.expireYear = e.target.value;
  }

  back() {
    this.route.navigateByUrl(this.backBtnPath);
  }
}
