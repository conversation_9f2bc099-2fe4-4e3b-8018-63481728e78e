<div id="accordion">
  <div class="card">
    <div id="collapseOne" class="collapse show">
      <form  #form="ngForm">
        <div class="form-group mb-3" id="cardnumber">
          <label for="card-number">
            {{
              contents && contents.site_specific.checkout_payment
                ? contents.site_specific.checkout_payment.lbl_card_number
                : "Card Number"
            }}
          </label>
          <!-- <div class="iFram">
          </div> -->
          <input
            id="cardNo"
            numberOnly
            (keyup)="keyUp.next($event.target.value)"
            type="text"
            name="cardNo"
            ngModel
            required
            class="form-control"
          />
          <img [src]="cardTypeimg" class="credicardImg" />
        </div>
        <div class="form-group mb-3">
          <label for="name-on-number">
            {{
              contents && contents.site_specific.checkout_payment
                ? contents.site_specific.checkout_payment.lbl_name
                : "Name on Card"
            }}
          </label>
          <input
            id="cardName"
            type="text"
            name="cardHolderName"
            ngModel
            required
            placeholder="Name on Card "
            class="form-control"
          />
        </div>
        <div class="row mb-3">
          <label for="" class="col-md-12">
            {{
              contents && contents.site_specific.checkout_payment
                ? contents.site_specific.checkout_payment.lbl_expiration_data
                : "Expiration Date"
            }}
          </label>
          <div class="form-group col-md-6 ">
            <select (change)="selectExpireMonth($event)" class="form-control">
              <option [value]="month.value" *ngFor="let month of months">{{
                month.text
              }}</option>
            </select>
          </div>
          <div class="form-group col-md-6 ">
            <select
              id="EventXCardYear"
              (change)="selectExpireYear($event)"
              class="form-control"
            >
              <option [value]="year.value" *ngFor="let year of years">{{
                year.text
              }}</option>
            </select>
          </div>
        </div>
        <div class="form-group mb-3">
          <label for="cvv_number">
            {{
              contents && contents.site_specific.checkout_payment
                ? contents.site_specific.checkout_payment.lbl_cvv
                : "CVV Number"
            }}
          </label>
          <input
            maxlength="4"
            numberOnly
            name="cvcNo"
            required
            #ca="ngModel"
            [(ngModel)]="cvcNo"
            (ngModelChange)="checkCVC()"
            type="text"
            placeholder="CVV Number "
            class="form-control"
          />
        </div>

        <div
          class="row justify-content-center mt-5 mb-5 "
          style="position: relative;"
        >
         
          <div
            *ngIf="loader; else buttonShow"
            class="m-loader"
            [ngClass]="loaderColor"
            style="width: 30px; display: inline-block;"
          ></div>
          <ng-template #buttonShow>
              <a
              *ngIf="backBtnPath"
              (click)="back()"
              class="btn theme-btn caps mr-5 mb-3"
              style="margin-left:0px!important; float:left"
            >
              <i class="fa fa-backward"></i>&nbsp;Back</a
            >
            
            <button
              [disabled]="!form.valid || cvcNo.length < 3 || !token"
              class="btn col-md-4 theme-btn"
              [ngClass]="btnColor"
              (click)="submitGoMerchant(form,false)"
            >
           {{fromOrderDetails==false ? 'Authorize' : 'Submit'}} 
              <!-- {{ contents.site_specific.checkout_payment.btn_continue }} -->
            </button>
            &nbsp;&nbsp;
            <button
              *ngIf="fromOrderDetails==false"
              [disabled]="!form.valid || cvcNo.length < 3 || !token"
              class="btn theme-btn"
              [ngClass]="btnColor"
              (click)="submitGoMerchant(form,true)"
            >
              Authorize & capture
            </button>
          </ng-template>
        </div>
      </form>
    </div>
  </div>
</div>
