import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { GoMerchantCheckoutComponent } from './go-merchant-checkout.component';
import { NumberOnlyDirectiveModule } from '../../../modules/directive/directive.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    NumberOnlyDirectiveModule
  ],
  declarations: [GoMerchantCheckoutComponent],
  exports: [GoMerchantCheckoutComponent]
})
export class GoMerchantCheckoutModule { }
