import {
  Component,
  OnInit,
  Output,
  EventEmitter,
  OnDestroy,
  Input,
  AfterContentInit,
  HostListener
} from "@angular/core";
import { Subscription, Subject, of } from "rxjs";
import { NgForm, FormControl } from "@angular/forms";
import {
  map,
  debounceTime,
  distinctUntilChanged,
  flatMap,
  delay
} from "rxjs/operators";
import { Route, Router } from "@angular/router";
import { monthsArray, createYearArray, iframe, validateCard } from "../../../globals/_classes/functions";

@Component({
  selector: "app-go-merchant-checkout",
  templateUrl: "./go-merchant-checkout.component.html",
  styleUrls: ["./go-merchant-checkout.component.css"]
})
export class GoMerchantCheckoutComponent
  implements OnInit, AfterContentInit, OnDestroy {
  @Input("inStore") inStore;
  @Input("btnColor") btnColor: string;
  @Input("loader") loader: boolean;
  @Input("loaderColor") loaderColor: string;
  @Input("contents") contents: any;
  @Input() backBtnPath: any;
  @Input() source:any;
  @Input() src:any;
  @Input() is_show_customer_registration:any;
  @Input() isAuthenticated:any;
  @Output("onSelectPayment") onSelectPayment = new EventEmitter();

  validCard = false;
  cardTypeimg = "./assets/img/home/<USER>/generic.png";
  cardNo: string;
  cvcNo = "";
  cardName: string;
  expireMonth: any;
  expireYear: any;
  sub: Subscription;
  token;
  paymentInfo = new Object();
  months = monthsArray;
  fromOrderDetails
  years = createYearArray();
  public keyUp = new Subject<string>();
  @HostListener("window:message", ["$event"])
  onMassage(event) {
    if (event && event.origin === "https://fts.cardconnect.com:8443") {
      //  this.token = JSON.parse(event.data).message;
    }
  }
  @HostListener("window:resize", ["$event"])
  onResize(event) {
    iframe("cardName", ".iFram");
  }
  constructor(private router: Router) {}

  ngOnInit() {

    const data=JSON.parse(sessionStorage.getItem("fromOrderDetails"))
    if(data !=undefined && data !="" && data !=null)
    {
      this.fromOrderDetails=data;
    }
    else{
      this.fromOrderDetails=false;
    }


    if (this.inStore) {
      const obj = {
        site_specific: {
          checkout_payment: {
            lbl_card_number: "Card Number",
            lbl_name: "Name on Card",
            lbl_expiration_data: "Expiration Date",
            lbl_cvv: "CVV Number",
            btn_continue: "Submit"
          }
        }
      };
      this.contents = obj;
    }


    this.btnColor = this.btnColor ? this.btnColor : "btn-orange";
    this.loaderColor = this.loaderColor ? this.loaderColor : "m-loader--orange";

    const subscription = this.keyUp
      .pipe(
        map(event => event),
        debounceTime(1000),
        distinctUntilChanged(),
        flatMap(search => of(search).pipe(delay(500)))
      )
      .subscribe(data => {
        const res = validateCard(data.replace(/[^\d]+/g, ""));
        if (res.status) {
          this.token = data;//Number(data); // assign card no in token
          this.cardNo = data;
          this.validCard = res.status;
          this.cardTypeimg = res.cardImg;
          this.cardName = res.cardName;
        } else {
          this.cardTypeimg = res.cardImg;
          this.cardNo = "";
          this.validCard = res.status;
          this.cardTypeimg = "./assets/img/home/<USER>/generic.png";
          this.cardName = res.cardName;
          this.token = null;
        }
      });
  }
  checkCVC() {
    if (this.cvcNo) {
      this.cvcNo = this.cvcNo.replace(/[^\d]+/g, "");
    }
  }

  submitGoMerchant(form: NgForm,capture) {
    this.loader = true;
    this.paymentInfo["payment_gateway_name"] = "goMerchant";
    this.expireMonth = this.expireMonth
      ? this.expireMonth
      : this.months[0].value;
    this.expireYear = this.expireYear ? this.expireYear : this.years[0].value;
    this.paymentInfo["accttyppe"] = this.cardName;
    this.paymentInfo["account"] = this.token; // assign card no in token
    this.paymentInfo["expiry"] = this.expireMonth + this.expireYear;
    this.paymentInfo["cvv2"] = form.value.cvcNo;

    if(this.fromOrderDetails==false)
    {
      this.paymentInfo["capture"] = capture;
    }

    this.onSelectPayment.emit(this.paymentInfo);
  }

  ngAfterContentInit() {
    iframe("cardName", ".iFram");
  }

  ngOnDestroy() {}

  selectExpireMonth(e) {
    this.expireMonth = e.target.value;
  }
  selectExpireYear(e) {
    this.expireYear = e.target.value;
  }

  back() {
    this.router.navigateByUrl(this.backBtnPath);
  }
}
