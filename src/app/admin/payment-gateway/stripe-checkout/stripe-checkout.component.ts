import {
  Component,
  OnInit,
  EventEmitter,
  Output,
  Input,
  OnChanges,
  ViewChild,
  ElementRef
} from "@angular/core";
import { FormGroup, FormBuilder, Validators } from "@angular/forms";
import { debounceTime, map, distinctUntilChanged, flatMap, delay } from "rxjs/operators";


import { Router } from "@angular/router";
import { Subject, of } from "rxjs";
import { monthsArray, createYearArray, validateCard } from "../../../globals/_classes/functions";
import { AlertService } from "../../../modules/alert/alert.service";

declare var Stripe: any;

@Component({
  selector: "app-stripe-checkout",
  templateUrl: "./stripe-checkout.component.html",
  styleUrls: ["./stripe-checkout.component.css"]
})
export class StripeCheckoutComponent implements OnInit, OnChanges {
  @Input("inStore") inStore;
  @Input("btnColor") btnColor;
  @Input("loader") loader;
  @Input("loaderColor") loaderColor;
  @Input() contents: any;
  @Input("key") key;
  @Input() backBtnPath;
  @Output("onSelectPayment") onSelectPayment = new EventEmitter();
  @ViewChild("hasCusAlert", {static: false}) alertContainer: ElementRef;

  paymentInfo = new Object();
  form: FormGroup;
  cardTypeimg = "./assets/img/home/<USER>/generic.png";
  cardNo: string;
  cvcNo = "";
  cardName: string;
  expireMonth: any;
  expireYear: any;
  validCard: boolean;
  months = monthsArray;
  years = createYearArray();
  public keyUp = new Subject<string>();
  fromOrderDetails;

  constructor(private router: Router, private fb: FormBuilder,private alertS: AlertService) {
    this.form = this.fb.group({
      cardNo: ["", Validators.required],
      cvcNo: ["", Validators.required],
      cardHolderName: ["", Validators.required]
    });
    this.checkCVC();
    this.checkCard();
  }
  ngOnChanges() {}
  ngOnInit() {

    const data=JSON.parse(sessionStorage.getItem("fromOrderDetails"))
    if(data !=undefined && data !="" && data !=null)
    {
      this.fromOrderDetails=data;
    }
    else{
      this.fromOrderDetails=false;
    }

    if (this.inStore) {
      const obj = {
        site_specific: {
          checkout_payment: {
            lbl_card_number: "Card Number",
            lbl_name: "Name on Card",
            lbl_expiration_data: "Expiration Date",
            lbl_cvv: "CVV Number",
            btn_continue: "Submit"
          }
        }
      };
      this.contents = obj;
    }

    this.btnColor = this.btnColor ? this.btnColor : "btn-orange";
    this.loaderColor = this.loaderColor ? this.loaderColor : "m-loader--orange";
    this.loadExternalScript()
      .then(res => {
        Stripe.setPublishableKey(this.key);
      })
      .catch(err => {
        console.log(err);
      });


      const subscription = this.keyUp
      .pipe(
        map(event => event),
        debounceTime(1000),
        distinctUntilChanged(),
        flatMap(search => of(search).pipe(delay(500)))
      )
      .subscribe(data => {
        const res = validateCard(data.replace(/[^\d]+/g, ""));
        if (res.status) {
         this.key=data;
          this.cardNo = data;
          this.validCard = res.status;
          this.cardTypeimg = res.cardImg;
          this.cardName = res.cardName;
        } else {
          this.cardTypeimg = res.cardImg;
          this.cardNo = "";
          this.validCard = res.status;
          this.cardTypeimg = "./assets/img/home/<USER>/generic.png";
          this.cardName = res.cardName;
          this.key=null;
        }
      });
  }

  private loadExternalScript() {
    // tslint:disable-next-line:no-unused-expression
    return new Promise((resolve, reject) => {
      const scriptElement = document.createElement("script");
      scriptElement.src = "https://js.stripe.com/v2/";
      scriptElement.onload = resolve;
      document.body.appendChild(scriptElement);
    });
  }

  submitStripe(capture) {
    this.loader = true;
    Stripe.card.createToken(
      {
        number: this.cardNo,
        exp_month: this.expireMonth,
        exp_year: this.expireYear,
        cvc: this.cvcNo
      },
      (status: number, response: any) => {
        if (status === 200) {
          this.paymentInfo["payment_gateway_name"] = "Stripe";
          this.expireMonth = this.expireMonth
            ? this.expireMonth
            : this.months[0].value;
          this.expireYear = this.expireYear
            ? this.expireYear
            : this.years[0].value;
          this.paymentInfo["account"] = response.id;
          this.paymentInfo["name_on_card"]=this.form.get('cardHolderName').value;
          if(this.fromOrderDetails==false)
          {
            this.paymentInfo["capture"] = capture;
          }
          this.onSelectPayment.emit(this.paymentInfo);
          
        } else {
          this.loader=false;
          this.alertS.error(
            this.alertContainer,
            response.error.message,
            true,
            5000
          );
          console.log(response.error.message);
        }
      }
    );
  }

  private checkCVC() {
    this.form
      .get("cvcNo")
      .valueChanges.pipe(debounceTime(500))
      .subscribe(res => {
        this.cvcNo = this.form.get("cvcNo").value;
      });
    //   this.cvcNo = this.cvcNo.replace(/[^\d]+/g, '');
  }
  checkCard() {
    this.form
      .get("cardNo")
      .valueChanges.pipe(debounceTime(500))
      .subscribe(data => {
        const cardNo = data;
        const res = validateCard(cardNo.replace(/[^\d]+/g, ""));
        if (res.status) {
          this.cardNo = this.form.get("cardNo").value;
          this.validCard = res.status;
          this.cardTypeimg = res.cardImg;
          this.cardName = res.cardName;
        }
      });
  }

  selectExpireMonth(e) {
    this.expireMonth = e.target.value;
  }
  selectExpireYear(e) {
    this.expireYear = e.target.value;
  }

  back() {
    this.router.navigateByUrl(this.backBtnPath);
  }
}
